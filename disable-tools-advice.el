;; Function to disable tools advice
(defun ai-auto-complete-disable-tools-advice ()
  "Disable the tools advice on ai-auto-complete-complete function."
  (interactive)
  (when (advice-member-p 'ai-auto-complete-tools-advice-request 'ai-auto-complete-complete)
    (advice-remove 'ai-auto-complete-complete 'ai-auto-complete-tools-advice-request)
    (message "Removed tools advice from ai-auto-complete-complete"))
  
  ;; Also set the tools-enabled variable to nil
  (when (boundp 'ai-auto-complete-tools-enabled)
    (setq ai-auto-complete-tools-enabled nil)
    (message "Disabled ai-auto-complete-tools-enabled")))
