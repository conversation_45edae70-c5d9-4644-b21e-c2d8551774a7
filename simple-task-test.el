;;; simple-task-test.el --- Simple test for task management tools -*- lexical-binding: t -*-

;;; Commentary:
;; Simple test to verify task management tools work with vector/list conversion

;;; Code:

;; Load required modules
(add-to-list 'load-path "ai-auto-complete-modules")
(load-file "ai-auto-complete-modules/tools/tools-core.el")
(load-file "ai-auto-complete-modules/tools/task-management.el")

;; Enable tools
(setq ai-auto-complete-tools-enabled t)

(message "=== SIMPLE TASK MANAGEMENT TEST ===")

;; Test 1: Check tool registration
(message "1. Checking tool registration...")
(let ((tools '("create_task_plan" "execute_planned_step" "update_task_plan" "get_task_plan_status")))
  (dolist (tool-name tools)
    (if (gethash tool-name ai-auto-complete-tools)
        (message "   ✓ %s registered" tool-name)
      (message "   ✗ %s NOT registered" tool-name))))

;; Test 2: Test with vector input (simulating LLM JSON array)
(message "2. Testing create_task_plan with vector input...")
(let* ((test-params-vector 
        `((task_description . "Test task with vector input")
          (agent_name . "test-agent")
          (steps . [((description . "Run echo command")
                    (tool_name . "run_command") 
                    (tool_params . ((command . "echo 'Hello World'"))))])))
       (result-vector (condition-case err
                          (ai-auto-complete-tool-create-task-plan test-params-vector)
                        (error (format "ERROR: %s" (error-message-string err))))))
  (message "   Vector result: %s" result-vector))

;; Test 3: Test with list input (normal case)
(message "3. Testing create_task_plan with list input...")
(let* ((test-params-list 
        `((task_description . "Test task with list input")
          (agent_name . "test-agent")
          (steps . (((description . "Run echo command")
                    (tool_name . "run_command") 
                    (tool_params . ((command . "echo 'Hello World'"))))))))
       (result-list (condition-case err
                        (ai-auto-complete-tool-create-task-plan test-params-list)
                      (error (format "ERROR: %s" (error-message-string err))))))
  (message "   List result: %s" result-list))

;; Test 4: Check active plans
(message "4. Checking active task plans...")
(message "   Active plans count: %d" (hash-table-count ai-auto-complete-active-task-plans))
(maphash (lambda (id plan)
           (message "   Plan ID: %s - Request: %s" 
                   id 
                   (ai-auto-complete-task-plan-original-request plan)))
         ai-auto-complete-active-task-plans)

(message "=== SIMPLE TEST COMPLETED ===")

(provide 'simple-task-test)
;;; simple-task-test.el ends here
