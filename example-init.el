;;; example-init.el --- Example initialization for ai-auto-complete-unified

;;; Commentary:
;; This is an example initialization file for ai-auto-complete-unified.
;; Copy the relevant parts to your own init.el file.

;;; Code:

;; Add the package directory to load-path
;; Replace this path with the actual location of the package on your system
(add-to-list 'load-path "~/.emacs.d/ai-auto-complete-unified")
(add-to-list 'load-path (expand-file-name "ai-auto-complete-modules" "~/.emacs.d/ai-auto-complete-unified"))

;; Ensure chat-mode is loaded before the main package
(require 'chat-mode)

;; Load the main package
(require 'ai-auto-complete-unified)

;; Configure API keys (replace with your actual API keys)
(setq ai-auto-complete-gemini-api-key "YOUR_GEMINI_API_KEY_HERE")
(setq ai-auto-complete-openai-api-key "YOUR_OPENAI_API_KEY_HERE")
(setq ai-auto-complete-anthropic-api-key "YOUR_ANTHROPIC_API_KEY_HERE")
(setq ai-auto-complete-openrouter-api-key "YOUR_OPENROUTER_API_KEY_HERE")

;; Set your preferred default backend
(setq ai-auto-complete-backend 'gemini)  ;; Options: 'gemini, 'openai, 'anthropic, 'openrouter

;; Configure models for each backend (optional)
(setq ai-auto-complete-gemini-model "gemini-pro")
(setq ai-auto-complete-openai-model "gpt-4o")
(setq ai-auto-complete-anthropic-model "claude-3-opus-20240229")
(setq ai-auto-complete-openrouter-model "anthropic/claude-3-opus:20240229")

;; Enable mode-specific models (optional)
(setq ai-auto-complete-use-mode-specific-models t)

;; Configure code completion settings
(setq ai-auto-complete-code-backend 'gemini)
(setq ai-auto-complete-code-gemini-model "gemini-pro")
(setq ai-auto-complete-code-openai-model "gpt-4o")

;; Configure text completion settings
(setq ai-auto-complete-text-backend 'anthropic)
(setq ai-auto-complete-text-anthropic-model "claude-3-sonnet-20240229")

;; Configure chat settings
(setq ai-auto-complete-chat-backend 'openai)
(setq ai-auto-complete-chat-openai-model "gpt-4o")

;; Configure context settings
(setq ai-auto-complete-max-context-lines 100)
(setq ai-auto-complete-text-max-context-lines 200)
(setq ai-auto-complete-auto-context t)

;; Configure completion behavior
(setq ai-auto-complete-auto-trigger t)
(setq ai-auto-complete-debounce-delay 0.5)
(setq ai-auto-complete-text-debounce-delay 1.0)

;; Configure UI settings
(setq ai-auto-complete-overlay-face '(:background "#303030" :foreground "#a0a0a0"))
(setq ai-auto-complete-text-overlay-face '(:background "#303030" :foreground "#a0a0a0"))

;; Enable tools for chat mode (if available)
(when (fboundp 'ai-auto-complete-tools-enable)
  (ai-auto-complete-tools-enable))

;; Set up keybindings (optional)
(global-set-key (kbd "C-c C-a c") 'ai-auto-complete-mode)  ;; Toggle code completion mode
(global-set-key (kbd "C-c C-a t") 'ai-auto-complete-text-mode)  ;; Toggle text completion mode
(global-set-key (kbd "C-c C-a h") 'ai-auto-complete-chat)  ;; Open chat interface

;; Enable global modes (optional)
;; Uncomment these lines to enable the modes globally
;; (global-ai-auto-complete-mode 1)  ;; Enable code completion globally
;; (global-ai-auto-complete-text-mode 1)  ;; Enable text completion globally

;; Add hooks to enable modes for specific major modes (optional)
(add-hook 'prog-mode-hook 'ai-auto-complete-mode)
(add-hook 'text-mode-hook 'ai-auto-complete-text-mode)
(add-hook 'markdown-mode-hook 'ai-auto-complete-text-mode)
(add-hook 'org-mode-hook 'ai-auto-complete-text-mode)

;; Configure session persistence for chat (optional)
(setq ai-auto-complete-chat-sessions-directory "~/.emacs.d/ai-auto-complete-chat-sessions")
(setq ai-auto-complete-session-auto-save t)

;; Configure debug mode (optional)
;; Uncomment this line to enable debug mode
;; (when (fboundp 'ai-auto-complete-debug-mode)
;;   (ai-auto-complete-debug-mode 1))

(provide 'example-init)
;;; example-init.el ends here
