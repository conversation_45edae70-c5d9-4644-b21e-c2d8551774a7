;; Stop MCP timers
(when (boundp 'ai-auto-complete-mcp-status-indicator-timer)
  (when ai-auto-complete-mcp-status-indicator-timer
    (message "Stopping MCP status indicator timer")
    (cancel-timer ai-auto-complete-mcp-status-indicator-timer)
    (setq ai-auto-complete-mcp-status-indicator-timer nil)))

;; Disable status indicators
(when (boundp 'ai-auto-complete-mcp-enable-status-indicators)
  (setq ai-auto-complete-mcp-enable-status-indicators nil)
  (message "Disabled MCP status indicators"))

;; Set a high refresh interval
(when (boundp 'ai-auto-complete-mcp-status-indicator-refresh-interval)
  (setq ai-auto-complete-mcp-status-indicator-refresh-interval 300)
  (message "Set MCP status indicator refresh interval to 300 seconds"))

;; Message to confirm changes
(message "MCP timers stopped and status indicators disabled")
