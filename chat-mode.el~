;;; chat-mode.el --- Chat mode for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file contains the minor mode definition for chat functionality.

;;; Code:

(require 'chat-customization)
(require 'utils)



;; Define the mode line variable if it's not already defined
(defvar ai-auto-complete--active-mode-line " AI:Chat"
  "Mode line indicator for AI Auto Complete modes.")

;; Define the pending request variable if it's not already defined
(defvar ai-auto-complete--pending-request nil
  "Flag to track if a request is in progress.")

;; Define the chat backend variable if it's not already defined
(defvar ai-auto-complete-chat-backend nil
  "Backend to use for chat mode.")

;; Define the global backend variable if it's not already defined
(defvar ai-auto-complete-backend 'gemini
  "Default backend to use for AI Auto Complete.")

;; Initialize the chat backend to the global backend if not set
(unless ai-auto-complete-chat-backend
  (setq ai-auto-complete-chat-backend ai-auto-complete-backend))

;; Chat mode variables
(defvar ai-auto-complete--chat-history nil
  "History of messages in the chat buffer.")

(defvar ai-auto-complete--chat-in-progress nil
  "Flag to track if a chat request is in progress.")

(defvar ai-auto-complete--chat-input-marker nil
  "Marker for the current input position in the chat buffer.")

(defvar ai-auto-complete--current-session-id nil
  "ID of the current chat session.")

(defvar ai-auto-complete--session-modified nil
  "Flag to track if the current session has been modified.")


(defvar ai-auto-complete-chat-mode-map
  (let ((map (make-sparse-keymap)))
    (define-key map (kbd "RET") 'ai-auto-complete-chat-send-input) ; this fucntion is in chat.el
    (define-key map (kbd "C-c C-c") 'ai-auto-complete-chat-cancel-request)
    (define-key map (kbd "C-c C-k") 'ai-auto-complete-chat-clear)
    (define-key map (kbd "C-c C-n") 'ai-auto-complete-next-backend)
    ;(define-key map (kbd "C-c C-m") 'ai-auto-complete-select-openrouter-model)
    (define-key map (kbd "C-c C-s") 'ai-auto-complete-chat-session-save)
    (define-key map (kbd "C-c C-l") 'ai-auto-complete-chat-session-load)
    (define-key map (kbd "C-c C-f") 'ai-auto-complete-add-file-to-context)
    (define-key map (kbd "C-c C-b") 'ai-auto-complete-add-buffer-to-context)
    map)
  "Keymap for ai-auto-complete-chat-mode.")


;;;###autoload
(define-minor-mode ai-auto-complete-chat-mode
  "Minor mode for AI-assisted chat interface."
  :lighter ai-auto-complete--active-mode-line
  :keymap ai-auto-complete-chat-mode-map
  :group 'ai-auto-complete-chat
  (if ai-auto-complete-chat-mode
      (progn
        (ai-auto-complete--update-mode-line)
         (setq buffer-read-only t))
    (when ai-auto-complete--chat-in-progress
      (ai-auto-complete-chat-cancel-request))))

(provide 'chat-mode)
;;; chat-mode.el ends here
