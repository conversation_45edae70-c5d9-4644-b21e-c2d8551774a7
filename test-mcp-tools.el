;; Test file for MCP tools conversion

(require 'cl-lib)

;; Define a debug mode variable
(defvar test-debug-mode t
  "Whether to enable debug output.")

;; Helper function to convert a parameter list to a proper plist
(defun test-convert-parameter-to-plist (param)
  "Convert PARAM to a proper plist format.
If PARAM is already a plist, return it as is.
If PARAM is a list of strings, convert it to a plist.
If PARAM is a hash-table, convert it to a plist."
  (cond
   ;; If param is nil, return nil
   ((null param)
    nil)

   ;; If param is already a plist (has keyword symbols), return it as is
   ((and (listp param)
         (> (length param) 0)
         (keywordp (car param)))
    param)

   ;; If param is a list but not a plist, convert it to a plist
   ((listp param)
    (let ((result nil)
          (current-key nil))
      (dolist (item param)
        (if current-key
            (progn
              ;; Convert boolean values
              (let ((value (cond
                            ((and (stringp item) (string= item "true")) t)
                            ((and (stringp item) (string= item "false")) nil)
                            (t item))))
                (setq result (plist-put result
                                       (intern (concat ":" current-key))
                                       value))
                (setq current-key nil)))
          (setq current-key item)))
      result))

   ;; If param is a hash-table, convert it to a plist
   ((hash-table-p param)
    (let ((result nil))
      (maphash (lambda (key value)
                 (setq result (plist-put result
                                        (intern (concat ":" key))
                                        value)))
               param)
      result))

   ;; For any other type, return nil
   (t
    nil)))

;; Convert tools from hash-table to list format
(defun test-convert-tools-to-list (tools server-name)
  "Convert TOOLS to a list format for SERVER-NAME.
If TOOLS is already a list, return it as is.
If TOOLS is a hash-table, convert it to a list of plists.
For any other type, return nil and log a warning."
  (cond
   ;; If tools is nil, return nil
   ((null tools)
    nil)

   ;; If tools is a hash table, convert it to a list
   ((hash-table-p tools)
    (let ((tools-list '()))
      ;; Debug output to help diagnose the issue
      (when test-debug-mode
        (message "Converting hash-table tools for server %s with %d keys"
                 server-name (hash-table-count tools)))

      (maphash (lambda (name tool-data)
                 ;; Debug output for each tool
                 (when test-debug-mode
                   (message "  - Processing tool %s (type: %s)" name (type-of tool-data)))

                 (cond
                  ;; Special case for the format in the JSON file: ["description", "desc text", "parameters", "name", "name"]
                  ((and (listp tool-data)
                        (> (length tool-data) 1)
                        (stringp (car tool-data))
                        (string= (car tool-data) "description"))
                   (let* ((description (if (> (length tool-data) 1) (nth 1 tool-data) "No description"))
                          (parameters-index (cl-position "parameters" tool-data :test #'string=))
                          (parameters (when parameters-index
                                        (let ((param-data (nthcdr (1+ parameters-index) tool-data))
                                              (param-list '()))
                                          (when (and param-data (> (length param-data) 0))
                                            ;; Create a parameter plist from the remaining items
                                            (let ((param-plist (list :name (car param-data))))
                                              (push param-plist param-list)))
                                          param-list)))
                          (tool-plist (list :name name
                                           :description description)))

                     ;; Add parameters if we found any
                     (when parameters
                       (setq tool-plist (plist-put tool-plist :parameters parameters)))

                     (push tool-plist tools-list)
                     (when test-debug-mode
                       (message "    - Added tool %s from special format list" name))))

                  ;; If tool-data is a list, ensure it's a proper plist
                  ((listp tool-data)
                   (let ((tool-plist (test-convert-parameter-to-plist tool-data)))
                     (if tool-plist
                         (let ((tool-with-name (if (plist-member tool-plist :name)
                                                  tool-plist
                                                (plist-put (copy-sequence tool-plist) :name name))))
                           (push tool-with-name tools-list)
                           (when test-debug-mode
                             (message "    - Added tool %s from list data" name)))
                       ;; If conversion failed, create a basic tool plist
                       (push (list :name name
                                  :description "No description"
                                  :parameters nil)
                             tools-list)
                       (when test-debug-mode
                         (message "    - Added basic tool %s after conversion failure" name)))))

                  ;; If tool-data is not a list, create a proper tool plist
                  (t
                   (when test-debug-mode
                     (message "    - Converting tool %s for server %s (type: %s)"
                              name server-name (type-of tool-data)))
                   (let ((description "No description")
                         (tool-plist (list :name name
                                          :description description)))
                     (push tool-plist tools-list)))))
               tools)

      ;; Debug output for the final result
      (when test-debug-mode
        (message "Converted %d tools for server %s" (length tools-list) server-name))

      (nreverse tools-list)))

   ;; For any other type, log a warning and return nil
   (t
    (message "Warning: Tools data for server %s is in an unexpected format: %s"
             server-name (type-of tools))
    nil)))

;; Test function
(defun test-mcp-tools-conversion ()
  "Test the conversion of tools from hash-table to list format."
  (interactive)
  (message "Testing MCP server tools conversion...")

  ;; Create a test hash table with tools in the format from the JSON file
  (let ((test-tools (make-hash-table :test 'equal)))
    ;; Add a tool with the special format
    (puthash "text_to_sound_effects" 
             (list "description" 
                   "Convert text description of a sound effect to sound effect with a given duration." 
                   "parameters" 
                   "name" 
                   "name")
             test-tools)
    
    ;; Add another tool with the special format
    (puthash "brave_local_search" 
             (list "description" 
                   "Search for local businesses and services." 
                   "parameters" 
                   "query" 
                   "query")
             test-tools)

    ;; Convert the tools to list format
    (let ((converted-tools (test-convert-tools-to-list test-tools "test-server")))
      ;; Display the original and converted tools
      (message "Original tools (hash-table): %S" test-tools)
      (message "Converted tools (list): %S" converted-tools)
      (message "Number of tools: %d" (length converted-tools))

      ;; Display each tool
      (dolist (tool converted-tools)
        (message "Tool: %s - %s" 
                 (plist-get tool :name) 
                 (plist-get tool :description))))))

;; Run the test
(test-mcp-tools-conversion)
