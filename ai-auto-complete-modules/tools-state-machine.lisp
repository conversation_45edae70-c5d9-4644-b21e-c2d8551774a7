;;; tools-state-machine.el --- State machine for tools in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a state machine implementation for tools in the AI Auto Complete package.
;; It replaces the deeply nested callback structure with a flatter, more maintainable approach.

;;; Code:

(require 'cl-lib)

;; Define a state object to track tool processing
(cl-defstruct (ai-auto-complete-tool-state (:constructor ai-auto-complete-tool-state-create)
                                          (:copier nil)) ;; :copier nil is good practice unless a copier is specifically needed.
  "Represents the state of tool processing for a single LLM response.
This structure holds all necessary information to manage the
multi-step process of identifying tool calls, executing them,
accumulating results, and continuing the LLM interaction.

Slots:
- `response`: The current LLM response string being processed.
- `tool-calls`: A list of parsed tool calls (e.g., `((tool-name . params) ...)`).
- `current-tool-index`: The index of the tool call currently being processed.
- `tool-results`: A string accumulating the XML-formatted results of executed tools.
- `success-count`: Counter for successfully executed tools.
- `error-count`: Counter for tools that failed during execution.
- `depth`: Recursion depth, used to prevent infinite tool call loops.
- `callback`: The final callback function to invoke with the processed response.
- `backend`: The AI backend used for the initial and continuation LLM calls.
- `original-response`: The initial LLM response that contained tool calls.
- `agent-name`: The name of the agent, if any, that initiated the request.
- `history`: The conversation history up to the point of the current tool processing."
  (response nil :read-only nil :documentation "Current response being processed.")
  (tool-calls nil :read-only nil :documentation "List of tool calls to process.")
  (current-tool-index 0 :read-only nil :documentation "Index of current tool being processed.")
  (tool-results "" :read-only nil :documentation "Accumulated tool results.")
  (success-count 0 :read-only nil :documentation "Number of successful tool calls.")
  (error-count 0 :read-only nil :documentation "Number of failed tool calls.")
  (depth 0 :read-only nil :documentation "Current recursion depth.")
  (callback nil :read-only nil :documentation "Final callback to call with result.")
  (backend nil :read-only nil :documentation "Backend to use for LLM calls.")
  (original-response nil :read-only nil :documentation "Original response with tool calls.")
  (agent-name nil :read-only nil :documentation "Name of the agent making the request.")
  (history nil :read-only nil :documentation "Conversation history for context."))

;; Trampoline function to flatten recursion
(defun ai-auto-complete-tools-trampoline (initial-fn &rest initial-args)
  "Execute a sequence of functions in a state machine without deep recursion.
INITIAL-FN is called with INITIAL-ARGS. If it returns a list
where the `car` is a function, that function is called with the
`cdr` of the list as its arguments. This process repeats until
the returned value is not such a list.

This pattern is used to implement the tool processing state
machine, allowing each step (e.g., parsing, executing a tool,
calling the LLM) to return the next function to execute along
with its state. This avoids deep call stacks that could lead to
stack overflow errors, especially with multiple nested tool calls.

For example, `(list #'next-function new-state)` would cause
`next-function` to be called with `new-state`."
  (let ((result (apply initial-fn initial-args)))
    (while (and result (listp result) (functionp (car result)))
      (setq result (apply (car result) (cdr result))))
    result))

;; Main entry point for tool processing
(defun ai-auto-complete-tools-process-with-state-machine (response callback &optional agent-name)
  "Process an LLM RESPONSE for tool calls using a state machine.

This is the primary entry point for handling responses that may
contain tool invocations. It initializes the state machine via
`ai-auto-complete-tools-trampoline` and `ai-auto-complete-tools-start-processing`.

If tools are disabled, or if the RESPONSE is an error message or
empty, it bypasses tool processing and directly invokes the
CALLBACK.

AGENT-NAME is the optional name of the agent that made the
request. This is used for context and potentially for
agent-specific tool availability or behavior in the future.

CALLBACK is the function to call with the final, processed response."
  (let ((effective-callback (if callback
                               callback
                             (lambda (resp)
                               (ai-auto-complete-tools-default-callback resp agent-name))))
        ;; Get the current conversation history
        (current-history (when (and (boundp 'ai-auto-complete--chat-history)
                                   ai-auto-complete--chat-history)
                          ai-auto-complete--chat-history)))
    (cond
     ;; Check if tools are disabled
     ((not ai-auto-complete-tools-enabled)
      (message "Tools not enabled, returning original response")
      (funcall effective-callback response))

     ;; Check if the response is an error message
     ((string-match-p "^Error:" response)
      (message "Detected error response, not processing for tools: %s"
               (substring response 0 (min 100 (length response))))
      (funcall effective-callback response))

     ;; Check if the response is empty or nil
     ((or (null response) (string-empty-p (string-trim response)))
      (message "Empty response received, not processing for tools")
      (funcall effective-callback (or response "")))

     ;; Process normal response with the state machine
     (t
      (ai-auto-complete-tools-trampoline
       #'ai-auto-complete-tools-start-processing
       response effective-callback agent-name current-history)))))

;; Start processing a response
(defun ai-auto-complete-tools-start-processing (response callback &optional agent-name history)
  "Initialize and start the tool processing state machine for RESPONSE.

This function is the first step in the state machine, called by
`ai-auto-complete-tools-process-with-state-machine` via the trampoline.

It checks if the RESPONSE contains any tool calls (identified by
the presence of \"<tool name=\").
- If no tool calls are found, or if the response is an error,
  it directly invokes the CALLBACK with the original RESPONSE.
- If tool calls are present, it parses them using
  `ai-auto-complete-tools-parse-response`.
  - If parsing yields no tool calls, it invokes CALLBACK.
  - Otherwise, it creates an `ai-auto-complete-tool-state` object
    and returns `(list #'ai-auto-complete-tools-process-next-tool state)`
    to proceed to the next state in the trampoline.

AGENT-NAME and HISTORY are passed to populate the state object."
  (message "ai-auto-complete-tools-start-processing called with history: %s"
           (if history (format "%d messages" (length history)) "nil"))

  ;; Check if the response is an error message
  (if (string-match-p "^Error:" response)
      (progn
        (message "Detected error response, not processing for tools: %s"
                 (substring response 0 (min 100 (length response))))
        (funcall callback response))

    ;; Check if there are any tool calls in the response
    (if (not (string-match-p "<tool name=" response))
        ;; No tool calls, just return the response
        (progn
          (message "No tool calls found, returning response")
          (funcall callback response))
      ;; Parse tool calls
      (let ((tool-calls (ai-auto-complete-tools-parse-response response)))
        (message "Found %d tool calls in response" (length tool-calls))
        (if (null tool-calls)
            ;; No tool calls found after parsing
            (progn
              (message "No tool calls found after parsing, returning response")
              (funcall callback response))
          ;; Create initial state and start processing
          (let* ((backend (if (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode)
                             ai-auto-complete-backend
                           (ai-auto-complete-get-current-backend)))
                 (state (ai-auto-complete-tool-state-create
                         :response response
                         :tool-calls tool-calls
                         :callback callback
                         :backend backend
                         :original-response response
                         :agent-name agent-name
                         :history history)))
            (list #'ai-auto-complete-tools-process-next-tool state)))))))

;; Process the next tool in the queue
(defun ai-auto-complete-tools-process-next-tool (state)
  "Process the next tool call specified in STATE.

This function is a step in the tool processing state machine,
designed to be called via `ai-auto-complete-tools-trampoline`.

It checks if there are more tool calls to process from
`ai-auto-complete-tool-state-tool-calls` in STATE.

- If all tool calls are processed, it returns `(list #'ai-auto-complete-tools-continue-with-llm state)`
  to transition to the LLM continuation phase.
- Otherwise, it executes the current tool call:
  - Retrieves the tool function and parameters.
  - Calls the tool function, handling potential errors.
  - Appends the formatted tool result (or error) to `ai-auto-complete-tool-state-tool-results`.
  - Increments `ai-auto-complete-tool-state-current-tool-index`.
  - Returns `(list #'ai-auto-complete-tools-process-next-tool state)` to process the subsequent tool call."
  (let ((tool-calls (ai-auto-complete-tool-state-tool-calls state))
        (current-index (ai-auto-complete-tool-state-current-tool-index state)))

    ;; Check if we've processed all tools
    (if (>= current-index (length tool-calls))
        ;; All tools processed, continue with LLM
        (list #'ai-auto-complete-tools-continue-with-llm state)

      ;; Process the current tool
      (let* ((tool-call (nth current-index tool-calls))
             (tool-name (car tool-call))
             (params (cdr tool-call))
             (tool (gethash tool-name ai-auto-complete-tools))
             (tool-fn (and tool (plist-get tool :function))))

        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "[TOOLS-DEBUG] Executing tool: %s" tool-name))

        ;; Execute the tool and update state
        (let ((result nil)
              (error-occurred nil)
              (error-msg nil))

          ;; Try to execute the tool
          (condition-case err
              (when tool-fn
                (setq result (funcall tool-fn params))
                (setf (ai-auto-complete-tool-state-success-count state)
                      (1+ (ai-auto-complete-tool-state-success-count state))))
            (error
             (setq error-occurred t)
             (setq error-msg (error-message-string err))
             (setf (ai-auto-complete-tool-state-error-count state)
                   (1+ (ai-auto-complete-tool-state-error-count state)))))

          ;; Handle the result or error
          (let ((formatted-result
                 (cond
                  (error-occurred
                   (let ((msg (format "ERROR executing tool %s: %s" tool-name error-msg)))
                     (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                       (message "[TOOLS-DEBUG] %s" msg))
                     (format "\n\n<tool_result name=\"%s\">\n%s\n</tool_result>" tool-name msg)))

                  ((not tool-fn)
                   (let ((msg (format "ERROR: Tool %s not found or has no function" tool-name)))
                     (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                       (message "[TOOLS-DEBUG] %s" msg))
                     (setf (ai-auto-complete-tool-state-error-count state)
                           (1+ (ai-auto-complete-tool-state-error-count state)))
                     (format "\n\n<tool_result name=\"%s\">\n%s\n</tool_result>" tool-name msg)))

                  (t
                   (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                     (message "[TOOLS-DEBUG] Tool %s executed successfully" tool-name))
                   (format "\n\n<tool_result name=\"%s\">\n%s\n</tool_result>" tool-name result)))))

            ;; Update state with the result
            (setf (ai-auto-complete-tool-state-tool-results state)
                  (concat (ai-auto-complete-tool-state-tool-results state) formatted-result))

            ;; Move to the next tool
            (setf (ai-auto-complete-tool-state-current-tool-index state) (1+ current-index))

            ;; Continue processing
            (list #'ai-auto-complete-tools-process-next-tool state)))))))

;; Continue the conversation with the LLM
(defun ai-auto-complete-tools-continue-with-llm (state)
  "After all tools in a batch are executed, continue interaction with the LLM.

This function is a step in the tool processing state machine.
It is called when `ai-auto-complete-tools-process-next-tool`
determines all parsed tool calls in the current batch from
`ai-auto-complete-tool-state-tool-calls` have been processed.

It constructs a new prompt for the LLM, which includes:
- The original LLM response (with tool calls stripped).
- The accumulated `tool-results` from the executed tools.
- Relevant `history` from the STATE.

This new prompt instructs the LLM to synthesize a final response
to the user, incorporating the information gathered from the
tool executions. The LLM's response is then handled by
`ai-auto-complete-tools-handle-continuation`.
If recursion depth is exceeded, it returns an error message."
  (let* ((tool-results (ai-auto-complete-tool-state-tool-results state))
        (success-count (ai-auto-complete-tool-state-success-count state))
        (error-count (ai-auto-complete-tool-state-error-count state))
        (tool-calls (ai-auto-complete-tool-state-tool-calls state))
        (original-response (ai-auto-complete-tool-state-original-response state))
        (callback (ai-auto-complete-tool-state-callback state))
        (backend (ai-auto-complete-tool-state-backend state))
        (depth (ai-auto-complete-tool-state-depth state))
        (agent-name (ai-auto-complete-tool-state-agent-name state))
        (current-history (ai-auto-complete-tool-state-history state)))

    ;; Log summary of tool execution
    (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
      (message "[TOOLS-DEBUG] Tool execution summary: %d successful, %d failed"
               success-count error-count))

    ;; Check if we've reached the maximum recursion depth
    (if (> depth 5)
        (progn
          (message "Maximum recursion depth reached, returning simplified response")
          ;; Ensure we preserve the agent-name when calling the callback
          (if (and callback (functionp callback))
              (funcall callback (concat original-response
                                      "\n\n[ERROR: Maximum tool recursion depth reached. Please continue without further tool calls.]"))
            ;; If no callback, use the default callback with agent-name
            (ai-auto-complete-tools-default-callback
             (concat original-response
                    "\n\n[ERROR: Maximum tool recursion depth reached. Please continue without further tool calls.]")
             agent-name)))

      ;; Prepare the continuation prompt with tool results
      (let* ((response-without-tools (ai-auto-complete-tools-get-response-without-tools original-response))
             ;; Improved continuation prompt with clearer instructions and conversation history
             (continuation-prompt
              (let ((history-text-for-prompt ""))
                ;; Build history text from the conversation history
                (when current-history
                  (setq history-text-for-prompt "\n\nConversation history:\n")
                  (dolist (msg (reverse (ai-auto-complete-tool-state-history state)))
                    (let ((role (car msg))
                          (content (cdr msg)))
                      (setq history-text-for-prompt (concat history-text-for-prompt
                                               (cond
                                                ((eq role 'user) "User: ")
                                                ((eq role 'agent) (format "Agent %s: " (car content)))
                                                ((eq role 'tool-result) "Tool Results: ")
                                                (t "Assistant: "))
                                               (if (eq role 'agent) (cdr content) content)
                                               "\n\n")))))) ; End of history-text-for-prompt construction

                (format "You have previously responded: \n\n%s\n\n%sThe necessary tools have been executed and their results are now part of the conversation history. Based on this new information, please provide a complete and helpful response that incorporates it. You should NOT just acknowledge receiving the results or ask what to do next - you should actually use the information to fulfill the user's original request.\n\n%s"
                        response-without-tools
                        history-text-for-prompt
                        (if (> error-count 0)
                            (format "NOTE: %d of %d tool calls had errors. The details are in the history."
                                    error-count (length tool-calls))
                          "All tool calls completed successfully and their results are in the history.")))))

        ;; Add tool calls and results to conversation history
        (when (and (boundp 'ai-auto-complete--chat-history) ;; Check if global history exists
                   (eq ai-auto-complete--chat-history current-history)) ;; Ensure we're modifying the same history object
          ;; Create a formatted string of tool calls and results for the history
          (let ((tool-history-entry (format "Tool calls executed:\n%s\n\n%s"
                                           tool-results
                                           (if (> error-count 0)
                                               (format "NOTE: %d of %d tool calls had errors."
                                                       error-count (length tool-calls))
                                              "All tool calls completed successfully."))))
            ;; Add the tool calls and results to the history as a 'tool-result entry
            (push (cons 'tool-result tool-history-entry) current-history) ;; Modify the history from state
            (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
              (message "[TOOLS-DEBUG] Added tool calls and results to conversation history")
              (message "[TOOLS-DEBUG] Current history has %d messages" (length current-history))
              ;; Debug the history content
              (let ((count 0))
                (dolist (hist-msg current-history)
                  (setq count (1+ count))
                  (message "[TOOLS-DEBUG] History item %d - Type: %s, Role: %s"
                           count
                           (type-of hist-msg)
                           (if (listp hist-msg) (car hist-msg) "unknown"))))))))

        ;; Send the continuation prompt to the LLM
        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "[TOOLS-DEBUG] Sending continuation prompt to LLM"))

        ;; Add more debug logging
        (message "[TOOLS-DEBUG] Continuation with agent-name: %s" (or agent-name "nil"))
        (message "[TOOLS-DEBUG] Callback type: %s" (type-of callback))

        ;; Add error handling around the LLM call
        (condition-case err
            ;; Call the LLM with the continuation prompt
            (progn
              (message "[TOOLS-DEBUG] Calling ai-auto-complete-complete with agent-name: %s" (or agent-name "nil"))
              (message "[TOOLS-DEBUG] Passing history with %d messages"
                       (if current-history
                           (length current-history)
                         0))

              ;; Add more detailed debug logging for history
              (when (and (boundp 'ai-auto-complete-tools-debug-mode)
                         ai-auto-complete-tools-debug-mode
                         current-history)
                (let ((count 0))
                  (dolist (msg current-history)
                    (setq count (1+ count))
                    (message "[TOOLS-DEBUG] History item %d - Type: %s, Role: %s"
                             count
                             (type-of msg)
                             (if (listp msg) (car msg) "unknown")))))

              (ai-auto-complete-complete
               backend
               continuation-prompt
               current-history  ;; Pass the (potentially updated) conversation history from state
               (lambda (continuation-response)
                 (message "[TOOLS-DEBUG] Continuation response callback called with agent-name: %s" (or agent-name "nil"))
                 (ai-auto-complete-tools-handle-continuation
                  continuation-response response-without-tools callback (1+ depth) agent-name current-history))
               agent-name))

          ;; Handle any errors that occur during the LLM call
          (error
           (let ((error-msg (format "ERROR during LLM continuation call: %s"
                                    (error-message-string err))))
             (message "[TOOLS-ERROR] %s" error-msg)
             ;; Ensure we preserve the agent-name when calling the callback
             (if (and callback (functionp callback))
                 (funcall callback (concat response-without-tools
                                         "\n\n[ERROR: The AI encountered an error while processing tool results. "
                                         "Please try again or simplify your request.]"))
               ;; If no callback, use the default callback with agent-name
               (ai-auto-complete-tools-default-callback
                (concat response-without-tools
                       "\n\n[ERROR: The AI encountered an error while processing tool results. "
                       "Please try again or simplify your request.]")
                agent-name)))))

        ;; Return nil to signal the end of this branch of the state machine
        nil))))

;; Handle the continuation response from the LLM
(defun ai-auto-complete-tools-handle-continuation (continuation-response response-without-tools callback depth &optional agent-name history)
  "Handle the LLM's CONTINUATION-RESPONSE after tool results were provided.

This function is a step in the tool processing state machine,
typically invoked as a callback from `ai-auto-complete-complete`
after the LLM has processed tool results.

It checks if the CONTINUATION-RESPONSE itself contains further
tool calls.
- If yes, and if the recursion DEPTH is not exceeded, it
  re-enters the tool processing loop by calling
  `ai-auto-complete-tools-start-processing` via the trampoline.
  The `response-without-tools` (which is the text part of the
  original LLM response that led to the first batch of tool calls)
  is prepended to the final result.
- If no, it invokes the final CALLBACK with the combined response."
  (message "[TOOLS-DEBUG] ai-auto-complete-tools-handle-continuation called with agent-name: %s" (or agent-name "nil"))
  (message "[TOOLS-DEBUG] Callback type: %s" (type-of callback))

  ;; Get the current conversation history
  (let ((current-history (when (and (boundp 'ai-auto-complete--chat-history)
                                   ai-auto-complete--chat-history)
                          ai-auto-complete--chat-history)))

    ;; Add debug logging for the conversation history
    (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
      (message "[TOOLS-DEBUG] handle-continuation has access to history with %d messages"
               (if history (length history) 0)) ;; Use the passed history
      ;; Debug the history content
      (let ((count 0))
        (dolist (hist-msg history) ;; Use the passed history
          (setq count (1+ count))
          (message "[TOOLS-DEBUG] History item %d - Type: %s, Role: %s"
                   count
                   (type-of hist-msg)
                   (if (listp hist-msg) (car hist-msg) "unknown"))))))
    ;; Check if the continuation response is an error
    (if (string-match-p "^Error:" continuation-response)
        (progn
          (message "Detected error in continuation response, not processing for tools: %s"
                   (substring continuation-response 0 (min 100 (length continuation-response))))
          ;; Ensure we preserve the agent-name when calling the callback
          (if (and callback (functionp callback))
              (funcall callback (concat response-without-tools
                                       "\n\n[ERROR: The AI encountered an error while processing tool results. "
                                       "Please try again or simplify your request.]"))
            ;; If no callback, use the default callback with agent-name
            (ai-auto-complete-tools-default-callback
             (concat response-without-tools
                    "\n\n[ERROR: The AI encountered an error while processing tool results. "
                    "Please try again or simplify your request.]")
             agent-name)))

    ;; Check if the continuation response has more tool calls
    (if (string-match-p "<tool name=" continuation-response)
        ;; If it does, process them with a new state machine
        (let ((new-tool-calls (ai-auto-complete-tools-parse-response continuation-response)))
          (if new-tool-calls
              ;; Pass the agent-name and updated history to the callback to ensure it's preserved
              (ai-auto-complete-tools-trampoline
               #'ai-auto-complete-tools-start-processing
               continuation-response
               (lambda (final-response)
                 ;; Ensure we preserve the agent-name when calling the callback
                 (if (and callback (functionp callback))
                     (funcall callback (concat response-without-tools "\n\n" final-response))
                   ;; If no callback, use the default callback with agent-name
                   (ai-auto-complete-tools-default-callback
                    (concat response-without-tools "\n\n" final-response)
                    agent-name)))
               agent-name
               history)  ;; Pass the current conversation history
            ;; No tool calls found (shouldn't happen given the string-match above)
            ;; Ensure we preserve the agent-name when calling the callback
            (if (and callback (functionp callback))
                (funcall callback (concat response-without-tools "\n\n" continuation-response))
              ;; If no callback, use the default callback with agent-name
              (ai-auto-complete-tools-default-callback
               (concat response-without-tools "\n\n" continuation-response)
               agent-name))))
      ;; No more tool calls, return the combined response
      ;; Ensure we preserve the agent-name when calling the callback
      (if (and callback (functionp callback))
          (funcall callback (concat response-without-tools "\n\n" continuation-response))
        ;; If no callback, use the default callback with agent-name
        (ai-auto-complete-tools-default-callback
         (concat response-without-tools "\n\n" continuation-response)
         agent-name))))))

(provide 'tools/tools-state-machine)
;;; tools-state-machine.el ends here
