;;; enhanced-chat.el --- Enhanced chat interface for AI Auto Complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides an enhanced chat interface for AI Auto Complete.
;; It adds support for streaming, markdown rendering, and interactive elements.

;;; Code:

(require 'cl-lib)
(require 'ui/markdown-renderer)
(require 'ui/streaming)

;; Customization options for the enhanced chat interface
(defgroup ai-auto-complete-enhanced-chat nil
  "Settings for the enhanced chat interface in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-enhanced-chat-")

(defcustom ai-auto-complete-enhanced-chat-buffer-name "*AI Auto Complete Chat*"
  "Name of the enhanced chat buffer."
  :type 'string
  :group 'ai-auto-complete-enhanced-chat)

(defcustom ai-auto-complete-enhanced-chat-show-timestamps t
  "Whether to show timestamps for messages and events."
  :type 'boolean
  :group 'ai-auto-complete-enhanced-chat)

(defcustom ai-auto-complete-enhanced-chat-show-tool-calls t
  "Whether to show tool calls in the chat interface."
  :type 'boolean
  :group 'ai-auto-complete-enhanced-chat)

(defcustom ai-auto-complete-enhanced-chat-show-tool-results t
  "Whether to show tool results in the chat interface."
  :type 'boolean
  :group 'ai-auto-complete-enhanced-chat)

(defcustom ai-auto-complete-enhanced-chat-show-agent-controls t
  "Whether to show agent controls in the chat interface."
  :type 'boolean
  :group 'ai-auto-complete-enhanced-chat)

;; Define faces for the enhanced chat interface
(defface ai-auto-complete-enhanced-chat-timestamp-face
  '((t :foreground "#888a85" :slant italic :height 0.8))
  "Face for timestamps in the enhanced chat interface."
  :group 'ai-auto-complete-enhanced-chat)

(defface ai-auto-complete-enhanced-chat-tool-face
  '((t :foreground "#ad7fa8" :weight bold))
  "Face for tool calls in the enhanced chat interface."
  :group 'ai-auto-complete-enhanced-chat)

(defface ai-auto-complete-enhanced-chat-tool-result-face
  '((t :foreground "#729fcf" :weight bold))
  "Face for tool results in the enhanced chat interface."
  :group 'ai-auto-complete-enhanced-chat)

(defface ai-auto-complete-enhanced-chat-agent-face
  '((t :foreground "#fcaf3e" :weight bold))
  "Face for agent messages in the enhanced chat interface."
  :group 'ai-auto-complete-enhanced-chat)

(defface ai-auto-complete-enhanced-chat-button-face
  '((t :box t :foreground "#729fcf" :background "#eeeeec"))
  "Face for buttons in the enhanced chat interface."
  :group 'ai-auto-complete-enhanced-chat)

(defface ai-auto-complete-enhanced-chat-header-face
  '((t :height 1.2 :weight bold :foreground "#3465a4" :background "#eeeeec"))
  "Face for the header in the enhanced chat interface."
  :group 'ai-auto-complete-enhanced-chat)

;; Variables for tracking the enhanced chat state
(defvar-local ai-auto-complete-enhanced-chat-mode nil
  "Mode variable for the enhanced chat interface.")

(defvar-local ai-auto-complete-enhanced-chat-input-marker nil
  "Marker for the current input position in the enhanced chat buffer.")

(defvar-local ai-auto-complete-enhanced-chat-header-marker nil
  "Marker for the header section in the enhanced chat buffer.")

(defvar-local ai-auto-complete-enhanced-chat-content-marker nil
  "Marker for the content section in the enhanced chat buffer.")

(defvar-local ai-auto-complete-enhanced-chat-footer-marker nil
  "Marker for the footer section in the enhanced chat buffer.")

(defvar-local ai-auto-complete-enhanced-chat-active-agent nil
  "The currently active agent in the enhanced chat interface.")

(defvar-local ai-auto-complete-enhanced-chat-available-agents nil
  "List of available agents in the enhanced chat interface.")

;; Define the enhanced chat mode map
(defvar ai-auto-complete-enhanced-chat-mode-map
  (let ((map (make-sparse-keymap)))
    (define-key map (kbd "RET") 'ai-auto-complete-enhanced-chat-send-message)
    (define-key map (kbd "C-c C-c") 'ai-auto-complete-enhanced-chat-cancel)
    (define-key map (kbd "C-c C-k") 'ai-auto-complete-enhanced-chat-clear)
    (define-key map (kbd "C-c C-s") 'ai-auto-complete-enhanced-chat-save-session)
    (define-key map (kbd "C-c C-l") 'ai-auto-complete-enhanced-chat-load-session)
    (define-key map (kbd "C-c C-a") 'ai-auto-complete-enhanced-chat-select-agent)
    (define-key map (kbd "C-c C-t") 'ai-auto-complete-enhanced-chat-toggle-timestamps)
    (define-key map (kbd "C-c C-r") 'ai-auto-complete-enhanced-chat-toggle-tool-results)
    map)
  "Keymap for the enhanced chat interface.")

;; Define the enhanced chat mode
(define-minor-mode ai-auto-complete-enhanced-chat-mode
  "Minor mode for the enhanced chat interface."
  :init-value nil
  :lighter " EnhancedChat"
  :keymap ai-auto-complete-enhanced-chat-mode-map
  (if ai-auto-complete-enhanced-chat-mode
      (ai-auto-complete-enhanced-chat-initialize)
    (ai-auto-complete-enhanced-chat-cleanup)))

;; Main function to start the enhanced chat interface
(defun ai-auto-complete-enhanced-chat ()
  "Start or switch to the enhanced chat interface."
  (interactive)
  (let ((chat-buffer (get-buffer-create ai-auto-complete-enhanced-chat-buffer-name)))
    (with-current-buffer chat-buffer
      ;; Set up the buffer with text-mode as the base mode
      (unless (eq major-mode 'text-mode)
        (text-mode))

      ;; Enable our enhanced chat minor mode
      (unless ai-auto-complete-enhanced-chat-mode
        (ai-auto-complete-enhanced-chat-mode 1)))

    ;; Switch to the chat buffer
    (switch-to-buffer chat-buffer)))

;; Initialize the enhanced chat interface
(defun ai-auto-complete-enhanced-chat-initialize ()
  "Initialize the enhanced chat interface."
  (let ((inhibit-read-only t))
    ;; Clear the buffer
    (erase-buffer)

    ;; Create the header section
    (insert (propertize "AI Auto Complete Enhanced Chat\n"
                       'face 'ai-auto-complete-enhanced-chat-header-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))
    (insert (propertize (format-time-string "Started on %Y-%m-%d %H:%M:%S\n")
                       'face 'ai-auto-complete-enhanced-chat-timestamp-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add control buttons
    (ai-auto-complete-enhanced-chat-insert-control-buttons)

    ;; Set the header marker
    (setq ai-auto-complete-enhanced-chat-header-marker (point-marker))

    ;; Add a separator
    (insert (propertize "\n" 'read-only t 'front-sticky t 'rear-nonsticky t))

    ;; Set the content marker
    (setq ai-auto-complete-enhanced-chat-content-marker (point-marker))

    ;; Add some instructions
    (insert (propertize "Type your message below and press Enter to send.\n\n"
                       'face 'ai-auto-complete-system-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add the input prompt
    (insert (propertize "USER: "
                       'face 'ai-auto-complete-user-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Set the input marker
    (setq ai-auto-complete-enhanced-chat-input-marker (point-marker))

    ;; Make sure the cursor is at the input position
    (goto-char (point-max))

    ;; Ensure the buffer is writable at the input position
    (put-text-property (point) (point) 'read-only nil)))

;; Insert control buttons in the header
(defun ai-auto-complete-enhanced-chat-insert-control-buttons ()
  "Insert control buttons in the header of the enhanced chat interface."
  (let ((inhibit-read-only t))
    ;; Create a button bar
    (insert (propertize "[ "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add agent selection button
    (insert-text-button "Select Agent"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-select-agent-action
                       'follow-link t
                       'help-echo "Select an agent to chat with")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add save session button
    (insert-text-button "Save Session"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-save-session-action
                       'follow-link t
                       'help-echo "Save the current chat session")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add load session button
    (insert-text-button "Load Session"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-load-session-action
                       'follow-link t
                       'help-echo "Load a saved chat session")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add clear chat button
    (insert-text-button "Clear Chat"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-clear-action
                       'follow-link t
                       'help-echo "Clear the chat history")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add toggle timestamps button
    (insert-text-button (if ai-auto-complete-enhanced-chat-show-timestamps
                           "Hide Timestamps"
                         "Show Timestamps")
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-toggle-timestamps-action
                       'follow-link t
                       'help-echo "Toggle display of timestamps")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add toggle tool results button
    (insert-text-button (if ai-auto-complete-enhanced-chat-show-tool-results
                           "Hide Tool Results"
                         "Show Tool Results")
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-enhanced-chat-toggle-tool-results-action
                       'follow-link t
                       'help-echo "Toggle display of tool results")

    (insert (propertize " ]\n"
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))))

;; Button action functions
(defun ai-auto-complete-enhanced-chat-select-agent-action (button)
  "Action for the select agent button."
  (ai-auto-complete-enhanced-chat-select-agent))

(defun ai-auto-complete-enhanced-chat-save-session-action (button)
  "Action for the save session button."
  (ai-auto-complete-enhanced-chat-save-session))

(defun ai-auto-complete-enhanced-chat-load-session-action (button)
  "Action for the load session button."
  (ai-auto-complete-enhanced-chat-load-session))

(defun ai-auto-complete-enhanced-chat-clear-action (button)
  "Action for the clear chat button."
  (ai-auto-complete-enhanced-chat-clear))

(defun ai-auto-complete-enhanced-chat-toggle-timestamps-action (button)
  "Action for the toggle timestamps button."
  (ai-auto-complete-enhanced-chat-toggle-timestamps))

(defun ai-auto-complete-enhanced-chat-toggle-tool-results-action (button)
  "Action for the toggle tool results button."
  (ai-auto-complete-enhanced-chat-toggle-tool-results))

;; Main interactive functions
(defun ai-auto-complete-enhanced-chat-send-message ()
  "Send the current message in the enhanced chat interface."
  (interactive)
  (when ai-auto-complete-enhanced-chat-mode
    (let ((input-text (buffer-substring-no-properties
                      ai-auto-complete-enhanced-chat-input-marker
                      (point-max))))
      (when (not (string-empty-p (string-trim input-text)))
        ;; Start streaming for the user message
        (ai-auto-complete-streaming-start 'user)
        (ai-auto-complete-streaming-update input-text)
        (ai-auto-complete-streaming-complete)

        ;; Check if the message is directed to a specific agent
        (if (and (string-match "^@\\([A-Za-z0-9_-]+\\)\\s-+\\(.+\\)$" input-text)
                 (boundp 'ai-auto-complete-agents-enabled)
                 ai-auto-complete-agents-enabled)
            (let ((agent-name (match-string 1 input-text))
                  (agent-message (match-string 2 input-text)))
              ;; Set the active agent
              (setq ai-auto-complete-enhanced-chat-active-agent agent-name)

              ;; Start streaming for the agent response
              (ai-auto-complete-streaming-start 'agent agent-name)

              ;; Process the agent message (this would call the actual agent processing)
              (when (fboundp 'ai-auto-complete-process-agent-message)
                (ai-auto-complete-process-agent-message agent-name agent-message
                                                      (lambda (response)
                                                        ;; Simulate streaming for the agent response
                                                        (ai-auto-complete-streaming-simulate response 'agent agent-name)))))

          ;; Regular message to the default assistant
          (progn
            ;; Start streaming for the assistant response
            (ai-auto-complete-streaming-start 'assistant)

            ;; Process the message (this would call the actual backend)
            (let ((prompt (ai-auto-complete-chat-build-prompt)))
              (cond
               ((eq ai-auto-complete-backend 'gemini)
                (when (fboundp 'ai-auto-complete--gemini-complete)
                  (ai-auto-complete--gemini-complete prompt
                                                   (lambda (response)
                                                     (ai-auto-complete-streaming-simulate response)))))
               ((eq ai-auto-complete-backend 'openai)
                (when (fboundp 'ai-auto-complete--openai-complete)
                  (ai-auto-complete--openai-complete prompt
                                                   (lambda (response)
                                                     (ai-auto-complete-streaming-simulate response)))))
               ((eq ai-auto-complete-backend 'anthropic)
                (when (fboundp 'ai-auto-complete--anthropic-complete)
                  (ai-auto-complete--anthropic-complete prompt
                                                      (lambda (response)
                                                        (ai-auto-complete-streaming-simulate response)))))
               ((eq ai-auto-complete-backend 'openrouter)
                (when (fboundp 'ai-auto-complete--openrouter-complete)
                  (ai-auto-complete--openrouter-complete prompt
                                                       (lambda (response)
                                                         (ai-auto-complete-streaming-simulate response)))))))))

        ;; Clear the input area
        (let ((inhibit-read-only t))
          (delete-region ai-auto-complete-enhanced-chat-input-marker (point-max))
          (goto-char ai-auto-complete-enhanced-chat-input-marker))))))

(defun ai-auto-complete-enhanced-chat-select-agent ()
  "Select an agent to chat with."
  (interactive)
  (when (and (boundp 'ai-auto-complete-agents-enabled)
             ai-auto-complete-agents-enabled
             (boundp 'ai-auto-complete-agents)
             (hash-table-p ai-auto-complete-agents))
    (let* ((agents (hash-table-keys ai-auto-complete-agents))
           (agent-name (completing-read "Select agent: " agents nil t)))
      (when (not (string-empty-p agent-name))
        (setq ai-auto-complete-enhanced-chat-active-agent agent-name)
        (message "Selected agent: %s" agent-name)))))

(defun ai-auto-complete-enhanced-chat-save-session ()
  "Save the current chat session."
  (interactive)
  (when (fboundp 'ai-auto-complete-chat-save-session)
    (ai-auto-complete-chat-save-session)))

(defun ai-auto-complete-enhanced-chat-load-session ()
  "Load a saved chat session."
  (interactive)
  (when (fboundp 'ai-auto-complete-chat-load-session)
    (ai-auto-complete-chat-load-session)))

(defun ai-auto-complete-enhanced-chat-clear ()
  "Clear the chat history."
  (interactive)
  (when (yes-or-no-p "Clear the chat history? ")
    (setq ai-auto-complete--chat-history nil)
    (ai-auto-complete-enhanced-chat-initialize)))

(defun ai-auto-complete-enhanced-chat-toggle-timestamps ()
  "Toggle display of timestamps."
  (interactive)
  (setq ai-auto-complete-enhanced-chat-show-timestamps
        (not ai-auto-complete-enhanced-chat-show-timestamps))
  (message "Timestamps %s"
           (if ai-auto-complete-enhanced-chat-show-timestamps "enabled" "disabled"))
  (ai-auto-complete-enhanced-chat-refresh))

(defun ai-auto-complete-enhanced-chat-toggle-tool-results ()
  "Toggle display of tool results."
  (interactive)
  (setq ai-auto-complete-enhanced-chat-show-tool-results
        (not ai-auto-complete-enhanced-chat-show-tool-results))
  (message "Tool results %s"
           (if ai-auto-complete-enhanced-chat-show-tool-results "enabled" "disabled"))
  (ai-auto-complete-enhanced-chat-refresh))

(defun ai-auto-complete-enhanced-chat-refresh ()
  "Refresh the enhanced chat interface."
  (when ai-auto-complete-enhanced-chat-mode
    (let ((inhibit-read-only t))
      ;; Save the current input text
      (let ((input-text (when (and ai-auto-complete-enhanced-chat-input-marker
                                  (marker-position ai-auto-complete-enhanced-chat-input-marker))
                         (buffer-substring-no-properties
                          ai-auto-complete-enhanced-chat-input-marker
                          (point-max)))))
        ;; Reinitialize the interface
        (ai-auto-complete-enhanced-chat-initialize)

        ;; Display the conversation history
        (when (boundp 'ai-auto-complete--chat-history)
          (ai-auto-complete-enhanced-chat-display-conversation))

        ;; Restore the input text
        (when input-text
          (goto-char ai-auto-complete-enhanced-chat-input-marker)
          (insert input-text))))))

(defun ai-auto-complete-enhanced-chat-display-conversation ()
  "Display the current conversation history in the enhanced chat buffer."
  (when (and (boundp 'ai-auto-complete--chat-history)
             ai-auto-complete--chat-history)
    (let ((inhibit-read-only t))
      ;; Find where the content begins
      (goto-char ai-auto-complete-enhanced-chat-content-marker)

      ;; Clear the content area
      (delete-region ai-auto-complete-enhanced-chat-content-marker
                    (if (and ai-auto-complete-enhanced-chat-input-marker
                            (marker-position ai-auto-complete-enhanced-chat-input-marker))
                        ai-auto-complete-enhanced-chat-input-marker
                      (point-max)))

      ;; Display each message in the history (in reverse order since history is newest-first)
      (dolist (msg (reverse ai-auto-complete--chat-history))
        (let ((role (car msg))
              (content (cdr msg)))
          (cond
           ((eq role 'user)
            (when ai-auto-complete-enhanced-chat-show-timestamps
              (insert (propertize (format-time-string "[%H:%M:%S] ")
                                 'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t)))
            (insert (propertize "USER: "
                               'face 'ai-auto-complete-user-face
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))
            (insert (propertize (ai-auto-complete-markdown-render content)
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t)))

           ((eq role 'assistant)
            (when ai-auto-complete-enhanced-chat-show-timestamps
              (insert (propertize (format-time-string "[%H:%M:%S] ")
                                 'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t)))
            (insert (propertize "AI: "
                               'face 'ai-auto-complete-assistant-face
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))
            (insert (propertize (ai-auto-complete-markdown-render content)
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t)))

           ((eq role 'agent)
            (when ai-auto-complete-enhanced-chat-show-timestamps
              (insert (propertize (format-time-string "[%H:%M:%S] ")
                                 'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t)))
            (let ((agent-name (car content))
                  (agent-content (cdr content)))
              (insert (propertize (format "AGENT-%s: " agent-name)
                                 'face 'ai-auto-complete-enhanced-chat-agent-face
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t))
              (insert (propertize (ai-auto-complete-markdown-render agent-content)
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t))))

           ((eq role 'tool)
            (when (and ai-auto-complete-enhanced-chat-show-tool-calls
                      ai-auto-complete-enhanced-chat-show-timestamps)
              (insert (propertize (format-time-string "[%H:%M:%S] ")
                                 'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t)))
            (when ai-auto-complete-enhanced-chat-show-tool-calls
              (insert (propertize "TOOL: "
                                 'face 'ai-auto-complete-enhanced-chat-tool-face
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t))
              (insert (propertize content
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t))))

           ((eq role 'tool-result)
            (when (and ai-auto-complete-enhanced-chat-show-tool-results
                      ai-auto-complete-enhanced-chat-show-timestamps)
              (insert (propertize (format-time-string "[%H:%M:%S] ")
                                 'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t)))
            (when ai-auto-complete-enhanced-chat-show-tool-results
              (insert (propertize "RESULT: "
                                 'face 'ai-auto-complete-enhanced-chat-tool-result-face
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t))
              (insert (propertize (ai-auto-complete-markdown-render content)
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t)))))

          ;; Add a newline after each message
          (insert (propertize "\n\n"
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t))))

      ;; Add the input prompt
      (insert (propertize "USER: "
                         'face 'ai-auto-complete-user-face
                         'read-only t
                         'front-sticky t
                         'rear-nonsticky t))

      ;; Set the input marker
      (setq ai-auto-complete-enhanced-chat-input-marker (point-marker))

      ;; Make sure the cursor is at the input position
      (goto-char (point-max))

      ;; Ensure the buffer is writable at the input position
      (put-text-property (point) (point) 'read-only nil))))

;; Cleanup function
(defun ai-auto-complete-enhanced-chat-cleanup ()
  "Clean up the enhanced chat interface."
  (when ai-auto-complete-enhanced-chat-input-marker
    (set-marker ai-auto-complete-enhanced-chat-input-marker nil))
  (when ai-auto-complete-enhanced-chat-header-marker
    (set-marker ai-auto-complete-enhanced-chat-header-marker nil))
  (when ai-auto-complete-enhanced-chat-content-marker
    (set-marker ai-auto-complete-enhanced-chat-content-marker nil))
  (when ai-auto-complete-enhanced-chat-footer-marker
    (set-marker ai-auto-complete-enhanced-chat-footer-marker nil)))

(provide 'ui/enhanced-chat)
;;; enhanced-chat.el ends here
