;;; markdown-renderer.el --- Markdown rendering for chat interface -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides markdown rendering functionality for the chat interface.
;; It converts markdown text to formatted text with proper faces and properties.

;;; Code:

(require 'cl-lib)
(require 'subr-x)
(require 'font-lock)

;; Define faces for markdown elements
(defface ai-auto-complete-markdown-heading-1-face
  '((t :height 1.5 :weight bold :inherit variable-pitch))
  "Face for level 1 headings in markdown."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-markdown-heading-2-face
  '((t :height 1.3 :weight bold :inherit variable-pitch))
  "Face for level 2 headings in markdown."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-markdown-heading-3-face
  '((t :height 1.1 :weight bold :inherit variable-pitch))
  "Face for level 3 headings in markdown."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-markdown-bold-face
  '((t :weight bold))
  "Face for bold text in markdown."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-markdown-italic-face
  '((t :slant italic))
  "Face for italic text in markdown."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-markdown-code-face
  '((t :inherit fixed-pitch :background "#f0f0f0" :foreground "#333333"))
  "Face for inline code in markdown."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-markdown-link-face
  '((t :foreground "blue" :underline t))
  "Face for links in markdown."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-markdown-blockquote-face
  '((t :slant italic :foreground "#777777" :background "#f9f9f9"))
  "Face for blockquotes in markdown."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-markdown-list-face
  '((t :weight bold))
  "Face for list markers in markdown."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-markdown-code-block-face
  '((t :inherit fixed-pitch :background "#f5f5f5"))
  "Face for code blocks in markdown."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-markdown-table-face
  '((t :inherit fixed-pitch))
  "Face for tables in markdown."
  :group 'ai-auto-complete-chat)

;; Define a variable to store syntax highlighting functions for different languages
(defvar ai-auto-complete-markdown-syntax-highlighting-functions
  '(("elisp" . ai-auto-complete-markdown-highlight-elisp)
    ("emacs-lisp" . ai-auto-complete-markdown-highlight-elisp)
    ("lisp" . ai-auto-complete-markdown-highlight-elisp)
    ("python" . ai-auto-complete-markdown-highlight-python)
    ("js" . ai-auto-complete-markdown-highlight-javascript)
    ("javascript" . ai-auto-complete-markdown-highlight-javascript)
    ("typescript" . ai-auto-complete-markdown-highlight-javascript)
    ("json" . ai-auto-complete-markdown-highlight-json)
    ("html" . ai-auto-complete-markdown-highlight-html)
    ("css" . ai-auto-complete-markdown-highlight-css)
    ("shell" . ai-auto-complete-markdown-highlight-shell)
    ("bash" . ai-auto-complete-markdown-highlight-shell))
  "Alist mapping language names to syntax highlighting functions.")

;; Main function to render markdown text
(defun ai-auto-complete-markdown-render (text)
  "Render TEXT as markdown and return the formatted text."
  (with-temp-buffer
    (insert text)
    (ai-auto-complete-markdown-render-buffer)
    (buffer-string)))

;; Function to render markdown in the current buffer
(defun ai-auto-complete-markdown-render-buffer ()
  "Render markdown in the current buffer."
  (let ((inhibit-read-only t))
    ;; Process code blocks first to avoid interference with other patterns
    (ai-auto-complete-markdown-render-code-blocks)

    ;; Process other markdown elements
    (ai-auto-complete-markdown-render-headings)
    (ai-auto-complete-markdown-render-bold-italic)
    (ai-auto-complete-markdown-render-inline-code)
    (ai-auto-complete-markdown-render-links)
    (ai-auto-complete-markdown-render-blockquotes)
    (ai-auto-complete-markdown-render-lists)
    (ai-auto-complete-markdown-render-tables)))

;; Helper functions for rendering different markdown elements

(defun ai-auto-complete-markdown-render-headings ()
  "Render markdown headings in the current buffer."
  (goto-char (point-min))
  (while (re-search-forward "^\\(#\\{1,6\\}\\)\\s-+\\(.+\\)$" nil t)
    (let* ((level (length (match-string 1)))
           (heading-text (match-string 2))
           (face (cond
                  ((= level 1) 'ai-auto-complete-markdown-heading-1-face)
                  ((= level 2) 'ai-auto-complete-markdown-heading-2-face)
                  (t 'ai-auto-complete-markdown-heading-3-face))))
      (replace-match (propertize heading-text 'face face) nil nil nil 0))))

(defun ai-auto-complete-markdown-render-bold-italic ()
  "Render bold and italic text in the current buffer."
  ;; Bold
  (goto-char (point-min))
  (while (re-search-forward "\\*\\*\\(.+?\\)\\*\\*" nil t)
    (replace-match (propertize (match-string 1) 'face 'ai-auto-complete-markdown-bold-face) nil nil nil 0))

  ;; Italic
  (goto-char (point-min))
  (while (re-search-forward "\\*\\(.+?\\)\\*" nil t)
    (replace-match (propertize (match-string 1) 'face 'ai-auto-complete-markdown-italic-face) nil nil nil 0)))

(defun ai-auto-complete-markdown-render-inline-code ()
  "Render inline code in the current buffer."
  (goto-char (point-min))
  (while (re-search-forward "`\\([^`\n]+?\\)`" nil t)
    (replace-match (propertize (match-string 1) 'face 'ai-auto-complete-markdown-code-face) nil nil nil 0)))

(defun ai-auto-complete-markdown-render-links ()
  "Render links in the current buffer."
  (goto-char (point-min))
  (while (re-search-forward "\\[\\(.+?\\)\\](\\(.+?\\))" nil t)
    (let ((text (match-string 1))
          (url (match-string 2)))
      (replace-match (propertize text
                                'face 'ai-auto-complete-markdown-link-face
                                'mouse-face 'highlight
                                'help-echo (format "Link: %s" url)
                                'ai-auto-complete-link url)
                    nil nil nil 0))))

(defun ai-auto-complete-markdown-render-blockquotes ()
  "Render blockquotes in the current buffer."
  (goto-char (point-min))
  (while (re-search-forward "^>\\s-+\\(.+\\)$" nil t)
    (replace-match (propertize (concat "│ " (match-string 1))
                              'face 'ai-auto-complete-markdown-blockquote-face)
                  nil nil nil 0)))

(defun ai-auto-complete-markdown-render-lists ()
  "Render lists in the current buffer."
  ;; Unordered lists
  (goto-char (point-min))
  (while (re-search-forward "^\\(\\s-*\\)\\(-\\|\\*\\|\\+\\)\\s-+\\(.+\\)$" nil t)
    (let ((indent (match-string 1))
          (marker (match-string 2))
          (content (match-string 3)))
      (replace-match (concat indent
                            (propertize marker 'face 'ai-auto-complete-markdown-list-face)
                            " "
                            content)
                    nil nil nil 0)))

  ;; Ordered lists
  (goto-char (point-min))
  (while (re-search-forward "^\\(\\s-*\\)\\([0-9]+\\)\\.\\s-+\\(.+\\)$" nil t)
    (let ((indent (match-string 1))
          (number (match-string 2))
          (content (match-string 3)))
      (replace-match (concat indent
                            (propertize (concat number ".") 'face 'ai-auto-complete-markdown-list-face)
                            " "
                            content)
                    nil nil nil 0))))

(defun ai-auto-complete-markdown-render-code-blocks ()
  "Render code blocks in the current buffer."
  (goto-char (point-min))
  (while (re-search-forward "```\\([a-zA-Z0-9_-]*\\)\n\\(\\(.\\|\n\\)*?\\)```" nil t)
    (let* ((lang (match-string 1))
           (code (match-string 2))
           (highlighted-code code)
           (highlight-fn (cdr (assoc lang ai-auto-complete-markdown-syntax-highlighting-functions))))

      ;; Apply syntax highlighting if available for this language
      (when (and highlight-fn (functionp highlight-fn))
        (setq highlighted-code (funcall highlight-fn code)))

      ;; Create a button for copying the code
      (let ((copy-button (propertize "[Copy]"
                                    'face '(:box t :foreground "blue")
                                    'mouse-face 'highlight
                                    'help-echo "Copy code to clipboard"
                                    'ai-auto-complete-copy-code code
                                    'button t)))

        ;; Replace the code block with highlighted version and copy button
        (replace-match
         (concat
          (propertize (format "```%s " lang) 'face 'font-lock-comment-face)
          copy-button
          "\n"
          (propertize highlighted-code 'face 'ai-auto-complete-markdown-code-block-face)
          (propertize "```" 'face 'font-lock-comment-face))
         nil nil nil 0)))))

(defun ai-auto-complete-markdown-render-tables ()
  "Render tables in the current buffer."
  (goto-char (point-min))
  (while (re-search-forward "^\\(|.*|\\)$" nil t)
    (let ((line (match-string 0)))
      (replace-match (propertize line 'face 'ai-auto-complete-markdown-table-face) nil nil nil 0))))

;; Syntax highlighting functions for different languages
(defun ai-auto-complete-markdown-highlight-elisp (code)
  "Apply syntax highlighting to Emacs Lisp CODE."
  (with-temp-buffer
    (insert code)
    (delay-mode-hooks (emacs-lisp-mode))
    (font-lock-ensure)
    (buffer-string)))

(defun ai-auto-complete-markdown-highlight-python (code)
  "Apply syntax highlighting to Python CODE."
  (with-temp-buffer
    (insert code)
    (when (fboundp 'python-mode)
      (delay-mode-hooks (python-mode))
      (font-lock-ensure))
    (buffer-string)))

(defun ai-auto-complete-markdown-highlight-javascript (code)
  "Apply syntax highlighting to JavaScript CODE."
  (with-temp-buffer
    (insert code)
    (when (fboundp 'js-mode)
      (delay-mode-hooks (js-mode))
      (font-lock-ensure))
    (buffer-string)))

(defun ai-auto-complete-markdown-highlight-json (code)
  "Apply syntax highlighting to JSON CODE."
  (with-temp-buffer
    (insert code)
    (when (fboundp 'json-mode)
      (delay-mode-hooks (json-mode))
      (font-lock-ensure))
    (buffer-string)))

(defun ai-auto-complete-markdown-highlight-html (code)
  "Apply syntax highlighting to HTML CODE."
  (with-temp-buffer
    (insert code)
    (when (fboundp 'html-mode)
      (delay-mode-hooks (html-mode))
      (font-lock-ensure))
    (buffer-string)))

(defun ai-auto-complete-markdown-highlight-css (code)
  "Apply syntax highlighting to CSS CODE."
  (with-temp-buffer
    (insert code)
    (when (fboundp 'css-mode)
      (delay-mode-hooks (css-mode))
      (font-lock-ensure))
    (buffer-string)))

(defun ai-auto-complete-markdown-highlight-shell (code)
  "Apply syntax highlighting to shell CODE."
  (with-temp-buffer
    (insert code)
    (when (fboundp 'sh-mode)
      (delay-mode-hooks (sh-mode))
      (font-lock-ensure))
    (buffer-string)))

;; Function to handle click events on copy buttons
(defun ai-auto-complete-markdown-copy-code (event)
  "Copy code to clipboard when a copy button is clicked.
EVENT is the mouse event."
  (interactive "e")
  (let* ((pos (posn-point (event-end event)))
         (code (get-text-property pos 'ai-auto-complete-copy-code)))
    (when code
      (kill-new code)
      (message "Code copied to clipboard"))))

;; Add mouse click handler for copy buttons
(define-key special-mode-map [mouse-1] 'ai-auto-complete-markdown-copy-code)

(provide 'ui/markdown-renderer)
;;; markdown-renderer.el ends here
