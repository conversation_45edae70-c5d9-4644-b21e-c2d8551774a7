;;; history-tools.el --- Command history tools for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides command history tools for the AI Auto Complete package.
;; These tools track and manage the history of commands executed by the LLM.

;;; Code:

;; Try to load tools-core, but handle failure gracefully
(condition-case err
    (require 'tools/tools-core)
  (error
   (message "Warning: Failed to load tools/tools-core: %s" (error-message-string err))
   (load (expand-file-name "tools-core.el"
                          (file-name-directory (or load-file-name buffer-file-name))))))

(require 'cl-lib)

;; Variables for command history
(defvar ai-auto-complete-command-history '()
  "History of commands executed by the LLM.
Each entry is a plist with properties:
- :command - The executed command
- :timestamp - When it was executed
- :output - The command output
- :exit-code - The exit status
- :context - The user request that led to this command
- :directory - The working directory
- :duration - How long the command took to execute")

(defcustom ai-auto-complete-command-history-limit 100
  "Maximum number of commands to keep in history."
  :type 'integer
  :group 'ai-auto-complete-tools)

(defcustom ai-auto-complete-command-history-output-limit 1000
  "Maximum number of characters to store from command output."
  :type 'integer
  :group 'ai-auto-complete-tools)

(defcustom ai-auto-complete-command-history-save t
  "Whether to save command history between sessions."
  :type 'boolean
  :group 'ai-auto-complete-tools)

(defcustom ai-auto-complete-command-history-file
  (expand-file-name "ai-auto-complete-command-history.el" user-emacs-directory)
  "File to save command history to."
  :type 'file
  :group 'ai-auto-complete-tools)

;; Function to add a command to the history
(defun ai-auto-complete-add-to-command-history (command output exit-code context directory duration)
  "Add a command to the history.
COMMAND is the executed command.
OUTPUT is the command output.
EXIT-CODE is the exit status.
CONTEXT is the user request that led to this command.
DIRECTORY is the working directory.
DURATION is how long the command took to execute."
  (message "DEBUG: add_to_command_history - Adding command: %s" command)
  (condition-case err
      (progn
        ;; Truncate output if needed
        (when (and output (> (length output) ai-auto-complete-command-history-output-limit))
          (setq output (concat (substring output 0 ai-auto-complete-command-history-output-limit)
                               "\n... [Output truncated] ...")))

        ;; Add to history
        (push (list :command command
                    :timestamp (current-time-string)
                    :output output
                    :exit-code exit-code
                    :context context
                    :directory directory
                    :duration duration)
              ai-auto-complete-command-history)

        ;; Limit history size
        (when (> (length ai-auto-complete-command-history) ai-auto-complete-command-history-limit)
          (setq ai-auto-complete-command-history
                (cl-subseq ai-auto-complete-command-history 0 ai-auto-complete-command-history-limit)))

        ;; Save history if enabled
        (when ai-auto-complete-command-history-save
          (ai-auto-complete-save-command-history))

        (message "DEBUG: add_to_command_history - Command added successfully"))
    (error
     (message "DEBUG: add_to_command_history - Error adding command: %s" (error-message-string err)))))

;; Function to save command history
(defun ai-auto-complete-save-command-history ()
  "Save command history to file."
  (message "DEBUG: save_command_history - Saving command history")
  (condition-case err
      (when ai-auto-complete-command-history-save
        (with-temp-file ai-auto-complete-command-history-file
          (insert ";; AI Auto Complete Command History\n")
          (insert ";; Automatically generated. Do not edit.\n\n")
          (insert "(setq ai-auto-complete-command-history\n")
          (insert "      '")
          (prin1 ai-auto-complete-command-history (current-buffer))
          (insert ")\n"))
        (message "DEBUG: save_command_history - Command history saved successfully"))
    (error
     (message "DEBUG: save_command_history - Error saving command history: %s" (error-message-string err)))))

;; Function to load command history
(defun ai-auto-complete-load-command-history ()
  "Load command history from file."
  (message "DEBUG: load_command_history - Loading command history")
  (condition-case err
      (when (and ai-auto-complete-command-history-save
                 (file-exists-p ai-auto-complete-command-history-file))
        (load ai-auto-complete-command-history-file t)
        (message "DEBUG: load_command_history - Command history loaded successfully"))
    (error
     (message "DEBUG: load_command_history - Error loading command history: %s" (error-message-string err)))))

;; Function to get command history
(defun ai-auto-complete-tool-get-command-history (params)
  "Get the command execution history.
PARAMS should be an alist with:
- 'limit': Optional number of history items to return (default: 10)
- 'filter': Optional string to filter commands (default: nil)
- 'include_output': Optional boolean to include command output (default: t)"
  (message "DEBUG: get_command_history called with params: %S" params)
  (condition-case err
      (let* ((limit (or (cdr (assoc 'limit params)) 10))
             (filter (cdr (assoc 'filter params)))
             (include-output (if (assoc 'include_output params)
                                 (cdr (assoc 'include_output params))
                               t))
             (filtered-history ai-auto-complete-command-history)
             (result "# Command Execution History\n\n"))

        (message "DEBUG: get_command_history - Processing with limit: %d, filter: %s, include_output: %s"
                 limit filter include-output)
        (message "DEBUG: get_command_history - History size: %d" (length ai-auto-complete-command-history))

        ;; Apply filter if provided
        (when filter
          (condition-case filter-err
              (progn
                (message "DEBUG: get_command_history - Applying filter: %s" filter)
                (setq filtered-history
                      (cl-remove-if-not
                       (lambda (item)
                         (string-match-p filter (plist-get item :command)))
                       filtered-history))
                (message "DEBUG: get_command_history - After filtering: %d items" (length filtered-history)))
            (error
             (message "DEBUG: get_command_history - Error applying filter: %s" (error-message-string filter-err))
             (setq filtered-history ai-auto-complete-command-history))))

        ;; Limit the number of items
        (when (> (length filtered-history) limit)
          (message "DEBUG: get_command_history - Limiting to %d items" limit)
          (setq filtered-history
                (cl-subseq filtered-history 0 limit)))

        ;; Format the result
        (if (null filtered-history)
            (progn
              (message "DEBUG: get_command_history - No history available")
              (setq result (concat result "No command history available.\n")))

          (progn
            (message "DEBUG: get_command_history - Formatting %d history items" (length filtered-history))

            ;; Add table header
            (setq result (concat result
                                 "| Timestamp | Command | Exit Code | Directory | Duration |\n"
                                 "|-----------|---------|-----------|-----------|----------|\n"))

            ;; Add each command to the table
            (condition-case table-err
                (dolist (item filtered-history)
                  (condition-case item-err
                      (let ((timestamp (plist-get item :timestamp))
                            (command (plist-get item :command))
                            (exit-code (plist-get item :exit-code))
                            (directory (plist-get item :directory))
                            (duration (plist-get item :duration)))

                        (message "DEBUG: get_command_history - Processing command: %s" command)

                        (setq result (concat result
                                             "| " (or timestamp "Unknown") " | `"
                                             (if (and command (> (length command) 30))
                                                 (concat (substring command 0 27) "...")
                                               (or command "Unknown"))
                                             "` | " (if exit-code (number-to-string exit-code) "N/A")
                                             " | `" (if (and directory (> (length directory) 20))
                                                        (concat (substring directory 0 17) "...")
                                                      (or directory "Unknown"))
                                             "` | " (if duration (format "%.2f sec" duration) "N/A")
                                             " |\n")))
                    (error
                     (message "DEBUG: get_command_history - Error processing item: %s" (error-message-string item-err))
                     (setq result (concat result "| Error | Error processing history item | - | - | - |\n")))))
              (error
               (message "DEBUG: get_command_history - Error creating table: %s" (error-message-string table-err))
               (setq result (concat result "Error creating history table: " (error-message-string table-err) "\n\n"))))

            ;; Add detailed information for each command
            (when include-output
              (message "DEBUG: get_command_history - Including detailed output")
              (setq result (concat result "\n## Detailed Command Information\n\n"))

              (condition-case details-err
                  (dolist (item filtered-history)
                    (condition-case item-err
                        (let ((timestamp (plist-get item :timestamp))
                              (command (plist-get item :command))
                              (output (plist-get item :output))
                              (exit-code (plist-get item :exit-code))
                              (context (plist-get item :context))
                              (directory (plist-get item :directory))
                              (duration (plist-get item :duration)))

                          (message "DEBUG: get_command_history - Adding details for command: %s" command)

                          (setq result (concat result
                                               "### Command: `" (or command "Unknown") "`\n\n"
                                               "- **Timestamp:** " (or timestamp "Unknown") "\n"
                                               "- **Directory:** `" (or directory "Unknown") "`\n"
                                               "- **Exit Code:** " (if exit-code (number-to-string exit-code) "N/A") "\n"
                                               "- **Duration:** " (if duration (format "%.2f seconds" duration) "N/A") "\n"
                                               "- **Context:** " (or context "No context available") "\n\n"
                                               "#### Output:\n\n```\n" (or output "[No output]") "\n```\n\n"
                                               "---\n\n")))
                      (error
                       (message "DEBUG: get_command_history - Error adding details: %s" (error-message-string item-err))
                       (setq result (concat result "### Error\n\nFailed to process command details: "
                                            (error-message-string item-err) "\n\n---\n\n")))))
                (error
                 (message "DEBUG: get_command_history - Error adding detailed information: %s"
                          (error-message-string details-err))
                 (setq result (concat result "Error adding detailed information: "
                                      (error-message-string details-err) "\n\n")))))))

        ;; Return the result
        (message "DEBUG: get_command_history - Returning result of length: %d" (length result))
        result)

    (error
     (let ((error-msg (format "ERROR: Failed to get command history: %s" (error-message-string err))))
       (message "DEBUG: get_command_history - %s" error-msg)
       (concat "# Command History Error\n\n" error-msg)))))

;; Function to repeat a command from history
(defun ai-auto-complete-tool-repeat-command (params)
  "Repeat a command from history.
PARAMS should be an alist with:
- 'index': Index of the command in history (0 is most recent)
- 'modify': Optional string with modifications to apply to the command"
  (message "DEBUG: repeat_command called with params: %S" params)
  (condition-case err
      (let* ((index (cdr (assoc 'index params)))
             (modify (cdr (assoc 'modify params))))

        ;; Check if index is provided
        (if (not index)
            (let ((error-msg "ERROR: No index specified"))
              (message "DEBUG: repeat_command - %s" error-msg)
              error-msg)

          (message "DEBUG: repeat_command - Repeating command at index: %d" index)
          (message "DEBUG: repeat_command - History size: %d" (length ai-auto-complete-command-history))

          ;; Check if index is valid
          (if (or (< index 0) (>= index (length ai-auto-complete-command-history)))
              (let ((error-msg (format "ERROR: Invalid index %d. History contains %d commands."
                                       index (length ai-auto-complete-command-history))))
                (message "DEBUG: repeat_command - %s" error-msg)
                error-msg)

            ;; Get the command
            (condition-case cmd-err
                (let* ((item (nth index ai-auto-complete-command-history))
                       (command (plist-get item :command))
                       (directory (plist-get item :directory))
                       (modified-command (if modify
                                             (replace-regexp-in-string (regexp-quote command) modify command)
                                           command)))

                  (message "DEBUG: repeat_command - Original command: %s" command)
                  (message "DEBUG: repeat_command - Modified command: %s" modified-command)
                  (message "DEBUG: repeat_command - Directory: %s" directory)

                  ;; Execute the command
                  (condition-case exec-err
                      (let* ((start-time (float-time))
                             (output-buffer (generate-new-buffer " *command-output*"))
                             (error-buffer (generate-new-buffer " *command-error*"))
                             (exit-code nil)
                             (default-directory (or directory default-directory)))

                        (message "DEBUG: repeat_command - Executing command...")

                        ;; Run the command
                        (let ((cmd-output "")
                              (cmd-exit-code 0))
                          (condition-case run-err
                              (progn
                                (setq cmd-exit-code 0)
                                (setq cmd-output (shell-command-to-string modified-command)))
                            (error
                             (let ((error-msg (format "ERROR: Failed to execute command: %s"
                                                     (error-message-string run-err))))
                               (message "DEBUG: repeat_command - %s" error-msg)
                               (when (buffer-live-p output-buffer)
                                 (kill-buffer output-buffer))
                               (when (buffer-live-p error-buffer)
                                 (kill-buffer error-buffer))
                               (error error-msg))))

                          ;; Get command output
                          (let ((stdout cmd-output)
                                (stderr "")
                                (exit-code cmd-exit-code)
                                (end-time (float-time))
                                (duration (- (float-time) start-time)))

                            (message "DEBUG: repeat_command - Command executed in %.2f seconds" duration)

                            ;; Clean up buffers
                            (when (buffer-live-p output-buffer)
                              (kill-buffer output-buffer))
                            (when (buffer-live-p error-buffer)
                              (kill-buffer error-buffer))

                            ;; Add to history
                            (condition-case hist-err
                                (progn
                                  (message "DEBUG: repeat_command - Adding to command history")
                                  (ai-auto-complete-add-to-command-history
                                   modified-command
                                   (if (string-empty-p stderr) stdout (concat stdout "\n\nSTDERR:\n" stderr))
                                   exit-code
                                   (format "Repeated command %d%s" index
                                           (if modify (format " with modification: %s" modify) ""))
                                   directory
                                   duration))
                              (error
                               (message "DEBUG: repeat_command - Error adding to history: %s"
                                        (error-message-string hist-err))))

                            ;; Format the result
                            (let ((result "# Command Execution Result\n\n"))
                              (setq result (concat result
                                                   "**Original Command:** `" command "`\n\n"
                                                   "**Executed Command:** `" modified-command "`\n\n"
                                                   "**Working Directory:** `" directory "`\n\n"
                                                   "**Exit Code:** " (number-to-string exit-code) "\n\n"
                                                   "**Duration:** " (format "%.2f seconds" duration) "\n\n"))

                              ;; Add standard output
                              (unless (string-empty-p stdout)
                                (setq result (concat result
                                                     "## Standard Output\n\n```\n" stdout "\n```\n\n")))

                              ;; Add standard error
                              (unless (string-empty-p stderr)
                                (setq result (concat result
                                                     "## Standard Error\n\n```\n" stderr "\n```\n\n")))

                              ;; Return the result
                              (message "DEBUG: repeat_command - Returning result of length: %d" (length result))
                              result))))

                    (error
                     (let ((error-msg (format "ERROR: Failed to execute command: %s"
                                             (error-message-string exec-err))))
                       (message "DEBUG: repeat_command - %s" error-msg)
                       (concat "# Command Execution Error\n\n" error-msg)))))

              (error
               (let ((error-msg (format "ERROR: Failed to get command from history: %s"
                                       (error-message-string cmd-err))))
                 (message "DEBUG: repeat_command - %s" error-msg)
                 (concat "# Command History Error\n\n" error-msg)))))))

    (error
     (let ((error-msg (format "ERROR: Failed to repeat command: %s" (error-message-string err))))
       (message "DEBUG: repeat_command - %s" error-msg)
       (concat "# Command Repeat Error\n\n" error-msg)))))

;; Function to clear command history
(defun ai-auto-complete-tool-clear-command-history (params)
  "Clear the command execution history.
PARAMS is ignored."
  (message "DEBUG: clear_command_history called with params: %S" params)
  (condition-case err
      (progn
        (message "DEBUG: clear_command_history - Clearing command history")
        (setq ai-auto-complete-command-history '())
        (when ai-auto-complete-command-history-save
          (condition-case save-err
              (progn
                (ai-auto-complete-save-command-history)
                (message "DEBUG: clear_command_history - Command history file updated"))
            (error
             (message "DEBUG: clear_command_history - Error saving empty history: %s"
                      (error-message-string save-err)))))
        (message "DEBUG: clear_command_history - Command history cleared successfully")
        "Command history has been cleared.")
    (error
     (let ((error-msg (format "ERROR: Failed to clear command history: %s" (error-message-string err))))
       (message "DEBUG: clear_command_history - %s" error-msg)
       (concat "# Command History Error\n\n" error-msg)))))

;; Advice function to track command execution
(defun ai-auto-complete-track-command-execution (command output exit-code context directory duration)
  "Track command execution for history.
COMMAND is the executed command.
OUTPUT is the command output.
EXIT-CODE is the exit status.
CONTEXT is the user request that led to this command.
DIRECTORY is the working directory.
DURATION is how long the command took to execute."
  (message "DEBUG: track_command_execution - Tracking command: %s" command)
  (ai-auto-complete-add-to-command-history command output exit-code context directory duration))

;; Register history tools
(defun ai-auto-complete-register-history-tools ()
  "Register command history tools."
  (message "DEBUG: register_history_tools - Registering history tools")

  ;; Load command history
  (condition-case err
      (progn
        (ai-auto-complete-load-command-history)
        (message "DEBUG: register_history_tools - Command history loaded"))
    (error
     (message "DEBUG: register_history_tools - Error loading command history: %s"
              (error-message-string err))))

  ;; Get command history
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "get_command_history"
         "Get the command execution history"
         #'ai-auto-complete-tool-get-command-history
         '(("limit" . "Optional number of history items to return")
           ("filter" . "Optional string to filter commands")
           ("include_output" . "Optional boolean to include command output")))
        (message "DEBUG: register_history_tools - get_command_history tool registered"))
    (error
     (message "DEBUG: register_history_tools - Error registering get_command_history: %s"
              (error-message-string err))))

  ;; Repeat command
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "repeat_command"
         "Repeat a command from history"
         #'ai-auto-complete-tool-repeat-command
         '(("index" . "Index of the command in history (0 is most recent)")
           ("modify" . "Optional string with modifications to apply to the command")))
        (message "DEBUG: register_history_tools - repeat_command tool registered"))
    (error
     (message "DEBUG: register_history_tools - Error registering repeat_command: %s"
              (error-message-string err))))

  ;; Clear command history
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "clear_command_history"
         "Clear the command execution history"
         #'ai-auto-complete-tool-clear-command-history
         '())
        (message "DEBUG: register_history_tools - clear_command_history tool registered"))
    (error
     (message "DEBUG: register_history_tools - Error registering clear_command_history: %s"
              (error-message-string err))))

  (message "DEBUG: register_history_tools - All history tools registered successfully"))

;; Register history tools when this module is loaded
(ai-auto-complete-register-history-tools)

(provide 'tools/history-tools)
;;; history-tools.el ends here
