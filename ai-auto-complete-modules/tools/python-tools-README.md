# Python Interpreter Tools for AI Auto Complete

This module provides comprehensive Python interpreter access for AI Auto Complete, allowing LLMs/agents to execute Python code, manage interactive Python REPLs, and create visualizations.

## Overview

The Python tools module gives AI agents full access and control over the Python interpreter, enabling them to:

1. Execute Python code synchronously and get the results
2. Start and manage interactive Python REPLs
3. Create data visualizations with matplotlib
4. Perform data analysis and manipulation

## Available Tools

### `python_execute`

Execute Python code synchronously and return the result.

**Parameters:**
- `code`: Python code to execute
- `timeout`: Optional timeout in seconds
- `safe_mode_override`: Optional boolean to override safe mode

**Example:**
```xml
<tool name="python_execute">
<parameters>
{
  "code": "import math\n\nfor i in range(5):\n    print(f'{i}: {math.factorial(i)}')",
  "timeout": 5
}
</parameters>
</tool>
```

### `python_start_repl`

Start an interactive Python REPL.

**Parameters:**
- `name`: Optional name for the REPL

**Example:**
```xml
<tool name="python_start_repl">
<parameters>
{
  "name": "data_analysis"
}
</parameters>
</tool>
```

### `python_send_input`

Send input to a running Python REPL.

**Parameters:**
- `process_id`: Process ID returned by python_start_repl
- `input`: Input to send to the REPL

**Example:**
```xml
<tool name="python_send_input">
<parameters>
{
  "process_id": "py-0",
  "input": "import numpy as np\nnp.random.rand(3,3)"
}
</parameters>
</tool>
```

### `python_get_repl_output`

Get output from a running Python REPL.

**Parameters:**
- `process_id`: Process ID returned by python_start_repl
- `clear`: Optional boolean to clear the output buffer after reading

**Example:**
```xml
<tool name="python_get_repl_output">
<parameters>
{
  "process_id": "py-0",
  "clear": true
}
</parameters>
</tool>
```

### `python_terminate_repl`

Terminate a running Python REPL.

**Parameters:**
- `process_id`: Process ID returned by python_start_repl

**Example:**
```xml
<tool name="python_terminate_repl">
<parameters>
{
  "process_id": "py-0"
}
</parameters>
</tool>
```

### `python_list_repls`

List all running Python REPLs.

**Parameters:** None

**Example:**
```xml
<tool name="python_list_repls">
<parameters>
{}
</parameters>
</tool>
```

### `python_plot`

Execute Python code with matplotlib visualization.

**Parameters:**
- `code`: Python code to execute (should include matplotlib commands)
- `filename`: Optional filename to save the plot
- `timeout`: Optional timeout in seconds
- `safe_mode_override`: Optional boolean to override safe mode

**Example:**
```xml
<tool name="python_plot">
<parameters>
{
  "code": "import matplotlib.pyplot as plt\nimport numpy as np\n\nx = np.linspace(0, 10, 100)\ny = np.sin(x)\n\nplt.figure(figsize=(10, 6))\nplt.plot(x, y)\nplt.title('Sine Wave')\nplt.xlabel('x')\nplt.ylabel('sin(x)')",
  "filename": "sine_wave.png"
}
</parameters>
</tool>
```

## Safety Features

The Python tools module includes safety features to prevent potentially dangerous operations:

1. **Safe Mode**: By default, certain potentially dangerous modules and functions are restricted
2. **Timeout**: All Python code execution has a configurable timeout to prevent infinite loops
3. **Output Limiting**: Large outputs are truncated to prevent buffer overflow

## Customization

You can customize the Python tools through the Emacs customization interface:

```
M-x ai-auto-complete-customize-python
```

Available customization options:

- `ai-auto-complete-python-command`: Command to use for Python interpreter (default: "python3")
- `ai-auto-complete-python-timeout`: Default timeout in seconds for Python code execution (default: 10)
- `ai-auto-complete-python-safe-mode`: Whether to run Python code in safe mode with restricted imports (default: t)
- `ai-auto-complete-python-restricted-modules`: List of potentially dangerous Python modules or functions to restrict in safe mode

## Python Agent

The package also includes a specialized Python agent that has access to all Python tools:

```
@python Write a function to calculate the Fibonacci sequence
```

The Python agent has expertise in Python programming, data analysis, and visualization, and can help with:

1. Code development and debugging
2. Data analysis and manipulation
3. Creating visualizations
4. Explaining Python concepts and best practices

## Usage Examples

### Basic Code Execution

```
@python Execute this code:
```
```python
def factorial(n):
    if n == 0 or n == 1:
        return 1
    else:
        return n * factorial(n-1)

for i in range(10):
    print(f"{i}! = {factorial(i)}")
```

### Interactive Data Analysis

```
@python Start a REPL and analyze this dataset:
```
```python
import pandas as pd
import matplotlib.pyplot as plt

# Load the dataset
data = pd.read_csv('data.csv')

# Show basic statistics
print(data.describe())

# Create a histogram
plt.figure(figsize=(10, 6))
data['value'].hist(bins=20)
plt.title('Distribution of Values')
plt.savefig('histogram.png')
```

### Creating Visualizations

```
@python Create a visualization of a sine and cosine wave
```

## Integration with Other Tools

The Python tools can be combined with other AI Auto Complete tools for powerful workflows:

1. Use `read_file` to load data or code files
2. Process them with Python
3. Save results with `write_file`
4. Visualize data with `python_plot`

This integration enables complex data analysis and programming workflows entirely within Emacs.
