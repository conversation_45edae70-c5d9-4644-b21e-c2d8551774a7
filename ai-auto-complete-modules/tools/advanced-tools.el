;;; advanced-tools.el --- Advanced tool implementations for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides advanced tool implementations for the AI Auto Complete package.
;; These tools provide more sophisticated functionality for code analysis and manipulation.

;;; Code:

;; Try to load tools-core, but handle failure gracefully
(condition-case err
    (require 'tools/tools-core)
  (error
   (message "Warning: Failed to load tools/tools-core: %s" (error-message-string err))
   (load (expand-file-name "tools-core.el"
                          (file-name-directory (or load-file-name buffer-file-name))))))

(require 'grep)
(require 'cl-lib)

;; Try to load s library, but provide fallback if not available
(condition-case nil
    (require 's)
  (error
   (message "Warning: s library not available, using fallback string functions")
   ;; Define minimal fallback for s.el functions we need
   ;; (s-trim "  hello world  ")
   (defun s-trim (s)
     "Trim leading and trailing whitespace from string S.
This is a fallback implementation if the 's' library is not available.

Signature: (s-trim S)"
     (replace-regexp-in-string "\\`[ \t\n\r]+" ""
                              (replace-regexp-in-string "[ \t\n\r]+\\'" "" s)))

   ;; (s-join "-" '("apple" "banana" "cherry"))
   (defun s-join (separator strings)
     "Join a list of STRINGS with SEPARATOR.
This is a fallback implementation if the 's' library is not available.

Signature: (s-join SEPARATOR STRINGS)"
     (mapconcat 'identity strings separator))

   ;; (s-lines "first line\nsecond line\n\nfourth line\n")
   (defun s-lines (s)
     "Split string S into a list of lines, using newline as delimiter.
This fallback uses `(split-string S \"\\n\")`, which may include
empty strings if S has consecutive newlines or a trailing newline
(e.g., \"a\\n\\nb\" -> (\"a\" \"\" \"b\"), \"a\\n\" -> (\"a\" \"\")).

Signature: (s-lines S)"
     (split-string s "\n"))))

;; Example call to test ai-auto-complete-tool-search-files:
;; (ai-auto-complete-tool-search-files '((path . ".") (regex . "defun") (file_pattern . "*.el")))

;; Search files tool
;; (ai-auto-complete-tool-search-files
;;  '((path . "/path/to/project")
;;    (regex . "my_function_name")
;;    (file_pattern . "*.py")))
(defun ai-auto-complete-tool-search-files (params)
  "Search files in a directory using a regular expression.
Returns a string containing matches, each with its file name, line number,
and a few lines of context (typically 2 lines before and 2 lines after the match).
This tool is useful for finding specific code patterns or textual content
across multiple files.

PARAMS is an alist with the following keys:
- 'path' (string): The directory path to search within.
- 'regex' (string): The regular expression pattern to search for.
- 'file_pattern' (string, optional): A shell glob pattern to filter
  files to be searched (e.g., \"*.el\", \"src/**/*.py\"). If nil or
  not provided, `grep -r`'s default behavior for file selection is used.

Signature: (ai-auto-complete-tool-search-files PARAMS)"
  (let ((path (cdr (assoc 'path params)))
        (regex (cdr (assoc 'regex params)))
        (file-pattern (cdr (assoc 'file_pattern params))))
    (cond
     ((not path)
      "ERROR: No path specified")
     ((not regex)
      "ERROR: No regex specified")
     ((not (file-directory-p path))
      (format "ERROR: Directory %s does not exist" path))
     (t
      (condition-case err
          (let* ((default-directory path)
                 (grep-command (if file-pattern
                                  (format "grep -n -r --include=\"%s\" -e \"%s\" ." file-pattern regex)
                                (format "grep -n -r -e \"%s\" ." regex)))
                 (output-buffer (generate-new-buffer " *grep-output*"))
                 (error-buffer (generate-new-buffer " *grep-error*"))
                 (exit-code nil)
                 (output "")
                 (lines nil)
                 (result ""))

            ;; Run the grep command using shell-command-to-string
            (message "DEBUG: search_files - Running command: %s" grep-command)
            (setq output (shell-command-to-string grep-command))
            (setq exit-code 0) ;; Assume success
            (message "DEBUG: search_files - Command executed successfully")

            ;; Split output into lines
            (setq lines (split-string output "\n" t))

            ;; Format the results with context
            (dolist (line lines)
              (when (string-match "\\(.*\\):\\([0-9]+\\):\\(.*\\)" line)
                (let* ((file (match-string 1 line))
                       (line-num (string-to-number (match-string 2 line)))
                       (content (match-string 3 line))
                       (context-before "")
                       (context-after ""))

                  ;; Try to get context lines before and after the match
                  (condition-case nil
                      (with-temp-buffer
                        (insert-file-contents file)
                        (let ((start-line (max 1 (- line-num 2)))
                              (end-line (+ line-num 2)))
                          ;; Get context before
                          (goto-char (point-min))
                          (forward-line (1- start-line))
                          (let ((start-pos (point)))
                            (forward-line (- line-num start-line))
                            (setq context-before (buffer-substring-no-properties start-pos (point))))

                          ;; Get context after
                          (forward-line 1)
                          (let ((start-pos (point)))
                            (forward-line (- end-line line-num))
                            (setq context-after (buffer-substring-no-properties start-pos (point))))))
                    (error nil))

                  ;; Add to result
                  (setq result (concat result
                                      (format "File: %s, Line: %d\n" file line-num)
                                      "Context:\n```\n"
                                      context-before
                                      content "\n"
                                      context-after
                                      "```\n\n")))))

            (if (string-empty-p result)
                (format "No matches found for regex '%s' in %s" regex path)
              (format "Search results for regex '%s' in %s:\n\n%s" regex path result)))
        (error (format "ERROR: Failed to search files: %s" (error-message-string err))))))))


;; List files tool
;; (ai-auto-complete-tool-list-files '((path . "/etc") (recursive . nil)))
;; (ai-auto-complete-tool-list-files '((path . "~/.emacs.d") (recursive . t)))
(defun ai-auto-complete-tool-list-files (params)
  "List files and directories within a specified path.
Returns a string listing the files and directories. For non-recursive
listings, directories are suffixed with '/'. For recursive listings,
it attempts to use the `find . -type f -o -type d | sort` command for
efficiency, falling back to Emacs Lisp's `directory-files-recursively`
if the `find` command fails. Relative paths (e.g., './file') are used
in the recursive output.

PARAMS is an alist with the following keys:
- 'path' (string): The directory path whose contents are to be listed.
- 'recursive' (boolean, optional): If non-nil, the listing will be
  recursive. If nil or not provided, only the immediate contents of
  'path' are listed.

Signature: (ai-auto-complete-tool-list-files PARAMS)"
  (let ((path (cdr (assoc 'path params)))
        (recursive (cdr (assoc 'recursive params))))
    (if (not path)
        "ERROR: No path specified"
      (if (not (file-directory-p path))
          (format "ERROR: Directory %s does not exist" path)
        (condition-case err
            (let ((result ""))
              (if recursive
                  ;; Recursive listing
                  (let ((default-directory path))
                    (condition-case err
                        (let ((find-output-buffer (generate-new-buffer " *find-output*"))
                              (find-error-buffer (generate-new-buffer " *find-error*"))
                              (find-command "find . -type f -o -type d | sort")
                              (find-result ""))

                          ;; Run the find command using shell-command-to-string
                          (message "DEBUG: list_files - Running command: %s" find-command)
                          (setq find-result (shell-command-to-string find-command))
                          (message "DEBUG: list_files - Command executed successfully")

                          (setq result find-result)
                          (format "Files and directories in %s (recursive):\n%s" path result))
                      (error
                       ;; Fallback to directory-files-recursively if shell command fails
                       (let ((files (directory-files-recursively path "." t)))
                         (setq result (mapconcat
                                      (lambda (f)
                                        (let ((rel-path (file-relative-name f path)))
                                          (if (file-directory-p f)
                                              (concat "./" rel-path "/")
                                            (concat "./" rel-path))))
                                      files "\n")))
                         (format "Files and directories in %s (recursive):\n%s" path result)))))
                ;; Non-recursive listing
                (let* ((files (directory-files path t))
                       (formatted-files
                        (mapcar (lambda (f)
                                  (let ((name (file-name-nondirectory f)))
                                    (if (file-directory-p f)
                                        (concat name "/")
                                      name)))
                                files)))
                  (format "Files and directories in %s:\n%s"
                          path
                          (mapconcat 'identity formatted-files "\n"))))
          (error (format "ERROR: Failed to list files: %s" (error-message-string err))))))))

;; List code definition names tool
;; (ai-auto-complete-tool-list-code-definitions '((path . "/path/to/source/project")))
(defun ai-auto-complete-tool-list-code-definitions (params)
  "List top-level code definitions from source files in a directory.
This tool uses `find` to locate files and then `grep` and `sed`
commands tailored for various programming languages (e.g., Elisp, Python,
JavaScript, Java, C/C++, Ruby, Go, Rust, PHP) to extract definition names.
Returns a string with definitions grouped by file, showing relative file paths.

PARAMS is an alist with the following key:
- 'path' (string): The directory path containing the source code to analyze.
Signature: (ai-auto-complete-tool-list-code-definitions PARAMS)"
  (let ((path (cdr (assoc 'path params))))
    (if (not path)
        "ERROR: No path specified"
      (if (not (file-directory-p path))
          (format "ERROR: Directory %s does not exist" path)
        (condition-case err
            (let* ((default-directory path)
                   (result "")
                   (file-types '(("\\.el$" . elisp)
                                ("\\.lisp$" . elisp)
                                ("\\.cl$" . elisp)
                                ("\\.scm$" . elisp)
                                ("\\.py$" . python)
                                ("\\.js$" . javascript)
                                ("\\.ts$" . typescript)
                                ("\\.java$" . java)
                                ("\\.c$" . c)
                                ("\\.cpp$" . cpp)
                                ("\\.h$" . c-header)
                                ("\\.rb$" . ruby)
                                ("\\.go$" . go)
                                ("\\.rs$" . rust)
                                ("\\.php$" . php)))
                   (find-command "find . -type f | sort")
                   (output-buffer (generate-new-buffer " *find-output*"))
                   (error-buffer (generate-new-buffer " *find-error*"))
                   (exit-code nil)
                   (output "")
                   (files nil))

              ;; Run the find command using shell-command-to-string
              (message "DEBUG: list_code_definition_names - Running command: %s" find-command)
              (setq output (shell-command-to-string find-command))
              (setq exit-code 0) ;; Assume success
              (message "DEBUG: list_code_definition_names - Command executed successfully")

              ;; Split output into files
              (setq files (split-string output "\n" t))

              ;; Process each file
              (dolist (file files)
                (let ((file-type nil))
                  ;; Determine file type
                  (dolist (type file-types)
                    (when (string-match (car type) file)
                      (setq file-type (cdr type))))

                  (when file-type
                    (let ((definitions ""))
                      ;; Extract definitions based on file type
                      (cond
                       ((eq file-type 'elisp)
                        (let* ((cmd (format "grep -n \"^(def\\|^(cl-def\" \"%s\" | sed 's/:.*//'| xargs -I{} sed -n '{}p' \"%s\" | sed 's/^(//' | sed 's/ .*//' | sort | uniq"
                                           file file))
                               (cmd-buffer (generate-new-buffer " *cmd-output*"))
                               (cmd-error (generate-new-buffer " *cmd-error*"))
                               (cmd-result ""))
                          (message "DEBUG: list_code_definition_names - Running command: %s" cmd)
                          (setq cmd-result (shell-command-to-string cmd))
                          (message "DEBUG: list_code_definition_names - Command executed successfully")
                          (setq definitions cmd-result)))

                       ((eq file-type 'python)
                        (let* ((cmd (format "grep -n \"^def \\|^class \" \"%s\" | sed 's/:.*//'| xargs -I{} sed -n '{}p' \"%s\" | sed 's/:.*//' | sort | uniq"
                                           file file))
                               (cmd-buffer (generate-new-buffer " *cmd-output*"))
                               (cmd-error (generate-new-buffer " *cmd-error*"))
                               (cmd-result ""))
                          (message "DEBUG: list_code_definition_names - Running command: %s" cmd)
                          (setq cmd-result (shell-command-to-string cmd))
                          (message "DEBUG: list_code_definition_names - Command executed successfully")
                          (setq definitions cmd-result)))

                       ((memq file-type '(javascript typescript))
                        (let* ((cmd (format "grep -n \"^\\(function\\|class\\|const\\|let\\|var\\) \\|^\\(export\\|async\\) \" \"%s\" | sed 's/:.*//'| xargs -I{} sed -n '{}p' \"%s\" | sed 's/[{=].*//' | sort | uniq"
                                           file file))
                               (cmd-buffer (generate-new-buffer " *cmd-output*"))
                               (cmd-error (generate-new-buffer " *cmd-error*"))
                               (cmd-result ""))
                          (message "DEBUG: list_code_definition_names - Running elisp extraction command: %s" cmd)
                          (setq cmd-result (shell-command-to-string cmd))
                          (message "DEBUG: list_code_definition_names - Elisp extraction command executed successfully")
                          (setq definitions cmd-result)))

                       ((eq file-type 'java)
                        (let* ((cmd (format "grep -n \"^\\(public\\|private\\|protected\\) \\(class\\|interface\\|enum\\|void\\|[a-zA-Z0-9_]*\\) \" \"%s\" | sed 's/:.*//'| xargs -I{} sed -n '{}p' \"%s\" | sed 's/{.*//' | sort | uniq"
                                           file file))
                               (cmd-buffer (generate-new-buffer " *cmd-output*"))
                               (cmd-error (generate-new-buffer " *cmd-error*"))
                               (cmd-result ""))
                          (message "DEBUG: list_code_definition_names - Running python extraction command: %s" cmd)
                          (setq cmd-result (shell-command-to-string cmd))
                          (message "DEBUG: list_code_definition_names - Python extraction command executed successfully")
                          (setq definitions cmd-result)))

                       ((memq file-type '(c cpp c-header))
                        (let* ((cmd (format "grep -n \"^[a-zA-Z0-9_]* [a-zA-Z0-9_]*(.*)\" \"%s\" | grep -v \"^#\\|^extern\\|;\" | sed 's/:.*//'| xargs -I{} sed -n '{}p' \"%s\" | sed 's/{.*//' | sort | uniq"
                                           file file))
                               (cmd-buffer (generate-new-buffer " *cmd-output*"))
                               (cmd-error (generate-new-buffer " *cmd-error*"))
                               (cmd-result ""))
                          (message "DEBUG: list_code_definition_names - Running command: %s" cmd)
                          (setq cmd-result (shell-command-to-string cmd))
                          (message "DEBUG: list_code_definition_names - Command executed successfully")
                          (setq definitions cmd-result)))

                       ((eq file-type 'ruby)
                        (let* ((cmd (format "grep -n \"^\\(def\\|class\\|module\\) \" \"%s\" | sed 's/:.*//'| xargs -I{} sed -n '{}p' \"%s\" | sed 's/:.*//' | sort | uniq"
                                           file file))
                               (cmd-buffer (generate-new-buffer " *cmd-output*"))
                               (cmd-error (generate-new-buffer " *cmd-error*"))
                               (cmd-result ""))
                          (message "DEBUG: list_code_definition_names - Running command: %s" cmd)
                          (setq cmd-result (shell-command-to-string cmd))
                          (message "DEBUG: list_code_definition_names - Command executed successfully")
                          (setq definitions cmd-result)))

                       ((eq file-type 'go)
                        (let* ((cmd (format "grep -n \"^\\(func\\|type\\) \" \"%s\" | sed 's/:.*//'| xargs -I{} sed -n '{}p' \"%s\" | sed 's/{.*//' | sort | uniq"
                                           file file))
                               (cmd-buffer (generate-new-buffer " *cmd-output*"))
                               (cmd-error (generate-new-buffer " *cmd-error*"))
                               (cmd-result ""))
                          (message "DEBUG: list_code_definition_names - Running command: %s" cmd)
                          (setq cmd-result (shell-command-to-string cmd))
                          (message "DEBUG: list_code_definition_names - Command executed successfully")
                          (setq definitions cmd-result)))

                       ((eq file-type 'rust)
                        (let* ((cmd (format "grep -n \"^\\(fn\\|struct\\|enum\\|trait\\|impl\\) \" \"%s\" | sed 's/:.*//'| xargs -I{} sed -n '{}p' \"%s\" | sed 's/{.*//' | sort | uniq"
                                           file file))
                               (cmd-buffer (generate-new-buffer " *cmd-output*"))
                               (cmd-error (generate-new-buffer " *cmd-error*"))
                               (cmd-result ""))
                          (message "DEBUG: list_code_definition_names - Running command: %s" cmd)
                          (setq cmd-result (shell-command-to-string cmd))
                          (message "DEBUG: list_code_definition_names - Command executed successfully")
                          (setq definitions cmd-result)))

                       ((eq file-type 'php)
                        (let* ((cmd (format "grep -n \"^\\(function\\|class\\) \" \"%s\" | sed 's/:.*//'| xargs -I{} sed -n '{}p' \"%s\" | sed 's/{.*//' | sort | uniq"
                                           file file))
                               (cmd-buffer (generate-new-buffer " *cmd-output*"))
                               (cmd-error (generate-new-buffer " *cmd-error*"))
                               (cmd-result ""))
                          (message "DEBUG: list_code_definition_names - Running command: %s" cmd)
                          (setq cmd-result (shell-command-to-string cmd))
                          (message "DEBUG: list_code_definition_names - Command executed successfully")
                          (setq definitions cmd-result))))

                      ;; Add to result if we found definitions
                      (unless (string-empty-p definitions)
                        (setq result (concat result
                                            (format "File: %s\nDefinitions:\n%s\n"
                                                    file definitions))))))))

              (if (string-empty-p result)
                  (format "No code definitions found in %s" path)
                (format "Code definitions in %s:\n\n%s" path result)))
          (error (format "ERROR: Failed to list code definitions: %s" (error-message-string err))))))))

;; Apply diff tool
;; (ai-auto-complete-tool-apply-diff
;;  '((path . "/tmp/example.txt")
;;    (diff . "<<<<<<< SEARCH
;; old content line 1
;; old content line 2
;; =======
;; new content line 1
;; new content line 2
;; >>>>>>> REPLACE")
;;    (start_line . 5)
;;    (end_line . 6)))
(defun ai-auto-complete-tool-apply-diff (params)
  "Apply a textual diff to a file, replacing a specified range of lines.
The content to be replaced (the 'SEARCH' part of the diff) must exactly
match the content of the file between 'start_line' and 'end_line' (inclusive).
The comparison between the 'SEARCH' text and the file content is performed
after trimming leading/trailing whitespace from both.

PARAMS is an alist with the following keys:
- 'path' (string): The path to the file to be modified.
- 'diff' (string): The diff content, formatted as:
  <<<<<<< SEARCH
  [exact content to match and replace]
  =======
  [new content to insert]
  >>>>>>> REPLACE
  Both [exact content] and [new content] can be multi-line.
- 'start_line' (integer): The starting line number (1-indexed) of the section to replace.
- 'end_line' (integer): The ending line number (1-indexed) of the section to replace.

Signature: (ai-auto-complete-tool-apply-diff PARAMS)"
  (let ((path (cdr (assoc 'path params)))
        (diff (cdr (assoc 'diff params)))
        (start-line (cdr (assoc 'start_line params)))
        (end-line (cdr (assoc 'end_line params))))
    (cond
     ((not path)
      "ERROR: No path specified")
     ((not diff)
      "ERROR: No diff specified")
     ((not start-line)
      "ERROR: No start_line specified")
     ((not end-line)
      "ERROR: No end_line specified")
     ((not (file-exists-p path))
      (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (with-temp-buffer
            ;; Read the file
            (insert-file-contents path)

            ;; Parse the diff
            (let ((search-text nil)
                  (replace-text nil))

              ;; Extract search and replace text from diff using buffer positions
              (with-temp-buffer
                (insert diff)
                (goto-char (point-min))

                (let ((search-start nil)
                      (search-end nil)
                      (replace-start nil)
                      (replace-end nil))

                  ;; Find <<<<<<< SEARCH
                  (unless (re-search-forward "<<<<<<< SEARCH\n" nil t)
                    (error "Invalid diff format: missing <<<<<<< SEARCH"))
                  (setq search-start (point))

                  ;; Find =======
                  (unless (re-search-forward "=======\n" nil t)
                    (error "Invalid diff format: missing ======="))
                  (setq search-end (match-beginning 0))
                  (setq replace-start (point))

                  ;; Find >>>>>>> REPLACE
                  (unless (re-search-forward ">>>>>>> REPLACE" nil t)
                    (error "Invalid diff format: missing >>>>>>> REPLACE"))
                  (setq replace-end (match-beginning 0))

                  ;; Extract texts
                  (setq search-text (buffer-substring-no-properties search-start search-end))
                  (setq replace-text (buffer-substring-no-properties replace-start replace-end))))

              ;; Convert line numbers to positions
              (let ((line-count 1)
                    (start-pos nil)
                    (end-pos nil))
                (goto-char (point-min))

                ;; Find start position
                (while (and (< line-count start-line)
                            (not (eobp)))
                  (forward-line 1)
                  (setq line-count (1+ line-count)))
                (setq start-pos (point))

                ;; Find end position
                (while (and (<= line-count end-line)
                            (not (eobp)))
                  (forward-line 1)
                  (setq line-count (1+ line-count)))
                (setq end-pos (point))

                ;; Extract current text
                (let ((current-text (buffer-substring-no-properties start-pos end-pos)))
                  ;; Verify that search text matches current text
                  (if (string= (string-trim search-text) (string-trim current-text))
                      (progn
                        ;; Replace the text
                        (delete-region start-pos end-pos)
                        (goto-char start-pos)
                        (insert replace-text)

                        ;; Write back to file
                        (write-region (point-min) (point-max) path)
                        (format "Successfully applied diff to %s" path))
                    (format "ERROR: Search text does not match content in file %s between lines %d and %d"
                            path start-line end-line))))))
        (error (format "ERROR: Failed to apply diff: %s" (error-message-string err))))))))



;; (ai-auto-complete-tool-exact-search-and-replace
;;  '((path . "/tmp/another-example.txt")
;;    (search . "the exact text to find")
;;    (replace . "the new replacement text")
;;    (start_line . 10)
;;    (end_line . 12)))
;; (ai-auto-complete-tool-exact-search-and-replace
;;  '((path . "/tmp/full-file-replace.txt")
;;    (search . "entire old content")
;;    (replace . "entire new content")))
(defun ai-auto-complete-tool-exact-search-and-replace (params)
  "Perform an exact search and replace operation within a specified file.
The 'search' text must exactly match the content of the file, or the
content within the 'start_line' and 'end_line' (inclusive, 1-indexed) if provided.
The comparison is done after trimming leading/trailing whitespace from both
the 'search' text and the target file content. If a match is found, the
matched region is replaced with 'replace' text.

PARAMS is an alist with the following keys:
- 'path' (string): The path to the file to modify.
- 'search' (string): The exact text to search for.
- 'replace' (string): The text to replace the 'search' text with.
- 'start_line' (integer, optional): The 1-indexed starting line number to
  confine the search and replace operation.
- 'end_line' (integer, optional): The 1-indexed ending line number to
  confine the search and replace operation. If 'start_line' is
  provided, 'end_line' should also be provided to define a specific range.

Signature: (ai-auto-complete-tool-exact-search-and-replace PARAMS)"
  (let ((path (cdr (assoc 'path params)))
        (search-text (cdr (assoc 'search params)))
        (replace-text (cdr (assoc 'replace params)))
        (start-line (cdr (assoc 'start_line params)))
        (end-line (cdr (assoc 'end_line params))))
    (cond
     ((not path)
      "ERROR: No path specified")
     ((not search-text)
      "ERROR: No search text specified")
     ((not replace-text)
      "ERROR: No replace text specified")
     ((not (file-exists-p path))
      (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (with-temp-buffer
            ;; Read the file
            (insert-file-contents path)

            (let ((start-pos (point-min))
                  (end-pos (point-max)))
              ;; If line numbers provided, convert to positions
              (when (and start-line end-line)
                (goto-char (point-min))
                (let ((line-count 1))
                  ;; Find start position
                  (while (and (< line-count start-line)
                            (not (eobp)))
                    (forward-line 1)
                    (setq line-count (1+ line-count)))
                  (setq start-pos (point))

                  ;; Find end position
                  (while (and (<= line-count end-line)
                            (not (eobp)))
                    (forward-line 1)
                    (setq line-count (1+ line-count)))
                  (setq end-pos (point))))

              ;; Extract current text
              (let ((current-text (buffer-substring-no-properties start-pos end-pos)))
                ;; Verify that search text matches current text
                (if (string= (string-trim search-text) (string-trim current-text))
                    (progn
                      ;; Replace the text
                      (delete-region start-pos end-pos)
                      (goto-char start-pos)
                      (insert replace-text)

                      ;; Write back to file
                      (write-region (point-min) (point-max) path)
                      (format "Successfully applied changes to %s" path))
                  (format "ERROR: Search text does not match content in file %s%s"
                         path
                         (if (and start-line end-line)
                             (format " between lines %d and %d" start-line end-line)
                           ""))))))
        (error (format "ERROR: Failed to apply changes: %s" (error-message-string err))))))))

;; Register advanced tools
;; (ai-auto-complete-register-advanced-tools)
(defun ai-auto-complete-register-advanced-tools ()
  "Register all advanced tools defined in this file with the AI auto-complete system.
This function iterates through a predefined set of advanced tools:
- search_files
- list_files
- list_code_definition_names
- apply_diff
- exact_search_and_replace
It registers each one using `ai-auto-complete-register-tool`,
making them available for use by the AI.

This function takes no arguments.
Signature: (ai-auto-complete-register-advanced-tools)"
  (ai-auto-complete-register-tool
   "search_files"
   "Search files in a directory using a regular expression. Returns matches with file name, line number, and context (typically 2 lines before and 2 lines after the match). Useful for finding code patterns or specific textual content across multiple files."
   #'ai-auto-complete-tool-search-files
   '(("path" . "Path to the directory to search")
     ("regex" . "Regular expression to search for")
     ("file_pattern" . "Optional shell glob pattern (e.g., \"*.py\", \"src/**/*.c\") to filter files. If omitted, searches files based on grep's default recursive behavior (usually text files).")))

  (ai-auto-complete-register-tool
   "list_files"
   "List files and directories within a specified path. Non-recursive listing shows immediate contents; directories are suffixed with '/'. Recursive listing shows all contents using relative paths (e.g., './file'). Do not use this tool to verify file creation; the user will confirm that."
   #'ai-auto-complete-tool-list-files
   '(("path" . "Path to the directory to list")
     ("recursive" . "Boolean: true for recursive listing, false or omitted for non-recursive (listing immediate contents).")))

  (ai-auto-complete-register-tool
   "list_code_definition_names"
   "List top-level code definitions (e.g., functions, classes) from source files in a directory. Output includes definitions grouped by their relative file paths. Helps in understanding codebase structure."
   #'ai-auto-complete-tool-list-code-definitions
   '(("path" . "Path to the directory to analyze")))

  (ai-auto-complete-register-tool
   "apply_diff"
   "Apply a textual diff to replace a specified range of lines in a file. The 'SEARCH' part of the diff (content to be replaced) must exactly match the content of the file between 'start_line' and 'end_line'. Comparison is done after trimming leading/trailing whitespace from both the 'SEARCH' text and the file content. It is highly recommended to use the 'read_file' tool first to get the exact content if unsure."
   #'ai-auto-complete-tool-apply-diff
   '(("path" . "Path to the file to modify")
     ("diff" . "Diff block in the format: <<<<<<< SEARCH\n[exact content]\n=======\n[new content]\n>>>>>>> REPLACE")
     ("start_line" . "The 1-indexed starting line number of the section in the file to replace.")
     ("end_line" . "The 1-indexed ending line number of the section in the file to replace.")))

  (ai-auto-complete-register-tool
   "exact_search_and_replace"
   "Perform an exact search and replace in a file. If 'start_line' and 'end_line' are provided, the 'search' text must exactly match the content within that line range. If line numbers are omitted, the 'search' text must exactly match the entire file content. Comparison is done after trimming leading/trailing whitespace from both 'search' text and file content. Useful for precise modifications."
   #'ai-auto-complete-tool-exact-search-and-replace
   '(("path" . "Path to the file to modify")
     ("search" . "The exact text to search for. The comparison with file content is whitespace-trimmed.")
     ("replace" . "Text to replace with")
     ("start_line" . "Optional 1-indexed starting line number. If provided, 'end_line' is also required. Defines the specific range for search and replace.")
     ("end_line" . "Optional 1-indexed ending line number. If provided, 'start_line' is also required. Defines the specific range for search and replace."))))

;; Register advanced tools when this module is loaded
(ai-auto-complete-register-advanced-tools)

(provide 'tools/advanced-tools)
;;; advanced-tools.el ends here
