;;; custom-tools.el --- Custom tool creation for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides custom tool creation capabilities for the AI Auto Complete package.
;; These tools allow the LLM to define and use custom tools during a session.

;;; Code:

;; Try to load tools-core, but handle failure gracefully
(condition-case err
    (require 'tools/tools-core)
  (error
   (message "Warning: Failed to load tools/tools-core: %s" (error-message-string err))
   (load (expand-file-name "tools-core.el"
                          (file-name-directory (or load-file-name buffer-file-name))))))

(require 'cl-lib)

;; Try to load json, but provide fallback if not available
(condition-case nil
    (require 'json)
  (error
   (message "Warning: json library not available, using fallback json functions")

   ;; Define minimal fallback for json functions we need
   (defun json-read-from-string (string)
     "Read the JSON object contained in STRING and return it."
     (condition-case nil
         (read string)
       (error nil)))))

;; Variables for custom tools
(defvar ai-auto-complete-custom-tools (make-hash-table :test 'equal)
  "Hash table of custom tools defined by the LLM.")

(defcustom ai-auto-complete-custom-tools-save nil
  "Whether to save custom tools between sessions."
  :type 'boolean
  :group 'ai-auto-complete-tools)

(defcustom ai-auto-complete-custom-tools-file
  (expand-file-name "ai-auto-complete-custom-tools.el" user-emacs-directory)
  "File to save custom tools to."
  :type 'file
  :group 'ai-auto-complete-tools)

;; Function to define a custom tool
(defun ai-auto-complete-tool-define-custom-tool (params)
  "Define a custom tool.
PARAMS should be an alist with:
- 'name': Name of the custom tool
- 'description': Description of what the tool does
- 'parameters': List of parameters the tool accepts
- 'implementation': Either a shell script or a sequence of tool calls
- 'type': Type of implementation ('shell' or 'tool_sequence')"
  (message "DEBUG: define_custom_tool called with params: %S" params)
  (catch 'error
    (let* ((name (cdr (assoc 'name params)))
           (description (cdr (assoc 'description params)))
           (parameters (cdr (assoc 'parameters params)))
           (implementation (cdr (assoc 'implementation params)))
           (implementation-type (cdr (assoc 'type params))))

      ;; Validate parameters
      (unless (and name description implementation implementation-type)
        (let ((error-msg "ERROR: Missing required parameters. Please provide 'name', 'description', 'implementation', and 'type'."))
          (message "DEBUG: define_custom_tool - %s" error-msg)
          (throw 'error error-msg)))

      (message "DEBUG: define_custom_tool - Creating tool: %s" name)

      ;; Check if name is valid
      (when (or (string-match-p "[^a-zA-Z0-9_]" name)
                (string-match-p "^[0-9]" name))
        (let ((error-msg (format "ERROR: Invalid tool name '%s'. Tool names must contain only letters, numbers, and underscores, and must not start with a number." name)))
          (message "DEBUG: define_custom_tool - %s" error-msg)
          (throw 'error error-msg)))

      ;; Check if tool already exists
      (when (and (hash-table-p ai-auto-complete-tools)
                 (gethash name ai-auto-complete-tools)
                 (not (and (hash-table-p ai-auto-complete-custom-tools)
                           (gethash name ai-auto-complete-custom-tools)
                           (plist-get (gethash name ai-auto-complete-custom-tools) :custom))))
        (let ((error-msg (format "ERROR: A built-in tool with name '%s' already exists. Please choose a different name." name)))
          (message "DEBUG: define_custom_tool - %s" error-msg)
          (throw 'error error-msg)))

    ;; Create the custom tool function
    (let ((tool-function
           (cond
            ;; Shell script implementation
            ((string= implementation-type "shell")
             (lambda (tool-params)
               (message "DEBUG: Custom tool '%s' called with params: %S" name tool-params)
               (condition-case err
                   (let* ((script-file (make-temp-file "ai-custom-tool-" nil ".sh"))
                          (param-vars "")
                          (script implementation))

                     (message "DEBUG: Custom tool '%s' - Creating script file: %s" name script-file)

                     ;; Add parameter variables to the script
                     (dolist (param parameters)
                       (condition-case nil
                           (let* ((param-name (car param))
                                  (param-value (cdr (assoc (intern param-name) tool-params))))
                             (when param-value
                               (setq param-vars
                                     (concat param-vars
                                             (format "%s=\"%s\"\n" param-name param-value)))))
                         (error
                          (message "DEBUG: Custom tool '%s' - Error processing parameter: %S" name param))))

                     ;; Write the script with parameters
                     (with-temp-file script-file
                       (insert "#!/bin/bash\n\n")
                       (insert param-vars)
                       (insert "\n")
                       (insert script))

                     ;; Make executable
                     (set-file-modes script-file #o755)
                     (message "DEBUG: Custom tool '%s' - Script file created and made executable" name)

                     ;; Run the script
                     (let* ((start-time (float-time))
                            (output (shell-command-to-string script-file))
                            (end-time (float-time))
                            (duration (- end-time start-time)))
                       (message "DEBUG: Custom tool '%s' - Script executed in %.2f seconds" name duration)

                       ;; Clean up
                       (delete-file script-file)
                       (message "DEBUG: Custom tool '%s' - Script file deleted" name)

                       ;; Track in history if available
                       (when (fboundp 'ai-auto-complete-add-to-command-history)
                         (condition-case nil
                             (ai-auto-complete-add-to-command-history
                              (format "Custom tool '%s' (shell script)" name)
                              output
                              0
                              (format "Executed custom tool '%s' with parameters: %s"
                                      name (prin1-to-string tool-params))
                              default-directory
                              duration)
                           (error
                            (message "DEBUG: Custom tool '%s' - Error adding to command history" name))))

                       ;; Return output
                       (message "DEBUG: Custom tool '%s' - Returning output of length: %d" name (length output))
                       output))

                 (error
                  (let ((error-msg (format "ERROR: Failed to execute custom tool '%s': %s"
                                           name (error-message-string err))))
                    (message "DEBUG: Custom tool '%s' - %s" name error-msg)
                    error-msg)))))

            ;; Tool sequence implementation
            ((string= implementation-type "tool_sequence")
             (lambda (tool-params)
               (message "DEBUG: Custom tool '%s' (tool sequence) called with params: %S" name tool-params)
               (let ((result "")
                     (tool-sequence nil))

                 ;; Parse the tool sequence
                 (condition-case err
                     (setq tool-sequence (json-read-from-string implementation))
                   (error
                    (let ((error-msg (format "ERROR: Failed to parse tool sequence: %s" (error-message-string err))))
                      (message "DEBUG: Custom tool '%s' - %s" name error-msg)
                      (setq result (concat result error-msg)))))

                 ;; Check if tool sequence is valid
                 (unless (and tool-sequence (listp tool-sequence))
                   (let ((error-msg "ERROR: Invalid tool sequence. Expected a JSON array of tool calls."))
                     (message "DEBUG: Custom tool '%s' - %s" name error-msg)
                     (message "DEBUG: Custom tool '%s' - Implementation: %s" name implementation)
                     (setq result error-msg)
                     (throw 'error result)))

                 (message "DEBUG: Custom tool '%s' - Executing sequence of %d tools" name (length tool-sequence))

                 ;; Execute each tool in sequence
                 (dolist (tool-call tool-sequence)
                   (condition-case err
                       (let* ((tool-name (cdr (assoc 'name tool-call)))
                              (tool-params-json (cdr (assoc 'parameters tool-call)))
                              (tool-params-alist nil)
                              (processed-params nil))

                         (message "DEBUG: Custom tool '%s' - Processing tool call: %s" name tool-name)

                         ;; Parse tool parameters
                         (condition-case err
                             (setq tool-params-alist (json-read-from-string tool-params-json))
                           (error
                            (let ((error-msg (format "ERROR: Failed to parse parameters for tool '%s': %s"
                                                    tool-name (error-message-string err))))
                              (message "DEBUG: Custom tool '%s' - %s" name error-msg)
                              (setq result (concat result (format "## %s\n\n" error-msg)))
                              (return))))

                         ;; Replace parameter references
                         (setq processed-params
                               (mapcar
                                (lambda (param-pair)
                                  (condition-case nil
                                      (let* ((param-name (car param-pair))
                                             (param-value (cdr param-pair)))
                                        (if (and (stringp param-value)
                                                 (string-match "\\$\\([a-zA-Z0-9_]+\\)" param-value))
                                            (let* ((var-name (match-string 1 param-value))
                                                   (var-value (cdr (assoc (intern var-name) tool-params))))
                                              (cons param-name (or var-value param-value)))
                                          param-pair))
                                    (error
                                     (message "DEBUG: Custom tool '%s' - Error processing parameter: %S"
                                              name param-pair)
                                     param-pair)))
                                tool-params-alist))

                         (message "DEBUG: Custom tool '%s' - Processed parameters: %S" name processed-params)

                         ;; Get the tool function
                         (if (and (hash-table-p ai-auto-complete-tools)
                                  (gethash tool-name ai-auto-complete-tools))
                             (let* ((tool (gethash tool-name ai-auto-complete-tools))
                                    (tool-fn (plist-get tool :function)))

                               ;; Execute the tool
                               (if (functionp tool-fn)
                                   (condition-case err
                                       (let ((output (funcall tool-fn processed-params)))
                                         (message "DEBUG: Custom tool '%s' - Tool '%s' executed successfully"
                                                  name tool-name)
                                         (setq result (concat result
                                                              (format "## Result from tool '%s':\n\n" tool-name)
                                                              output "\n\n")))
                                     (error
                                      (let ((error-msg (format "ERROR: Failed to execute tool '%s': %s"
                                                              tool-name (error-message-string err))))
                                        (message "DEBUG: Custom tool '%s' - %s" name error-msg)
                                        (setq result (concat result (format "## %s\n\n" error-msg))))))

                                 (let ((error-msg (format "ERROR: Tool '%s' has no function defined" tool-name)))
                                   (message "DEBUG: Custom tool '%s' - %s" name error-msg)
                                   (setq result (concat result (format "## %s\n\n" error-msg))))))

                           (let ((error-msg (format "ERROR: Tool '%s' not found" tool-name)))
                             (message "DEBUG: Custom tool '%s' - %s" name error-msg)
                             (setq result (concat result (format "## %s\n\n" error-msg))))))

                     (error
                      (let ((error-msg (format "ERROR: Failed to process tool call: %s"
                                              (error-message-string err))))
                        (message "DEBUG: Custom tool '%s' - %s" name error-msg)
                        (setq result (concat result (format "## %s\n\n" error-msg)))))))

                 (message "DEBUG: Custom tool '%s' - Returning result of length: %d" name (length result))
                 result)))

            ;; Unknown implementation type
            (t (lambda (tool-params)
                 (format "ERROR: Unknown implementation type: %s" implementation-type))))))

      ;; Convert parameters to the format expected by register-tool
      (condition-case err
          (let ((param-alist '()))
            ;; Parse parameters if it's a string (JSON)
            (when (stringp parameters)
              (message "DEBUG: define_custom_tool - Parsing parameters JSON: %s" parameters)
              (condition-case err
                  (let ((parsed-params (json-read-from-string parameters)))
                    ;; Handle different formats of parameters
                    (cond
                     ;; Array of objects with single key-value pair
                     ((and (vectorp parsed-params) (> (length parsed-params) 0))
                      (message "DEBUG: define_custom_tool - Parameters is a vector of length %d" (length parsed-params))
                      (setq parameters nil)
                      (dotimes (i (length parsed-params))
                        (let ((param-obj (aref parsed-params i)))
                          (when (listp param-obj)
                            (dolist (pair param-obj)
                              (push pair parameters))))))

                     ;; Array of key-value pairs
                     ((listp parsed-params)
                      (message "DEBUG: define_custom_tool - Parameters is a list")
                      (setq parameters parsed-params))

                     ;; Unknown format
                     (t
                      (let ((error-msg (format "ERROR: Invalid parameters format: %s" parameters)))
                        (message "DEBUG: define_custom_tool - %s" error-msg)
                        (throw 'error error-msg)))))
                (error
                 (let ((error-msg (format "ERROR: Failed to parse parameters JSON: %s" (error-message-string err))))
                   (message "DEBUG: define_custom_tool - %s" error-msg)
                   (throw 'error error-msg)))))

            (message "DEBUG: define_custom_tool - Parsed parameters: %S" parameters)

            ;; Process parameters
            (condition-case err
                (dolist (param parameters)
                  (let ((param-name (car param))
                        (param-description (cdr param)))
                    (push (cons param-name param-description) param-alist)))
              (error
               (let ((error-msg (format "ERROR: Failed to process parameters: %s" (error-message-string err))))
                 (message "DEBUG: define_custom_tool - %s" error-msg)
                 (throw 'error error-msg))))

            (message "DEBUG: define_custom_tool - Processed parameters: %S" param-alist)

            ;; Register the custom tool
            (puthash name
                     (list :description description
                           :function tool-function
                           :parameters param-alist
                           :implementation implementation
                           :implementation-type implementation-type
                           :custom t)
                     ai-auto-complete-custom-tools)

            (message "DEBUG: define_custom_tool - Custom tool added to custom-tools hash table")

            ;; Register with the main tool system
            (condition-case err
                (progn
                  (ai-auto-complete-register-tool
                   name
                   description
                   tool-function
                   param-alist)
                  (message "DEBUG: define_custom_tool - Custom tool registered with main tool system"))
              (error
               (let ((error-msg (format "ERROR: Failed to register tool with main system: %s" (error-message-string err))))
                 (message "DEBUG: define_custom_tool - %s" error-msg)
                 (remhash name ai-auto-complete-custom-tools)
                 (throw 'error error-msg))))

            ;; Save custom tools if enabled
            (when (and (boundp 'ai-auto-complete-custom-tools-save) ai-auto-complete-custom-tools-save)
              (condition-case err
                  (progn
                    (ai-auto-complete-save-custom-tools)
                    (message "DEBUG: define_custom_tool - Custom tools saved to file"))
                (error
                 (message "DEBUG: define_custom_tool - Failed to save custom tools: %s" (error-message-string err)))))

            ;; Return success message
            (let ((success-msg (format "Custom tool '%s' defined successfully. You can now use it like any other tool." name)))
              (message "DEBUG: define_custom_tool - %s" success-msg)
              success-msg))

        (error
         (let ((error-msg (format "ERROR: Failed to define custom tool: %s" (error-message-string err))))
           (message "DEBUG: define_custom_tool - %s" error-msg)
           error-msg)))))))

;; Function to list custom tools
(defun ai-auto-complete-tool-list-custom-tools (params)
  "List all custom tools.
PARAMS is ignored."
  (message "DEBUG: list_custom_tools called with params: %S" params)
  (catch 'error
    (let ((result "# Custom Tools\n\n"))

      ;; Check if custom tools hash table is initialized
      (unless (hash-table-p ai-auto-complete-custom-tools)
        (message "DEBUG: list_custom_tools - Custom tools hash table not initialized, initializing now")
        (setq ai-auto-complete-custom-tools (make-hash-table :test 'equal)))

      ;; Check if there are any custom tools
      (if (= (hash-table-count ai-auto-complete-custom-tools) 0)
          (progn
            (message "DEBUG: list_custom_tools - No custom tools defined")
            (setq result (concat result "No custom tools have been defined.\n\n"
                                 "You can define custom tools using the `define_custom_tool` function.\n")))

        ;; Add table header
        (message "DEBUG: list_custom_tools - Found %d custom tools" (hash-table-count ai-auto-complete-custom-tools))
        (setq result (concat result
                             "| Tool Name | Description | Type | Parameters |\n"
                             "|-----------|-------------|------|------------|\n"))

        ;; Add each custom tool
        (condition-case err
            (maphash
             (lambda (tool-name tool-info)
               (condition-case err
                   (let* ((description (plist-get tool-info :description))
                          (implementation-type (plist-get tool-info :implementation-type))
                          (parameters (plist-get tool-info :parameters))
                          (param-str ""))

                     (message "DEBUG: list_custom_tools - Processing tool: %s" tool-name)

                     ;; Format parameters
                     (condition-case err
                         (dolist (param parameters)
                           (condition-case nil
                               (setq param-str (concat param-str (format "`%s`, " (car param))))
                             (error
                              (message "DEBUG: list_custom_tools - Error processing parameter: %S" param))))
                       (error
                        (message "DEBUG: list_custom_tools - Error formatting parameters: %s"
                                 (error-message-string err))))

                     (condition-case err
                         (when (and (not (string-empty-p param-str))
                                    (>= (length param-str) 2))
                           (setq param-str (substring param-str 0 (- (length param-str) 2))))
                       (error
                        (message "DEBUG: list_custom_tools - Error trimming parameter string: %s"
                                 (error-message-string err))))

                     ;; Add tool to table
                     (condition-case err
                         (setq result (concat result
                                              "| `" tool-name "` | "
                                              (if (and description (> (length description) 30))
                                                  (concat (substring description 0 27) "...")
                                                (or description "No description"))
                                              " | " (or implementation-type "unknown") " | " param-str " |\n"))
                       (error
                        (message "DEBUG: list_custom_tools - Error adding tool to table: %s"
                                 (error-message-string err)))))

                 (error
                  (let ((error-msg (format "ERROR: Failed to process tool '%s': %s"
                                           tool-name (error-message-string err))))
                    (message "DEBUG: list_custom_tools - %s" error-msg)
                    (setq result (concat result "| `" tool-name "` | Error processing tool | - | - |\n"))))))
             ai-auto-complete-custom-tools)

          (error
           (let ((error-msg (format "ERROR: Failed to list custom tools: %s" (error-message-string err))))
             (message "DEBUG: list_custom_tools - %s" error-msg)
             (setq result (concat result error-msg "\n\n"))))))

      ;; Add information about how to use custom tools
      (setq result (concat result "\n"
                           "## Using Custom Tools\n\n"
                           "Custom tools can be used just like built-in tools. For example:\n\n"
                           "```xml\n"
                           "<tool name=\"custom_tool_name\">\n"
                           "<parameters>\n"
                           "{\"param1\": \"value1\", \"param2\": \"value2\"}\n"
                           "</parameters>\n"
                           "</tool>\n"
                           "```\n\n"
                           "## Creating Custom Tools\n\n"
                           "You can create new custom tools using the `define_custom_tool` function.\n\n"
                           "### Shell Script Implementation\n\n"
                           "```xml\n"
                           "<tool name=\"define_custom_tool\">\n"
                           "<parameters>\n"
                           "{\n"
                           "  \"name\": \"my_tool\",\n"
                           "  \"description\": \"My custom tool\",\n"
                           "  \"parameters\": [{\"param1\": \"Description of param1\"}],\n"
                           "  \"type\": \"shell\",\n"
                           "  \"implementation\": \"echo \\\"Hello, $param1!\\\"\"\n"
                           "}\n"
                           "</parameters>\n"
                           "</tool>\n"
                           "```\n\n"
                           "### Tool Sequence Implementation\n\n"
                           "```xml\n"
                           "<tool name=\"define_custom_tool\">\n"
                           "<parameters>\n"
                           "{\n"
                           "  \"name\": \"combined_tool\",\n"
                           "  \"description\": \"Combines multiple tools\",\n"
                           "  \"parameters\": [{\"directory\": \"Directory to analyze\"}],\n"
                           "  \"type\": \"tool_sequence\",\n"
                           "  \"implementation\": \"[{\\\"name\\\": \\\"list_directory\\\", \\\"parameters\\\": {\\\"path\\\": \\\"$directory\\\"}}, {\\\"name\\\": \\\"run_command\\\", \\\"parameters\\\": {\\\"command\\\": \\\"du -sh $directory\\\"}}]\"\n"
                           "}\n"
                           "</parameters>\n"
                           "</tool>\n"
                           "```\n"))

      ;; Return the result
      (message "DEBUG: list_custom_tools - Returning result of length: %d" (length result))
      result)))



;; Function to delete a custom tool
(defun ai-auto-complete-tool-delete-custom-tool (params)
  "Delete a custom tool.
PARAMS should be an alist with:
- 'name': Name of the custom tool to delete"
  (message "DEBUG: delete_custom_tool called with params: %S" params)
  (catch 'error
    (let* ((name (cdr (assoc 'name params))))

      ;; Check if name is provided
      (if (not name)
          (progn
            (message "DEBUG: delete_custom_tool - No tool name specified")
            (throw 'error "ERROR: No tool name specified"))

        (message "DEBUG: delete_custom_tool - Deleting tool: %s" name)

        ;; Check if custom tools hash table is initialized
        (unless (hash-table-p ai-auto-complete-custom-tools)
          (message "DEBUG: delete_custom_tool - Custom tools hash table not initialized, initializing now")
          (setq ai-auto-complete-custom-tools (make-hash-table :test 'equal)))

        ;; Check if tool exists and is custom
        (if (not (and (gethash name ai-auto-complete-custom-tools)
                      (plist-get (gethash name ai-auto-complete-custom-tools) :custom)))
            (let ((error-msg (format "ERROR: No custom tool with name '%s' exists." name)))
              (message "DEBUG: delete_custom_tool - %s" error-msg)
              (throw 'error error-msg))

          ;; Remove the tool
          (condition-case err
              (progn
                (remhash name ai-auto-complete-custom-tools)
                (message "DEBUG: delete_custom_tool - Removed from custom-tools hash table")

                (when (and (hash-table-p ai-auto-complete-tools)
                           (gethash name ai-auto-complete-tools))
                  (remhash name ai-auto-complete-tools)
                  (message "DEBUG: delete_custom_tool - Removed from main tools hash table"))

                ;; Save custom tools if enabled
                (when (and (boundp 'ai-auto-complete-custom-tools-save) ai-auto-complete-custom-tools-save)
                  (condition-case err
                      (progn
                        (ai-auto-complete-save-custom-tools)
                        (message "DEBUG: delete_custom_tool - Custom tools saved to file"))
                    (error
                     (message "DEBUG: delete_custom_tool - Failed to save custom tools: %s"
                              (error-message-string err)))))

                ;; Return success message
                (let ((success-msg (format "Custom tool '%s' has been deleted." name)))
                  (message "DEBUG: delete_custom_tool - %s" success-msg)
                  success-msg))

            (error
             (let ((error-msg (format "ERROR: Failed to delete custom tool '%s': %s"
                                      name (error-message-string err))))
               (message "DEBUG: delete_custom_tool - %s" error-msg)
               (throw 'error error-msg)))))))))

;; Function to save custom tools
(defun ai-auto-complete-save-custom-tools ()
  "Save custom tools to file."
  (when ai-auto-complete-custom-tools-save
    (with-temp-file ai-auto-complete-custom-tools-file
      (insert ";; AI Auto Complete Custom Tools\n")
      (insert ";; Automatically generated. Do not edit.\n\n")
      (insert "(setq ai-auto-complete-custom-tools\n")
      (insert "      (let ((hash-table (make-hash-table :test 'equal)))\n")

      ;; Add each custom tool
      (maphash
       (lambda (tool-name tool-info)
         (let ((description (plist-get tool-info :description))
               (parameters (plist-get tool-info :parameters))
               (implementation (plist-get tool-info :implementation))
               (implementation-type (plist-get tool-info :implementation-type)))

           (insert (format "        (puthash \"%s\"\n" tool-name))
           (insert (format "                 '(:description %S\n" description))
           (insert (format "                   :parameters %S\n" parameters))
           (insert (format "                   :implementation %S\n" implementation))
           (insert (format "                   :implementation-type %S\n" implementation-type))
           (insert "                   :custom t)\n")
           (insert "                 hash-table)\n")))
       ai-auto-complete-custom-tools)

      (insert "        hash-table))\n\n")

      ;; Add code to register the tools
      (insert ";; Register custom tools\n")
      (insert "(maphash\n")
      (insert " (lambda (tool-name tool-info)\n")
      (insert "   (let ((description (plist-get tool-info :description))\n")
      (insert "         (parameters (plist-get tool-info :parameters))\n")
      (insert "         (implementation (plist-get tool-info :implementation))\n")
      (insert "         (implementation-type (plist-get tool-info :implementation-type)))\n")
      (insert "     (ai-auto-complete-tool-define-custom-tool\n")
      (insert "      `((name . ,tool-name)\n")
      (insert "        (description . ,description)\n")
      (insert "        (parameters . ,parameters)\n")
      (insert "        (implementation . ,implementation)\n")
      (insert "        (type . ,implementation-type)))))\n")
      (insert " ai-auto-complete-custom-tools)\n"))))

;; Function to load custom tools
(defun ai-auto-complete-load-custom-tools ()
  "Load custom tools from file."
  (when (and ai-auto-complete-custom-tools-save
             (file-exists-p ai-auto-complete-custom-tools-file))
    (load ai-auto-complete-custom-tools-file t)))

;; Register custom tool functions
(defun ai-auto-complete-register-custom-tool-functions ()
  "Register custom tool functions."
  ;; Load custom tools
  (ai-auto-complete-load-custom-tools)

  ;; Define custom tool
  (ai-auto-complete-register-tool
   "define_custom_tool"
   "Define a custom tool that can be used like any other tool"
   #'ai-auto-complete-tool-define-custom-tool
   '(("name" . "Name of the custom tool")
     ("description" . "Description of what the tool does")
     ("parameters" . "List of parameters the tool accepts")
     ("implementation" . "Either a shell script or a sequence of tool calls")
     ("type" . "Type of implementation ('shell' or 'tool_sequence')")))

  ;; List custom tools
  (ai-auto-complete-register-tool
   "list_custom_tools"
   "List all custom tools"
   #'ai-auto-complete-tool-list-custom-tools
   '())

  ;; Delete custom tool
  (ai-auto-complete-register-tool
   "delete_custom_tool"
   "Delete a custom tool"
   #'ai-auto-complete-tool-delete-custom-tool
   '(("name" . "Name of the custom tool to delete"))))

;; Test function for custom tools
(defun ai-auto-complete-custom-tools-test ()
  "Test the custom tool creation functionality.
This function sends a prompt that asks the LLM to create and use custom tools."
  (interactive)
  ;; Enable tools and debug mode
  (unless ai-auto-complete-tools-enabled
    (ai-auto-complete-tools-enable))
  (setq ai-auto-complete-tools-debug-mode t)

  ;; Create a test prompt that will trigger custom tool creation
  (let ((test-prompt "I need you to help me create and use custom tools:

1. First, create a custom shell script tool called 'system_summary' that combines several system information commands
2. Then, use the custom tool to get a system summary
3. Create another custom tool called 'find_large_files' that finds files larger than a specified size
4. Use the custom tool to find large files in the current directory
5. List all custom tools that have been created

Please make these tool calls in sequence, analyzing the results of each before proceeding to the next."))

    (message "Sending custom tools test prompt to LLM")

    ;; Send the prompt to the current backend
    (let ((backend (ai-auto-complete-get-current-backend)))
      (message "Using backend: %s" backend)
      (ai-auto-complete-complete
       backend
       test-prompt
       nil
       (lambda (response)
         (message "Received final response from LLM for custom tools test")
         (with-current-buffer (get-buffer-create "*AI Custom Tools Test*")
           (erase-buffer)
           (insert "Custom Tools Test Results:\n\n")
           (insert "The following response demonstrates the application's custom tool creation capabilities.\n\n")
           (insert "Test prompt:\n")
           (insert test-prompt)
           (insert "\n\nResponse:\n")
           (insert response)
           (display-buffer (current-buffer)))))))

  (message "Custom tools test initiated. Results will appear in the *AI Custom Tools Test* buffer."))

;; Register custom tool functions when this module is loaded
(ai-auto-complete-register-custom-tool-functions)

(provide 'tools/custom-tools)
;;; custom-tools.el ends here
