;;; python-tools.el --- Python interpreter tools for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides Python interpreter tools for the AI Auto Complete package.
;; These tools give the LLM full access and control over the Python interpreter.

;;; Code:

;; Try to load tools-core, but handle failure gracefully
(condition-case nil
    (require 'tools/tools-core)
  (error
   (message "Warning: Failed to load tools/tools-core")
   (load (expand-file-name "tools-core.el"
                          (file-name-directory (or load-file-name buffer-file-name))))))
(require 'cl-lib)

;; Variables for Python interpreter management
(defvar ai-auto-complete-python-processes (make-hash-table :test 'equal)
  "Hash table of running Python processes, mapping process IDs to process objects.")

(defvar ai-auto-complete-python-process-counter 0
  "Counter for generating unique Python process IDs.")

(defvar ai-auto-complete-python-output-limit 10000
  "Maximum number of characters to return from Python process output.")

;; Customization group for Python tools
(defgroup ai-auto-complete-python nil
  "Settings for Python interpreter tools in AI Auto Complete."
  :group 'ai-auto-complete-tools
  :prefix "ai-auto-complete-python-")

;; Customization options
(defcustom ai-auto-complete-python-command "python3"
  "Command to use for Python interpreter."
  :type 'string
  :group 'ai-auto-complete-python)

(defcustom ai-auto-complete-python-timeout 10
  "Default timeout in seconds for Python code execution."
  :type 'integer
  :group 'ai-auto-complete-python)

(defcustom ai-auto-complete-python-safe-mode t
  "Whether to run Python code in safe mode with restricted imports."
  :type 'boolean
  :group 'ai-auto-complete-python)

(defcustom ai-auto-complete-python-restricted-modules
  '("os.system" "subprocess" "shutil.rmtree" "os.remove" "os.unlink")
  "List of potentially dangerous Python modules or functions to restrict in safe mode."
  :type '(repeat string)
  :group 'ai-auto-complete-python)

;; Helper functions
(defun ai-auto-complete-python-is-safe-code (code)
  "Check if Python CODE is safe to execute.
Returns t if safe, or a string with the reason if unsafe."
  (message "DEBUG: python_is_safe_code - Checking code safety")
  (if (not ai-auto-complete-python-safe-mode)
      (progn
        (message "DEBUG: python_is_safe_code - Safe mode disabled, allowing code")
        t)
    (let ((unsafe-found nil))
      (dolist (module ai-auto-complete-python-restricted-modules)
        (when (string-match-p (format "\\b%s\\b" (regexp-quote module)) code)
          (message "DEBUG: python_is_safe_code - Found restricted module/function: %s" module)
          (setq unsafe-found (format "Code contains restricted module/function: %s" module))))
      (if unsafe-found
          (message "DEBUG: python_is_safe_code - Code is unsafe: %s" unsafe-found)
        (message "DEBUG: python_is_safe_code - Code passed safety check"))
      (or unsafe-found t))))

;; Execute Python code synchronously
(defun ai-auto-complete-tool-python-execute (params)
  "Execute Python code synchronously.
PARAMS should be an alist with:
- 'code': The Python code to execute
- 'timeout': Optional timeout in seconds (default: value of ai-auto-complete-python-timeout)
- 'safe_mode_override': Optional boolean to override safe mode"
  (message "DEBUG: python_execute called with params: %S" params)
  (let* ((code (cdr (assoc 'code params)))
         (timeout (or (cdr (assoc 'timeout params)) ai-auto-complete-python-timeout))
         (safe-mode-override (cdr (assoc 'safe_mode_override params)))
         (output-buffer nil)
         (error-buffer nil)
         (temp-file nil)
         (result-alist nil)
         (exit-code nil))

    (message "DEBUG: python_execute - Code length: %d, Timeout: %d, Safe mode override: %s"
             (if code (length code) 0) timeout (if safe-mode-override "yes" "no"))

    ;; Check if code is provided
    (if (not code)
        (progn
          (message "DEBUG: python_execute - No Python code specified")
          "ERROR: No Python code specified")

      ;; Check if code is safe
      (let ((safety-check (ai-auto-complete-python-is-safe-code code)))
        (if (and (not safe-mode-override)
                 (not (eq safety-check t)))
            (let ((error-msg (format "ERROR: %s\n\nTo execute this code, set 'safe_mode_override' to true." safety-check)))
              (message "DEBUG: python_execute - %s" error-msg)
              error-msg)

          ;; Create buffers and temp file
          (setq output-buffer (generate-new-buffer " *python-output*"))
          (setq error-buffer (generate-new-buffer " *python-error*"))
          (setq temp-file (make-temp-file "python-code-" nil ".py"))

          ;; Write code to temporary file
          (message "DEBUG: python_execute - Writing code to temporary file: %s" temp-file)
          (unwind-protect
              (progn
                (with-temp-file temp-file
                  (insert code))

                ;; Run the Python code
                (message "DEBUG: python_execute - Running Python code with timeout: %d seconds" timeout)
                (unwind-protect
                    (progn
                      ;; Run the code with timeout
                      (with-timeout (timeout
                                     (message "DEBUG: python_execute - Execution timed out after %d seconds" timeout)
                                     (push (cons 'timeout t) result-alist)
                                     (push (cons 'error "Python execution timed out") result-alist))
                        ;; Execute the Python code
                        (message "DEBUG: python_execute - Executing Python code")
                        (condition-case exec-err
                            (progn
                              (setq exit-code
                                    (call-process ai-auto-complete-python-command nil
                                                  (list output-buffer error-buffer) nil
                                                  temp-file))
                              (message "DEBUG: python_execute - Python code executed with exit code: %d" exit-code))
                          (error
                           (let ((error-msg (format "Error executing Python code: %s" (error-message-string exec-err))))
                             (message "DEBUG: python_execute - %s" error-msg)
                             (push (cons 'error error-msg) result-alist)))))

                      ;; Get command output
                      (message "DEBUG: python_execute - Getting command output")
                      (when (buffer-live-p output-buffer)
                        (with-current-buffer output-buffer
                          (push (cons 'stdout (buffer-string)) result-alist)))

                      ;; Get error output
                      (message "DEBUG: python_execute - Getting error output")
                      (when (buffer-live-p error-buffer)
                        (with-current-buffer error-buffer
                          (push (cons 'stderr (buffer-string)) result-alist)))

                      ;; Add exit code
                      (push (cons 'exit_code exit-code) result-alist))

                  ;; Clean up in unwind-protect
                  (message "DEBUG: python_execute - Cleaning up temporary files and buffers")
                  (when (file-exists-p temp-file)
                    (delete-file temp-file))
                  (when (buffer-live-p output-buffer)
                    (kill-buffer output-buffer))
                  (when (buffer-live-p error-buffer)
                    (kill-buffer error-buffer))))

              ;; Clean up in case of error in with-temp-file
              (message "DEBUG: python_execute - Cleaning up in case of error")
              (when (file-exists-p temp-file)
                (delete-file temp-file))
              (when (buffer-live-p output-buffer)
                (kill-buffer output-buffer))
              (when (buffer-live-p error-buffer)
                (kill-buffer error-buffer))))

          ;; Format the result
          (message "DEBUG: python_execute - Formatting result")
          (let* ((stdout (or (cdr (assoc 'stdout result-alist)) ""))
                 (stderr (or (cdr (assoc 'stderr result-alist)) ""))
                 (error-msg (cdr (assoc 'error result-alist)))
                 (timeout (cdr (assoc 'timeout result-alist)))
                 (exit-code (or (cdr (assoc 'exit_code result-alist)) -1))
                 (success (and (not error-msg) (not timeout) (= exit-code 0))))

            (message "DEBUG: python_execute - Success: %s, Exit code: %d, Stdout length: %d, Stderr length: %d"
                     (if success "yes" "no") exit-code (length stdout) (length stderr))

            (let ((result (format "Python Execution %s\n\n%s%s%s"
                                  (if success "Successful" "Failed")
                                  (if (string-empty-p stdout)
                                      ""
                                    (format "Output:\n```\n%s\n```\n\n" stdout))
                                  (if (string-empty-p stderr)
                                      ""
                                    (format "Error Output:\n```\n%s\n```\n\n" stderr))
                                  (cond
                                   (error-msg (format "Error: %s" error-msg))
                                   (timeout "Error: Execution timed out")
                                   ((not (= exit-code 0)) (format "Error: Process exited with code %d" exit-code))
                                   (t "")))))
              (message "DEBUG: python_execute - Returning result of length: %d" (length result))
              result))))))

;; Register Python tools
(defun ai-auto-complete-register-python-tools ()
  "Register Python interpreter tools."
  (message "DEBUG: register_python_tools - Registering Python tools")

  ;; Execute Python code
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "python_execute"
         "Execute Python code synchronously and return the result"
         #'ai-auto-complete-tool-python-execute
         '(("code" . "Python code to execute")
           ("timeout" . "Optional timeout in seconds")
           ("safe_mode_override" . "Optional boolean to override safe mode")))
        (message "DEBUG: register_python_tools - python_execute tool registered"))
    (error
     (message "DEBUG: register_python_tools - Error registering python_execute: %s"
              (error-message-string err))))

  (message "DEBUG: register_python_tools - All Python tools registered successfully"))

;; Register Python tools when this module is loaded
(ai-auto-complete-register-python-tools)

(provide 'tools/python-tools)
;;; python-tools.el ends here
