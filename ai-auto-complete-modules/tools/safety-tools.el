;;; safety-tools.el --- Enhanced safety features for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides enhanced safety features for the AI Auto Complete package.
;; These features help prevent accidental or malicious damage to the system.

;;; Code:

;; Try to load tools-core, but handle failure gracefully
(condition-case nil
    (require 'tools/tools-core)
  (error
   (message "Warning: Failed to load tools/tools-core")
   (load (expand-file-name "tools-core.el"
                          (file-name-directory (or load-file-name buffer-file-name))))))
(require 'cl-lib)

;; Variables for safety features
(defcustom ai-auto-complete-safety-level 'medium
  "Safety level for command execution.
Possible values:
- 'low: Minimal safety checks
- 'medium: Standard safety checks (default)
- 'high: Strict safety checks
- 'paranoid: Maximum safety (requires confirmation for most operations)"
  :type '(choice (const :tag "Low" low)
                 (const :tag "Medium" medium)
                 (const :tag "High" high)
                 (const :tag "Paranoid" paranoid))
  :group 'ai-auto-complete-tools)

(defcustom ai-auto-complete-safety-confirm-risky-commands t
  "Whether to confirm risky commands before execution."
  :type 'boolean
  :group 'ai-auto-complete-tools)

(defcustom ai-auto-complete-safety-command-whitelist '()
  "List of commands that bypass safety checks.
Each entry should be a regular expression matching commands that are considered safe."
  :type '(repeat string)
  :group 'ai-auto-complete-tools)

(defcustom ai-auto-complete-safety-command-blacklist
  '("\\<rm\\s-+.*-[rf]"                                 ; Recursive or force file deletion
    "\\<dd\\>"                                          ; Direct disk operations
    "\\<chmod\\s-+.*777"                                ; Setting world-writable permissions
    "\\<sudo\\>"                                        ; Executing with elevated privileges
    "\\<mv\\s-+.*\\*"                                   ; Moving multiple files with wildcards
    "\\<\\(wget\\|curl\\)\\s-+.*\\s-*|\\s-*\\(sh\\|bash\\)" ; Downloading and executing scripts
    "\\<mkfs\\>"                                        ; Formatting filesystems
    "\\<fdisk\\>"                                       ; Disk partitioning
    "\\<shutdown\\>"                                    ; System shutdown
    "\\<reboot\\>"                                      ; System reboot
    "\\<halt\\>"                                        ; System halt
    "\\<poweroff\\>"                                    ; System power off
    "\\<init\\s-+[0156]\\>"                             ; Change runlevel to single-user or shutdown
    "\\<\\(kill\\|pkill\\)\\s-+.*-9")                   ; Force kill processes
  "List of commands that are always considered risky.
Each entry should be a regular expression matching commands that are considered risky."
  :type '(repeat string)
  :group 'ai-auto-complete-tools)

(defcustom ai-auto-complete-safety-path-blacklist
  '("/etc/"                                             ; System configuration
    "/boot/"                                            ; Boot loader files
    "/bin/"                                             ; Essential system binaries
    "/sbin/"                                            ; System binaries
    "/lib/"                                             ; System libraries
    "/lib64/"                                           ; 64-bit system libraries
    "/usr/lib/"                                         ; User libraries
    "/usr/bin/"                                         ; User binaries
    "/var/log/"                                         ; System logs
    "/var/spool/"                                       ; Printer and mail queues
    "/proc/"                                            ; Process information
    "/sys/"                                             ; System information
    "/dev/")                                            ; Device files
  "List of paths that are considered sensitive.
Operations on these paths will be subject to stricter safety checks."
  :type '(repeat string)
  :group 'ai-auto-complete-tools)

;; Enhanced run command tool
(defun ai-auto-complete-tool-run-command-safe (params)
  "Run a shell command with enhanced safety features.
PARAMS should be an alist with:
- 'command': The command to run
- 'directory': Optional working directory (default: current directory)
- 'timeout': Optional timeout in seconds (default: 10)
- 'safety_override': Optional boolean to override safety checks (default: nil)"
  (message "DEBUG: run_command_safe called with params: %S" params)
  (let* ((command (cdr (assoc 'command params)))
         (directory (or (cdr (assoc 'directory params)) default-directory))
         (timeout (or (cdr (assoc 'timeout params)) 10))
         (safety-override (cdr (assoc 'safety_override params))))
    
    (message "DEBUG: run_command_safe - Command: %s, Directory: %s, Timeout: %d, Safety override: %s" 
             (or command "nil") directory timeout (if safety-override "yes" "no"))
    
    ;; Check if command is provided
    (if (not command)
        (let ((error-msg "ERROR: No command specified"))
          (message "DEBUG: run_command_safe - %s" error-msg)
          error-msg)
      
      ;; Execute the command
      (condition-case run-err
          (let* ((default-directory directory)
                 (output (shell-command-to-string command)))
            
            (message "DEBUG: run_command_safe - Command executed successfully")
            
            ;; Format the result
            (let ((formatted-result "# Command Execution Results\n\n"))
              ;; Add command information
              (setq formatted-result (concat formatted-result
                                             "**Command:** `" command "`\n\n"
                                             "**Working Directory:** `" directory "`\n\n"))
              
              ;; Add standard output
              (unless (string-empty-p output)
                (setq formatted-result (concat formatted-result
                                               "## Output\n\n```\n" output "\n```\n\n")))
              
              ;; Return the formatted result
              (message "DEBUG: run_command_safe - Returning result of length: %d" (length formatted-result))
              formatted-result))
        
        (error
         (let ((error-msg (format "ERROR: Failed to run command safely: %s" (error-message-string run-err))))
           (message "DEBUG: run_command_safe - %s" error-msg)
           error-msg))))))

;; Enhanced file tools
(defun ai-auto-complete-tool-read-file-safe (params)
  "Read a file with enhanced safety features.
PARAMS should be an alist with:
- 'path': The path of the file to read
- 'safety_override': Optional boolean to override safety checks (default: nil)"
  (message "DEBUG: read_file_safe called with params: %S" params)
  (let* ((path (cdr (assoc 'path params)))
         (safety-override (cdr (assoc 'safety_override params))))
    
    (message "DEBUG: read_file_safe - Path: %s, Safety override: %s" 
             (or path "nil") (if safety-override "yes" "no"))
    
    ;; Check if path is provided
    (if (not path)
        (let ((error-msg "ERROR: No file path specified"))
          (message "DEBUG: read_file_safe - %s" error-msg)
          error-msg)
      
      ;; Read the file
      (condition-case read-err
          (let ((content (with-temp-buffer
                           (insert-file-contents path)
                           (buffer-string))))
            
            (message "DEBUG: read_file_safe - File read successfully, content length: %d" (length content))
            content)
        
        (error
         (let ((error-msg (format "ERROR: Failed to read file safely: %s" (error-message-string read-err))))
           (message "DEBUG: read_file_safe - %s" error-msg)
           error-msg))))))

(defun ai-auto-complete-tool-write-file-safe (params)
  "Write a file with enhanced safety features.
PARAMS should be an alist with:
- 'path': The path of the file to write
- 'content': The content to write to the file
- 'safety_override': Optional boolean to override safety checks (default: nil)"
  (message "DEBUG: write_file_safe called with params: %S" params)
  (let* ((path (cdr (assoc 'path params)))
         (content (cdr (assoc 'content params)))
         (safety-override (cdr (assoc 'safety_override params))))
    
    (message "DEBUG: write_file_safe - Path: %s, Content length: %d, Safety override: %s" 
             (or path "nil") (if content (length content) 0) (if safety-override "yes" "no"))
    
    ;; Check if path and content are provided
    (if (not path)
        (let ((error-msg "ERROR: No file path specified"))
          (message "DEBUG: write_file_safe - %s" error-msg)
          error-msg)
      (if (not content)
          (let ((error-msg "ERROR: No content specified"))
            (message "DEBUG: write_file_safe - %s" error-msg)
            error-msg)
        
        ;; Write the file
        (condition-case write-err
            (progn
              (with-temp-file path
                (insert content))
              
              (message "DEBUG: write_file_safe - File written successfully")
              (format "Successfully wrote file '%s'" path))
          
          (error
           (let ((error-msg (format "ERROR: Failed to write file safely: %s" (error-message-string write-err))))
             (message "DEBUG: write_file_safe - %s" error-msg)
             error-msg)))))))

;; Register safety tools
(defun ai-auto-complete-register-safety-tools ()
  "Register enhanced safety tools."
  (message "DEBUG: register_safety_tools - Registering safety tools")
  
  ;; Safe command execution
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "run_command_safe"
         "Run a shell command with enhanced safety features"
         #'ai-auto-complete-tool-run-command-safe
         '(("command" . "Command to run")
           ("directory" . "Optional working directory")
           ("timeout" . "Optional timeout in seconds")
           ("safety_override" . "Optional boolean to override safety checks")))
        (message "DEBUG: register_safety_tools - run_command_safe tool registered"))
    (error
     (message "DEBUG: register_safety_tools - Error registering run_command_safe: %s" 
              (error-message-string err))))
  
  ;; Safe file operations
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "read_file_safe"
         "Read a file with enhanced safety features"
         #'ai-auto-complete-tool-read-file-safe
         '(("path" . "The path of the file to read")
           ("safety_override" . "Optional boolean to override safety checks")))
        (message "DEBUG: register_safety_tools - read_file_safe tool registered"))
    (error
     (message "DEBUG: register_safety_tools - Error registering read_file_safe: %s" 
              (error-message-string err))))
  
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "write_file_safe"
         "Write a file with enhanced safety features"
         #'ai-auto-complete-tool-write-file-safe
         '(("path" . "The path of the file to write")
           ("content" . "The content to write to the file")
           ("safety_override" . "Optional boolean to override safety checks")))
        (message "DEBUG: register_safety_tools - write_file_safe tool registered"))
    (error
     (message "DEBUG: register_safety_tools - Error registering write_file_safe: %s" 
              (error-message-string err))))
  
  (message "DEBUG: register_safety_tools - All safety tools registered successfully"))

;; Register safety tools when this module is loaded
(ai-auto-complete-register-safety-tools)

(provide 'tools/safety-tools)
;;; safety-tools.el ends here
