;;; task-management.el --- Task management tools for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides task management tools for the AI Auto Complete package.
;; These tools allow agents to create and execute multi-step plans, helping them
;; organize complex tasks into smaller, manageable steps.

;;; Code:

(require 'cl-lib)
(require 'tools/tools-core)

;; Define a task plan structure
(cl-defstruct (ai-auto-complete-task-plan (:constructor ai-auto-complete-task-plan-create)
                                         (:copier nil))
  "Task plan for organizing complex tasks into steps."
  (id nil :read-only t :documentation "Unique identifier for this plan.")
  (original-request "" :read-only nil :documentation "The user's initial high-level goal.")
  (status 'pending :read-only nil :documentation "Status of the plan: pending, in-progress, completed, failed.")
  (sub-tasks nil :read-only nil :documentation "List of sub-tasks in the plan.")
  (current-step-index 0 :read-only nil :documentation "Index of the current step being executed.")
  (llm-notes "" :read-only nil :documentation "Optional notes from the LLM about the plan.")
  (agent-name nil :read-only nil :documentation "Name of the agent that created this plan.")
  (creation-time nil :read-only t :documentation "Time when the plan was created."))

;; Define a sub-task structure
(cl-defstruct (ai-auto-complete-sub-task (:constructor ai-auto-complete-sub-task-create)
                                        (:copier nil))
  "Sub-task within a task plan."
  (id nil :read-only t :documentation "Unique identifier for this sub-task.")
  (description "" :read-only nil :documentation "Natural language description of this step.")
  (tool-name nil :read-only nil :documentation "Name of the tool to be called for this step.")
  (tool-params nil :read-only nil :documentation "Parameters for the tool call.")
  (status 'pending :read-only nil :documentation "Status: pending, completed, failed.")
  (result nil :read-only nil :documentation "Result of the tool call after execution."))

;; Global hash table to store active task plans
(defvar ai-auto-complete-active-task-plans (make-hash-table :test 'equal)
  "Hash table of active task plans, mapping plan IDs to plan objects.")

;; Generate a unique ID for a new plan or sub-task
(defun ai-auto-complete-generate-unique-id (prefix)
  "Generate a unique ID with PREFIX."
  (format "%s-%s" prefix (format-time-string "%Y%m%d%H%M%S%N")))

;; Check for placeholder content in tool parameters
(defun ai-auto-complete-check-for-placeholders (params)
  "Check if PARAMS contains placeholder values and return a warning message if found.
Returns nil if no placeholders are found."
  (let ((placeholder-found nil)
        (placeholder-params nil))

    ;; Check each parameter for placeholder content
    (dolist (param params)
      (let ((param-name (car param))
            (param-value (cdr param)))
        (when (and (stringp param-value)
                   (or (string-match-p "\\[PLACEHOLDER\\]" param-value)
                       (string-match-p "\\[placeholder\\]" param-value)
                       (string-match-p "PLACEHOLDER" param-value)
                       (string-match-p "placeholder" param-value)))
          (setq placeholder-found t)
          (push param-name placeholder-params))))

    (when placeholder-found
      (format "Found placeholder content in parameter(s): %s"
              (mapconcat (lambda (p) (format "%s" p)) placeholder-params ", ")))))

;; Create a new task plan
(defun ai-auto-complete-create-task-plan (task-description steps agent-name &optional notes)
  "Create a new task plan for TASK-DESCRIPTION with STEPS.
AGENT-NAME is the name of the agent creating the plan.
Optional NOTES can be provided for additional context."
  (let* ((plan-id (ai-auto-complete-generate-unique-id "plan"))
         (sub-tasks nil))

    ;; Create sub-tasks from the provided steps
    (dolist (step steps)
      (let* ((step-id (ai-auto-complete-generate-unique-id "step"))
             (description (cdr (assoc 'description step)))
             (tool-name (cdr (assoc 'tool_name step)))
             (tool-params-raw (cdr (assoc 'tool_params step)))
             ;; Convert tool_params from vector to alist if necessary
             (tool-params (cond
                           ((vectorp tool-params-raw)
                            ;; Convert vector of key-value pairs to alist
                            (let ((result nil))
                              (dotimes (i (length tool-params-raw))
                                (let ((pair (aref tool-params-raw i)))
                                  (when (consp pair)
                                    (push pair result))))
                              (nreverse result)))
                           ((listp tool-params-raw) tool-params-raw)  ; Already an alist
                           (t tool-params-raw))))                     ; Keep as-is for other types

        ;; Check for placeholders and add a warning note if found
        (let ((placeholder-warning (ai-auto-complete-check-for-placeholders tool-params)))
          (when placeholder-warning
            (message "WARNING: Task plan step '%s' contains placeholders: %s" description placeholder-warning)))

        (let ((sub-task (ai-auto-complete-sub-task-create
                         :id step-id
                         :description description
                         :tool-name tool-name
                         :tool-params tool-params
                         :status 'pending
                         :result nil)))
          (push sub-task sub-tasks))))

    ;; Create the plan object
    (let ((plan (ai-auto-complete-task-plan-create
                 :id plan-id
                 :original-request task-description
                 :status 'pending
                 :sub-tasks (nreverse sub-tasks)
                 :current-step-index 0
                 :llm-notes (or notes "")
                 :agent-name agent-name
                 :creation-time (current-time))))

      ;; Store the plan in the hash table
      (puthash plan-id plan ai-auto-complete-active-task-plans)

      ;; Return the plan ID and first step info
      (let* ((first-step (car (ai-auto-complete-task-plan-sub-tasks plan)))
             (first-step-desc (if first-step
                                 (ai-auto-complete-sub-task-description first-step)
                               "No steps defined")))
        (cons plan-id first-step-desc)))))

;; Get a task plan by ID
(defun ai-auto-complete-get-task-plan (plan-id)
  "Get a task plan by PLAN-ID."
  (gethash plan-id ai-auto-complete-active-task-plans))

;; Execute the current step of a task plan
(defun ai-auto-complete-execute-planned-step (plan-id)
  "Execute the current step of the task plan with PLAN-ID."
  (let ((plan (ai-auto-complete-get-task-plan plan-id)))
    (if (not plan)
        (format "ERROR: Task plan with ID %s not found" plan-id)

      (let ((current-index (ai-auto-complete-task-plan-current-step-index plan))
            (sub-tasks (ai-auto-complete-task-plan-sub-tasks plan)))

        ;; Check if we've completed all steps
        (if (>= current-index (length sub-tasks))
            (format "All steps in plan %s have been completed. The task '%s' should be complete."
                    plan-id
                    (ai-auto-complete-task-plan-original-request plan))

          ;; Get the current sub-task
          (let* ((sub-task (nth current-index sub-tasks))
                 (tool-name (ai-auto-complete-sub-task-tool-name sub-task))
                 (tool-params (ai-auto-complete-sub-task-tool-params sub-task))
                 (tool-result nil)
                 (success t))

            ;; Update plan status to in-progress if it was pending
            (when (eq (ai-auto-complete-task-plan-status plan) 'pending)
              (setf (ai-auto-complete-task-plan-status plan) 'in-progress))

            ;; Check for placeholder content before executing
            (let ((placeholder-warning (ai-auto-complete-check-for-placeholders tool-params)))
              (if placeholder-warning
                  (progn
                    (setq success nil)
                    (setq tool-result (format "ERROR: %s\n\nPlease update the task plan with actual values instead of placeholders. Use update_task_plan to provide real content." placeholder-warning)))

                ;; Execute the tool
                (condition-case err
                    (setq tool-result (ai-auto-complete-execute-tool tool-name tool-params))
                  (error
                   (setq success nil)
                   (setq tool-result (format "ERROR: Failed to execute tool %s: %s"
                                             tool-name
                                             (error-message-string err)))))))

            ;; Update the sub-task with the result
            (setf (ai-auto-complete-sub-task-result sub-task) tool-result)
            (setf (ai-auto-complete-sub-task-status sub-task) (if success 'completed 'failed))

            ;; Increment the current step index
            (setf (ai-auto-complete-task-plan-current-step-index plan) (1+ current-index))

            ;; Check if this was the last step
            (let ((next-index (1+ current-index))
                  (next-step nil)
                  (next-step-desc ""))

              (if (>= next-index (length sub-tasks))
                  ;; This was the last step
                  (progn
                    (setf (ai-auto-complete-task-plan-status plan) 'completed)
                    (format "Step %d '%s' completed. Result: %s\n\nAll steps in plan %s completed. The task '%s' should be complete."
                            (1+ current-index)
                            (ai-auto-complete-sub-task-description sub-task)
                            tool-result
                            plan-id
                            (ai-auto-complete-task-plan-original-request plan)))

                ;; There are more steps
                (progn
                  (setq next-step (nth next-index sub-tasks))
                  (setq next-step-desc (ai-auto-complete-sub-task-description next-step))
                  (format "Step %d '%s' completed. Result: %s\n\nNext step (%d) is: '%s'. To proceed, call 'execute_planned_step' with plan_id: %s. To modify the plan, call 'update_task_plan'."
                          (1+ current-index)
                          (ai-auto-complete-sub-task-description sub-task)
                          tool-result
                          (1+ next-index)
                          next-step-desc
                          plan-id))))))))))

;; Update an existing task plan
(defun ai-auto-complete-update-task-plan (plan-id updated-steps)
  "Update the task plan with PLAN-ID using UPDATED-STEPS."
  (let ((plan (ai-auto-complete-get-task-plan plan-id)))
    (if (not plan)
        (format "ERROR: Task plan with ID %s not found" plan-id)

      ;; Create new sub-tasks from the updated steps
      (let ((new-sub-tasks nil)
            (current-index (ai-auto-complete-task-plan-current-step-index plan)))

        ;; Create sub-tasks from the provided steps
        (dolist (step updated-steps)
          (let* ((step-id (ai-auto-complete-generate-unique-id "step"))
                 (description (cdr (assoc 'description step)))
                 (tool-name (cdr (assoc 'tool_name step)))
                 (tool-params-raw (cdr (assoc 'tool_params step)))
                 ;; Convert tool_params from vector to alist if necessary
                 (tool-params (cond
                               ((vectorp tool-params-raw)
                                ;; Convert vector of key-value pairs to alist
                                (let ((result nil))
                                  (dotimes (i (length tool-params-raw))
                                    (let ((pair (aref tool-params-raw i)))
                                      (when (consp pair)
                                        (push pair result))))
                                  (nreverse result)))
                               ((listp tool-params-raw) tool-params-raw)  ; Already an alist
                               (t tool-params-raw)))                      ; Keep as-is for other types
                 (sub-task (ai-auto-complete-sub-task-create
                            :id step-id
                            :description description
                            :tool-name tool-name
                            :tool-params tool-params
                            :status 'pending
                            :result nil)))
            (push sub-task new-sub-tasks)))

        ;; Update the plan with the new sub-tasks
        (setf (ai-auto-complete-task-plan-sub-tasks plan) (nreverse new-sub-tasks))

        ;; Adjust current step index if needed
        (when (>= current-index (length new-sub-tasks))
          (setf (ai-auto-complete-task-plan-current-step-index plan) 0))

        ;; Automatically execute all updated steps
        (ai-auto-complete-execute-all-steps plan-id)))))

;; Get the status of a task plan
(defun ai-auto-complete-get-task-plan-status (plan-id)
  "Get the status of the task plan with PLAN-ID."
  (let ((plan (ai-auto-complete-get-task-plan plan-id)))
    (if (not plan)
        (format "ERROR: Task plan with ID %s not found" plan-id)

      (let* ((status (ai-auto-complete-task-plan-status plan))
             (current-index (ai-auto-complete-task-plan-current-step-index plan))
             (sub-tasks (ai-auto-complete-task-plan-sub-tasks plan))
             (total-steps (length sub-tasks))
             (completed-steps (cl-count-if
                               (lambda (task)
                                 (eq (ai-auto-complete-sub-task-status task) 'completed))
                               sub-tasks))
             (failed-steps (cl-count-if
                            (lambda (task)
                              (eq (ai-auto-complete-sub-task-status task) 'failed))
                            sub-tasks))
             (current-step (when (< current-index total-steps)
                             (nth current-index sub-tasks)))
             (current-step-desc (if current-step
                                   (ai-auto-complete-sub-task-description current-step)
                                 "No current step")))

        (format "Task Plan: %s\nOriginal Request: %s\nStatus: %s\nProgress: %d/%d steps completed, %d failed\nCurrent Step: %s"
                plan-id
                (ai-auto-complete-task-plan-original-request plan)
                status
                completed-steps
                total-steps
                failed-steps
                (if (>= current-index total-steps)
                    "All steps completed"
                  (format "Step %d - %s" (1+ current-index) current-step-desc)))))))

;; Execute all steps in a task plan automatically
(defun ai-auto-complete-execute-all-steps (plan-id)
  "Execute all steps in the task plan with PLAN-ID and return comprehensive results."
  (let ((plan (ai-auto-complete-get-task-plan plan-id)))
    (if (not plan)
        (format "ERROR: Task plan with ID %s not found" plan-id)

      (let* ((sub-tasks (ai-auto-complete-task-plan-sub-tasks plan))
             (total-steps (length sub-tasks))
             (results nil)
             (step-number 1)
             (all-successful t))

        ;; Execute each step in sequence
        (dolist (sub-task sub-tasks)
          (let* ((description (ai-auto-complete-sub-task-description sub-task))
                 (tool-name (ai-auto-complete-sub-task-tool-name sub-task))
                 (tool-params (ai-auto-complete-sub-task-tool-params sub-task))
                 (success t)
                 (tool-result nil))

            ;; Check for placeholder content before executing
            (let ((placeholder-warning (ai-auto-complete-check-for-placeholders tool-params)))
              (if placeholder-warning
                  (progn
                    (setq success nil)
                    (setq all-successful nil)
                    (setq tool-result (format "ERROR: %s\n\nPlease update the task plan with actual values instead of placeholders." placeholder-warning)))

                ;; Execute the tool
                (condition-case err
                    (setq tool-result (ai-auto-complete-execute-tool tool-name tool-params))
                  (error
                   (setq success nil)
                   (setq all-successful nil)
                   (setq tool-result (format "ERROR: Failed to execute tool %s: %s"
                                             tool-name
                                             (error-message-string err)))))))

            ;; Update sub-task status and result
            (setf (ai-auto-complete-sub-task-status sub-task) (if success 'completed 'failed))
            (setf (ai-auto-complete-sub-task-result sub-task) tool-result)

            ;; Add result to our collection
            (push (format "Step %d '%s' - %s: %s"
                          step-number
                          description
                          (if success "SUCCESS" "FAILED")
                          tool-result)
                  results)

            ;; If this step failed, stop execution
            (when (not success)
              (setf (ai-auto-complete-task-plan-status plan) 'failed)
              (setf (ai-auto-complete-task-plan-current-step-index plan) (1- step-number))
              (return))

            (setq step-number (1+ step-number))))

        ;; Update plan status
        (when all-successful
          (setf (ai-auto-complete-task-plan-status plan) 'completed)
          (setf (ai-auto-complete-task-plan-current-step-index plan) total-steps))

        ;; Format and return results
        (let ((results-text (mapconcat 'identity (nreverse results) "\n")))
          (if all-successful
              (format "Task plan %s created and executed. Results:\n%s\nAll steps completed successfully."
                      plan-id results-text)
            (format "Task plan %s created and executed. Results:\n%s\nExecution stopped due to failure."
                    plan-id results-text)))))))

;; Tool function for creating a task plan (now auto-executes)
(defun ai-auto-complete-tool-create-task-plan (params)
  "Create a new task plan with the parameters in PARAMS and automatically execute all steps."
  (let ((task-description (cdr (assoc 'task_description params)))
        (steps-raw (cdr (assoc 'steps params)))
        (notes (cdr (assoc 'notes params)))
        (agent-name (cdr (assoc 'agent_name params))))

    ;; Convert steps from vector to list if necessary
    (let ((steps (cond
                  ((vectorp steps-raw) (append steps-raw nil))  ; Convert vector to list
                  ((listp steps-raw) steps-raw)                 ; Already a list
                  (t nil))))                                    ; Invalid type

      (cond
       ((not task-description)
        "ERROR: No task_description specified")
       ((not steps)
        "ERROR: No steps specified or steps is not a valid list/array")
       ((not agent-name)
        "ERROR: No agent_name specified")
       (t
        (let* ((result (ai-auto-complete-create-task-plan task-description steps agent-name notes))
               (plan-id (car result)))
          ;; Automatically execute all steps
          (ai-auto-complete-execute-all-steps plan-id)))))))

;; Tool function for executing a planned step
(defun ai-auto-complete-tool-execute-planned-step (params)
  "Execute the current step of a task plan with the parameters in PARAMS."
  (let ((plan-id (cdr (assoc 'plan_id params))))
    (if (not plan-id)
        "ERROR: No plan_id specified"
      (ai-auto-complete-execute-planned-step plan-id))))

;; Tool function for updating a task plan
(defun ai-auto-complete-tool-update-task-plan (params)
  "Update a task plan with the parameters in PARAMS."
  (let ((plan-id (cdr (assoc 'plan_id params)))
        (updated-steps-raw (cdr (assoc 'updated_steps params))))

    ;; Convert updated_steps from vector to list if necessary
    (let ((updated-steps (cond
                          ((vectorp updated-steps-raw) (append updated-steps-raw nil))  ; Convert vector to list
                          ((listp updated-steps-raw) updated-steps-raw)                 ; Already a list
                          (t nil))))                                                    ; Invalid type

      (cond
       ((not plan-id)
        "ERROR: No plan_id specified")
       ((not updated-steps)
        "ERROR: No updated_steps specified or updated_steps is not a valid list/array")
       (t
        (ai-auto-complete-update-task-plan plan-id updated-steps))))))

;; Tool function for getting the status of a task plan
(defun ai-auto-complete-tool-get-task-plan-status (params)
  "Get the status of a task plan with the parameters in PARAMS."
  (let ((plan-id (cdr (assoc 'plan_id params))))
    (if (not plan-id)
        "ERROR: No plan_id specified"
      (ai-auto-complete-get-task-plan-status plan-id))))

;; Register task management tools
(defun ai-auto-complete-register-task-management-tools ()
  "Register all task management tools with the AI auto-complete system."
  (ai-auto-complete-register-tool
   "create_task_plan"
   "Create a multi-step task plan for complex tasks and automatically execute all steps. Returns complete results from all executed steps."
   #'ai-auto-complete-tool-create-task-plan
   '(("task_description" . "The overall goal or task description")
     ("steps" . "A list of steps, each with description, tool_name, and tool_params")
     ("notes" . "Optional notes about the plan")
     ("agent_name" . "Name of the agent creating the plan")))

  (ai-auto-complete-register-tool
   "execute_planned_step"
   "Execute the current step in a task plan. Returns the result and information about the next step."
   #'ai-auto-complete-tool-execute-planned-step
   '(("plan_id" . "The ID of the plan to execute a step from")))

  (ai-auto-complete-register-tool
   "update_task_plan"
   "Update an existing task plan with new or modified steps and automatically execute all updated steps."
   #'ai-auto-complete-tool-update-task-plan
   '(("plan_id" . "The ID of the plan to update")
     ("updated_steps" . "A list of updated steps, each with description, tool_name, and tool_params")))

  (ai-auto-complete-register-tool
   "get_task_plan_status"
   "Get the current status and progress of a task plan."
   #'ai-auto-complete-tool-get-task-plan-status
   '(("plan_id" . "The ID of the plan to get status for"))))

;; Register task management tools when this module is loaded
(ai-auto-complete-register-task-management-tools)

(provide 'tools/task-management)
;;; task-management.el ends here
