;;; tools-ui.el --- UI components for tools in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides UI components for tools in the AI Auto Complete package.

;;; Code:

(require 'tools/tools-core)
(require 'tools/advanced-tools)

;; Customization group for tools
(defgroup ai-auto-complete-tools nil
  "Settings for tools in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-tools-")

;; Debug options for tools
(defcustom ai-auto-complete-tools-debug-mode nil
  "Enable detailed debugging for tool calls.
When enabled, additional information about tool calls, execution,
and responses will be logged to the *Messages* buffer."
  :type 'boolean
  :group 'ai-auto-complete-tools)

;; Toggle tools
(defun ai-auto-complete-tools-toggle ()
  "Toggle tool use in AI Auto Complete."
  (interactive)
  (setq ai-auto-complete-tools-enabled (not ai-auto-complete-tools-enabled))
  (message "AI Auto Complete tools %s" (if ai-auto-complete-tools-enabled "enabled" "disabled")))

;; Toggle debug mode
(defun ai-auto-complete-tools-toggle-debug ()
  "Toggle debug mode for tools in AI Auto Complete."
  (interactive)
  (setq ai-auto-complete-tools-debug-mode (not ai-auto-complete-tools-debug-mode))
  (message "AI Auto Complete tools debug mode %s"
           (if ai-auto-complete-tools-debug-mode "enabled" "disabled")))

;; List available tools
(defun ai-auto-complete-tools-list ()
  "List available tools."
  (interactive)
  (with-output-to-temp-buffer "*AI Auto Complete Tools*"
    (princ "Available AI Auto Complete Tools:\n\n")
    (princ "The AI Auto Complete system supports multiple tool calls in a single response.\n")
    (princ "LLMs can make multiple tool calls, and the application will execute all of them\n")
    (princ "and return all results to the LLM for further processing.\n\n")
    (princ "Tools are called using XML syntax:\n")
    (princ "<tool name=\"tool_name\">\n")
    (princ "<parameters>\n")
    (princ "{\"param1\": \"value1\", \"param2\": \"value2\"}\n")
    (princ "</parameters>\n")
    (princ "</tool>\n\n")
    (princ "Multiple tools can be called in sequence:\n")
    (princ "<tool name=\"tool1\">...</tool>\n")
    (princ "<tool name=\"tool2\">...</tool>\n")
    (princ "<tool name=\"tool3\">...</tool>\n\n")
    (princ "Available Tools:\n")
    (princ "----------------\n\n")
    (maphash (lambda (name tool)
               (let ((description (plist-get tool :description))
                     (parameters (plist-get tool :parameters)))
                 (princ (format "- %s: %s\n" name description))
                 (princ "  Parameters:\n")
                 (dolist (param parameters)
                   (princ (format "    - %s: %s\n" (car param) (cdr param))))
                 (princ "\n")))
             ai-auto-complete-tools)))

;; Test tools
(defun ai-auto-complete-tools-test-search-files ()
  "Test the search_files tool interactively."
  (interactive)
  (let* ((path (read-directory-name "Directory to search: "))
         (regex (read-string "Regex to search for: "))
         (file-pattern (read-string "File pattern (optional): ")))
    (let ((params `((path . ,path)
                   (regex . ,regex))))
      (when (not (string-empty-p file-pattern))
        (setq params (append params `((file_pattern . ,file-pattern)))))
      (with-output-to-temp-buffer "*AI Auto Complete Tool Test*"
        (princ "Search Files Tool Test Results:\n\n")
        (princ (ai-auto-complete-tool-search-files params))))))

(defun ai-auto-complete-tools-test-list-files ()
  "Test the list_files tool interactively."
  (interactive)
  (let* ((path (read-directory-name "Directory to list: "))
         (recursive (y-or-n-p "List recursively? ")))
    (let ((params `((path . ,path)
                   (recursive . ,recursive))))
      (with-output-to-temp-buffer "*AI Auto Complete Tool Test*"
        (princ "List Files Tool Test Results:\n\n")
        (princ (ai-auto-complete-tool-list-files params))))))

(defun ai-auto-complete-tools-test-list-code-definitions ()
  "Test the list_code_definition_names tool interactively."
  (interactive)
  (let* ((path (read-directory-name "Directory to analyze: ")))
    (let ((params `((path . ,path))))
      (with-output-to-temp-buffer "*AI Auto Complete Tool Test*"
        (princ "List Code Definitions Tool Test Results:\n\n")
        (princ (ai-auto-complete-tool-list-code-definitions params))))))

(defun ai-auto-complete-tools-test-apply-diff ()
  "Test the apply_diff tool interactively."
  (interactive)
  (let* ((path (read-file-name "File to modify: "))
         (start-line (read-number "Start line: " 1))
         (end-line (read-number "End line: " 10))
         (diff (read-string "Diff (format: <<<<<<< SEARCH\\n[content]\\n=======\\n[new content]\\n>>>>>>> REPLACE): ")))
    (let ((params `((path . ,path)
                   (start_line . ,start-line)
                   (end_line . ,end-line)
                   (diff . ,diff))))
      (with-output-to-temp-buffer "*AI Auto Complete Tool Test*"
        (princ "Apply Diff Tool Test Results:\n\n")
        (princ (ai-auto-complete-tool-apply-diff params))))))

;; Menu integration
(defun ai-auto-complete-add-tools-menu-items ()
  "Add tools menu items to the AI Auto Complete menu."
  (when (and (boundp 'ai-auto-complete-menu) ai-auto-complete-menu)
    (easy-menu-add-item ai-auto-complete-menu nil
                       '("Tools"
                         ["Enable Tools" ai-auto-complete-tools-toggle
                          :style toggle :selected ai-auto-complete-tools-enabled]
                         ["Enable Debug Mode" ai-auto-complete-tools-toggle-debug
                          :style toggle :selected ai-auto-complete-tools-debug-mode]
                         ["List Available Tools" ai-auto-complete-tools-list t]
                         "---"
                         ["Test Tool Recursion" ai-auto-complete-tools-test-recursion t]
                         ["Test Multiple Tool Calls" ai-auto-complete-tools-test-multiple t]
                         ["Test Linux Tools" ai-auto-complete-linux-tools-test t]
                         ["Test Terminal Tools" ai-auto-complete-terminal-tools-test t]
                         ["Test Command Tools" ai-auto-complete-command-tools-test t]
                         ["Test History Tools" ai-auto-complete-history-tools-test t]
                         ["Test Custom Tools" ai-auto-complete-custom-tools-test t]
                         ["Test Safety Tools" ai-auto-complete-safety-tools-test t]
                         ["Test Python Tools" ai-auto-complete-python-tools-test t]
                         "---"
                         ["Test Search Files" ai-auto-complete-tools-test-search-files t]
                         ["Test List Files" ai-auto-complete-tools-test-list-files t]
                         ["Test List Code Definitions" ai-auto-complete-tools-test-list-code-definitions t]
                         ["Test Apply Diff" ai-auto-complete-tools-test-apply-diff t])
                       "---")))

(provide 'tools/tools-ui)
;;; tools-ui.el ends here
