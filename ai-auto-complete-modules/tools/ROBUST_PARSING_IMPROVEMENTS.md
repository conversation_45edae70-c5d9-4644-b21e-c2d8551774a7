# Robust Tool Parsing Improvements

## Problem

The original `ai-auto-complete-tools-parse-response` function was too strict and would fail completely when encountering malformed XML tags from LLM responses. This caused tool calls to be completely ignored when:

- Missing closing `</parameters>` tags
- Missing closing `</tool>` tags  
- Missing opening `<parameters>` tags
- Malformed JSON content

## Example of the Issue

From the logs:
```
[TOOLS-DEBUG] Found tool call #1: update_task_plan
[TOOLS-DEBUG] ERROR: Missing </parameters> tag for tool update_task_plan
[TOOLS-DEBUG] ERROR: Missing </tool> tag for tool update_task_plan
[TOOLS-DEBUG] Parsed 0 tool calls from response
```

The function found the tool call but rejected it due to missing closing tags, resulting in 0 parsed tool calls.

## Solution

Made the parsing function **robust and fault-tolerant** by:

### 1. **Graceful Handling of Missing Tags**
- **Missing `</parameters>`**: Extract content until `</tool>` or next tool
- **Missing `</tool>`**: Extract content until next tool or end of response
- **Missing `<parameters>`**: Look for JSON-like content directly in tool body
- **Missing both**: Use fallback extraction methods

### 2. **Smart Content Extraction**
```elisp
(params-content 
 (cond
  ;; Best case: both parameters tags are present
  ((and params-content-start params-end)
   (substring response params-content-start params-end))
  ;; Missing closing tag: extract until tool end or next tool
  ((and params-content-start (not params-end))
   (let ((extract-end (or tool-end next-tool-start (length response))))
     (substring response params-content-start extract-end)))
  ;; Missing opening tag: try to find JSON-like content
  ((not params-start)
   (let ((tool-content (substring response tool-start content-end)))
     (when (string-match "\\s-*\\([{[].*\\)" tool-content)
       (match-string 1 tool-content))))
  ;; Fallback: empty parameters
  (t "")))
```

### 3. **Fallback Parameter Parsing**
When JSON parsing fails, try to extract simple key-value pairs:
```elisp
(defun ai-auto-complete-tools-parse-simple-params (content)
  "Fallback function to parse simple parameter formats when JSON parsing fails."
  ;; Try to find simple key: value patterns
  ;; Try to find quoted strings as values
  ;; Return best-effort parameter extraction
```

### 4. **Always Return Tool Calls**
The function now **always** adds tool calls to the result, even if parameter parsing fails:
```elisp
;; Always add the tool call, even if parameters parsing failed
(push (cons tool-name (or params '())) tool-calls)
```

### 5. **Better Debug Messages**
Changed from "ERROR" to "WARNING" for missing tags since they're now handled gracefully:
```elisp
(message "[TOOLS-DEBUG] WARNING: Missing </parameters> tag for tool %s, will try to extract anyway" tool-name)
```

## Test Function

Added `ai-auto-complete-tools-test-robust-parsing()` to test various malformed XML scenarios:

```elisp
M-x ai-auto-complete-tools-test-robust-parsing
```

This tests:
1. Missing `</parameters>` tag
2. Missing `</tool>` tag  
3. Missing both closing tags
4. Missing `<parameters>` tag
5. Multiple tools with mixed issues
6. Simple key:value format
7. Completely malformed content with quoted strings

## Results

### Before (Strict Parsing)
```
Input: <tool name="update_task_plan"><parameters>{"task_id": "123"}</tool>
Result: []  # 0 tool calls - completely rejected
```

### After (Robust Parsing)
```
Input: <tool name="update_task_plan"><parameters>{"task_id": "123"}</tool>
Result: [("update_task_plan" . ((task_id . "123")))]  # 1 tool call - successfully extracted
```

## Benefits

1. **Higher Success Rate**: Tool calls are no longer lost due to minor XML formatting issues
2. **Better User Experience**: LLM responses with malformed XML still trigger tool execution
3. **Fault Tolerance**: System continues working even with imperfect LLM output
4. **Backward Compatible**: Perfect XML still works exactly as before
5. **Graceful Degradation**: Even completely malformed content attempts parameter extraction

## Edge Cases Handled

- **Truncated responses**: When LLM response is cut off mid-tool
- **Multiple tools**: Correctly handles boundaries between multiple tool calls
- **Nested content**: Handles JSON within XML within text
- **Alternative formats**: Supports simple key:value parameter format
- **Empty parameters**: Gracefully handles tools with no parameters

## Usage

The function is automatically used by the tool processing system. No changes needed to existing code - it's a drop-in replacement that's more robust.

To test the improvements:
```elisp
M-x ai-auto-complete-tools-test-robust-parsing
```

This will show how the function handles various malformed XML scenarios and successfully extracts tool calls that would have been lost with the previous strict parsing.
