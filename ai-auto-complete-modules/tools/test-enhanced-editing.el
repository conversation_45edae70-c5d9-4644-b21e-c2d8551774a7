;;; test-enhanced-editing.el --- Tests for enhanced editing tools -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides tests for the enhanced editing tools to ensure they work correctly.

;;; Code:

(require 'tools/enhanced-editing-tools)

;; Create a test file for our experiments
(defun create-test-file ()
  "Create a test file for editing experiments."
  (let ((test-content "#!/usr/bin/env python3
# Test file for enhanced editing tools

def hello_world():
    print(\"Hello, World!\")

def add_numbers(a, b):
    return a + b

def main():
    hello_world()
    result = add_numbers(5, 3)
    print(f\"Result: {result}\")

if __name__ == \"__main__\":
    main()
"))
    (with-temp-file "/tmp/test_editing.py"
      (insert test-content))
    (message "Created test file: /tmp/test_editing.py")))

;; Test functions
(defun test-read-file-with-lines ()
  "Test reading file with line numbers."
  (interactive)
  (create-test-file)
  (let ((result (ai-auto-complete-tool-read-file-with-lines '((path . "/tmp/test_editing.py")))))
    (message "Read file with lines result:\n%s" result)))

(defun test-insert-lines ()
  "Test inserting lines."
  (interactive)
  (create-test-file)
  (let ((result (ai-auto-complete-tool-insert-lines 
                 '((path . "/tmp/test_editing.py")
                   (line_number . 2)
                   (content . "# This is a new comment\n# Added by insert_lines tool")))))
    (message "Insert lines result: %s" result)
    ;; Show the result
    (ai-auto-complete-tool-read-file-with-lines '((path . "/tmp/test_editing.py")))))

(defun test-replace-lines ()
  "Test replacing lines."
  (interactive)
  (create-test-file)
  (let ((result (ai-auto-complete-tool-replace-lines
                 '((path . "/tmp/test_editing.py")
                   (start_line . 4)
                   (end_line . 5)
                   (content . "def improved_hello():\n    \"\"\"An improved hello function.\"\"\"\n    print(\"Hello, improved world!\")")))))
    (message "Replace lines result: %s" result)
    ;; Show the result
    (ai-auto-complete-tool-read-file-with-lines '((path . "/tmp/test_editing.py")))))

(defun test-insert-after-pattern ()
  "Test inserting after pattern."
  (interactive)
  (create-test-file)
  (let ((result (ai-auto-complete-tool-insert-after-pattern
                 '((path . "/tmp/test_editing.py")
                   (pattern . "def hello_world\\(\\):")
                   (content . "    \"\"\"Prints a greeting message.\"\"\"")))))
    (message "Insert after pattern result: %s" result)
    ;; Show the result
    (ai-auto-complete-tool-read-file-with-lines '((path . "/tmp/test_editing.py")))))

(defun test-edit-function ()
  "Test editing a function."
  (interactive)
  (create-test-file)
  (let ((result (ai-auto-complete-tool-edit-function
                 '((path . "/tmp/test_editing.py")
                   (function_name . "add_numbers")
                   (content . "def add_numbers(a, b):\n    \"\"\"Add two numbers and return the result.\"\"\"\n    result = a + b\n    print(f\"Adding {a} + {b} = {result}\")\n    return result")))))
    (message "Edit function result: %s" result)
    ;; Show the result
    (ai-auto-complete-tool-read-file-with-lines '((path . "/tmp/test_editing.py")))))

(defun test-multiple-edits ()
  "Test applying multiple edits atomically."
  (interactive)
  (create-test-file)
  (let ((result (ai-auto-complete-tool-apply-multiple-edits
                 '((path . "/tmp/test_editing.py")
                   (edits . (((type . "insert_lines")
                             (line_number . 1)
                             (content . "# Enhanced with multiple edits"))
                            ((type . "insert_after_pattern")
                             (pattern . "def hello_world")
                             (content . "    \"\"\"Docstring added by multiple edits.\"\"\""))
                            ((type . "replace_pattern")
                             (pattern . "print\\(\"Hello, World!\"\\)")
                             (content . "    print(\"Hello, Enhanced World!\")"))))))))
    (message "Multiple edits result: %s" result)
    ;; Show the result
    (ai-auto-complete-tool-read-file-with-lines '((path . "/tmp/test_editing.py")))))

(defun run-all-tests ()
  "Run all enhanced editing tool tests."
  (interactive)
  (message "=== Testing Enhanced Editing Tools ===")
  
  (message "\n1. Testing read_file_with_lines...")
  (test-read-file-with-lines)
  
  (message "\n2. Testing insert_lines...")
  (test-insert-lines)
  
  (message "\n3. Testing replace_lines...")
  (test-replace-lines)
  
  (message "\n4. Testing insert_after_pattern...")
  (test-insert-after-pattern)
  
  (message "\n5. Testing edit_function...")
  (test-edit-function)
  
  (message "\n6. Testing multiple_edits...")
  (test-multiple-edits)
  
  (message "\n=== All tests completed ==="))

;; Interactive test menu
(defun enhanced-editing-test-menu ()
  "Show a menu for testing enhanced editing tools."
  (interactive)
  (let ((choice (read-char-choice 
                 "Enhanced Editing Tests:
1. Read file with lines
2. Insert lines
3. Replace lines  
4. Insert after pattern
5. Edit function
6. Multiple edits
7. Run all tests
q. Quit

Choose test: " '(?1 ?2 ?3 ?4 ?5 ?6 ?7 ?q))))
    (cond
     ((eq choice ?1) (test-read-file-with-lines))
     ((eq choice ?2) (test-insert-lines))
     ((eq choice ?3) (test-replace-lines))
     ((eq choice ?4) (test-insert-after-pattern))
     ((eq choice ?5) (test-edit-function))
     ((eq choice ?6) (test-multiple-edits))
     ((eq choice ?7) (run-all-tests))
     ((eq choice ?q) (message "Exiting test menu"))
     (t (message "Invalid choice")))))

(provide 'tools/test-enhanced-editing)
;;; test-enhanced-editing.el ends here
