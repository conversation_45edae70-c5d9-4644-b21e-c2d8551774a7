;;; linux-tools.el --- Enhanced Linux tools for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides enhanced Linux tools for the AI Auto Complete package.
;; These tools give the LLM more powerful access to the Linux environment.

;;; Code:

;; Try to load tools-core, but handle failure gracefully
(condition-case err
    (require 'tools/tools-core)
  (error
   (message "Warning: Failed to load tools/tools-core: %s" (error-message-string err))
   (load (expand-file-name "tools-core.el"
                          (file-name-directory (or load-file-name buffer-file-name))))))
(require 'cl-lib)

;; Variables for process management
(defvar ai-auto-complete-linux-processes (make-hash-table :test 'equal)
  "Hash table of running processes, mapping process IDs to process objects.")

(defvar ai-auto-complete-linux-process-counter 0
  "Counter for generating unique process IDs.")

(defvar ai-auto-complete-linux-process-output-limit 10000
  "Maximum number of characters to return from process output.")

(defcustom ai-auto-complete-linux-safe-commands t
  "Whether to restrict commands to a safe subset.
When enabled, potentially dangerous commands will be blocked."
  :type 'boolean
  :group 'ai-auto-complete-tools)

(defcustom ai-auto-complete-linux-dangerous-commands
  '("rm" "rmdir" "mkfs" "dd" "mv" "chown" "chmod" "kill" "pkill" "shutdown" "reboot" "halt" "poweroff" "init" "telinit")
  "List of dangerous commands that should be restricted when safe mode is enabled."
  :type '(repeat string)
  :group 'ai-auto-complete-tools)

;; System environment information tool
;; Example 1: Get standard system information (default behavior)
;; (ai-auto-complete-tool-get-system-info nil)
;; Example 2: Get full system information, including environment variables
;; (ai-auto-complete-tool-get-system-info '((detail_level . "full") (include_env . t)))
;; Example 3: Get basic system information, without environment variables
;; (ai-auto-complete-tool-get-system-info '((detail_level . "basic") (include_env . nil)))
(defun ai-auto-complete-tool-get-system-info (params)
  "Get comprehensive system information.
PARAMS may contain optional keys:
- 'detail_level': 'basic', 'standard', or 'full' (default: 'standard')
- 'include_env': Whether to include environment variables (t or nil, default: nil)"
  (message "DEBUG: get_system_info called with params: %S" params)
  (condition-case err
      (let* ((detail-level (or (cdr (assoc 'detail_level params)) "standard"))
             (include-env (cdr (assoc 'include_env params)))
             (info-commands
              (cond
               ((string= detail-level "basic")
                (message "DEBUG: get_system_info - Using basic detail level")
                '(("OS Information" . "uname -a")
                  ("Current Directory" . "pwd")
                  ("Disk Space" . "df -h")))
               ((string= detail-level "full")
                (message "DEBUG: get_system_info - Using full detail level")
                '(("OS Information" . "uname -a")
                  ("OS Release" . "cat /etc/os-release 2>/dev/null || echo 'OS release information not available'")
                  ("Kernel Information" . "uname -r")
                  ("Current Directory" . "pwd")
                  ("Current User" . "whoami")
                  ("Host Information" . "hostname")
                  ("CPU Information" . "lscpu | grep -E 'Model name|^CPU\\(s\\)|Architecture'")
                  ("Memory Information" . "free -h")
                  ("Disk Space" . "df -h")
                  ("Disk Usage" . "du -sh . 2>/dev/null || echo 'Disk usage information not available'")
                  ("Network Interfaces" . "ip addr show 2>/dev/null || ifconfig 2>/dev/null || echo 'Network information not available'")
                  ("Installed Packages" . "dpkg -l | wc -l 2>/dev/null || rpm -qa | wc -l 2>/dev/null || echo 'Package information not available'")
                  ("System Uptime" . "uptime")
                  ("Running Processes" . "ps aux | wc -l")))
               (t  ; standard (default)
                (message "DEBUG: get_system_info - Using standard detail level")
                '(("OS Information" . "uname -a")
                  ("Current Directory" . "pwd")
                  ("Current User" . "whoami")
                  ("Memory Information" . "free -h")
                  ("Disk Space" . "df -h")
                  ("System Uptime" . "uptime")))))
             (result "# System Information\n\n"))

        (message "DEBUG: get_system_info - Processing %d commands" (length info-commands))

        ;; Run each command and append the output
        (dolist (cmd-pair info-commands)
          (condition-case cmd-err
              (let* ((label (car cmd-pair))
                     (cmd (cdr cmd-pair))
                     (output ""))

                (message "DEBUG: get_system_info - Running command: %s" cmd)
                (condition-case exec-err
                    (setq output (shell-command-to-string cmd))
                  (error
                   (let ((error-msg (format "Error executing command '%s': %s"
                                           cmd (error-message-string exec-err))))
                     (message "DEBUG: get_system_info - %s" error-msg)
                     (setq output (concat "ERROR: " error-msg)))))

                (message "DEBUG: get_system_info - Command output length: %d" (length output))
                (setq result (concat result
                                     "## " label "\n\n"
                                     "```\n"
                                     (condition-case trim-err
                                         (string-trim output)
                                       (error
                                        (message "DEBUG: get_system_info - Error trimming output: %s"
                                                 (error-message-string trim-err))
                                        output))
                                     "\n```\n\n")))
            (error
             (let ((error-msg (format "Error processing command pair: %s"
                                     (error-message-string cmd-err))))
               (message "DEBUG: get_system_info - %s" error-msg)
               (setq result (concat result
                                    "## Error\n\n"
                                    "```\n"
                                    error-msg
                                    "\n```\n\n"))))))

        ;; Add environment variables if requested
        (when include-env
          (message "DEBUG: get_system_info - Including environment variables")
          (condition-case env-err
              (progn
                (setq result (concat result "## Environment Variables\n\n```\n"))
                (let ((env-output ""))
                  (condition-case exec-err
                      (setq env-output (shell-command-to-string "env | sort"))
                    (error
                     (let ((error-msg (format "Error getting environment variables: %s"
                                             (error-message-string exec-err))))
                       (message "DEBUG: get_system_info - %s" error-msg)
                       (setq env-output (concat "ERROR: " error-msg)))))

                  (setq result (concat result env-output "```\n\n"))))
            (error
             (let ((error-msg (format "Error processing environment variables: %s"
                                     (error-message-string env-err))))
               (message "DEBUG: get_system_info - %s" error-msg)
               (setq result (concat result
                                    "## Environment Variables Error\n\n"
                                    "```\n"
                                    error-msg
                                    "\n```\n\n")))))

          ;; Add Emacs-specific information
          (message "DEBUG: get_system_info - Adding Emacs information")
          (condition-case emacs-err
              (setq result (concat result "## Emacs Information\n\n"
                                   "- Emacs Version: " (emacs-version) "\n"
                                   "- Default Directory: " default-directory "\n"
                                   "- Major Mode: " (symbol-name major-mode) "\n"))
            (error
             (let ((error-msg (format "Error getting Emacs information: %s"
                                     (error-message-string emacs-err))))
               (message "DEBUG: get_system_info - %s" error-msg)
               (setq result (concat result
                                    "## Emacs Information Error\n\n"
                                    "```\n"
                                    error-msg
                                    "\n```\n\n"))))))

        ;; Return the formatted result
        (message "DEBUG: get_system_info - Returning result of length: %d" (length result))
        result)

    (error
     (let ((error-msg (format "ERROR: Failed to get system information: %s" (error-message-string err))))
       (message "DEBUG: get_system_info - %s" error-msg)
       (concat "# System Information Error\n\n" error-msg)))))

;; Enhanced command execution
;; Example 1: Run a simple command with default timeout and directory
;; (ai-auto-complete-tool-run-command-enhanced '((command . "ls -l")))
;; Example 2: Run a command with a specific timeout and in a different directory
;; (ai-auto-complete-tool-run-command-enhanced
;;  '((command . "grep -r 'TODO' .")
;;    (timeout . 10) (directory . "/home/<USER>/projects/my-project")))
(defun ai-auto-complete-tool-run-command-enhanced (params)
  "Run a shell command with enhanced output and error handling.
PARAMS should be an alist with:
- 'command': The command to run
- 'timeout': Optional timeout in seconds (default: 30)
- 'directory': Optional working directory (default: current directory)"
  (message "DEBUG: run_command_enhanced called with params: %S" params)
  (condition-case err
      (let* ((command (cdr (assoc 'command params)))
             (timeout (or (cdr (assoc 'timeout params)) 30))
             (directory (or (cdr (assoc 'directory params)) default-directory))
             (result "# Command Execution Result\n\n"))

        ;; Check if command is provided
        (if (not command)
            (let ((error-msg "ERROR: No command specified"))
              (message "DEBUG: run_command_enhanced - %s" error-msg)
              error-msg)

          (message "DEBUG: run_command_enhanced - Command: %s, Timeout: %d, Directory: %s"
                   command timeout directory)

          ;; Check if command is safe
          (if (and (boundp 'ai-auto-complete-linux-safe-commands)
                   ai-auto-complete-linux-safe-commands
                   (fboundp 'ai-auto-complete-linux-is-dangerous-command)
                   (ai-auto-complete-linux-is-dangerous-command command))
              (let ((error-msg (format "ERROR: Command '%s' is potentially dangerous and has been blocked. If you need to run this command, ask the user to disable safe mode." command)))
                (message "DEBUG: run_command_enhanced - %s" error-msg)
                error-msg)

            ;; Run the command
            (condition-case exec-err
                (let* ((start-time (float-time))
                       (output "")
                       (exit-code 0)
                       (default-directory directory))

                  (message "DEBUG: run_command_enhanced - Executing command")

                  ;; Run the command with timeout
                  (with-timeout (timeout
                                 (let ((timeout-msg (format "ERROR: Command timed out after %d seconds" timeout)))
                                   (message "DEBUG: run_command_enhanced - %s" timeout-msg)
                                   (setq output (concat "ERROR: " timeout-msg))
                                   (setq exit-code 1)))
                    (condition-case run-err
                        (setq output (shell-command-to-string command))
                      (error
                       (let ((error-msg (format "ERROR: Failed to execute command: %s"
                                               (error-message-string run-err))))
                         (message "DEBUG: run_command_enhanced - %s" error-msg)
                         (setq output (concat "ERROR: " error-msg))
                         (setq exit-code 1)))))

                  ;; Calculate duration
                  (let ((end-time (float-time))
                        (duration (- (float-time) start-time)))

                    (message "DEBUG: run_command_enhanced - Command executed in %.2f seconds" duration)

                    ;; Format the result
                    (setq result (concat result
                                         "**Command:** `" command "`\n\n"
                                         "**Working Directory:** `" directory "`\n\n"
                                         "**Exit Code:** " (number-to-string exit-code) "\n\n"
                                         "**Duration:** " (format "%.2f seconds" duration) "\n\n"))

                    ;; Add command output
                    (setq result (concat result
                                         "## Output\n\n```\n"
                                         (if (> (length output) ai-auto-complete-linux-process-output-limit)
                                             (concat (substring output 0 ai-auto-complete-linux-process-output-limit)
                                                     "\n... [Output truncated due to size] ...")
                                           output)
                                         "\n```\n\n"))

                    ;; Return the formatted result
                    (message "DEBUG: run_command_enhanced - Returning result of length: %d" (length result))
                    result))

              (error
               (let ((error-msg (format "ERROR: Failed to execute command: %s"
                                       (error-message-string exec-err))))
                 (message "DEBUG: run_command_enhanced - %s" error-msg)
                 (concat "# Command Execution Error\n\n" error-msg)))))))

    (error
     (let ((error-msg (format "ERROR: Failed to run command: %s" (error-message-string err))))
       (message "DEBUG: run_command_enhanced - %s" error-msg)
       (concat "# Command Execution Error\n\n" error-msg)))))

;; Asynchronous command execution
;; Example 1: Start a long-running process with a custom name
;; (ai-auto-complete-tool-start-process
;;  '((command . "sleep 300") (name . "long-sleep-task")))
;; Example 2: Start a process in a specific directory with default name
;; (ai-auto-complete-tool-start-process
;;  '((command . "tail -f /var/log/syslog") (directory . "/")))
(defun ai-auto-complete-tool-start-process (params)
  "Start an asynchronous process.
PARAMS should be an alist with:
- 'command': The command to run
- 'directory': Optional working directory (default: current directory)
- 'name': Optional process name (default: auto-generated)"
  (message "DEBUG: start_process called with params: %S" params)
  (condition-case err
      (let* ((command (cdr (assoc 'command params)))
             (directory (or (cdr (assoc 'directory params)) default-directory))
             (name (or (cdr (assoc 'name params)) (format "process-%d" ai-auto-complete-linux-process-counter)))
             (process-id (format "proc-%d" ai-auto-complete-linux-process-counter))
             (output-buffer (generate-new-buffer (format "*%s-output*" name)))
             (process nil))

        ;; Check if command is provided
        (if (not command)
            (let ((error-msg "ERROR: No command specified"))
              (message "DEBUG: start_process - %s" error-msg)
              error-msg)

          (message "DEBUG: start_process - Command: %s, Directory: %s, Name: %s"
                   command directory name)

          ;; Check if command is safe
          (if (and (boundp 'ai-auto-complete-linux-safe-commands)
                   ai-auto-complete-linux-safe-commands
                   (fboundp 'ai-auto-complete-linux-is-dangerous-command)
                   (ai-auto-complete-linux-is-dangerous-command command))
              (let ((error-msg (format "ERROR: Command '%s' is potentially dangerous and has been blocked. If you need to run this command, ask the user to disable safe mode." command)))
                (message "DEBUG: start_process - %s" error-msg)
                (when (buffer-live-p output-buffer)
                  (kill-buffer output-buffer))
                error-msg)

            ;; Start the process
            (condition-case start-err
                (progn
                  (message "DEBUG: start_process - Starting process with ID: %s" process-id)
                  ;; Set default directory for the process
                  (let ((default-directory directory))
                    ;; Start the process
                    (condition-case proc-err
                        (progn
                          (message "DEBUG: start_process - Creating process with start-process-shell-command")
                          (setq process (start-process-shell-command
                                         name output-buffer command))
                          (message "DEBUG: start_process - Process created successfully"))
                      (error
                       (let ((error-msg (format "ERROR: Failed to create process: %s"
                                               (error-message-string proc-err))))
                         (message "DEBUG: start_process - %s" error-msg)
                         (when (buffer-live-p output-buffer)
                           (kill-buffer output-buffer))
                         (error error-msg))))

                    ;; Set process sentinel
                    (condition-case sentinel-err
                        (progn
                          (message "DEBUG: start_process - Setting process sentinel")
                          (set-process-sentinel
                           process
                           (lambda (proc event)
                             (message "DEBUG: Process %s: %s"
                                      (process-name proc) (string-trim event))))
                          (message "DEBUG: start_process - Process sentinel set"))
                      (error
                       (let ((error-msg (format "ERROR: Failed to set process sentinel: %s"
                                               (error-message-string sentinel-err))))
                         (message "DEBUG: start_process - %s" error-msg)
                         ;; Continue despite sentinel error
                         )))

                    ;; Increment the process counter
                    (setq ai-auto-complete-linux-process-counter (1+ ai-auto-complete-linux-process-counter))
                    (message "DEBUG: start_process - Process counter incremented to %d"
                             ai-auto-complete-linux-process-counter)

                    ;; Store the process in the hash table
                    (condition-case hash-err
                        (progn
                          (message "DEBUG: start_process - Storing process in hash table")
                          (puthash process-id
                                   (list :process process
                                         :buffer output-buffer
                                         :command command
                                         :directory directory
                                         :start-time (current-time))
                                   ai-auto-complete-linux-processes)
                          (message "DEBUG: start_process - Process stored in hash table"))
                      (error
                       (let ((error-msg (format "ERROR: Failed to store process in hash table: %s"
                                               (error-message-string hash-err))))
                         (message "DEBUG: start_process - %s" error-msg)
                         ;; Continue despite hash table error
                         )))

                    ;; Return success message with process ID
                    (let ((success-msg (format "Process started successfully.\n\nProcess ID: `%s`\n\nUse this ID with the `get_process_output` tool to retrieve output or the `terminate_process` tool to stop the process." process-id)))
                      (message "DEBUG: start_process - %s" success-msg)
                      success-msg)))

              ;; Handle errors
              (error
               (let ((error-msg (format "ERROR: Failed to start process: %s"
                                       (error-message-string start-err))))
                 (message "DEBUG: start_process - %s" error-msg)
                 (when (buffer-live-p output-buffer)
                   (kill-buffer output-buffer))
                 error-msg))))))

    (error
     (let ((error-msg (format "ERROR: Failed to start process: %s" (error-message-string err))))
       (message "DEBUG: start_process - %s" error-msg)
       error-msg))))
;; Get process output
;; Example 1: Get output for a specific process ID
;; (ai-auto-complete-tool-get-process-output '((process_id . "proc-0")))
;; Example 2: Attempt to get output for another process ID
;; (ai-auto-complete-tool-get-process-output '((process_id . "proc-123")))
(defun ai-auto-complete-tool-get-process-output (params)
  "Get output from an asynchronous process.
PARAMS should be an alist with:
- 'process_id': The process ID returned by start_process"
  (message "DEBUG: get_process_output called with params: %S" params)
  (condition-case err
      (let* ((process-id (cdr (assoc 'process_id params)))
             (process-info (and process-id (gethash process-id ai-auto-complete-linux-processes))))

        ;; Check if process ID is provided
        (if (not process-id)
            (let ((error-msg "ERROR: No process ID specified"))
              (message "DEBUG: get_process_output - %s" error-msg)
              error-msg)

          (message "DEBUG: get_process_output - Process ID: %s" process-id)

          ;; Check if process exists
          (if (not process-info)
              (let ((error-msg (format "ERROR: Process with ID '%s' not found" process-id)))
                (message "DEBUG: get_process_output - %s" error-msg)
                error-msg)

            ;; Get process information
            (condition-case proc-err
                (let* ((process (plist-get process-info :process))
                       (buffer (plist-get process-info :buffer))
                       (command (plist-get process-info :command))
                       (directory (plist-get process-info :directory))
                       (start-time (plist-get process-info :start-time))
                       (running (and process (process-live-p process)))
                       (exit-code (and process (not running) (process-exit-status process)))
                       (elapsed-time (float-time (time-subtract (current-time) start-time)))
                       (output ""))

                  (message "DEBUG: get_process_output - Process: %s, Status: %s, Running time: %.2f seconds"
                           (if process (process-name process) "nil")
                           (if running "Running" "Terminated")
                           elapsed-time)

                  ;; Get output from buffer
                  (condition-case buf-err
                      (progn
                        (message "DEBUG: get_process_output - Getting output from buffer")
                        (if (buffer-live-p buffer)
                            (with-current-buffer buffer
                              (setq output (buffer-string))
                              (message "DEBUG: get_process_output - Output length: %d" (length output)))
                          (message "DEBUG: get_process_output - Buffer is not live")))
                    (error
                     (let ((error-msg (format "ERROR: Failed to get output from buffer: %s"
                                             (error-message-string buf-err))))
                       (message "DEBUG: get_process_output - %s" error-msg)
                       (setq output (concat "ERROR: " error-msg)))))

                  ;; Format the result
                  (message "DEBUG: get_process_output - Formatting result")
                  (condition-case fmt-err
                      (let ((formatted-result "# Process Output\n\n"))
                        ;; Add process information
                        (setq formatted-result (concat formatted-result
                                                       "**Process ID:** `" process-id "`\n\n"
                                                       "**Command:** `" (or command "Unknown") "`\n\n"
                                                       "**Working Directory:** `" (or directory "Unknown") "`\n\n"
                                                       "**Status:** " (if running "Running" "Terminated") "\n\n"
                                                       "**Running Time:** " (format "%.2f seconds" elapsed-time) "\n\n"))

                        ;; Add exit code if process has terminated
                        (when (and (not running) exit-code)
                          (setq formatted-result (concat formatted-result
                                                         "**Exit Code:** " (number-to-string exit-code) "\n\n")))

                        ;; Add output
                        (when (and output (not (string-empty-p output)))
                          (setq formatted-result (concat formatted-result
                                                         "## Output\n\n```\n"
                                                         (if (and (boundp 'ai-auto-complete-linux-process-output-limit)
                                                                  (> (length output) ai-auto-complete-linux-process-output-limit))
                                                             (concat (substring output 0 ai-auto-complete-linux-process-output-limit)
                                                                     "\n... [Output truncated due to size] ...")
                                                           output)
                                                         "\n```\n\n")))

                        ;; Return the formatted result
                        (message "DEBUG: get_process_output - Returning result of length: %d"
                                 (length formatted-result))
                        formatted-result)
                    (error
                     (let ((error-msg (format "ERROR: Failed to format process output: %s"
                                             (error-message-string fmt-err))))
                       (message "DEBUG: get_process_output - %s" error-msg)
                       (concat "# Process Output Error\n\n" error-msg)))))
              (error
               (let ((error-msg (format "ERROR: Failed to get process information: %s"
                                       (error-message-string proc-err))))
                 (message "DEBUG: get_process_output - %s" error-msg)
                 (concat "# Process Output Error\n\n" error-msg)))))))

    (error
     (let ((error-msg (format "ERROR: Failed to get process output: %s" (error-message-string err))))
       (message "DEBUG: get_process_output - %s" error-msg)
       (concat "# Process Output Error\n\n" error-msg)))))

;; Terminate process
;; Example 1: Terminate a process normally
;; (ai-auto-complete-tool-terminate-process '((process_id . "proc-0")))
;; Example 2: Force terminate a process
;; (ai-auto-complete-tool-terminate-process
;;  '((process_id . "proc-1") (force . t)))
(defun ai-auto-complete-tool-terminate-process (params)
  "Terminate an asynchronous process.
PARAMS should be an alist with:
- 'process_id': The process ID returned by start_process
- 'force': Optional boolean to force termination (default: nil)"
  (message "DEBUG: terminate_process called with params: %S" params)
  (condition-case err
      (let* ((process-id (cdr (assoc 'process_id params)))
             (force (cdr (assoc 'force params)))
             (process-info (and process-id (gethash process-id ai-auto-complete-linux-processes))))

        ;; Check if process ID is provided
        (if (not process-id)
            (let ((error-msg "ERROR: No process ID specified"))
              (message "DEBUG: terminate_process - %s" error-msg)
              error-msg)

          (message "DEBUG: terminate_process - Process ID: %s, Force: %s"
                   process-id (if force "yes" "no"))

          ;; Check if process exists
          (if (not process-info)
              (let ((error-msg (format "ERROR: Process with ID '%s' not found" process-id)))
                (message "DEBUG: terminate_process - %s" error-msg)
                error-msg)

            ;; Get process information
            (condition-case proc-err
                (let* ((process (plist-get process-info :process))
                       (buffer (plist-get process-info :buffer))
                       (command (plist-get process-info :command))
                       (running (and process (process-live-p process))))

                  (message "DEBUG: terminate_process - Process: %s, Command: %s, Running: %s"
                           (if process (process-name process) "nil")
                           (or command "Unknown")
                           (if running "yes" "no"))

                  ;; Check if process is running
                  (if (not running)
                      (let ((info-msg (format "Process with ID '%s' is already terminated" process-id)))
                        (message "DEBUG: terminate_process - %s" info-msg)
                        info-msg)

                    ;; Terminate the process
                    (condition-case term-err
                        (progn
                          ;; Kill the process
                          (message "DEBUG: terminate_process - Terminating process")
                          (condition-case kill-err
                              (if force
                                  (progn
                                    (message "DEBUG: terminate_process - Using SIGKILL (force=true)")
                                    (signal-process process 'SIGKILL))
                                (progn
                                  (message "DEBUG: terminate_process - Using interrupt-process (force=false)")
                                  (interrupt-process process)))
                            (error
                             (let ((error-msg (format "ERROR: Failed to send signal to process: %s"
                                                     (error-message-string kill-err))))
                               (message "DEBUG: terminate_process - %s" error-msg)
                               (error error-msg))))

                          ;; Wait for the process to terminate
                          (message "DEBUG: terminate_process - Waiting for process to terminate")
                          (let ((wait-count 0))
                            (while (and (< wait-count 10)
                                        (process-live-p process))
                              (message "DEBUG: terminate_process - Still waiting... (attempt %d)" wait-count)
                              (sleep-for 0.1)
                              (setq wait-count (1+ wait-count))))

                          ;; Check if process is still running
                          (if (process-live-p process)
                              (progn
                                ;; Force kill if still running
                                (message "DEBUG: terminate_process - Process still running, using SIGKILL")
                                (condition-case force-kill-err
                                    (signal-process process 'SIGKILL)
                                  (error
                                   (message "DEBUG: terminate_process - Error in force kill: %s"
                                            (error-message-string force-kill-err))))

                                (let ((result-msg (format "Process with ID '%s' was forcibly terminated" process-id)))
                                  (message "DEBUG: terminate_process - %s" result-msg)
                                  result-msg))

                            (let ((success-msg (format "Process with ID '%s' was terminated successfully" process-id)))
                              (message "DEBUG: terminate_process - %s" success-msg)
                              success-msg)))

                      ;; Handle errors
                      (error
                       (let ((error-msg (format "ERROR: Failed to terminate process: %s"
                                               (error-message-string term-err))))
                         (message "DEBUG: terminate_process - %s" error-msg)
                         error-msg)))))

              (error
               (let ((error-msg (format "ERROR: Failed to get process information: %s"
                                       (error-message-string proc-err))))
                 (message "DEBUG: terminate_process - %s" error-msg)
                 error-msg))))))

    (error
     (let ((error-msg (format "ERROR: Failed to terminate process: %s" (error-message-string err))))
       (message "DEBUG: terminate_process - %s" error-msg)
       error-msg))))

;; List running processes
;; Example 1: List all processes (params is ignored but shown for consistency)
;; (ai-auto-complete-tool-list-processes nil)
;; (ai-auto-complete-tool-list-processes '())
(defun ai-auto-complete-tool-list-processes (params)
  "List all running processes started by the LLM.
PARAMS is ignored."
  (message "DEBUG: list_processes called with params: %S" params)
  (condition-case err
      (let ((formatted-result "# Running Processes\n\n"))
        ;; Check if there are any processes
        (if (= (hash-table-count ai-auto-complete-linux-processes) 0)
            (let ((empty-msg "No processes are currently running.\n\n"))
              (message "DEBUG: list_processes - %s" empty-msg)
              (concat formatted-result empty-msg))

          (message "DEBUG: list_processes - Found %d processes"
                   (hash-table-count ai-auto-complete-linux-processes))

          ;; Add table header
          (setq formatted-result (concat formatted-result
                                         "| Process ID | Command | Status | Running Time |\n"
                                         "|------------|---------|--------|-------------|\n"))

          ;; Add each process
          (condition-case map-err
              (progn
                (message "DEBUG: list_processes - Building process table")
                (maphash
                 (lambda (process-id process-info)
                   (condition-case proc-err
                       (let* ((process (plist-get process-info :process))
                              (command (plist-get process-info :command))
                              (start-time (plist-get process-info :start-time))
                              (running (and process (process-live-p process)))
                              (elapsed-time (float-time (time-subtract (current-time) start-time))))

                         (message "DEBUG: list_processes - Processing process ID: %s" process-id)

                         ;; Add process to table
                         (setq formatted-result (concat formatted-result
                                                        "| `" process-id "` | `"
                                                        (if (and command (> (length command) 30))
                                                            (concat (substring command 0 27) "...")
                                                          (or command "Unknown"))
                                                        "` | "
                                                        (if running "Running" "Terminated")
                                                        " | "
                                                        (format "%.2f seconds" elapsed-time)
                                                        " |\n")))
                     (error
                      (let ((error-msg (format "ERROR: Failed to process process ID %s: %s"
                                              process-id (error-message-string proc-err))))
                        (message "DEBUG: list_processes - %s" error-msg)
                        (setq formatted-result (concat formatted-result
                                                       "| `" process-id "` | Error | Error | Error |\n"))))))
                 ai-auto-complete-linux-processes)

                ;; Return the formatted result
                (message "DEBUG: list_processes - Returning result of length: %d" (length formatted-result))
                formatted-result)

            (error
             (let ((error-msg (format "ERROR: Failed to process process list: %s"
                                     (error-message-string map-err))))
               (message "DEBUG: list_processes - %s" error-msg)
               (concat formatted-result "\nError processing process list: " error-msg "\n\n"))))))

    (error
     (let ((error-msg (format "ERROR: Failed to list processes: %s" (error-message-string err))))
       (message "DEBUG: list_processes - %s" error-msg)
       (concat "# Process List Error\n\n" error-msg)))))
;; Create and execute a shell script
;; Example 1: Create a script and execute it with arguments
;; (ai-auto-complete-tool-create-shell-script
;;  '((content . "echo Hello $1") (execute . t) (args . "World")))
;; Example 2: Create a script at a specific path without executing it
;; (ai-auto-complete-tool-create-shell-script
;;  '((content . "ls -la > /tmp/listing.txt")
;;    (execute . nil)
;;    (path . "/tmp/my_script.sh")))
(defun ai-auto-complete-tool-create-shell-script (params)
  "Create and optionally execute a shell script.
PARAMS should be an alist with:
- 'content': The shell script content
- 'execute': Optional boolean to execute the script (default: nil)
- 'path': Optional path to save the script (default: temporary file)
- 'args': Optional arguments to pass to the script (default: none)"
  (message "DEBUG: create_shell_script called with params: %S" params)
  (condition-case err
      (let* ((content (cdr (assoc 'content params)))
             (execute (cdr (assoc 'execute params)))
             (path (cdr (assoc 'path params)))
             (args (cdr (assoc 'args params)))
             (temp-file (if path path (make-temp-file "ai-script-" nil ".sh")))
             (full-command (if args (format "%s %s" temp-file args) temp-file)))
        
        ;; Check if content is provided
        (if (not content)
            (let ((error-msg "ERROR: No script content specified"))
              (message "DEBUG: create_shell_script - %s" error-msg)
              error-msg)
          
          (message "DEBUG: create_shell_script - Content length: %d, Execute: %s, Path: %s, Args: %s" 
                   (length content) (if execute "yes" "no") (or path "temp") (or args "none"))
          
          ;; Check if script content is safe
          (if (and (boundp 'ai-auto-complete-linux-safe-commands)
                   ai-auto-complete-linux-safe-commands
                   (fboundp 'ai-auto-complete-linux-contains-dangerous-commands)
                   (ai-auto-complete-linux-contains-dangerous-commands content))
              (let ((error-msg (format "ERROR: Script contains potentially dangerous commands and has been blocked. If you need to run these commands, ask the user to disable safe mode.")))
                (message "DEBUG: create_shell_script - %s" error-msg)
                error-msg)
            
            ;; Create the script file
            (message "DEBUG: create_shell_script - Creating script at: %s" temp-file)
            (condition-case create-err
                (progn
                  ;; Write content to file
                  (condition-case write-err
                      (progn
                        (with-temp-file temp-file
                          (insert "#!/bin/bash\n\n")
                          (insert content))
                        (message "DEBUG: create_shell_script - Script content written to file"))
                    (error
                     (let ((error-msg (format "ERROR: Failed to write script content: %s" 
                                             (error-message-string write-err))))
                       (message "DEBUG: create_shell_script - %s" error-msg)
                       (error error-msg))))
                  
                  ;; Make the script executable
                  (condition-case chmod-err
                      (progn
                        (set-file-modes temp-file #o755)
                        (message "DEBUG: create_shell_script - Script made executable"))
                    (error
                     (let ((error-msg (format "ERROR: Failed to make script executable: %s" 
                                             (error-message-string chmod-err))))
                       (message "DEBUG: create_shell_script - %s" error-msg)
                       ;; Continue despite chmod error
                       )))
                  
                  ;; Execute the script if requested
                  (if execute
                      (progn
                        ;; Run the script and return the output
                        (message "DEBUG: create_shell_script - Executing script: %s" full-command)
                        (condition-case exec-err
                            (let ((result (ai-auto-complete-tool-run-command-enhanced
                                           `((command . ,full-command)))))
                              (message "DEBUG: create_shell_script - Script executed successfully")
                              result)
                          (error
                           (let ((error-msg (format "Script created successfully at: `%s`, but execution failed: %s"
                                                    temp-file (error-message-string exec-err))))
                             (message "DEBUG: create_shell_script - %s" error-msg)
                             error-msg))))
                    
                    ;; Return success message with script path
                    (let ((success-msg (format "Shell script created successfully at: `%s`\n\nTo execute the script, use the `run_command` tool with the command: `%s`"
                                               temp-file temp-file)))
                      (message "DEBUG: create_shell_script - %s" success-msg)
                      success-msg)))
              
              ;; Handle errors
              (error
               (let ((error-msg (format "ERROR: Failed to create shell script: %s" (error-message-string create-err))))
                 (message "DEBUG: create_shell_script - %s" error-msg)
                 error-msg))))))
    
    (error
     (let ((error-msg (format "ERROR: Failed to create shell script: %s" (error-message-string err))))
       (message "DEBUG: create_shell_script - %s" error-msg)
       error-msg))))

;; Helper function to check if a command is dangerous
;; Example 1: Check a dangerous command
;; (ai-auto-complete-linux-is-dangerous-command "sudo rm -rf /")
;; Example 2: Check a safe command
;; (ai-auto-complete-linux-is-dangerous-command "ls -l")
(defun ai-auto-complete-linux-is-dangerous-command (command)
  "Check if COMMAND is potentially dangerous."
  (message "DEBUG: is_dangerous_command - Checking command: %s" command)
  (let ((dangerous nil))
    ;; Check each dangerous command
    (dolist (dangerous-cmd ai-auto-complete-linux-dangerous-commands)
      (when (string-match-p (concat "\\<" (regexp-quote dangerous-cmd) "\\>") command)
        (message "DEBUG: is_dangerous_command - Found dangerous command: %s" dangerous-cmd)
        (setq dangerous t)))
    dangerous))

;; Helper function to check if a script contains dangerous commands
;; Example 1: Check a script with a dangerous command
;; (ai-auto-complete-linux-contains-dangerous-commands "echo 'Hello'\nrm important_file.txt")
;; Example 2: Check a safe script
;; (ai-auto-complete-linux-contains-dangerous-commands "echo 'Hello World'\nls -la")
(defun ai-auto-complete-linux-contains-dangerous-commands (script)
  "Check if SCRIPT contains potentially dangerous commands."
  (message "DEBUG: contains_dangerous_commands - Checking script of length: %d" (length script))
  (let ((dangerous nil))
    ;; Check each dangerous command
    (dolist (dangerous-cmd ai-auto-complete-linux-dangerous-commands)
      (when (string-match-p (concat "\\<" (regexp-quote dangerous-cmd) "\\>") script)
        (message "DEBUG: contains_dangerous_commands - Found dangerous command: %s" dangerous-cmd)
        (setq dangerous t)))
    dangerous))

;; Register Linux tools
(defun ai-auto-complete-register-linux-tools ()
  "Register enhanced Linux tools."
  (message "DEBUG: register_linux_tools - Registering Linux tools")
  
  ;; System information tool
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "get_system_info"
         "Get comprehensive information about the system environment"
         #'ai-auto-complete-tool-get-system-info
         '(("detail_level" . "Level of detail: 'basic', 'standard', or 'full'")
           ("include_env" . "Whether to include environment variables (t or nil)")))
        (message "DEBUG: register_linux_tools - get_system_info tool registered"))
    (error
     (message "DEBUG: register_linux_tools - Error registering get_system_info: %s" 
              (error-message-string err))))
  
  ;; Enhanced command execution
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "run_command_enhanced"
         "Run a shell command with enhanced output and error handling"
         #'ai-auto-complete-tool-run-command-enhanced
         '(("command" . "Command to run")
           ("timeout" . "Optional timeout in seconds")
           ("directory" . "Optional working directory")))
        (message "DEBUG: register_linux_tools - run_command_enhanced tool registered"))
    (error
     (message "DEBUG: register_linux_tools - Error registering run_command_enhanced: %s" 
              (error-message-string err))))
  
  ;; Asynchronous process management
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "start_process"
         "Start an asynchronous process"
         #'ai-auto-complete-tool-start-process
         '(("command" . "Command to run")
           ("directory" . "Optional working directory")
           ("name" . "Optional process name")))
        (message "DEBUG: register_linux_tools - start_process tool registered"))
    (error
     (message "DEBUG: register_linux_tools - Error registering start_process: %s" 
              (error-message-string err))))
  
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "get_process_output"
         "Get output from an asynchronous process"
         #'ai-auto-complete-tool-get-process-output
         '(("process_id" . "Process ID returned by start_process")))
        (message "DEBUG: register_linux_tools - get_process_output tool registered"))
    (error
     (message "DEBUG: register_linux_tools - Error registering get_process_output: %s" 
              (error-message-string err))))
  
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "terminate_process"
         "Terminate an asynchronous process"
         #'ai-auto-complete-tool-terminate-process
         '(("process_id" . "Process ID returned by start_process")
           ("force" . "Optional boolean to force termination")))
        (message "DEBUG: register_linux_tools - terminate_process tool registered"))
    (error
     (message "DEBUG: register_linux_tools - Error registering terminate_process: %s" 
              (error-message-string err))))
  
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "list_processes"
         "List all running processes started by the LLM"
         #'ai-auto-complete-tool-list-processes
         '())
        (message "DEBUG: register_linux_tools - list_processes tool registered"))
    (error
     (message "DEBUG: register_linux_tools - Error registering list_processes: %s" 
              (error-message-string err))))
  
  ;; Shell script creation and execution
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "create_shell_script"
         "Create and optionally execute a shell script"
         #'ai-auto-complete-tool-create-shell-script
         '(("content" . "The shell script content")
           ("execute" . "Optional boolean to execute the script")
           ("path" . "Optional path to save the script")
           ("args" . "Optional arguments to pass to the script")))
        (message "DEBUG: register_linux_tools - create_shell_script tool registered"))
    (error
     (message "DEBUG: register_linux_tools - Error registering create_shell_script: %s" 
              (error-message-string err))))
  
  (message "DEBUG: register_linux_tools - All Linux tools registered successfully"))


  (provide 'tools/linux-tools)
;;; linux-tools.el ends here  
