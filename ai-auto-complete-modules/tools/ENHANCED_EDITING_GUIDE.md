# Enhanced File Editing Tools for LLMs

This guide explains the comprehensive set of enhanced file editing tools designed to make file editing much more intuitive and effective for LLMs. These tools provide surgical editing capabilities with line-based operations, pattern matching, and incremental editing support.

## Overview

The enhanced editing tools solve the major limitations of traditional file editing tools by providing:

1. **Line-based operations** - Work with specific line numbers
2. **Pattern-based editing** - Edit based on content patterns
3. **Incremental editing** - Build up changes step by step
4. **File inspection** - Examine file structure before editing
5. **Atomic operations** - Apply multiple edits safely
6. **Surgical precision** - Make targeted changes without affecting other content

## Tool Categories

### 1. Enhanced File Reading Tools

#### `read_file_with_lines`
Reads file content with line numbers for easy reference.

**Example:**
```xml
<tool name="read_file_with_lines">
<parameters>
{"path": "example.py"}
</parameters>
</tool>
```

**Output:**
```
   1: def hello_world():
   2:     print("Hello, World!")
   3: 
   4: def goodbye():
   5:     print("Goodbye!")
```

#### `read_file_range`
Reads specific line ranges with line numbers.

**Example:**
```xml
<tool name="read_file_range">
<parameters>
{"path": "example.py", "start_line": 1, "end_line": 3}
</parameters>
</tool>
```

#### `get_file_info`
Gets file metadata including size, line count, and permissions.

### 2. Line-Based Editing Tools

#### `insert_lines`
Inserts content after a specific line number.

**Example - Insert after line 2:**
```xml
<tool name="insert_lines">
<parameters>
{"path": "example.py", "line_number": 2, "content": "    # This is a comment\n    return None"}
</parameters>
</tool>
```

#### `delete_lines`
Deletes a range of lines.

**Example - Delete lines 3-4:**
```xml
<tool name="delete_lines">
<parameters>
{"path": "example.py", "start_line": 3, "end_line": 4}
</parameters>
</tool>
```

#### `replace_lines`
Replaces a range of lines with new content.

**Example - Replace lines 1-2:**
```xml
<tool name="replace_lines">
<parameters>
{"path": "example.py", "start_line": 1, "end_line": 2, "content": "def improved_hello():\n    print(\"Hello, improved world!\")"}
</parameters>
</tool>
```

#### `append_to_file` / `prepend_to_file`
Safely add content to the beginning or end of files.

### 3. Pattern-Based Editing Tools

#### `insert_after_pattern`
Inserts content after the first line matching a pattern.

**Example - Insert after function definition:**
```xml
<tool name="insert_after_pattern">
<parameters>
{"path": "example.py", "pattern": "def hello_world\\(\\):", "content": "    \"\"\"Prints a greeting message.\"\"\""}
</parameters>
</tool>
```

#### `insert_before_pattern`
Inserts content before the first line matching a pattern.

#### `replace_pattern`
Replaces lines matching a pattern.

**Example - Replace all print statements:**
```xml
<tool name="replace_pattern">
<parameters>
{"path": "example.py", "pattern": ".*print\\(.*\\).*", "content": "    logging.info(\"Message logged\")", "all": true}
</parameters>
</tool>
```

#### `delete_pattern`
Deletes lines matching a pattern.

### 4. Advanced Editing Tools

#### `edit_function`
Edits a specific function by name (works across multiple languages).

**Example - Replace entire function:**
```xml
<tool name="edit_function">
<parameters>
{"path": "example.py", "function_name": "hello_world", "content": "def hello_world(name=\"World\"):\n    \"\"\"Prints a personalized greeting.\"\"\"\n    print(f\"Hello, {name}!\")"}
</parameters>
</tool>
```

#### `edit_section`
Edits content between two markers.

**Example - Replace content between comments:**
```xml
<tool name="edit_section">
<parameters>
{"path": "config.py", "start_marker": "# START CONFIG", "end_marker": "# END CONFIG", "content": "DEBUG = True\nLOG_LEVEL = 'INFO'"}
</parameters>
</tool>
```

#### `apply_multiple_edits`
Applies multiple edits atomically (all succeed or all fail).

**Example - Multiple operations:**
```xml
<tool name="apply_multiple_edits">
<parameters>
{"path": "example.py", "edits": [
  {"type": "insert_lines", "line_number": 0, "content": "#!/usr/bin/env python3"},
  {"type": "insert_after_pattern", "pattern": "def hello_world", "content": "    \"\"\"Docstring\"\"\""},
  {"type": "replace_pattern", "pattern": "print\\(", "content": "    logger.info(", "all": true}
]}
</parameters>
</tool>
```

## Best Practices for LLMs

### 1. Always Read Before Editing
```xml
<!-- First, examine the file structure -->
<tool name="read_file_with_lines">
<parameters>{"path": "target_file.py"}</parameters>
</tool>

<!-- Then make targeted edits -->
<tool name="replace_lines">
<parameters>{"path": "target_file.py", "start_line": 5, "end_line": 7, "content": "new content"}</parameters>
</tool>
```

### 2. Use Line Numbers for Precision
Instead of complex pattern matching, use line numbers when you know the exact location:

```xml
<!-- Precise line-based editing -->
<tool name="insert_lines">
<parameters>{"path": "file.py", "line_number": 10, "content": "new_function_call()"}</parameters>
</tool>
```

### 3. Use Patterns for Content-Based Editing
When you need to find and modify specific content:

```xml
<!-- Pattern-based editing -->
<tool name="insert_after_pattern">
<parameters>{"path": "file.py", "pattern": "class MyClass:", "content": "    def __init__(self):\n        pass"}</parameters>
</tool>
```

### 4. Build Complex Changes Incrementally
Break down complex edits into smaller, manageable steps:

```xml
<!-- Step 1: Add import -->
<tool name="insert_lines">
<parameters>{"path": "file.py", "line_number": 0, "content": "import logging"}</parameters>
</tool>

<!-- Step 2: Add function -->
<tool name="insert_after_pattern">
<parameters>{"path": "file.py", "pattern": "import logging", "content": "\ndef setup_logging():\n    logging.basicConfig(level=logging.INFO)"}</parameters>
</tool>

<!-- Step 3: Modify existing function -->
<tool name="replace_pattern">
<parameters>{"path": "file.py", "pattern": "print\\(", "content": "logging.info(", "all": true}</parameters>
</tool>
```

### 5. Use Atomic Operations for Safety
When making multiple related changes, use `apply_multiple_edits` to ensure consistency:

```xml
<tool name="apply_multiple_edits">
<parameters>
{"path": "file.py", "edits": [
  {"type": "insert_lines", "line_number": 0, "content": "import sys"},
  {"type": "replace_pattern", "pattern": "exit\\(\\)", "content": "sys.exit()", "all": true}
]}
</parameters>
</tool>
```

## Common Editing Patterns

### Adding a New Function
1. Read the file to understand structure
2. Find the appropriate location (after imports, before main, etc.)
3. Use `insert_after_pattern` or `insert_lines` to add the function

### Modifying Existing Function
1. Use `read_file_with_lines` to see line numbers
2. Use `edit_function` for complete replacement, or
3. Use `replace_lines` for partial modifications

### Refactoring Code
1. Use `search_files` to find all occurrences
2. Use `replace_pattern` with `"all": true` for global changes
3. Use `apply_multiple_edits` for complex refactoring

### Adding Configuration
1. Use `edit_section` between configuration markers
2. Use `insert_after_pattern` to add new config sections
3. Use `append_to_file` for simple additions

## Error Handling

All tools provide detailed error messages and maintain file integrity:

- **File not found**: Clear error message with file path
- **Line number out of range**: Automatic adjustment or clear error
- **Pattern not found**: Specific error indicating the pattern that failed
- **Atomic operation failure**: Automatic rollback to original state

## Integration with Existing Tools

These enhanced editing tools complement the existing tools:

- Use `search_files` to find content, then use enhanced editing tools to modify
- Use `list_files` to explore structure, then use enhanced editing tools to modify
- Use `read_file` for simple viewing, `read_file_with_lines` for editing preparation

The enhanced editing tools make file editing much more intuitive and effective for LLMs by providing the right level of granularity and safety for different editing scenarios.
