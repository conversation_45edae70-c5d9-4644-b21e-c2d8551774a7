;;; standard-tools.el --- Standard tool implementations for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides standard tool implementations for the AI Auto Complete package.

;;; Code:

;; Try to load tools-core, but handle failure gracefully
(condition-case err
    (require 'tools/tools-core)
  (error
   (message "Warning: Failed to load tools/tools-core: %s" (error-message-string err))
   (load (expand-file-name "tools-core.el"
                          (file-name-directory (or load-file-name buffer-file-name))))))

;; Read file tool
(defun ai-auto-complete-tool-read-file (params)
  "Read file tool implementation. Reads the file and 
  return its contents as a string.
PARAMS should be an alist with a 'path' key."
  (let ((path (cdr (assoc 'path params))))
    (if (not path)
        "ERROR: No path specified"
      (if (file-exists-p path)
          (condition-case err
              (with-temp-buffer
                (insert-file-contents path)
                (format "Content of %s:\n```\n%s\n```"
                        path
                        (buffer-string)))
            (error (format "ERROR: Failed to read file %s: %s" path (error-message-string err))))
        (format "ERROR: File %s does not exist" path)))))

;; Write file tool
(defun ai-auto-complete-tool-write-file (params)
  "Write file tool implementation.
  It will replacee the entire file contents with the new content.
PARAMS should be an alist with 'path' and 'content' keys."
  (let ((path (cdr (assoc 'path params)))
        (content (cdr (assoc 'content params))))
    (cond
     ((not path)
      "ERROR: No path specified")
     ((not content)
      "ERROR: No content specified")
     (t
      (condition-case err
          (progn
            (with-temp-file path
              (insert content))
            (format "Successfully wrote to file %s" path))
        (error (format "ERROR: Failed to write to file %s: %s" path (error-message-string err))))))))

;; List directory tool
(defun ai-auto-complete-tool-list-directory (params)
  "List directory tool implementation.
  Lists the files and directories in the specified path but doesn't recurse into subdirectories.
PARAMS should be an alist with a 'path' key."
  (let ((path (cdr (assoc 'path params))))
    (if (not path)
        "ERROR: No path specified"
      (if (file-directory-p path)
          (condition-case err
              (let ((files (directory-files path)))
                (format "Contents of directory %s:\n%s"
                        path
                        (mapconcat 'identity files "\n")))
            (error (format "ERROR: Failed to list directory %s: %s" path (error-message-string err))))
        (format "ERROR: Directory %s does not exist" path)))))

;; Run command tool
(defun ai-auto-complete-tool-run-command (params)
  "Run command tool implementation.
PARAMS should be an alist with a 'command' key."
  (message "DEBUG: run_command called with params: %S" params)
  (let ((command (cdr (assoc 'command params))))
    (if (not command)
        "ERROR: No command specified"
      (message "DEBUG: Running command: %s" command)
      (condition-case err
          (let ((exit-code 0) ;; Assume success if shell-command-to-string doesn't error
                (output "")
                ;; stderr is typically part of the error message if the command fails
                )

            ;; Run the command
            (message "DEBUG: Executing command with shell-command-to-string")
            (setq output (shell-command-to-string command))
            (message "DEBUG: Command executed successfully")

            ;; Format the result
            (let ((result (format "Command output (exit code %d):\n```\n%s\n```" exit-code output)))
              (message "DEBUG: Command result: %s" (substring result 0 (min 100 (length result))))
              result))
        (error
         (let ((error-msg (format "ERROR: Failed to run command %s: %s" command (error-message-string err))))
           error-msg))))))

;; Create directory tool
(defun ai-auto-complete-tool-create-directory (params)
  "Create directory tool implementation.
   PARAMS should be an alist with a 'path' key."
  (let ((path (cdr (assoc 'path params))))
    (if (not path)
        "ERROR: No path specified"
      (if (file-exists-p path)
          (format "ERROR: Path %s already exists" path)
        (condition-case err
            (progn
              (make-directory path t)
              (format "Successfully created directory %s" path))
          (error (format "ERROR: Failed to create directory %s: %s" path (error-message-string err))))))))

;; Delete file tool
(defun ai-auto-complete-tool-delete-file (params)
  "Delete file tool implementation.
   PARAMS should be an alist with a 'path' key."
  (let ((path (cdr (assoc 'path params))))
    (if (not path)
        "ERROR: No path specified"
      (if (not (file-exists-p path))
          (format "ERROR: File %s does not exist" path)
        (condition-case err
            (progn
              (delete-file path)
              (format "Successfully deleted file %s" path))
          (error (format "ERROR: Failed to delete file %s: %s" path (error-message-string err))))))))

;; Get current buffer content tool
(defun ai-auto-complete-tool-get-current-buffer ()
  "Get current buffer content tool implementation.
  Returns the content of the current buffer as a string."
  (condition-case err
      (let ((content (buffer-string)))
        (format "Current buffer content:\n```\n%s\n```" content))
    (error (format "ERROR: Failed to get current buffer content: %s" (error-message-string err)))))

;; Register standard tools
(defun ai-auto-complete-register-standard-tools ()
  "Register standard tools."
  (ai-auto-complete-register-tool
   "read_file"
   "Reads the entire content of a specified file and returns it as a string. The content is formatted with the file path and triple backticks for clarity."
   #'ai-auto-complete-tool-read-file
   '(("path" . "The absolute or relative path to the file whose content is to be read.")))

  (ai-auto-complete-register-tool
   "write_file"
   "Writes the provided content to a specified file. If the file exists, its entire content will be replaced. If the file does not exist, it will be created."
   #'ai-auto-complete-tool-write-file
   '(("path" . "The absolute or relative path to the file where the content will be written.")
     ("content" . "The textual content to be written into the file.")))

  (ai-auto-complete-register-tool
   "list_directory"
   "Lists the names of files and subdirectories directly within a specified directory. This is a non-recursive listing."
   #'ai-auto-complete-tool-list-directory
   '(("path" . "The absolute or relative path to the directory whose immediate contents are to be listed.")))

  (ai-auto-complete-register-tool
   "run_command"
   "Executes a given shell command and returns its standard output and exit code. Useful for interacting with the operating system."
   #'ai-auto-complete-tool-run-command
   '(("command" . "The shell command string to be executed (e.g., 'ls -l', 'echo hello').")))

  (ai-auto-complete-register-tool
   "create_directory"
   "Creates a new directory at the specified path. Can create parent directories if they do not exist."
   #'ai-auto-complete-tool-create-directory
   '(("path" . "The absolute or relative path where the new directory should be created.")))

  (ai-auto-complete-register-tool
   "delete_file"
   "Deletes the file specified by the path."
   #'ai-auto-complete-tool-delete-file
   '(("path" . "The absolute or relative path to the file to be deleted.")))

  (ai-auto-complete-register-tool
   "get_current_buffer"
   "Retrieves the entire textual content of the currently active Emacs buffer and returns it as a string, formatted with triple backticks."
   #'ai-auto-complete-tool-get-current-buffer
   '()))

;; Register standard tools when this module is loaded
(ai-auto-complete-register-standard-tools)

(provide 'tools/standard-tools)
;;; standard-tools.el ends here
