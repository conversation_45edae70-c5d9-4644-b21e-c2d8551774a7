;;; tools-integration.el --- Integration for tools in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides integration for tools in the AI Auto Complete package.
;; It uses a state machine approach to avoid callback hell.

;;; Code:

(require 'tools/tools-core)
(require 'tools/tools-state-machine)
(require 'core/backend)

;; Function to process tool responses using the state machine
(defun ai-auto-complete-tools-wrapped-callback (response callback &optional agent-name)
  "Process the RESPONSE for tools and pass to CALLBACK.
Uses a state machine approach to avoid callback hell.
AGENT-NAME is the optional name of the agent making the request."
  (message "Processing response for tools")
  (message "ai-auto-complete-tools-wrapped-callback called with agent-name: %s" (or agent-name "nil"))
  (let ((effective-callback (if callback
                               callback
                             (lambda (resp)
                               (ai-auto-complete-tools-default-callback resp agent-name)))))
    ;; Use the state machine processor instead of the recursive one
    (ai-auto-complete-tools-process-with-state-machine response effective-callback agent-name)))

;; Advice function to add tool definitions to requests
(defun ai-auto-complete-tools-advice-request (orig-fun &rest args)
  "Advice function to add tool definitions to requests.
Calls ORIG-FUN with ARGS, but modifies the context to include tool definitions if tools are enabled."
  (let* ((backend (nth 0 args))
         (context (nth 1 args))
         (history (nth 2 args))
         (callback (nth 3 args))
         (agent-name (when (>= (length args) 5) (nth 4 args)))  ;; Get the agent name if provided
         (rest-args (when (>= (length args) 6) (nthcdr 5 args))))  ;; Adjust rest-args to start from the 6th argument

    (message "Tools advice called with backend: %s (args: %d)" backend (length args))
    (message "Callback type: %s" (type-of callback))
    (when agent-name
      (message "Request is from agent: %s" agent-name))
    (message "Context (first 50 chars): %s" (substring context 0 (min 50 (length context))))

    (if (not ai-auto-complete-tools-enabled)
        (apply orig-fun args)

      ;; Create a callback closure for this specific request
      (let ((wrapped-callback (lambda (response)
                               (message "Wrapped callback called with agent-name: %s" (or agent-name "nil"))
                               (ai-auto-complete-tools-wrapped-callback response callback agent-name))))

        ;; Add tool definitions to the prompt
        (let ((modified-context (ai-auto-complete-tools-add-to-prompt context agent-name)))
          (message "Modified context with tool definitions")

          ;; Create a new args list with the modified context and wrapped callback
          (let ((new-args (list backend modified-context history wrapped-callback)))
            ;; Add agent-name if it was provided
            (when agent-name
              (setq new-args (append new-args (list agent-name)))
              (message "Added agent-name to new-args: %s" agent-name))

            ;; Add any remaining arguments from the original call
            (when rest-args
              (setq new-args (append new-args rest-args))
              (message "Added rest-args to new-args"))

            ;; Apply the original function with the new arguments
            (message "Calling original function with modified args (length: %d)" (length new-args))
            (apply orig-fun new-args)))))))

;; Setup function to add advice to backend functions
(defun ai-auto-complete-tools-setup ()
  "Set up tool integration with backends."
  (advice-add 'ai-auto-complete-complete :around
              #'ai-auto-complete-tools-advice-request))

;; Teardown function to remove advice from backend functions
(defun ai-auto-complete-tools-teardown ()
  "Remove tool integration with backends."
  (advice-remove 'ai-auto-complete-complete
                 #'ai-auto-complete-tools-advice-request))

;; Set up tool integration when this module is loaded
(ai-auto-complete-tools-setup)

(provide 'tools/tools-integration)
;;; tools-integration.el ends here
