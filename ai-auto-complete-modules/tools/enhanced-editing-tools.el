;;; enhanced-editing-tools.el --- Enhanced file editing tools for LLMs -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides enhanced file editing tools specifically designed to make
;; file editing more intuitive and effective for LLMs. These tools provide
;; surgical editing capabilities with line-based operations, pattern matching,
;; and incremental editing support.

;;; Code:

;; Try to load tools-core, but handle failure gracefully
(condition-case err
    (require 'tools/tools-core)
  (error
   (message "Warning: Failed to load tools/tools-core: %s" (error-message-string err))
   (load (expand-file-name "tools-core.el"
                          (file-name-directory (or load-file-name buffer-file-name))))))

(require 'cl-lib)

;;; Enhanced File Reading Tools

(defun ai-auto-complete-tool-read-file-with-lines (params)
  "Read file with line numbers for easy reference.
PARAMS should be an alist with a 'path' key."
  (let ((path (cdr (assoc 'path params))))
    (if (not path)
        "ERROR: No path specified"
      (if (file-exists-p path)
          (condition-case err
              (with-temp-buffer
                (insert-file-contents path)
                (let ((lines (split-string (buffer-string) "\n"))
                      (result "")
                      (line-num 1))
                  (dolist (line lines)
                    (setq result (concat result (format "%4d: %s\n" line-num line)))
                    (setq line-num (1+ line-num)))
                  (format "Content of %s with line numbers:\n```\n%s```" path result)))
            (error (format "ERROR: Failed to read file %s: %s" path (error-message-string err))))
        (format "ERROR: File %s does not exist" path)))))

(defun ai-auto-complete-tool-read-file-range (params)
  "Read specific line range from a file.
PARAMS should be an alist with 'path', 'start_line', and 'end_line' keys."
  (let ((path (cdr (assoc 'path params)))
        (start-line (cdr (assoc 'start_line params)))
        (end-line (cdr (assoc 'end_line params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not start-line) "ERROR: No start_line specified")
     ((not end-line) "ERROR: No end_line specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (with-temp-buffer
            (insert-file-contents path)
            (let ((lines (split-string (buffer-string) "\n"))
                  (result "")
                  (line-num 1))
              (when (> start-line (length lines))
                (error "Start line %d exceeds file length %d" start-line (length lines)))
              (when (> end-line (length lines))
                (setq end-line (length lines)))
              (dolist (line lines)
                (when (and (>= line-num start-line) (<= line-num end-line))
                  (setq result (concat result (format "%4d: %s\n" line-num line))))
                (setq line-num (1+ line-num)))
              (format "Lines %d-%d of %s:\n```\n%s```" start-line end-line path result)))
        (error (format "ERROR: Failed to read file range: %s" (error-message-string err))))))))

(defun ai-auto-complete-tool-get-file-info (params)
  "Get file metadata including size, line count, and basic info.
PARAMS should be an alist with a 'path' key."
  (let ((path (cdr (assoc 'path params))))
    (if (not path)
        "ERROR: No path specified"
      (if (file-exists-p path)
          (condition-case err
              (let* ((attrs (file-attributes path))
                     (size (nth 7 attrs))
                     (modified (nth 5 attrs))
                     (line-count 0))
                (with-temp-buffer
                  (insert-file-contents path)
                  (setq line-count (count-lines (point-min) (point-max))))
                (format "File info for %s:\n- Size: %d bytes\n- Lines: %d\n- Modified: %s\n- Readable: %s\n- Writable: %s"
                        path size line-count
                        (format-time-string "%Y-%m-%d %H:%M:%S" modified)
                        (if (file-readable-p path) "Yes" "No")
                        (if (file-writable-p path) "Yes" "No")))
            (error (format "ERROR: Failed to get file info: %s" (error-message-string err))))
        (format "ERROR: File %s does not exist" path)))))

;;; Line-Based Editing Tools

(defun ai-auto-complete-tool-insert-lines (params)
  "Insert lines at a specific position in the file.
PARAMS should be an alist with 'path', 'line_number', and 'content' keys.
The content will be inserted AFTER the specified line number."
  (let ((path (cdr (assoc 'path params)))
        (line-number (cdr (assoc 'line_number params)))
        (content (cdr (assoc 'content params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not line-number) "ERROR: No line_number specified")
     ((not content) "ERROR: No content specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (with-temp-buffer
            (insert-file-contents path)
            (let ((lines (split-string (buffer-string) "\n" t))
                  (new-lines (split-string content "\n" t)))
              (when (> line-number (length lines))
                (error "Line number %d exceeds file length %d" line-number (length lines)))
              ;; Insert new lines after the specified line
              (setq lines (append (cl-subseq lines 0 line-number)
                                 new-lines
                                 (cl-subseq lines line-number)))
              ;; Write back to file
              (erase-buffer)
              (insert (mapconcat 'identity lines "\n"))
              (write-region (point-min) (point-max) path)
              (format "Successfully inserted %d lines after line %d in %s"
                      (length new-lines) line-number path)))
        (error (format "ERROR: Failed to insert lines: %s" (error-message-string err))))))))

(defun ai-auto-complete-tool-delete-lines (params)
  "Delete specific line range from a file.
PARAMS should be an alist with 'path', 'start_line', and 'end_line' keys."
  (let ((path (cdr (assoc 'path params)))
        (start-line (cdr (assoc 'start_line params)))
        (end-line (cdr (assoc 'end_line params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not start-line) "ERROR: No start_line specified")
     ((not end-line) "ERROR: No end_line specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (with-temp-buffer
            (insert-file-contents path)
            (let ((lines (split-string (buffer-string) "\n" t)))
              (when (> start-line (length lines))
                (error "Start line %d exceeds file length %d" start-line (length lines)))
              (when (> end-line (length lines))
                (setq end-line (length lines)))
              (when (> start-line end-line)
                (error "Start line %d cannot be greater than end line %d" start-line end-line))
              ;; Remove the specified lines (convert to 0-based indexing)
              (let ((before-lines (cl-subseq lines 0 (1- start-line)))
                    (after-lines (cl-subseq lines end-line)))
                (setq lines (append before-lines after-lines)))
              ;; Write back to file
              (erase-buffer)
              (insert (mapconcat 'identity lines "\n"))
              (write-region (point-min) (point-max) path)
              (format "Successfully deleted lines %d-%d from %s"
                      start-line end-line path)))
        (error (format "ERROR: Failed to delete lines: %s" (error-message-string err))))))))

(defun ai-auto-complete-tool-replace-lines (params)
  "Replace specific line range with new content.
PARAMS should be an alist with 'path', 'start_line', 'end_line', and 'content' keys."
  (let ((path (cdr (assoc 'path params)))
        (start-line (cdr (assoc 'start_line params)))
        (end-line (cdr (assoc 'end_line params)))
        (content (cdr (assoc 'content params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not start-line) "ERROR: No start_line specified")
     ((not end-line) "ERROR: No end_line specified")
     ((not content) "ERROR: No content specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (with-temp-buffer
            (insert-file-contents path)
            (let ((lines (split-string (buffer-string) "\n" t))
                  (new-lines (split-string content "\n" t)))
              (when (> start-line (length lines))
                (error "Start line %d exceeds file length %d" start-line (length lines)))
              (when (> end-line (length lines))
                (setq end-line (length lines)))
              (when (> start-line end-line)
                (error "Start line %d cannot be greater than end line %d" start-line end-line))
              ;; Replace the specified lines (convert to 0-based indexing)
              (let ((before-lines (cl-subseq lines 0 (1- start-line)))
                    (after-lines (cl-subseq lines end-line)))
                (setq lines (append before-lines new-lines after-lines)))
              ;; Write back to file
              (erase-buffer)
              (insert (mapconcat 'identity lines "\n"))
              (write-region (point-min) (point-max) path)
              (format "Successfully replaced lines %d-%d in %s with %d new lines"
                      start-line end-line path (length new-lines))))
        (error (format "ERROR: Failed to replace lines: %s" (error-message-string err))))))))

(defun ai-auto-complete-tool-append-to-file (params)
  "Append content to the end of a file.
PARAMS should be an alist with 'path' and 'content' keys."
  (let ((path (cdr (assoc 'path params)))
        (content (cdr (assoc 'content params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not content) "ERROR: No content specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (progn
            (with-temp-buffer
              (insert content)
              (write-region (point-min) (point-max) path t))
            (format "Successfully appended content to %s" path))
        (error (format "ERROR: Failed to append to file: %s" (error-message-string err))))))))

(defun ai-auto-complete-tool-prepend-to-file (params)
  "Prepend content to the beginning of a file.
PARAMS should be an alist with 'path' and 'content' keys."
  (let ((path (cdr (assoc 'path params)))
        (content (cdr (assoc 'content params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not content) "ERROR: No content specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (let ((original-content ""))
            (with-temp-buffer
              (insert-file-contents path)
              (setq original-content (buffer-string)))
            (with-temp-file path
              (insert content)
              (insert original-content))
            (format "Successfully prepended content to %s" path))
        (error (format "ERROR: Failed to prepend to file: %s" (error-message-string err))))))))

;;; Pattern-Based Editing Tools

(defun ai-auto-complete-tool-insert-after-pattern (params)
  "Insert content after the first line matching a pattern.
PARAMS should be an alist with 'path', 'pattern', and 'content' keys."
  (let ((path (cdr (assoc 'path params)))
        (pattern (cdr (assoc 'pattern params)))
        (content (cdr (assoc 'content params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not pattern) "ERROR: No pattern specified")
     ((not content) "ERROR: No content specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (with-temp-buffer
            (insert-file-contents path)
            (let ((lines (split-string (buffer-string) "\n" t))
                  (new-lines (split-string content "\n" t))
                  (found-line nil)
                  (line-num 0))
              ;; Find the first line matching the pattern
              (dolist (line lines)
                (setq line-num (1+ line-num))
                (when (and (not found-line) (string-match pattern line))
                  (setq found-line line-num)))
              (if found-line
                  (progn
                    ;; Insert new lines after the found line
                    (setq lines (append (cl-subseq lines 0 found-line)
                                       new-lines
                                       (cl-subseq lines found-line)))
                    ;; Write back to file
                    (erase-buffer)
                    (insert (mapconcat 'identity lines "\n"))
                    (write-region (point-min) (point-max) path)
                    (format "Successfully inserted %d lines after pattern '%s' (line %d) in %s"
                            (length new-lines) pattern found-line path))
                (format "ERROR: Pattern '%s' not found in %s" pattern path))))
        (error (format "ERROR: Failed to insert after pattern: %s" (error-message-string err))))))))

(defun ai-auto-complete-tool-insert-before-pattern (params)
  "Insert content before the first line matching a pattern.
PARAMS should be an alist with 'path', 'pattern', and 'content' keys."
  (let ((path (cdr (assoc 'path params)))
        (pattern (cdr (assoc 'pattern params)))
        (content (cdr (assoc 'content params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not pattern) "ERROR: No pattern specified")
     ((not content) "ERROR: No content specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (with-temp-buffer
            (insert-file-contents path)
            (let ((lines (split-string (buffer-string) "\n" t))
                  (new-lines (split-string content "\n" t))
                  (found-line nil)
                  (line-num 0))
              ;; Find the first line matching the pattern
              (dolist (line lines)
                (setq line-num (1+ line-num))
                (when (and (not found-line) (string-match pattern line))
                  (setq found-line line-num)))
              (if found-line
                  (progn
                    ;; Insert new lines before the found line (convert to 0-based)
                    (setq lines (append (cl-subseq lines 0 (1- found-line))
                                       new-lines
                                       (cl-subseq lines (1- found-line))))
                    ;; Write back to file
                    (erase-buffer)
                    (insert (mapconcat 'identity lines "\n"))
                    (write-region (point-min) (point-max) path)
                    (format "Successfully inserted %d lines before pattern '%s' (line %d) in %s"
                            (length new-lines) pattern found-line path))
                (format "ERROR: Pattern '%s' not found in %s" pattern path))))
        (error (format "ERROR: Failed to insert before pattern: %s" (error-message-string err))))))))

(defun ai-auto-complete-tool-replace-pattern (params)
  "Replace lines matching a pattern with new content.
PARAMS should be an alist with 'path', 'pattern', 'content', and optional 'all' keys.
If 'all' is true, replace all matching lines; otherwise replace only the first match."
  (let ((path (cdr (assoc 'path params)))
        (pattern (cdr (assoc 'pattern params)))
        (content (cdr (assoc 'content params)))
        (replace-all (cdr (assoc 'all params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not pattern) "ERROR: No pattern specified")
     ((not content) "ERROR: No content specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (with-temp-buffer
            (insert-file-contents path)
            (let ((lines (split-string (buffer-string) "\n" t))
                  (new-lines (split-string content "\n" t))
                  (result-lines '())
                  (replacements 0))
              ;; Process each line
              (dolist (line lines)
                (if (and (string-match pattern line)
                         (or replace-all (= replacements 0)))
                    (progn
                      (setq result-lines (append result-lines new-lines))
                      (setq replacements (1+ replacements)))
                  (setq result-lines (append result-lines (list line)))))
              (if (> replacements 0)
                  (progn
                    ;; Write back to file
                    (erase-buffer)
                    (insert (mapconcat 'identity result-lines "\n"))
                    (write-region (point-min) (point-max) path)
                    (format "Successfully replaced %d line(s) matching pattern '%s' in %s"
                            replacements pattern path))
                (format "ERROR: Pattern '%s' not found in %s" pattern path))))
        (error (format "ERROR: Failed to replace pattern: %s" (error-message-string err))))))))

(defun ai-auto-complete-tool-delete-pattern (params)
  "Delete lines matching a pattern.
PARAMS should be an alist with 'path', 'pattern', and optional 'all' keys.
If 'all' is true, delete all matching lines; otherwise delete only the first match."
  (let ((path (cdr (assoc 'path params)))
        (pattern (cdr (assoc 'pattern params)))
        (delete-all (cdr (assoc 'all params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not pattern) "ERROR: No pattern specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (with-temp-buffer
            (insert-file-contents path)
            (let ((lines (split-string (buffer-string) "\n" t))
                  (result-lines '())
                  (deletions 0))
              ;; Process each line
              (dolist (line lines)
                (if (and (string-match pattern line)
                         (or delete-all (= deletions 0)))
                    (setq deletions (1+ deletions))
                  (setq result-lines (append result-lines (list line)))))
              (if (> deletions 0)
                  (progn
                    ;; Write back to file
                    (erase-buffer)
                    (insert (mapconcat 'identity result-lines "\n"))
                    (write-region (point-min) (point-max) path)
                    (format "Successfully deleted %d line(s) matching pattern '%s' from %s"
                            deletions pattern path))
                (format "ERROR: Pattern '%s' not found in %s" pattern path))))
        (error (format "ERROR: Failed to delete pattern: %s" (error-message-string err))))))))

;;; Advanced Editing Tools

(defun ai-auto-complete-tool-edit-function (params)
  "Edit a specific function by name (works for various programming languages).
PARAMS should be an alist with 'path', 'function_name', and 'content' keys."
  (let ((path (cdr (assoc 'path params)))
        (function-name (cdr (assoc 'function_name params)))
        (content (cdr (assoc 'content params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not function-name) "ERROR: No function_name specified")
     ((not content) "ERROR: No content specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (with-temp-buffer
            (insert-file-contents path)
            (let ((lines (split-string (buffer-string) "\n" t))
                  (new-lines (split-string content "\n" t))
                  (start-line nil)
                  (end-line nil)
                  (line-num 0)
                  (brace-count 0)
                  (in-function nil))
              ;; Find function start and end
              (dolist (line lines)
                (setq line-num (1+ line-num))
                (cond
                 ;; Look for function definition patterns
                 ((and (not in-function)
                       (or (string-match (format "^\\s-*def\\s-+%s\\s-*(" function-name) line)  ; Python
                           (string-match (format "^\\s-*function\\s-+%s\\s-*(" function-name) line)  ; JavaScript
                           (string-match (format "^\\s-*(defun\\s-+%s\\s-*(" function-name) line)  ; Elisp
                           (string-match (format "\\s-+%s\\s-*(" function-name) line)))  ; General
                  (setq start-line line-num)
                  (setq in-function t)
                  (setq brace-count 0)
                  ;; Count braces/parens in the current line
                  (dolist (char (string-to-list line))
                    (cond ((memq char '(?\{ ?\()) (setq brace-count (1+ brace-count)))
                          ((memq char '(?\} ?\))) (setq brace-count (1- brace-count))))))
                 ;; If we're in the function, count braces to find the end
                 (in-function
                  (dolist (char (string-to-list line))
                    (cond ((memq char '(?\{ ?\()) (setq brace-count (1+ brace-count)))
                          ((memq char '(?\} ?\))) (setq brace-count (1- brace-count)))))
                  (when (<= brace-count 0)
                    (setq end-line line-num)
                    (setq in-function nil)))))

              (if (and start-line end-line)
                  (progn
                    ;; Replace the function
                    (let ((before-lines (cl-subseq lines 0 (1- start-line)))
                          (after-lines (cl-subseq lines end-line)))
                      (setq lines (append before-lines new-lines after-lines)))
                    ;; Write back to file
                    (erase-buffer)
                    (insert (mapconcat 'identity lines "\n"))
                    (write-region (point-min) (point-max) path)
                    (format "Successfully replaced function '%s' (lines %d-%d) in %s"
                            function-name start-line end-line path))
                (format "ERROR: Function '%s' not found in %s" function-name path))))
        (error (format "ERROR: Failed to edit function: %s" (error-message-string err))))))))

(defun ai-auto-complete-tool-edit-section (params)
  "Edit content between two markers/patterns.
PARAMS should be an alist with 'path', 'start_marker', 'end_marker', and 'content' keys."
  (let ((path (cdr (assoc 'path params)))
        (start-marker (cdr (assoc 'start_marker params)))
        (end-marker (cdr (assoc 'end_marker params)))
        (content (cdr (assoc 'content params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not start-marker) "ERROR: No start_marker specified")
     ((not end-marker) "ERROR: No end_marker specified")
     ((not content) "ERROR: No content specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (with-temp-buffer
            (insert-file-contents path)
            (let ((lines (split-string (buffer-string) "\n" t))
                  (new-lines (split-string content "\n" t))
                  (start-line nil)
                  (end-line nil)
                  (line-num 0))
              ;; Find start and end markers
              (dolist (line lines)
                (setq line-num (1+ line-num))
                (cond
                 ((and (not start-line) (string-match start-marker line))
                  (setq start-line line-num))
                 ((and start-line (not end-line) (string-match end-marker line))
                  (setq end-line line-num))))

              (if (and start-line end-line)
                  (progn
                    ;; Replace content between markers (exclusive of markers)
                    (let ((before-lines (cl-subseq lines 0 start-line))
                          (after-lines (cl-subseq lines (1- end-line))))
                      (setq lines (append before-lines new-lines after-lines)))
                    ;; Write back to file
                    (erase-buffer)
                    (insert (mapconcat 'identity lines "\n"))
                    (write-region (point-min) (point-max) path)
                    (format "Successfully replaced section between '%s' and '%s' (lines %d-%d) in %s"
                            start-marker end-marker start-line end-line path))
                (format "ERROR: Could not find both markers '%s' and '%s' in %s"
                        start-marker end-marker path))))
        (error (format "ERROR: Failed to edit section: %s" (error-message-string err))))))))

(defun ai-auto-complete-tool-apply-multiple-edits (params)
  "Apply multiple edits atomically to a file.
PARAMS should be an alist with 'path' and 'edits' keys.
'edits' should be a list of edit operations, each with 'type' and relevant parameters."
  (let ((path (cdr (assoc 'path params)))
        (edits (cdr (assoc 'edits params))))
    (cond
     ((not path) "ERROR: No path specified")
     ((not edits) "ERROR: No edits specified")
     ((not (file-exists-p path)) (format "ERROR: File %s does not exist" path))
     (t
      (condition-case err
          (let ((backup-content "")
                (results '()))
            ;; Create backup
            (with-temp-buffer
              (insert-file-contents path)
              (setq backup-content (buffer-string)))

            ;; Apply each edit
            (dolist (edit edits)
              (let ((edit-type (cdr (assoc 'type edit)))
                    (edit-params (copy-alist edit)))
                ;; Remove 'type' from params and add 'path'
                (setq edit-params (assq-delete-all 'type edit-params))
                (push (cons 'path path) edit-params)

                (condition-case edit-err
                    (let ((result
                           (cond
                            ((string= edit-type "insert_lines")
                             (ai-auto-complete-tool-insert-lines edit-params))
                            ((string= edit-type "delete_lines")
                             (ai-auto-complete-tool-delete-lines edit-params))
                            ((string= edit-type "replace_lines")
                             (ai-auto-complete-tool-replace-lines edit-params))
                            ((string= edit-type "insert_after_pattern")
                             (ai-auto-complete-tool-insert-after-pattern edit-params))
                            ((string= edit-type "insert_before_pattern")
                             (ai-auto-complete-tool-insert-before-pattern edit-params))
                            ((string= edit-type "replace_pattern")
                             (ai-auto-complete-tool-replace-pattern edit-params))
                            ((string= edit-type "delete_pattern")
                             (ai-auto-complete-tool-delete-pattern edit-params))
                            (t (format "ERROR: Unknown edit type '%s'" edit-type)))))
                      (push result results))
                  (error
                   ;; Restore backup on error
                   (with-temp-file path
                     (insert backup-content))
                   (error "Edit failed: %s. File restored to original state."
                          (error-message-string edit-err))))))

            (format "Successfully applied %d edits to %s:\n%s"
                    (length edits) path
                    (mapconcat 'identity (reverse results) "\n")))
        (error (format "ERROR: Failed to apply multiple edits: %s" (error-message-string err))))))))

;;; Tool Registration

(defun ai-auto-complete-register-enhanced-editing-tools ()
  "Register all enhanced editing tools."

  ;; Enhanced file reading tools
  (ai-auto-complete-register-tool
   "read_file_with_lines"
   "Read file content with line numbers for easy reference. Each line is prefixed with its line number (e.g., '   1: content'). Essential for surgical editing operations."
   #'ai-auto-complete-tool-read-file-with-lines
   '(("path" . "Path to the file to read")))

  (ai-auto-complete-register-tool
   "read_file_range"
   "Read a specific range of lines from a file with line numbers. Useful for examining specific sections before editing."
   #'ai-auto-complete-tool-read-file-range
   '(("path" . "Path to the file to read")
     ("start_line" . "Starting line number (1-indexed)")
     ("end_line" . "Ending line number (1-indexed)")))

  (ai-auto-complete-register-tool
   "get_file_info"
   "Get file metadata including size, line count, modification time, and permissions. Useful for understanding file structure before editing."
   #'ai-auto-complete-tool-get-file-info
   '(("path" . "Path to the file to analyze")))

  ;; Line-based editing tools
  (ai-auto-complete-register-tool
   "insert_lines"
   "Insert new lines at a specific position in the file. The content is inserted AFTER the specified line number. Use line 0 to insert at the beginning."
   #'ai-auto-complete-tool-insert-lines
   '(("path" . "Path to the file to modify")
     ("line_number" . "Line number after which to insert (0 for beginning)")
     ("content" . "Content to insert (can be multi-line)")))

  (ai-auto-complete-register-tool
   "delete_lines"
   "Delete a specific range of lines from a file. Both start_line and end_line are inclusive."
   #'ai-auto-complete-tool-delete-lines
   '(("path" . "Path to the file to modify")
     ("start_line" . "Starting line number to delete (1-indexed)")
     ("end_line" . "Ending line number to delete (1-indexed)")))

  (ai-auto-complete-register-tool
   "replace_lines"
   "Replace a specific range of lines with new content. Both start_line and end_line are inclusive."
   #'ai-auto-complete-tool-replace-lines
   '(("path" . "Path to the file to modify")
     ("start_line" . "Starting line number to replace (1-indexed)")
     ("end_line" . "Ending line number to replace (1-indexed)")
     ("content" . "New content to replace with (can be multi-line)")))

  (ai-auto-complete-register-tool
   "append_to_file"
   "Append content to the end of a file. Simple and safe way to add content without affecting existing content."
   #'ai-auto-complete-tool-append-to-file
   '(("path" . "Path to the file to modify")
     ("content" . "Content to append")))

  (ai-auto-complete-register-tool
   "prepend_to_file"
   "Prepend content to the beginning of a file. Useful for adding headers or initial content."
   #'ai-auto-complete-tool-prepend-to-file
   '(("path" . "Path to the file to modify")
     ("content" . "Content to prepend")))

  ;; Pattern-based editing tools
  (ai-auto-complete-register-tool
   "insert_after_pattern"
   "Insert content after the first line matching a regular expression pattern. Useful for adding content after specific markers or function definitions."
   #'ai-auto-complete-tool-insert-after-pattern
   '(("path" . "Path to the file to modify")
     ("pattern" . "Regular expression pattern to match")
     ("content" . "Content to insert after the matching line")))

  (ai-auto-complete-register-tool
   "insert_before_pattern"
   "Insert content before the first line matching a regular expression pattern. Useful for adding content before specific markers or sections."
   #'ai-auto-complete-tool-insert-before-pattern
   '(("path" . "Path to the file to modify")
     ("pattern" . "Regular expression pattern to match")
     ("content" . "Content to insert before the matching line")))

  (ai-auto-complete-register-tool
   "replace_pattern"
   "Replace lines matching a regular expression pattern with new content. By default replaces only the first match; set 'all' to true to replace all matches."
   #'ai-auto-complete-tool-replace-pattern
   '(("path" . "Path to the file to modify")
     ("pattern" . "Regular expression pattern to match")
     ("content" . "Content to replace matching lines with")
     ("all" . "Optional: true to replace all matches, false/omitted for first match only")))

  (ai-auto-complete-register-tool
   "delete_pattern"
   "Delete lines matching a regular expression pattern. By default deletes only the first match; set 'all' to true to delete all matches."
   #'ai-auto-complete-tool-delete-pattern
   '(("path" . "Path to the file to modify")
     ("pattern" . "Regular expression pattern to match")
     ("all" . "Optional: true to delete all matches, false/omitted for first match only")))

  ;; Advanced editing tools
  (ai-auto-complete-register-tool
   "edit_function"
   "Edit a specific function by name. Works with Python (def), JavaScript (function), Elisp (defun), and other languages. Replaces the entire function definition."
   #'ai-auto-complete-tool-edit-function
   '(("path" . "Path to the file to modify")
     ("function_name" . "Name of the function to edit")
     ("content" . "New function content (complete function definition)")))

  (ai-auto-complete-register-tool
   "edit_section"
   "Edit content between two marker patterns. Replaces everything between the start and end markers (exclusive of the markers themselves)."
   #'ai-auto-complete-tool-edit-section
   '(("path" . "Path to the file to modify")
     ("start_marker" . "Regular expression pattern for the start marker")
     ("end_marker" . "Regular expression pattern for the end marker")
     ("content" . "New content to place between the markers")))

  (ai-auto-complete-register-tool
   "apply_multiple_edits"
   "Apply multiple editing operations atomically to a file. If any edit fails, the file is restored to its original state. Each edit should specify 'type' and relevant parameters."
   #'ai-auto-complete-tool-apply-multiple-edits
   '(("path" . "Path to the file to modify")
     ("edits" . "List of edit operations, each with 'type' and parameters"))))

;; Register enhanced editing tools when this module is loaded
(ai-auto-complete-register-enhanced-editing-tools)

(provide 'tools/enhanced-editing-tools)
;;; enhanced-editing-tools.el ends here

