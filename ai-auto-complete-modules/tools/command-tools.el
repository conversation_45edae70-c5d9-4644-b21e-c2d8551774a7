;;; command-tools.el --- Command suggestion tools for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides command suggestion tools for the AI Auto Complete package.
;; These tools help suggest and document Linux commands for various tasks.

;;; Code:

;; Try to load tools-core, but handle failure gracefully
(condition-case err
    (require 'tools/tools-core)
  (error
   (message "Warning: Failed to load tools/tools-core: %s" (error-message-string err))
   (load (expand-file-name "tools-core.el"
                          (file-name-directory (or load-file-name buffer-file-name))))))

(require 'cl-lib)

;; Provide fallback for delete-duplicates if not available
(unless (fboundp 'delete-duplicates)
  (defun delete-duplicates (list &rest args)
    "Delete duplicates in LIST.
Optional arguments are keyword value pairs.
Supported keywords are :test and :key.
:test is a function that takes two arguments and returns non-nil if they are
considered equal. The default is `equal'.
:key is a function to extract the part of each element to compare; it must be
a function that takes one argument. The default is `identity'."
    (let ((test (or (plist-get args :test) 'equal))
          (key (or (plist-get args :key) 'identity))
          (result nil))
      (dolist (elt list)
        (let ((key-val (funcall key elt)))
          (unless (cl-find key-val result :test test :key key)
            (push elt result))))
      (nreverse result))))

;; Database of Linux commands with descriptions and examples
(defvar ai-auto-complete-linux-command-database
  '(("find" . (:description "Search for files in a directory hierarchy"
               :category "file-search"
               :examples (("Find all .txt files" . "find . -name \"*.txt\"")
                          ("Find files modified in the last 24 hours" . "find . -mtime -1")
                          ("Find files larger than 10MB" . "find . -size +10M")
                          ("Find and delete empty files" . "find . -type f -empty -delete"))))

    ("grep" . (:description "Search for patterns in files"
               :category "text-search"
               :examples (("Search for a word in files" . "grep \"word\" file.txt")
                          ("Recursive case-insensitive search" . "grep -ri \"pattern\" .")
                          ("Show only filenames with matches" . "grep -l \"pattern\" *")
                          ("Show line numbers with matches" . "grep -n \"pattern\" file.txt"))))

    ("awk" . (:description "Pattern scanning and text processing language"
              :category "text-processing"
              :examples (("Print specific columns" . "awk '{print $1, $3}' file.txt")
                         ("Sum values in a column" . "awk '{sum += $1} END {print sum}' file.txt")
                         ("Filter lines by condition" . "awk '$3 > 100' file.txt")
                         ("Replace text in a file" . "awk '{gsub(/old/, \"new\"); print}' file.txt"))))

    ("sed" . (:description "Stream editor for filtering and transforming text"
              :category "text-processing"
              :examples (("Replace text in a file" . "sed 's/old/new/g' file.txt")
                         ("Delete lines matching a pattern" . "sed '/pattern/d' file.txt")
                         ("Print specific lines by number" . "sed -n '10,20p' file.txt")
                         ("Insert text at beginning of each line" . "sed 's/^/prefix/' file.txt"))))

    ("ls" . (:description "List directory contents"
             :category "file-management"
             :examples (("List all files including hidden" . "ls -la")
                        ("List files with human-readable sizes" . "ls -lh")
                        ("Sort files by size" . "ls -lS")
                        ("Sort files by modification time" . "ls -lt"))))

    ("cp" . (:description "Copy files and directories"
             :category "file-management"
             :examples (("Copy a file" . "cp source.txt destination.txt")
                        ("Copy a directory recursively" . "cp -r source_dir destination_dir")
                        ("Copy preserving attributes" . "cp -a source.txt destination.txt")
                        ("Copy with interactive prompt" . "cp -i source.txt destination.txt"))))

    ("mv" . (:description "Move (rename) files"
             :category "file-management"
             :examples (("Rename a file" . "mv oldname.txt newname.txt")
                        ("Move a file to another directory" . "mv file.txt /path/to/directory/")
                        ("Move with interactive prompt" . "mv -i source.txt destination.txt")
                        ("Move multiple files to a directory" . "mv file1.txt file2.txt directory/"))))

    ("rm" . (:description "Remove files or directories"
             :category "file-management"
             :examples (("Remove a file" . "rm file.txt")
                        ("Remove a directory recursively" . "rm -r directory/")
                        ("Force removal without prompt" . "rm -f file.txt")
                        ("Remove with interactive prompt" . "rm -i file.txt"))))

    ("mkdir" . (:description "Create directories"
                :category "file-management"
                :examples (("Create a directory" . "mkdir directory")
                           ("Create parent directories as needed" . "mkdir -p parent/child/grandchild")
                           ("Create directory with specific permissions" . "mkdir -m 755 directory")
                           ("Create multiple directories" . "mkdir dir1 dir2 dir3"))))

    ("chmod" . (:description "Change file mode bits (permissions)"
                :category "file-management"
                :examples (("Make a file executable" . "chmod +x file.sh")
                           ("Set specific permissions" . "chmod 644 file.txt")
                           ("Change permissions recursively" . "chmod -R 755 directory/")
                           ("Add write permission for group" . "chmod g+w file.txt"))))

    ("chown" . (:description "Change file owner and group"
                :category "file-management"
                :examples (("Change owner of a file" . "chown user file.txt")
                           ("Change owner and group" . "chown user:group file.txt")
                           ("Change ownership recursively" . "chown -R user:group directory/")
                           ("Change only the group" . "chown :group file.txt"))))

    ("tar" . (:description "Archive files"
              :category "archiving"
              :examples (("Create a tar archive" . "tar -cf archive.tar files")
                         ("Create a compressed tar archive" . "tar -czf archive.tar.gz files")
                         ("Extract a tar archive" . "tar -xf archive.tar")
                         ("List contents of a tar archive" . "tar -tf archive.tar"))))

    ("zip" . (:description "Package and compress files"
              :category "archiving"
              :examples (("Create a zip archive" . "zip archive.zip file1 file2")
                         ("Create a zip archive from a directory" . "zip -r archive.zip directory/")
                         ("Update files in a zip archive" . "zip -u archive.zip file")
                         ("Add password protection" . "zip -e archive.zip file"))))

    ("unzip" . (:description "Extract files from a ZIP archive"
                :category "archiving"
                :examples (("Extract a zip archive" . "unzip archive.zip")
                           ("List contents of a zip archive" . "unzip -l archive.zip")
                           ("Extract to a specific directory" . "unzip archive.zip -d directory/")
                           ("Extract a specific file" . "unzip archive.zip file"))))

    ("ps" . (:description "Report process status"
             :category "process-management"
             :examples (("Show all processes" . "ps aux")
                        ("Show process tree" . "ps -ejH")
                        ("Show processes for a specific user" . "ps -u username")
                        ("Show processes by resource usage" . "ps aux --sort=-%cpu"))))

    ("top" . (:description "Display and update sorted information about processes"
              :category "process-management"
              :examples (("Show interactive process viewer" . "top")
                         ("Show processes for a specific user" . "top -u username")
                         ("Sort processes by memory usage" . "top -o %MEM")
                         ("Update every 5 seconds" . "top -d 5"))))

    ("kill" . (:description "Send a signal to a process"
               :category "process-management"
               :examples (("Terminate a process" . "kill PID")
                          ("Force kill a process" . "kill -9 PID")
                          ("Send a specific signal" . "kill -SIGTERM PID")
                          ("Kill all processes by name" . "killall process_name"))))

    ("df" . (:description "Report file system disk space usage"
             :category "system-info"
             :examples (("Show disk usage" . "df")
                        ("Show human-readable sizes" . "df -h")
                        ("Show specific filesystem type" . "df -t ext4")
                        ("Show inodes usage" . "df -i"))))

    ("du" . (:description "Estimate file space usage"
             :category "system-info"
             :examples (("Show directory size" . "du -sh directory/")
                        ("Show sizes of subdirectories" . "du -h --max-depth=1")
                        ("Sort directories by size" . "du -h | sort -hr")
                        ("Show total size only" . "du -sh"))))

    ("free" . (:description "Display amount of free and used memory"
               :category "system-info"
               :examples (("Show memory usage" . "free")
                          ("Show human-readable sizes" . "free -h")
                          ("Show in megabytes" . "free -m")
                          ("Show in gigabytes" . "free -g"))))

    ("wget" . (:description "Non-interactive network downloader"
               :category "networking"
               :examples (("Download a file" . "wget URL")
                          ("Download to a specific filename" . "wget -O filename URL")
                          ("Download in background" . "wget -b URL")
                          ("Resume interrupted download" . "wget -c URL"))))

    ("curl" . (:description "Transfer data from or to a server"
               :category "networking"
               :examples (("Download a file" . "curl -O URL")
                          ("Send HTTP request" . "curl -X POST URL")
                          ("Send data with request" . "curl -d \"data\" URL")
                          ("Include headers in output" . "curl -i URL"))))

    ("ssh" . (:description "OpenSSH SSH client (remote login program)"
              :category "networking"
              :examples (("Connect to a remote server" . "ssh user@hostname")
                         ("Connect with specific port" . "ssh -p 2222 user@hostname")
                         ("Run a command on remote server" . "ssh user@hostname command")
                         ("Use a specific identity file" . "ssh -i key.pem user@hostname"))))

    ("scp" . (:description "Secure copy (remote file copy program)"
              :category "networking"
              :examples (("Copy file to remote server" . "scp file.txt user@hostname:/path/")
                         ("Copy file from remote server" . "scp user@hostname:/path/file.txt .")
                         ("Copy directory recursively" . "scp -r directory/ user@hostname:/path/")
                         ("Copy with specific port" . "scp -P 2222 file.txt user@hostname:/path/"))))

    ("rsync" . (:description "Fast, versatile file copying tool"
                :category "networking"
                :examples (("Sync files to a directory" . "rsync -av source/ destination/")
                           ("Sync files to a remote server" . "rsync -av source/ user@hostname:/path/")
                           ("Sync with deletion" . "rsync -av --delete source/ destination/")
                           ("Dry run to see what would be transferred" . "rsync -av --dry-run source/ destination/"))))

    ("git" . (:description "The stupid content tracker (version control)"
              :category "development"
              :examples (("Clone a repository" . "git clone URL")
                         ("Commit changes" . "git commit -m \"message\"")
                         ("Push changes to remote" . "git push origin branch")
                         ("Pull changes from remote" . "git pull origin branch"))))

    ("make" . (:description "GNU make utility to maintain groups of programs"
               :category "development"
               :examples (("Build using Makefile" . "make")
                          ("Build a specific target" . "make target")
                          ("Clean build files" . "make clean")
                          ("Use multiple jobs" . "make -j4"))))

    ("gcc" . (:description "GNU C and C++ compiler"
              :category "development"
              :examples (("Compile a C program" . "gcc -o output source.c")
                         ("Compile with optimization" . "gcc -O2 -o output source.c")
                         ("Compile with debugging symbols" . "gcc -g -o output source.c")
                         ("Compile multiple source files" . "gcc -o output source1.c source2.c"))))

    ("python" . (:description "Python interpreter"
                 :category "development"
                 :examples (("Run a Python script" . "python script.py")
                            ("Run Python interactively" . "python")
                            ("Run a module" . "python -m module_name")
                            ("Check syntax without running" . "python -m py_compile script.py"))))

    ("npm" . (:description "Node.js package manager"
              :category "development"
              :examples (("Install a package" . "npm install package_name")
                         ("Install dependencies from package.json" . "npm install")
                         ("Run a script" . "npm run script_name")
                         ("Initialize a new project" . "npm init"))))

    ("docker" . (:description "Container runtime and management"
                 :category "virtualization"
                 :examples (("Run a container" . "docker run image_name")
                            ("List running containers" . "docker ps")
                            ("Build an image" . "docker build -t image_name .")
                            ("Stop a container" . "docker stop container_id"))))

    ("systemctl" . (:description "Control the systemd system and service manager"
                    :category "system-management"
                    :examples (("Start a service" . "systemctl start service_name")
                               ("Stop a service" . "systemctl stop service_name")
                               ("Enable a service at boot" . "systemctl enable service_name")
                               ("Check service status" . "systemctl status service_name"))))

    ("journalctl" . (:description "Query the systemd journal"
                     :category "system-management"
                     :examples (("View system logs" . "journalctl")
                                ("View logs for a specific service" . "journalctl -u service_name")
                                ("View logs since boot" . "journalctl -b")
                                ("Follow logs in real-time" . "journalctl -f"))))

    ("cron" . (:description "Schedule periodic tasks"
               :category "system-management"
               :examples (("Edit crontab" . "crontab -e")
                          ("List crontab entries" . "crontab -l")
                          ("Remove all crontab entries" . "crontab -r")
                          ("Example crontab entry" . "0 * * * * command"))))

    ("at" . (:description "Schedule commands to be executed once"
             :category "system-management"
             :examples (("Run a command at a specific time" . "at 10:00")
                        ("Run a command tomorrow" . "at 10:00 tomorrow")
                        ("List scheduled jobs" . "atq")
                        ("Remove a scheduled job" . "atrm job_number"))))
    )
  "Database of Linux commands with descriptions and examples.")

;; Function to extract keywords from a task description
;; (ai-auto-complete-extract-keywords
;;  "Quickly find all large log files in the /var/log directory and show their sizes.")
(defun ai-auto-complete-extract-keywords (task)
  "Extract keywords from TASK description."
  (let ((keywords '())
        (stop-words '("a" "an" "the" "and" "or" "but" "in" "on" "at" "to" "for" "with" "by" "about" "as" "of")))
    ;; Split task into words
    (dolist (word (split-string task "[ \t\n,.;:!?'\"]+" t))
      (let ((word-lower (downcase word)))
        ;; Skip stop words and short words
        (unless (or (member word-lower stop-words)
                    (< (length word-lower) 3))
          (push word-lower keywords))))

    ;; Return unique keywords
    (delete-duplicates keywords :test 'string=)))

;; Function to suggest commands based on a task description
;; (ai-auto-complete-tool-suggest-commands
;;  '((task . "find all text files in the current directory")
;;    (category . "file-search")
;;    (num_suggestions . 3)
;;    (execute . nil)))
(defun ai-auto-complete-tool-suggest-commands (params)
  "Suggest Linux commands based on a description of the task.
PARAMS should be an alist with:
- 'task': Description of the task to accomplish
- 'category': Optional category to filter commands (default: all)
- 'num_suggestions': Optional number of suggestions to return (default: 5)
- 'execute': Optional boolean to execute the top suggestion (default: nil)"
  (message "DEBUG: suggest_commands called with params: %S" params)
  (let* ((task (cdr (assoc 'task params)))
         (category (cdr (assoc 'category params)))
         (num-suggestions (or (cdr (assoc 'num_suggestions params)) 5))
         (execute (cdr (assoc 'execute params)))
         (keywords (ai-auto-complete-extract-keywords task))
         (suggestions '())
         (result "# Command Suggestions\n\n"))

    ;; Check if task is provided
    (if (not task)
        (progn
          (message "DEBUG: suggest_commands - No task description provided")
          "ERROR: No task description provided")

      ;; Debug info
      (message "DEBUG: suggest_commands - Task: %s" task)
      (message "DEBUG: suggest_commands - Keywords: %s" keywords)
      (message "DEBUG: suggest_commands - Category filter: %s" category)

      ;; Check if command database is initialized
      (unless (hash-table-p ai-auto-complete-linux-command-database)
        (message "DEBUG: suggest_commands - Command database not initialized, initializing now")
        (let ((command-hash (make-hash-table :test 'equal)))
          (dolist (command-entry ai-auto-complete-linux-command-database)
            (puthash (car command-entry) (cdr command-entry) command-hash))
          (setq ai-auto-complete-linux-command-database command-hash)))

      ;; Find matching commands
      (condition-case err
          (progn
            (maphash (lambda (command-name command-info)
                       (let* ((description (plist-get command-info :description))
                              (cmd-category (plist-get command-info :category))
                              (examples (plist-get command-info :examples))
                              (score 0))

                         ;; Skip if category filter is applied and doesn't match
                         (when (or (not category) (string= category cmd-category))
                           ;; Score the command based on keyword matches
                           (dolist (keyword keywords)
                             (when (or (string-match-p keyword command-name)
                                       (string-match-p keyword description))
                               (setq score (1+ score))))

                           ;; Add to suggestions if score > 0
                           (when (> score 0)
                             (push (list command-name score command-info) suggestions)))))
                     ai-auto-complete-linux-command-database)

            ;; Sort suggestions by score
            (setq suggestions (sort suggestions (lambda (a b) (> (nth 1 a) (nth 1 b)))))

            ;; Limit to requested number
            (when (> (length suggestions) num-suggestions)
              (setq suggestions (cl-subseq suggestions 0 num-suggestions)))

            ;; Format the result
            (setq result (concat result "Based on your task description: \"" task "\"\n\n"))

            (if (null suggestions)
                (setq result (concat result "No specific commands found for this task. Consider using general-purpose tools like:\n\n- `find` for searching files\n- `grep` for searching content\n- `awk` or `sed` for text processing\n"))

              ;; Add each suggestion
              (dolist (suggestion suggestions)
                (let* ((command-name (nth 0 suggestion))
                       (command-info (nth 2 suggestion))
                       (description (plist-get command-info :description))
                       (examples (plist-get command-info :examples)))

                  (setq result (concat result "## " command-name "\n\n" description "\n\n### Examples:\n\n"))

                  ;; Add examples
                  (dolist (example examples)
                    (setq result (concat result "- " (car example) ":\n  ```bash\n  " (cdr example) "\n  ```\n\n")))))

              ;; Execute top suggestion if requested
              (when (and execute suggestions)
                (condition-case err
                    (let* ((top-command (car (nth 0 suggestions)))
                           (example (cdr (car (plist-get (nth 2 (car suggestions)) :examples))))
                           (output (shell-command-to-string example)))
                      (setq result (concat result "## Executed Command\n\n```bash\n" example "\n```\n\n### Output:\n\n```\n" output "\n```\n\n")))
                  (error
                   (let ((error-msg (format "ERROR: Failed to execute command: %s" (error-message-string err))))
                     (message "DEBUG: suggest_commands - %s" error-msg)
                     (setq result (concat result "## Execution Error\n\n" error-msg "\n\n"))))))))

        (error
         (let ((error-msg (format "ERROR: Failed to suggest commands: %s" (error-message-string err))))
           (message "DEBUG: suggest_commands - %s" error-msg)
           (setq result (concat result error-msg "\n\n")))))

      ;; Return the result
      (message "DEBUG: suggest_commands - Returning result of length: %s" (length result))
      result)))

;; Function to get command documentation
;; (ai-auto-complete-tool-get-command-doc
;;  '((command . "ls")))
(defun ai-auto-complete-tool-get-command-doc (params)
  "Get detailed documentation for a specific command.
PARAMS should be an alist with:
- 'command': The command to get documentation for"
  (message "DEBUG: get_command_doc called with params: %S" params)
  (let* ((command (cdr (assoc 'command params)))
         (result "# Command Documentation\n\n"))

    ;; Check if command is provided
    (if (not command)
        (progn
          (message "DEBUG: get_command_doc - No command specified")
          "ERROR: No command specified")

      (message "DEBUG: get_command_doc - Getting documentation for command: %s" command)

      ;; Check if command database is initialized
      (unless (hash-table-p ai-auto-complete-linux-command-database)
        (message "DEBUG: get_command_doc - Command database not initialized, initializing now")
        (let ((command-hash (make-hash-table :test 'equal)))
          (dolist (command-entry ai-auto-complete-linux-command-database)
            (puthash (car command-entry) (cdr command-entry) command-hash))
          (setq ai-auto-complete-linux-command-database command-hash)))

      ;; Get command info from database
      (condition-case err
          (let ((command-info (gethash command ai-auto-complete-linux-command-database)))
            (if (not command-info)
                ;; Command not in database, try to get man page
                (progn
                  (message "DEBUG: get_command_doc - Command not in database, trying man page")
                  (condition-case err
                      (let ((man-output (shell-command-to-string (format "man -P cat %s 2>/dev/null | head -n 100" command))))
                        (if (string-match-p "No manual entry for" man-output)
                            (let ((error-msg (format "Command '%s' not found in database and no man page available." command)))
                              (message "DEBUG: get_command_doc - %s" error-msg)
                              (setq result (concat result error-msg)))
                          (setq result (concat result "## " command "\n\n"
                                               "```\n"
                                               (if (> (length man-output) 2000)
                                                   (concat (substring man-output 0 2000) "\n... [Output truncated] ...")
                                                 man-output)
                                               "\n```\n\n"
                                               "For the full documentation, use the `man " command "` command."))))
                    (error
                     (let ((error-msg (format "ERROR: Failed to get man page: %s" (error-message-string err))))
                       (message "DEBUG: get_command_doc - %s" error-msg)
                       (setq result (concat result error-msg))))))

              ;; Command in database
              (progn
                (message "DEBUG: get_command_doc - Command found in database")
                (let ((description (plist-get command-info :description))
                      (category (plist-get command-info :category))
                      (examples (plist-get command-info :examples)))

                  (setq result (concat result "## " command "\n\n"
                                       "**Description:** " description "\n\n"
                                       "**Category:** " category "\n\n"
                                       "### Common Usage Examples:\n\n"))

                  ;; Add examples
                  (dolist (example examples)
                    (setq result (concat result "- " (car example) ":\n  ```bash\n  " (cdr example) "\n  ```\n\n")))

                  ;; Add man page excerpt if available
                  (condition-case err
                      (let ((man-output (shell-command-to-string (format "man -P cat %s 2>/dev/null | head -n 50" command))))
                        (unless (string-match-p "No manual entry for" man-output)
                          (setq result (concat result "### Man Page Excerpt:\n\n"
                                               "```\n"
                                               (if (> (length man-output) 1000)
                                                   (concat (substring man-output 0 1000) "\n... [Output truncated] ...")
                                                 man-output)
                                               "\n```\n\n"
                                               "For the full documentation, use the `man " command "` command."))))
                    (error
                     (message "DEBUG: get_command_doc - Failed to get man page excerpt: %s" (error-message-string err))))))))

        (error
         (let ((error-msg (format "ERROR: Failed to get command documentation: %s" (error-message-string err))))
           (message "DEBUG: get_command_doc - %s" error-msg)
           (setq result (concat result error-msg)))))

      ;; Return the result
      (message "DEBUG: get_command_doc - Returning result of length: %s" (length result))
      result)))

;; Function to list command categories
(defun ai-auto-complete-tool-list-command-categories (params)
  "List available command categories.
PARAMS is ignored."
  (message "DEBUG: list_command_categories called with params: %S" params)
  (let ((categories (make-hash-table :test 'equal))
        (result "# Command Categories\n\n"))

    ;; Check if command database is initialized
    (unless (hash-table-p ai-auto-complete-linux-command-database)
      (message "DEBUG: list_command_categories - Command database not initialized, initializing now")
      (let ((command-hash (make-hash-table :test 'equal)))
        (dolist (command-entry ai-auto-complete-linux-command-database)
          (puthash (car command-entry) (cdr command-entry) command-hash))
        (setq ai-auto-complete-linux-command-database command-hash)))

    ;; Count commands in each category
    (condition-case err
        (progn
          (maphash (lambda (command-name command-info)
                     (let ((category (plist-get command-info :category)))
                       (when category
                         (puthash category
                                  (1+ (or (gethash category categories) 0))
                                  categories))))
                   ai-auto-complete-linux-command-database)

          ;; Convert to list and sort
          (let ((category-list '()))
            (maphash (lambda (category count)
                       (push (cons category count) category-list))
                     categories)

            (setq category-list (sort category-list (lambda (a b) (string< (car a) (car b)))))

            ;; Format the result
            (setq result (concat result "| Category | Command Count | Description |\n"
                                 "|----------|---------------|-------------|\n"))

            (dolist (category category-list)
              (let ((category-name (car category))
                    (count (cdr category))
                    (description
                     (cond
                      ((string= (car category) "file-management") "Commands for managing files and directories")
                      ((string= (car category) "text-processing") "Commands for processing and manipulating text")
                      ((string= (car category) "file-search") "Commands for finding files and content")
                      ((string= (car category) "text-search") "Commands for searching text in files")
                      ((string= (car category) "archiving") "Commands for compressing and archiving files")
                      ((string= (car category) "process-management") "Commands for managing processes")
                      ((string= (car category) "system-info") "Commands for system information and monitoring")
                      ((string= (car category) "networking") "Commands for network operations")
                      ((string= (car category) "development") "Commands for software development")
                      ((string= (car category) "virtualization") "Commands for containers and virtual machines")
                      ((string= (car category) "system-management") "Commands for system administration")
                      (t "Miscellaneous commands"))))

                (setq result (concat result "| " category-name " | " (number-to-string count) " | " description " |\n"))))))

      (error
       (let ((error-msg (format "ERROR: Failed to list command categories: %s" (error-message-string err))))
         (message "DEBUG: list_command_categories - %s" error-msg)
         (setq result (concat result error-msg)))))

    ;; Return the result
    (message "DEBUG: list_command_categories - Returning result of length: %s" (length result))
    (concat result "\n\nUse the `suggest_commands` tool with the `category` parameter to filter suggestions by category.")))

;; Register command tools
(defun ai-auto-complete-register-command-tools ()
  "Register command suggestion tools."
  ;; Initialize command database hash table
  (let ((command-hash (make-hash-table :test 'equal)))
    (dolist (command-entry ai-auto-complete-linux-command-database)
      (puthash (car command-entry) (cdr command-entry) command-hash))
    (setq ai-auto-complete-linux-command-database command-hash))

  ;; Suggest commands
  (ai-auto-complete-register-tool
   "suggest_commands"
   "Suggest Linux commands based on a task description"
   #'ai-auto-complete-tool-suggest-commands
   '(("task" . "Description of the task to accomplish")
     ("category" . "Optional category to filter commands")
     ("num_suggestions" . "Optional number of suggestions to return")
     ("execute" . "Optional boolean to execute the top suggestion")))

  ;; Get command documentation
  (ai-auto-complete-register-tool
   "get_command_doc"
   "Get detailed documentation for a specific command"
   #'ai-auto-complete-tool-get-command-doc
   '(("command" . "The command to get documentation for")))

  ;; List command categories
  (ai-auto-complete-register-tool
   "list_command_categories"
   "List available command categories"
   #'ai-auto-complete-tool-list-command-categories
   '()))

;; Test function for command tools
(defun ai-auto-complete-command-tools-test ()
  "Test the command suggestion tools functionality.
This function sends a prompt that asks the LLM to use the command tools
to suggest commands for various tasks."
  (interactive)
  ;; Enable tools and debug mode
  (unless ai-auto-complete-tools-enabled
    (ai-auto-complete-tools-enable))
  (setq ai-auto-complete-tools-debug-mode t)

  ;; Create a test prompt that will trigger command tool usage
  (let ((test-prompt "I need you to help me find useful Linux commands:

1. First, list all available command categories using the list_command_categories tool
2. Then, suggest commands for finding large files on my system using the suggest_commands tool
3. Get detailed documentation for the 'find' command using the get_command_doc tool
4. Suggest commands for monitoring system resources
5. Suggest commands for working with compressed files

Please make these tool calls in sequence, analyzing the results of each before proceeding to the next."))

    (message "Sending command tools test prompt to LLM")

    ;; Send the prompt to the current backend
    (let ((backend (ai-auto-complete-get-current-backend)))
      (message "Using backend: %s" backend)
      (ai-auto-complete-complete
       backend
       test-prompt
       nil
       (lambda (response)
         (message "Received final response from LLM for command tools test")
         (with-current-buffer (get-buffer-create "*AI Command Tools Test*")
           (erase-buffer)
           (insert "Command Tools Test Results:\n\n")
           (insert "The following response demonstrates the application's command suggestion capabilities.\n\n")
           (insert "Test prompt:\n")
           (insert test-prompt)
           (insert "\n\nResponse:\n")
           (insert response)
           (display-buffer (current-buffer))))
           "code")))

  (message "Command tools test initiated. Results will appear in the *AI Command Tools Test* buffer."))

;; Register command tools when this module is loaded
(ai-auto-complete-register-command-tools)

(provide 'tools/command-tools)
;;; command-tools.el ends here
