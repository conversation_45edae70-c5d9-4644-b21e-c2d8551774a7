;;; terminal-tools.el --- Interactive terminal tools for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides interactive terminal tools for the AI Auto Complete package.
;; These tools allow the LLM to create and interact with terminal sessions.

;;; Code:

;; Try to load tools-core, but handle failure gracefully
(condition-case nil
    (require 'tools/tools-core)
  (error
   (message "Warning: Failed to load tools/tools-core")
   (load (expand-file-name "tools-core.el"
                          (file-name-directory (or load-file-name buffer-file-name))))))
(require 'term)
(require 'cl-lib)

;; Variables for terminal management
(defvar ai-auto-complete-interactive-terminals (make-hash-table :test 'equal)
  "Hash table of interactive terminals, mapping terminal IDs to their buffers and processes.")

(defvar ai-auto-complete-terminal-counter 0
  "Counter for generating unique terminal IDs.")

(defcustom ai-auto-complete-terminal-save-history t
  "Whether to save terminal output history for reference."
  :type 'boolean
  :group 'ai-auto-complete-tools)

(defcustom ai-auto-complete-terminal-history-limit 1000
  "Maximum number of lines to keep in terminal history."
  :type 'integer
  :group 'ai-auto-complete-tools)

(defvar ai-auto-complete-terminal-history (make-hash-table :test 'equal)
  "Hash table of terminal output history, mapping terminal IDs to their output history.")

(defcustom ai-auto-complete-terminal-default-shell "bash"
  "Default shell to use for terminals."
  :type 'string
  :group 'ai-auto-complete-tools)

(defcustom ai-auto-complete-terminal-timeout 30
  "Default timeout in seconds for terminal operations."
  :type 'integer
  :group 'ai-auto-complete-tools)

;; Terminal sentinel function
;; Note: This function is a process sentinel and is typically called by Emacs, not directly.
;; Example 1: Conceptual call for a process finishing
;; (ai-auto-complete-terminal-sentinel (get-process "my-terminal-process") "finished\n")
;; Example 2: Conceptual call for a process being killed
;; (ai-auto-complete-terminal-sentinel (get-process "another-term-proc") "killed\n")
(defun ai-auto-complete-terminal-sentinel (process event)
  "Handle terminal process events.
PROCESS is the terminal process.
EVENT is the process event."
  (message "DEBUG: terminal_sentinel - Process event: %s" event)
  (let ((event-str (string-trim event))
        (term-id nil))
    ;; Find the terminal ID for this process
    (maphash (lambda (id info)
               (when (eq process (plist-get info :process))
                 (setq term-id id)))
             ai-auto-complete-interactive-terminals)

    ;; Log the event
    (message "DEBUG: terminal_sentinel - Terminal %s: %s" (or term-id "unknown") event-str)

    ;; Handle process exit
    (when (string-match-p "\\(finished\\|exited\\|killed\\)" event-str)
      (message "DEBUG: terminal_sentinel - Terminal %s process has terminated" (or term-id "unknown"))
      (when term-id
        (let ((info (gethash term-id ai-auto-complete-interactive-terminals)))
          (when info
            ;; Mark terminal as terminated
            (puthash term-id
                     (plist-put info :terminated t)
                     ai-auto-complete-interactive-terminals)
            (message "DEBUG: terminal_sentinel - Terminal %s marked as terminated" term-id)

            ;; Add termination message to buffer
            (when-let ((buffer (plist-get info :buffer)))
              (when (buffer-live-p buffer)
                (with-current-buffer buffer
                  (let ((inhibit-read-only t))
                    (goto-char (point-max))
                    (insert (format "\n\n[Terminal process %s]\n" event-str))
                    (message "DEBUG: terminal_sentinel - Added termination message to buffer")))))))))))

;; Helper function to validate terminal ID
;; Example 1: Validate an existing terminal ID (assuming "term-0" exists)
;; (ai-auto-complete-terminal-validate-id "term-0")
;; Example 2: Validate a non-existent terminal ID
;; (ai-auto-complete-terminal-validate-id "term-999")
(defun ai-auto-complete-terminal-validate-id (terminal-id)
  "Validate TERMINAL-ID and return terminal info if valid.
Returns nil if invalid."
  (message "DEBUG: terminal_validate_id - Validating terminal ID: %s" (or terminal-id "nil"))
  (if (not terminal-id)
      (progn
        (message "DEBUG: terminal_validate_id - No terminal ID provided")
        nil)
    (let ((terminal-info (gethash terminal-id ai-auto-complete-interactive-terminals)))
      (if (not terminal-info)
          (progn
            (message "DEBUG: terminal_validate_id - Terminal ID not found: %s" terminal-id)
            nil)
        (message "DEBUG: terminal_validate_id - Terminal ID valid: %s" terminal-id)
        terminal-info))))

;; Helper function to check if terminal is running
;; Example 1: Check if a running terminal is running (assuming 'term-info-running' holds valid info for a live terminal)
;; (let ((term-info-running (gethash "term-0" ai-auto-complete-interactive-terminals)))
;;   (if term-info-running (ai-auto-complete-terminal-is-running term-info-running)))
;; Example 2: Check if a terminated terminal is running (assuming 'term-info-stopped' holds info for a terminated terminal)
;; (let ((term-info-stopped (plist-put (gethash "term-1" ai-auto-complete-interactive-terminals) :terminated t)))
;;   (if term-info-stopped (ai-auto-complete-terminal-is-running term-info-stopped)))
(defun ai-auto-complete-terminal-is-running (terminal-info)
  "Check if terminal with TERMINAL-INFO is running."
  (let* ((process (plist-get terminal-info :process))
         (terminated (plist-get terminal-info :terminated))
         (running (and process (process-live-p process) (not terminated))))
    (message "DEBUG: terminal_is_running - Terminal running: %s" (if running "yes" "no"))
    running))

;; Create an interactive terminal
;; Example 1: Create a terminal with a custom name and initial command, and show it
;; (ai-auto-complete-tool-create-terminal
;;  '((name . "my-dev-terminal") (command . "htop") (show . t)))
;; Example 2: Create a terminal in a specific directory, don't show it immediately
;; (ai-auto-complete-tool-create-terminal
;;  '((directory . "/tmp")
;;    (name . "temp-work")
;;    (show . nil)))
(defun ai-auto-complete-tool-create-terminal (params)
  "Create an interactive terminal.
PARAMS should be an alist with:
- 'name': Optional name for the terminal (default: auto-generated)
- 'directory': Optional working directory (default: current directory)
- 'command': Optional initial command to run (default: bash)
- 'show': Optional boolean to show the terminal buffer (default: t)"
  (message "DEBUG: create_terminal called with params: %S" params)
  (condition-case err
      (let* ((name (or (cdr (assoc 'name params)) (format "ai-terminal-%d" ai-auto-complete-terminal-counter)))
             (directory (or (cdr (assoc 'directory params)) default-directory))
             (command (or (cdr (assoc 'command params)) ai-auto-complete-terminal-default-shell))
             (show (if (assoc 'show params) (cdr (assoc 'show params)) t))
             (buffer-name (format "*%s*" name))
             (terminal-id (format "term-%d" ai-auto-complete-terminal-counter))
             (buffer (get-buffer-create buffer-name))
             (process nil))
        
        (message "DEBUG: create_terminal - Name: %s, Directory: %s, Command: %s, Show: %s" 
                 name directory command (if show "yes" "no"))
        
        ;; Ensure directory exists and is accessible
        (if (not (file-directory-p directory))
            (progn
              (message "DEBUG: create_terminal - Directory does not exist: %s" directory)
              (format "ERROR: Directory '%s' does not exist or is not accessible" directory))
          
          ;; Set up the terminal buffer
          (message "DEBUG: create_terminal - Setting up terminal buffer")
          (with-current-buffer buffer
            (term-mode)
            (setq default-directory directory)
            (message "DEBUG: create_terminal - Starting process with command: %s" command)
            (setq process (start-process name buffer "bash" "-c" command))
            (term-char-mode)
            (set-process-sentinel process #'ai-auto-complete-terminal-sentinel))
          
          ;; Increment the terminal counter
          (setq ai-auto-complete-terminal-counter (1+ ai-auto-complete-terminal-counter))
          (message "DEBUG: create_terminal - Terminal counter incremented to %d" ai-auto-complete-terminal-counter)
          
          ;; Store the terminal in the hash table
          (puthash terminal-id
                   (list :buffer buffer
                         :process process
                         :name name
                         :directory directory
                         :command command
                         :start-time (current-time)
                         :terminated nil)
                   ai-auto-complete-interactive-terminals)
          (message "DEBUG: create_terminal - Terminal stored in hash table with ID: %s" terminal-id)
          
          ;; Initialize terminal history if enabled
          (when ai-auto-complete-terminal-save-history
            (puthash terminal-id '() ai-auto-complete-terminal-history)
            (message "DEBUG: create_terminal - Terminal history initialized"))
          
          ;; Display the buffer if requested
          (when show
            (display-buffer buffer)
            (message "DEBUG: create_terminal - Terminal buffer displayed"))
          
          ;; Return success message with terminal ID
          (message "DEBUG: create_terminal - Terminal created successfully with ID: %s" terminal-id)
          (format "Interactive terminal created successfully.\n\nTerminal ID: `%s`\n\nUse this ID with the `send_to_terminal` tool to send commands or the `close_terminal` tool to close it." terminal-id)))
    
    (error
     (let ((error-msg (format "ERROR: Failed to create terminal: %s" (error-message-string err))))
       (message "DEBUG: create_terminal - %s" error-msg)
       error-msg))))
;; Send input to a terminal
;; Example 1: Send a command to terminal "term-0" and add a newline
;; (ai-auto-complete-tool-send-to-terminal
;;  '((terminal_id . "term-0") (input . "ls -l /tmp")))
;; Example 2: Send partial input to terminal "term-1" without a newline
;; (ai-auto-complete-tool-send-to-terminal
;;  '((terminal_id . "term-1") (input . "cd /var/lo") (add_newline . nil)))
(defun ai-auto-complete-tool-send-to-terminal (params)
  "Send input to an interactive terminal.
PARAMS should be an alist with:
- 'terminal_id': The terminal ID returned by create_terminal
- 'input': The text to send to the terminal
- 'add_newline': Optional boolean to add a newline (default: t)"
  (message "DEBUG: send_to_terminal called with params: %S" params)
  (condition-case err
      (let* ((terminal-id (cdr (assoc 'terminal_id params)))
             (input (cdr (assoc 'input params)))
             (add-newline (if (assoc 'add_newline params) (cdr (assoc 'add_newline params)) t))
             (terminal-info (ai-auto-complete-terminal-validate-id terminal-id)))
        
        (message "DEBUG: send_to_terminal - Terminal ID: %s, Input length: %d, Add newline: %s" 
                 (or terminal-id "nil") (if input (length input) 0) (if add-newline "yes" "no"))
        
        ;; Check if terminal ID is provided
        (if (not terminal-id)
            (let ((error-msg "ERROR: No terminal ID specified"))
              (message "DEBUG: send_to_terminal - %s" error-msg)
              error-msg)
          
          ;; Check if terminal exists
          (if (not terminal-info)
              (let ((error-msg (format "ERROR: Terminal with ID '%s' not found" terminal-id)))
                (message "DEBUG: send_to_terminal - %s" error-msg)
                error-msg)
            
            ;; Check if input is provided
            (if (not input)
                (let ((error-msg "ERROR: No input specified"))
                  (message "DEBUG: send_to_terminal - %s" error-msg)
                  error-msg)
              
              ;; Check if terminal is terminated
              (if (plist-get terminal-info :terminated)
                  (let ((error-msg (format "ERROR: Terminal with ID '%s' is terminated" terminal-id)))
                    (message "DEBUG: send_to_terminal - %s" error-msg)
                    error-msg)
                
                ;; Send input to terminal
                (let* ((process (plist-get terminal-info :process))
                       (buffer (plist-get terminal-info :buffer))
                       (full-input (if add-newline (concat input "\n") input)))
                  
                  (if (not (process-live-p process))
                      (let ((error-msg "ERROR: Terminal process is not running"))
                        (message "DEBUG: send_to_terminal - %s" error-msg)
                        error-msg)
                    
                    ;; Send input to process
                    (message "DEBUG: send_to_terminal - Sending input to process")
                    (process-send-string process full-input)
                    
                    ;; Save to history if enabled
                    (when ai-auto-complete-terminal-save-history
                      (let ((history (gethash terminal-id ai-auto-complete-terminal-history)))
                        (push (cons 'input input) history)
                        (puthash terminal-id history ai-auto-complete-terminal-history)
                        (message "DEBUG: send_to_terminal - Input added to terminal history")))
                    
                    ;; Return success message
                    (message "DEBUG: send_to_terminal - Input sent successfully")
                    (format "Input sent to terminal '%s' successfully." terminal-id))))))))
    
    (error
     (let ((error-msg (format "ERROR: Failed to send input to terminal: %s" (error-message-string err))))
       (message "DEBUG: send_to_terminal - %s" error-msg)
       error-msg))))

;; Get terminal output
;; Example 1: Get the last 20 lines from terminal "term-0" with a short delay
;; (ai-auto-complete-tool-get-terminal-output
;;  '((terminal_id . "term-0") (max_lines . 20) (delay . 0.2)))
;; Example 2: Get default number of lines from terminal "term-1" with default delay
;; (ai-auto-complete-tool-get-terminal-output
;;  '((terminal_id . "term-1")))
(defun ai-auto-complete-tool-get-terminal-output (params)
  "Get output from an interactive terminal.
PARAMS should be an alist with:
- 'terminal_id': The terminal ID returned by create_terminal
- 'max_lines': Optional maximum number of lines to return (default: 50)
- 'delay': Optional delay in seconds before retrieving output (default: 0.1)"
  (message "DEBUG: get_terminal_output called with params: %S" params)
  (condition-case err
      (let* ((terminal-id (cdr (assoc 'terminal_id params)))
             (max-lines (or (cdr (assoc 'max_lines params)) 50))
             (delay (or (cdr (assoc 'delay params)) 0.1))
             (terminal-info (ai-auto-complete-terminal-validate-id terminal-id)))
        
        (message "DEBUG: get_terminal_output - Terminal ID: %s, Max lines: %d, Delay: %.2f" 
                 (or terminal-id "nil") max-lines delay)
        
        ;; Check if terminal ID is provided
        (if (not terminal-id)
            (let ((error-msg "ERROR: No terminal ID specified"))
              (message "DEBUG: get_terminal_output - %s" error-msg)
              error-msg)
          
          ;; Check if terminal exists
          (if (not terminal-info)
              (let ((error-msg (format "ERROR: Terminal with ID '%s' not found" terminal-id)))
                (message "DEBUG: get_terminal_output - %s" error-msg)
                error-msg)
            
            ;; Get terminal output
            (let* ((buffer (plist-get terminal-info :buffer))
                   (process (plist-get terminal-info :process))
                   (name (plist-get terminal-info :name))
                   (running (and process (process-live-p process)))
                   (output ""))
              
              ;; Check if buffer exists
              (if (not (buffer-live-p buffer))
                  (let ((error-msg (format "ERROR: Terminal buffer for ID '%s' is not available" terminal-id)))
                    (message "DEBUG: get_terminal_output - %s" error-msg)
                    error-msg)
                
                ;; Add a small delay to allow the terminal to process commands
                (when (> delay 0)
                  (message "DEBUG: get_terminal_output - Waiting for %.2f seconds" delay)
                  (sleep-for delay))
                
                ;; Get output from buffer
                (message "DEBUG: get_terminal_output - Retrieving output from buffer")
                (with-current-buffer buffer
                  (let* ((point-max (point-max))
                         (line-count 0)
                         (start-pos point-max))
                    
                    ;; Count back max-lines lines
                    (save-excursion
                      (goto-char point-max)
                      (let ((remaining-lines max-lines))
                        (while (and (> remaining-lines 0) (not (bobp)))
                          (forward-line -1)
                          (setq remaining-lines (1- remaining-lines))
                          (setq line-count (1+ line-count))))
                      (setq start-pos (point)))
                    
                    ;; Get the output
                    (setq output (buffer-substring-no-properties start-pos point-max))
                    (message "DEBUG: get_terminal_output - Retrieved %d lines of output" line-count)))
                
                ;; Format the result
                (message "DEBUG: get_terminal_output - Formatting result")
                (let ((formatted-result "# Terminal Output\n\n"))
                  ;; Add terminal information
                  (setq formatted-result (concat formatted-result
                                                 "**Terminal ID:** `" terminal-id "`\n\n"
                                                 "**Name:** `" name "`\n\n"
                                                 "**Status:** " (if running "Running" "Terminated") "\n\n"))
                  
                  ;; Add output
                  (setq formatted-result (concat formatted-result
                                                 "## Last " (number-to-string max-lines) " lines of output\n\n"
                                                 "```\n" output "\n```\n\n"))
                  
                  ;; Return the formatted result
                  (message "DEBUG: get_terminal_output - Returning result of length: %d" (length formatted-result))
                  formatted-result))))))
    
    (error
     (let ((error-msg (format "ERROR: Failed to get terminal output: %s" (error-message-string err))))
       (message "DEBUG: get_terminal_output - %s" error-msg)
       error-msg))))
;; Close a terminal
(defun ai-auto-complete-tool-close-terminal (params)
  "Close an interactive terminal.
PARAMS should be an alist with:
- 'terminal_id': The terminal ID returned by create_terminal
- 'force': Optional boolean to force termination (default: nil)"
  (message "DEBUG: close_terminal called with params: %S" params)
  (condition-case err
      (let* ((terminal-id (cdr (assoc 'terminal_id params)))
             (force (cdr (assoc 'force params)))
             (terminal-info (ai-auto-complete-terminal-validate-id terminal-id)))
        
        (message "DEBUG: close_terminal - Terminal ID: %s, Force: %s" 
                 (or terminal-id "nil") (if force "yes" "no"))
        
        ;; Check if terminal ID is provided
        (if (not terminal-id)
            (let ((error-msg "ERROR: No terminal ID specified"))
              (message "DEBUG: close_terminal - %s" error-msg)
              error-msg)
          
          ;; Check if terminal exists
          (if (not terminal-info)
              (let ((error-msg (format "ERROR: Terminal with ID '%s' not found" terminal-id)))
                (message "DEBUG: close_terminal - %s" error-msg)
                error-msg)
            
            ;; Close the terminal
            (let* ((process (plist-get terminal-info :process))
                   (buffer (plist-get terminal-info :buffer))
                   (running (and process (process-live-p process))))
              
              ;; Check if terminal is already terminated
              (if (and (not running) (plist-get terminal-info :terminated))
                  (let ((msg (format "Terminal with ID '%s' is already terminated" terminal-id)))
                    (message "DEBUG: close_terminal - %s" msg)
                    msg)
                
                ;; Terminate the process
                (when (and process (process-live-p process))
                  (message "DEBUG: close_terminal - Terminating process with signal: %s" 
                           (if force "SIGKILL" "SIGTERM"))
                  (if force
                      (signal-process process 'SIGKILL)
                    (signal-process process 'SIGTERM)))
                
                ;; Mark as terminated
                (puthash terminal-id
                         (plist-put terminal-info :terminated t)
                         ai-auto-complete-interactive-terminals)
                (message "DEBUG: close_terminal - Terminal marked as terminated")
                
                ;; Return success message
                (message "DEBUG: close_terminal - Terminal closed successfully")
                (format "Terminal with ID '%s' has been closed." terminal-id))))))
    
    (error
     (let ((error-msg (format "ERROR: Failed to close terminal: %s" (error-message-string err))))
       (message "DEBUG: close_terminal - %s" error-msg)
       error-msg))))

;; List all terminals
;; Example 1: List all terminals (params is ignored but shown for consistency)
;; (ai-auto-complete-tool-list-terminals nil)
;; (ai-auto-complete-tool-list-terminals '())
(defun ai-auto-complete-tool-list-terminals (params)
  "List all interactive terminals.
PARAMS is ignored."
  (message "DEBUG: list_terminals called with params: %S" params)
  (condition-case err
      (let ((formatted-result "# Interactive Terminals\n\n"))
        ;; Check if there are any terminals
        (message "DEBUG: list_terminals - Terminal count: %d" (hash-table-count ai-auto-complete-interactive-terminals))
        (if (= (hash-table-count ai-auto-complete-interactive-terminals) 0)
            (progn
              (message "DEBUG: list_terminals - No terminals available")
              (concat formatted-result "No interactive terminals are currently available.\n\n"))
          
          ;; Add table header
          (message "DEBUG: list_terminals - Formatting terminal list")
          (setq formatted-result (concat formatted-result
                                         "| Terminal ID | Name | Status | Command | Directory |\n"
                                         "|-------------|------|--------|---------|----------|\n"))
          
          ;; Add each terminal
          (maphash
           (lambda (terminal-id terminal-info)
             (let* ((process (plist-get terminal-info :process))
                    (name (plist-get terminal-info :name))
                    (command (plist-get terminal-info :command))
                    (directory (plist-get terminal-info :directory))
                    (running (and process (process-live-p process)))
                    (status (if (plist-get terminal-info :terminated)
                                "Terminated"
                              (if running "Running" "Stopped"))))
               
               (message "DEBUG: list_terminals - Processing terminal: %s, Status: %s" terminal-id status)
               
               ;; Add terminal to table
               (setq formatted-result (concat formatted-result
                                              "| `" terminal-id "` | "
                                              name " | "
                                              status " | `"
                                              (if (> (length command) 30)
                                                  (concat (substring command 0 27) "...")
                                                command)
                                              "` | `"
                                              (if (> (length directory) 30)
                                                  (concat (substring directory 0 27) "...")
                                                directory)
                                              "` |\n"))))
           ai-auto-complete-interactive-terminals)
          
          ;; Return the formatted result
          (message "DEBUG: list_terminals - Returning result of length: %d" (length formatted-result))
          formatted-result))
    
    (error
     (let ((error-msg (format "ERROR: Failed to list terminals: %s" (error-message-string err))))
       (message "DEBUG: list_terminals - %s" error-msg)
       error-msg))))

;; Register terminal tools
(defun ai-auto-complete-register-terminal-tools ()
  "Register interactive terminal tools."
  (message "DEBUG: register_terminal_tools - Registering terminal tools")
  
  ;; Create terminal
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "create_terminal"
         "Create an interactive terminal session"
         #'ai-auto-complete-tool-create-terminal
         '(("name" . "Optional name for the terminal")
           ("directory" . "Optional working directory")
           ("command" . "Optional initial command to run")
           ("show" . "Optional boolean to show the terminal buffer")))
        (message "DEBUG: register_terminal_tools - create_terminal tool registered"))
    (error
     (message "DEBUG: register_terminal_tools - Error registering create_terminal: %s" 
              (error-message-string err))))
  
  ;; Send to terminal
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "send_to_terminal"
         "Send input to an interactive terminal"
         #'ai-auto-complete-tool-send-to-terminal
         '(("terminal_id" . "Terminal ID returned by create_terminal")
           ("input" . "Text to send to the terminal")
           ("add_newline" . "Optional boolean to add a newline")))
        (message "DEBUG: register_terminal_tools - send_to_terminal tool registered"))
    (error
     (message "DEBUG: register_terminal_tools - Error registering send_to_terminal: %s" 
              (error-message-string err))))
  
  ;; Get terminal output
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "get_terminal_output"
         "Get output from an interactive terminal"
         #'ai-auto-complete-tool-get-terminal-output
         '(("terminal_id" . "Terminal ID returned by create_terminal")
           ("max_lines" . "Optional maximum number of lines to return")
           ("delay" . "Optional delay in seconds before retrieving output")))
        (message "DEBUG: register_terminal_tools - get_terminal_output tool registered"))
    (error
     (message "DEBUG: register_terminal_tools - Error registering get_terminal_output: %s" 
              (error-message-string err))))
  ;; Close terminal
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "close_terminal"
         "Close an interactive terminal"
         #'ai-auto-complete-tool-close-terminal
         '(("terminal_id" . "Terminal ID returned by create_terminal")
           ("force" . "Optional boolean to force termination")))
        (message "DEBUG: register_terminal_tools - close_terminal tool registered"))
    (error
     (message "DEBUG: register_terminal_tools - Error registering close_terminal: %s" 
              (error-message-string err))))
  
  ;; List terminals
  (condition-case err
      (progn
        (ai-auto-complete-register-tool
         "list_terminals"
         "List all interactive terminals"
         #'ai-auto-complete-tool-list-terminals
         '())
        (message "DEBUG: register_terminal_tools - list_terminals tool registered"))
    (error
     (message "DEBUG: register_terminal_tools - Error registering list_terminals: %s" 
              (error-message-string err))))
  
  (message "DEBUG: register_terminal_tools - All terminal tools registered successfully"))

;; Test function for terminal tools
(defun ai-auto-complete-terminal-tools-test (&optional agent-name)
  "Test the interactive terminal tools functionality.
This function sends a prompt that asks the LLM to use the terminal tools
to create a terminal, run commands, and interact with it.
Optionally takes AGENT-NAME to specify which agent configuration to use.
If AGENT-NAME is nil, 'chat' is used by default."
  (interactive)
  (message "DEBUG: terminal_tools_test - Starting terminal tools test")
  
  ;; Enable tools and debug mode
  (unless ai-auto-complete-tools-enabled
    (ai-auto-complete-tools-enable)
    (message "DEBUG: terminal_tools_test - Tools enabled"))
  (setq ai-auto-complete-tools-debug-mode t)
  (message "DEBUG: terminal_tools_test - Debug mode enabled")
  
  ;; Determine the agent name and create a test prompt
  (let* ((effective-agent-name (or agent-name 'chat))
         (test-prompt "I need you to help me work with interactive terminals:

1. First, create a new terminal using the create_terminal tool
2. Then, send the command \"ls -la\" to the terminal using send_to_terminal
3. Get the output from the terminal using get_terminal_output
4. Send another command \"echo 'Hello from the terminal!'\" to the terminal
5. Get the updated output
6. Finally, close the terminal using close_terminal

Please make these tool calls in sequence, analyzing the results of each before proceeding to the next."))

    (message "DEBUG: terminal_tools_test - Using agent: %s" effective-agent-name)
    (message "DEBUG: terminal_tools_test - Sending terminal tools test prompt to LLM")

    ;; Send the prompt to the current backend
    (let ((backend (ai-auto-complete-get-current-backend)))
      (message "DEBUG: terminal_tools_test - Using backend: %s" backend)
      (ai-auto-complete-complete
       backend
       test-prompt
       nil ; history
       (lambda (response)
         (message "DEBUG: terminal_tools_test - Received final response from LLM")
         (with-current-buffer (get-buffer-create "*AI Terminal Tools Test*")
           (erase-buffer)
           (insert "Terminal Tools Test Results:\n\n")
           (insert "The following response demonstrates the application's interactive terminal capabilities.\n\n")
           (insert (format "Test prompt (using agent: %s):\n" effective-agent-name))
           (insert test-prompt)
           (insert "\n\nResponse:\n")
           (insert response)
           (display-buffer (current-buffer))
           (message "DEBUG: terminal_tools_test - Test results displayed in buffer")))
       effective-agent-name))) ; Pass the agent name
    (message "DEBUG: terminal_tools_test - Terminal tools test initiated"))

;; Register terminal tools when this module is loaded
(ai-auto-complete-register-terminal-tools)

(provide 'tools/terminal-tools)
;;; terminal-tools.el ends here
