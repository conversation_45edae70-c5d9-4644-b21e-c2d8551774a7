;;; test-tools.el --- Test script for AI Auto Complete tools -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a test script for the AI Auto Complete tools.

;;; Code:

;; Load the necessary modules
(condition-case err
    (progn
      (load (expand-file-name "tools-core.el" (file-name-directory (or load-file-name buffer-file-name))))
      (load (expand-file-name "standard-tools.el" (file-name-directory (or load-file-name buffer-file-name))))
      (load (expand-file-name "linux-tools.el" (file-name-directory (or load-file-name buffer-file-name))))
      (load (expand-file-name "advanced-tools.el" (file-name-directory (or load-file-name buffer-file-name))))
      (load (expand-file-name "command-tools.el" (file-name-directory (or load-file-name buffer-file-name))))
      (load (expand-file-name "custom-tools.el" (file-name-directory (or load-file-name buffer-file-name))))
      (load (expand-file-name "history-tools.el" (file-name-directory (or load-file-name buffer-file-name))))
      (load (expand-file-name "python-tools.el" (file-name-directory (or load-file-name buffer-file-name))))
      (load (expand-file-name "safety-tools.el" (file-name-directory (or load-file-name buffer-file-name))))
      (load (expand-file-name "terminal-tools.el" (file-name-directory (or load-file-name buffer-file-name)))))
  (error
   (message "Error loading tool modules: %s" (error-message-string err))))

;; Enable tools
(setq ai-auto-complete-tools-enabled t)
(setq ai-auto-complete-tools-debug-mode t)

;; Test function for run_command
(defun ai-auto-complete-test-run-command ()
  "Test the run_command tool."
  (interactive)
  (message "Testing run_command tool...")
  (let ((result (ai-auto-complete-execute-tool "run_command" '((command . "ls -la")))))
    (with-current-buffer (get-buffer-create "*Tool Test - run_command*")
      (erase-buffer)
      (insert "Test: run_command\n\n")
      (insert "Command: ls -la\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for run_command_enhanced
(defun ai-auto-complete-test-run-command-enhanced ()
  "Test the run_command_enhanced tool."
  (interactive)
  (message "Testing run_command_enhanced tool...")
  (let ((result (ai-auto-complete-execute-tool "run_command_enhanced" '((command . "ls -la")))))
    (with-current-buffer (get-buffer-create "*Tool Test - run_command_enhanced*")
      (erase-buffer)
      (insert "Test: run_command_enhanced\n\n")
      (insert "Command: ls -la\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for create_shell_script
(defun ai-auto-complete-test-create-shell-script ()
  "Test the create_shell_script tool."
  (interactive)
  (message "Testing create_shell_script tool...")
  (let* ((script-content "echo \"Hello from shell script\"\nls -la\necho \"Current directory: $(pwd)\"\n")
         (result (ai-auto-complete-execute-tool "create_shell_script"
                                               `((content . ,script-content)
                                                 (execute . t)))))
    (with-current-buffer (get-buffer-create "*Tool Test - create_shell_script*")
      (erase-buffer)
      (insert "Test: create_shell_script\n\n")
      (insert "Script content:\n```\n")
      (insert script-content)
      (insert "```\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for read_file
(defun ai-auto-complete-test-read-file ()
  "Test the read_file tool."
  (interactive)
  (message "Testing read_file tool...")
  (let* ((test-file (expand-file-name "test-tools.el"
                                     (file-name-directory
                                      (or load-file-name
                                          buffer-file-name
                                          "/home/<USER>/.emacs.d/20250503114145_20250428183208_20250426210850_ai-auto-complete-unified/ai-auto-complete-modules/tools/"))))
         (result (ai-auto-complete-execute-tool "read_file" `((path . ,test-file)))))
    (with-current-buffer (get-buffer-create "*Tool Test - read_file*")
      (erase-buffer)
      (insert "Test: read_file\n\n")
      (insert (format "File path: %s\n\n" test-file))
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for list_directory
(defun ai-auto-complete-test-list-directory ()
  "Test the list_directory tool."
  (interactive)
  (message "Testing list_directory tool...")
  (let* ((test-dir (file-name-directory
                    (or load-file-name
                        buffer-file-name
                        "/home/<USER>/.emacs.d/20250503114145_20250428183208_20250426210850_ai-auto-complete-unified/ai-auto-complete-modules/tools/test-tools.el")))
         (result (ai-auto-complete-execute-tool "list_directory" `((path . ,test-dir)))))
    (with-current-buffer (get-buffer-create "*Tool Test - list_directory*")
      (erase-buffer)
      (insert "Test: list_directory\n\n")
      (insert (format "Directory path: %s\n\n" test-dir))
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for get_system_info
(defun ai-auto-complete-test-get-system-info ()
  "Test the get_system_info tool."
  (interactive)
  (message "Testing get_system_info tool...")
  (let ((result (ai-auto-complete-execute-tool "get_system_info" '((detail_level . "basic")))))
    (with-current-buffer (get-buffer-create "*Tool Test - get_system_info*")
      (erase-buffer)
      (insert "Test: get_system_info\n\n")
      (insert "Detail level: basic\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for write_file
(defun ai-auto-complete-test-write-file ()
  "Test the write_file tool."
  (interactive)
  (message "Testing write_file tool...")
  (let* ((test-file "/tmp/ai-auto-complete-test-file.txt")
         (content "This is a test file created by ai-auto-complete-test-write-file.\nLine 2\nLine 3")
         (result (ai-auto-complete-execute-tool "write_file" `((path . ,test-file) (content . ,content)))))
    (with-current-buffer (get-buffer-create "*Tool Test - write_file*")
      (erase-buffer)
      (insert "Test: write_file\n\n")
      (insert (format "File path: %s\n\n" test-file))
      (insert (format "Content: %s\n\n" content))
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for create_directory
(defun ai-auto-complete-test-create-directory ()
  "Test the create_directory tool."
  (interactive)
  (message "Testing create_directory tool...")
  (let* ((test-dir "/tmp/ai-auto-complete-test-dir")
         (result (ai-auto-complete-execute-tool "create_directory" `((path . ,test-dir)))))
    (with-current-buffer (get-buffer-create "*Tool Test - create_directory*")
      (erase-buffer)
      (insert "Test: create_directory\n\n")
      (insert (format "Directory path: %s\n\n" test-dir))
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for search_files
(defun ai-auto-complete-test-search-files ()
  "Test the search_files tool."
  (interactive)
  (message "Testing search_files tool...")
  (let* ((test-dir (file-name-directory
                    (or load-file-name
                        buffer-file-name
                        "/home/<USER>/.emacs.d/20250503114145_20250428183208_20250426210850_ai-auto-complete-unified/ai-auto-complete-modules/tools/")))
         (result (ai-auto-complete-execute-tool "search_files"
                                               `((path . ,test-dir)
                                                 (regex . "defun.*test")
                                                 (file_pattern . "*.el")))))
    (with-current-buffer (get-buffer-create "*Tool Test - search_files*")
      (erase-buffer)
      (insert "Test: search_files\n\n")
      (insert (format "Directory path: %s\n\n" test-dir))
      (insert "Regex: defun.*test\n\n")
      (insert "File pattern: *.el\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for list_files
(defun ai-auto-complete-test-list-files ()
  "Test the list_files tool."
  (interactive)
  (message "Testing list_files tool...")
  (let* ((test-dir (file-name-directory
                    (or load-file-name
                        buffer-file-name
                        "/home/<USER>/.emacs.d/20250503114145_20250428183208_20250426210850_ai-auto-complete-unified/ai-auto-complete-modules/tools/")))
         (result (ai-auto-complete-execute-tool "list_files"
                                               `((path . ,test-dir)
                                                 (recursive . t)))))
    (with-current-buffer (get-buffer-create "*Tool Test - list_files*")
      (erase-buffer)
      (insert "Test: list_files\n\n")
      (insert (format "Directory path: %s\n\n" test-dir))
      (insert "Recursive: true\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for list_code_definition_names
(defun ai-auto-complete-test-list-code-definition-names ()
  "Test the list_code_definition_names tool."
  (interactive)
  (message "Testing list_code_definition_names tool...")
  (let* ((test-dir (file-name-directory
                    (or load-file-name
                        buffer-file-name
                        "/home/<USER>/.emacs.d/20250503114145_20250428183208_20250426210850_ai-auto-complete-unified/ai-auto-complete-modules/tools/")))
         (result (ai-auto-complete-execute-tool "list_code_definition_names"
                                               `((path . ,test-dir)))))
    (with-current-buffer (get-buffer-create "*Tool Test - list_code_definition_names*")
      (erase-buffer)
      (insert "Test: list_code_definition_names\n\n")
      (insert (format "Directory path: %s\n\n" test-dir))
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for exact_search_and_replace
(defun ai-auto-complete-test-exact-search-and-replace ()
  "Test the exact_search_and_replace tool."
  (interactive)
  (message "Testing exact_search_and_replace tool...")

  ;; First create a test file
  (let* ((test-file "/tmp/ai-auto-complete-test-search-replace.txt")
         (content "This is line 1\nThis is line 2\nThis is line 3\nThis is line 4\nThis is line 5\n"))

    ;; Create the test file
    (with-temp-file test-file
      (insert content))

    ;; Now test the exact_search_and_replace tool
    (let* ((search-text "This is line 2\nThis is line 3\n")
           (replace-text "This is REPLACED line 2\nThis is REPLACED line 3\n")
           (result (ai-auto-complete-execute-tool "exact_search_and_replace"
                                                 `((path . ,test-file)
                                                   (search . ,search-text)
                                                   (replace . ,replace-text)
                                                   (start_line . 2)
                                                   (end_line . 3)))))
      (with-current-buffer (get-buffer-create "*Tool Test - exact_search_and_replace*")
        (erase-buffer)
        (insert "Test: exact_search_and_replace\n\n")
        (insert (format "File path: %s\n\n" test-file))
        (insert "Search text:\n```\n")
        (insert search-text)
        (insert "```\n\n")
        (insert "Replace text:\n```\n")
        (insert replace-text)
        (insert "```\n\n")
        (insert "Result:\n\n")
        (insert result)
        (insert "\n\nFile content after replacement:\n```\n")
        (insert-file-contents test-file)
        (goto-char (point-max))
        (insert "```\n")
        (display-buffer (current-buffer))))))

;; Test function for apply_diff
(defun ai-auto-complete-test-apply-diff ()
  "Test the apply_diff tool."
  (interactive)
  (message "Testing apply_diff tool...")

  ;; First create a test file
  (let* ((test-file "/tmp/ai-auto-complete-test-apply-diff.txt")
         (content "Line 1: This is some test content\nLine 2: More test content\nLine 3: Even more content\nLine 4: Final line of content\n"))

    ;; Create the test file
    (with-temp-file test-file
      (insert content))

    ;; Now test the apply_diff tool
    (let* ((diff "<<<<<<< SEARCH
Line 2: More test content
Line 3: Even more content
=======
Line 2: REPLACED test content
Line 3: REPLACED more content
>>>>>>> REPLACE")
           (result (ai-auto-complete-execute-tool "apply_diff"
                                                 `((path . ,test-file)
                                                   (diff . ,diff)
                                                   (start_line . 2)
                                                   (end_line . 3)))))
      (with-current-buffer (get-buffer-create "*Tool Test - apply_diff*")
        (erase-buffer)
        (insert "Test: apply_diff\n\n")
        (insert (format "File path: %s\n\n" test-file))
        (insert "Diff:\n```\n")
        (insert diff)
        (insert "\n```\n\n")
        (insert "Result:\n\n")
        (insert result)
        (insert "\n\nFile content after applying diff:\n```\n")
        (insert-file-contents test-file)
        (goto-char (point-max))
        (insert "```\n")
        (display-buffer (current-buffer))))))

;; Test function for suggest_commands
(defun ai-auto-complete-test-suggest-commands ()
  "Test the suggest_commands tool."
  (interactive)
  (message "Testing suggest_commands tool...")
  (let ((result (ai-auto-complete-execute-tool "suggest_commands"
                                              '((task . "find large files on my system")
                                                (num_suggestions . 3)))))
    (with-current-buffer (get-buffer-create "*Tool Test - suggest_commands*")
      (erase-buffer)
      (insert "Test: suggest_commands\n\n")
      (insert "Task: find large files on my system\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for get_command_doc
(defun ai-auto-complete-test-get-command-doc ()
  "Test the get_command_doc tool."
  (interactive)
  (message "Testing get_command_doc tool...")
  (let ((result (ai-auto-complete-execute-tool "get_command_doc"
                                              '((command . "find")))))
    (with-current-buffer (get-buffer-create "*Tool Test - get_command_doc*")
      (erase-buffer)
      (insert "Test: get_command_doc\n\n")
      (insert "Command: find\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for list_command_categories
(defun ai-auto-complete-test-list-command-categories ()
  "Test the list_command_categories tool."
  (interactive)
  (message "Testing list_command_categories tool...")
  (let ((result (ai-auto-complete-execute-tool "list_command_categories" '())))
    (with-current-buffer (get-buffer-create "*Tool Test - list_command_categories*")
      (erase-buffer)
      (insert "Test: list_command_categories\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for define_custom_tool
(defun ai-auto-complete-test-define-custom-tool ()
  "Test the define_custom_tool tool."
  (interactive)
  (message "Testing define_custom_tool tool...")
  (let* ((tool-name "test_echo_tool")
         (tool-desc "A simple test tool that echoes the input")
         (tool-params "[{\"message\": \"Message to echo\"}]")
         (tool-impl "echo \"$message\"")
         (tool-type "shell")
         (result (ai-auto-complete-execute-tool "define_custom_tool"
                                               `((name . ,tool-name)
                                                 (description . ,tool-desc)
                                                 (parameters . ,tool-params)
                                                 (implementation . ,tool-impl)
                                                 (type . ,tool-type)))))
    (with-current-buffer (get-buffer-create "*Tool Test - define_custom_tool*")
      (erase-buffer)
      (insert "Test: define_custom_tool\n\n")
      (insert (format "Tool name: %s\n" tool-name))
      (insert (format "Description: %s\n" tool-desc))
      (insert (format "Parameters: %s\n" tool-params))
      (insert (format "Implementation: %s\n" tool-impl))
      (insert (format "Type: %s\n\n" tool-type))
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for list_custom_tools
(defun ai-auto-complete-test-list-custom-tools ()
  "Test the list_custom_tools tool."
  (interactive)
  (message "Testing list_custom_tools tool...")
  (let ((result (ai-auto-complete-execute-tool "list_custom_tools" '())))
    (with-current-buffer (get-buffer-create "*Tool Test - list_custom_tools*")
      (erase-buffer)
      (insert "Test: list_custom_tools\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for using a custom tool
(defun ai-auto-complete-test-use-custom-tool ()
  "Test using a custom tool."
  (interactive)
  (message "Testing using a custom tool...")
  (let* ((tool-name "test_echo_tool")
         (result (condition-case err
                     (ai-auto-complete-execute-tool tool-name '((message . "Hello from custom tool!")))
                   (error (format "ERROR: %s" (error-message-string err))))))
    (with-current-buffer (get-buffer-create "*Tool Test - use_custom_tool*")
      (erase-buffer)
      (insert "Test: use_custom_tool\n\n")
      (insert (format "Tool name: %s\n" tool-name))
      (insert "Message: Hello from custom tool!\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for delete_custom_tool
(defun ai-auto-complete-test-delete-custom-tool ()
  "Test the delete_custom_tool tool."
  (interactive)
  (message "Testing delete_custom_tool tool...")
  (let* ((tool-name "test_echo_tool")
         (result (ai-auto-complete-execute-tool "delete_custom_tool"
                                               `((name . ,tool-name)))))
    (with-current-buffer (get-buffer-create "*Tool Test - delete_custom_tool*")
      (erase-buffer)
      (insert "Test: delete_custom_tool\n\n")
      (insert (format "Tool name: %s\n\n" tool-name))
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for get_command_history
(defun ai-auto-complete-test-get-command-history ()
  "Test the get_command_history tool."
  (interactive)
  (message "Testing get_command_history tool...")

  ;; First, add some test commands to the history
  (ai-auto-complete-add-to-command-history
   "ls -la"
   "total 100\ndrwxr-xr-x 10 <USER> <GROUP> 4096 May 1 12:00 .\ndrwxr-xr-x 20 <USER> <GROUP> 4096 May 1 12:00 .."
   0
   "Test command for history"
   default-directory
   0.5)

  (ai-auto-complete-add-to-command-history
   "uname -a"
   "Linux hostname 5.15.0-generic #1 SMP x86_64 GNU/Linux"
   0
   "Test command for history"
   default-directory
   0.2)

  ;; Now test the get_command_history tool
  (let ((result (ai-auto-complete-execute-tool "get_command_history"
                                              '((limit . 5)
                                                (include_output . t)))))
    (with-current-buffer (get-buffer-create "*Tool Test - get_command_history*")
      (erase-buffer)
      (insert "Test: get_command_history\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for repeat_command
(defun ai-auto-complete-test-repeat-command ()
  "Test the repeat_command tool."
  (interactive)
  (message "Testing repeat_command tool...")

  ;; Make sure we have commands in history
  (unless ai-auto-complete-command-history
    (ai-auto-complete-add-to-command-history
     "echo 'Hello, world!'"
     "Hello, world!"
     0
     "Test command for repeat"
     default-directory
     0.1))

  ;; Test the repeat_command tool
  (let ((result (ai-auto-complete-execute-tool "repeat_command"
                                              '((index . 0)))))
    (with-current-buffer (get-buffer-create "*Tool Test - repeat_command*")
      (erase-buffer)
      (insert "Test: repeat_command\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for clear_command_history
(defun ai-auto-complete-test-clear-command-history ()
  "Test the clear_command_history tool."
  (interactive)
  (message "Testing clear_command_history tool...")

  ;; Make sure we have commands in history
  (unless ai-auto-complete-command-history
    (ai-auto-complete-add-to-command-history
     "echo 'Test command'"
     "Test command"
     0
     "Test command for clear"
     default-directory
     0.1))

  ;; Test the clear_command_history tool
  (let ((result (ai-auto-complete-execute-tool "clear_command_history" '())))
    (with-current-buffer (get-buffer-create "*Tool Test - clear_command_history*")
      (erase-buffer)
      (insert "Test: clear_command_history\n\n")
      (insert "Before clearing: " (number-to-string (length ai-auto-complete-command-history)) " commands in history\n\n")
      (insert "Result:\n\n")
      (insert result)
      (insert "\n\nAfter clearing: " (number-to-string (length ai-auto-complete-command-history)) " commands in history")
      (display-buffer (current-buffer)))))

;; Test function for python_execute
(defun ai-auto-complete-test-python-execute ()
  "Test the python_execute tool."
  (interactive)
  (message "Testing python_execute tool...")
  (let* ((python-code "print('Hello from Python!')\nfor i in range(5):\n    print(f'Number: {i}')")
         (result (ai-auto-complete-execute-tool "python_execute"
                                              `((code . ,python-code)
                                                (timeout . 5)))))
    (with-current-buffer (get-buffer-create "*Tool Test - python_execute*")
      (erase-buffer)
      (insert "Test: python_execute\n\n")
      (insert "Python code:\n```python\n")
      (insert python-code)
      (insert "\n```\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for python_start_repl
(defun ai-auto-complete-test-python-start-repl ()
  "Test the python_start_repl tool."
  (interactive)
  (message "Testing python_start_repl tool...")
  (let ((result (ai-auto-complete-execute-tool "python_start_repl"
                                             '((name . "test-repl")))))
    (with-current-buffer (get-buffer-create "*Tool Test - python_start_repl*")
      (erase-buffer)
      (insert "Test: python_start_repl\n\n")
      (insert "Name: test-repl\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for python_send_input
(defun ai-auto-complete-test-python-send-input ()
  "Test the python_send_input tool."
  (interactive)
  (message "Testing python_send_input tool...")

  ;; First, make sure we have a REPL running
  (let* ((repl-result (ai-auto-complete-execute-tool "python_start_repl"
                                                   '((name . "test-input-repl"))))
         (process-id (if (string-match "ID: \\([^\\s-]+\\)" repl-result)
                         (match-string 1 repl-result)
                       nil)))

    (if (not process-id)
        (message "ERROR: Failed to start Python REPL")

      ;; Now test sending input
      (let* ((python-input "print('Hello from Python REPL!')")
             (result (ai-auto-complete-execute-tool "python_send_input"
                                                  `((process_id . ,process-id)
                                                    (input . ,python-input)))))
        (with-current-buffer (get-buffer-create "*Tool Test - python_send_input*")
          (erase-buffer)
          (insert "Test: python_send_input\n\n")
          (insert (format "Process ID: %s\n" process-id))
          (insert (format "Input: %s\n\n" python-input))
          (insert "Result:\n\n")
          (insert result)
          (display-buffer (current-buffer)))))))

;; Test function for python_get_repl_output
(defun ai-auto-complete-test-python-get-repl-output ()
  "Test the python_get_repl_output tool."
  (interactive)
  (message "Testing python_get_repl_output tool...")

  ;; First, make sure we have a REPL running with some output
  (let* ((repl-result (ai-auto-complete-execute-tool "python_start_repl"
                                                   '((name . "test-output-repl"))))
         (process-id (if (string-match "ID: \\([^\\s-]+\\)" repl-result)
                         (match-string 1 repl-result)
                       nil)))

    (if (not process-id)
        (message "ERROR: Failed to start Python REPL")

      ;; Send some input to generate output
      (ai-auto-complete-execute-tool "python_send_input"
                                   `((process_id . ,process-id)
                                     (input . "print('Testing output retrieval')")))

      ;; Now test getting output
      (let ((result (ai-auto-complete-execute-tool "python_get_repl_output"
                                                 `((process_id . ,process-id)
                                                   (clear . nil)))))
        (with-current-buffer (get-buffer-create "*Tool Test - python_get_repl_output*")
          (erase-buffer)
          (insert "Test: python_get_repl_output\n\n")
          (insert (format "Process ID: %s\n" process-id))
          (insert "Clear: nil\n\n")
          (insert "Result:\n\n")
          (insert result)
          (display-buffer (current-buffer)))))))

;; Test function for python_terminate_repl
(defun ai-auto-complete-test-python-terminate-repl ()
  "Test the python_terminate_repl tool."
  (interactive)
  (message "Testing python_terminate_repl tool...")

  ;; First, make sure we have a REPL running
  (let* ((repl-result (ai-auto-complete-execute-tool "python_start_repl"
                                                   '((name . "test-terminate-repl"))))
         (process-id (if (string-match "ID: \\([^\\s-]+\\)" repl-result)
                         (match-string 1 repl-result)
                       nil)))

    (if (not process-id)
        (message "ERROR: Failed to start Python REPL")

      ;; Now test terminating the REPL
      (let ((result (ai-auto-complete-execute-tool "python_terminate_repl"
                                                 `((process_id . ,process-id)))))
        (with-current-buffer (get-buffer-create "*Tool Test - python_terminate_repl*")
          (erase-buffer)
          (insert "Test: python_terminate_repl\n\n")
          (insert (format "Process ID: %s\n\n" process-id))
          (insert "Result:\n\n")
          (insert result)
          (display-buffer (current-buffer)))))))

;; Test function for python_list_repls
(defun ai-auto-complete-test-python-list-repls ()
  "Test the python_list_repls tool."
  (interactive)
  (message "Testing python_list_repls tool...")

  ;; First, make sure we have a REPL running
  (ai-auto-complete-execute-tool "python_start_repl"
                               '((name . "test-list-repl")))

  ;; Now test listing REPLs
  (let ((result (ai-auto-complete-execute-tool "python_list_repls" '())))
    (with-current-buffer (get-buffer-create "*Tool Test - python_list_repls*")
      (erase-buffer)
      (insert "Test: python_list_repls\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for python_plot
(defun ai-auto-complete-test-python-plot ()
  "Test the python_plot tool."
  (interactive)
  (message "Testing python_plot tool...")
  (let* ((python-code "import matplotlib.pyplot as plt\nimport numpy as np\n\nx = np.linspace(0, 10, 100)\ny = np.sin(x)\n\nplt.figure(figsize=(8, 6))\nplt.plot(x, y)\nplt.title('Sine Wave')\nplt.xlabel('x')\nplt.ylabel('sin(x)')\n")
         (result (ai-auto-complete-execute-tool "python_plot"
                                              `((code . ,python-code)
                                                (filename . "/tmp/test-plot.png")
                                                (timeout . 10)))))
    (with-current-buffer (get-buffer-create "*Tool Test - python_plot*")
      (erase-buffer)
      (insert "Test: python_plot\n\n")
      (insert "Python code:\n```python\n")
      (insert python-code)
      (insert "\n```\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for run_command_safe
(defun ai-auto-complete-test-run-command-safe ()
  "Test the run_command_safe tool."
  (interactive)
  (message "Testing run_command_safe tool...")
  (let* ((command "ls -la")
         (result (ai-auto-complete-execute-tool "run_command_safe"
                                              `((command . ,command)
                                                (directory . ,default-directory)
                                                (timeout . 5)))))
    (with-current-buffer (get-buffer-create "*Tool Test - run_command_safe*")
      (erase-buffer)
      (insert "Test: run_command_safe\n\n")
      (insert "Command: " command "\n")
      (insert "Directory: " default-directory "\n\n")
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Test function for read_file_safe
(defun ai-auto-complete-test-read-file-safe ()
  "Test the read_file_safe tool."
  (interactive)
  (message "Testing read_file_safe tool...")
  (let* ((path (expand-file-name "tools/test-tools.el" default-directory))
         (result (ai-auto-complete-execute-tool "read_file_safe"
                                              `((path . ,path)))))
    (with-current-buffer (get-buffer-create "*Tool Test - read_file_safe*")
      (erase-buffer)
      (insert "Test: read_file_safe\n\n")
      (insert "Path: " path "\n\n")
      (insert "Result:\n\n")
      (insert (if (and result (> (length result) 1000))
                  (concat (substring result 0 1000) "...\n\n[Content truncated]")
                (or result "No result")))
      (display-buffer (current-buffer)))))

;; Test function for write_file_safe
(defun ai-auto-complete-test-write-file-safe ()
  "Test the write_file_safe tool."
  (interactive)
  (message "Testing write_file_safe tool...")
  (let* ((path (expand-file-name "test-write-file-safe.txt" default-directory))
         (content "This is a test file created by the write_file_safe tool.\nIt should be deleted after the test is complete.")
         (result (ai-auto-complete-execute-tool "write_file_safe"
                                              `((path . ,path)
                                                (content . ,content)))))
    (with-current-buffer (get-buffer-create "*Tool Test - write_file_safe*")
      (erase-buffer)
      (insert "Test: write_file_safe\n\n")
      (insert "Path: " path "\n")
      (insert "Content:\n```\n" content "\n```\n\n")
      (insert "Result:\n\n")
      (insert (or result "No result"))
      (insert "\n\n")

      ;; Try to read the file back to verify it was written
      (condition-case err
          (let ((file-content (with-temp-buffer
                                (insert-file-contents path)
                                (buffer-string))))
            (insert "File content verification:\n```\n" file-content "\n```\n\n"))
        (error
         (insert "Error reading file: " (error-message-string err) "\n\n")))

      ;; Clean up the test file
      (condition-case err
          (progn
            (when (file-exists-p path)
              (delete-file path)
              (insert "Test file deleted successfully."))
            (unless (file-exists-p path)
              (insert "Test file does not exist.")))
        (error
         (insert "Error deleting test file: " (error-message-string err))))

      (display-buffer (current-buffer)))))

;; Test function for create_terminal
(defun ai-auto-complete-test-create-terminal ()
  "Test the create_terminal tool."
  (interactive)
  (message "Testing create_terminal tool...")
  (let* ((result (ai-auto-complete-execute-tool "create_terminal"
                                              `((name . "test-terminal")
                                                (command . "echo 'Test terminal created'; sleep 1")
                                                (show . nil)))))
    (with-current-buffer (get-buffer-create "*Tool Test - create_terminal*")
      (erase-buffer)
      (insert "Test: create_terminal\n\n")
      (insert "Result:\n\n")
      (insert (or result "No result"))

      ;; Extract terminal ID from result
      (when (and result (string-match "Terminal ID: `\\(.*?\\)`" result))
        (let ((terminal-id (match-string 1 result)))
          (insert "\n\nExtracted Terminal ID: " terminal-id "\n")
          ;; Store terminal ID for other tests
          (setq ai-auto-complete-test-terminal-id terminal-id)))

      (display-buffer (current-buffer)))))

;; Test function for send_to_terminal
(defun ai-auto-complete-test-send-to-terminal ()
  "Test the send_to_terminal tool."
  (interactive)
  (message "Testing send_to_terminal tool...")
  (if (not (boundp 'ai-auto-complete-test-terminal-id))
      (message "ERROR: No test terminal ID available. Run create_terminal test first.")
    (let* ((terminal-id ai-auto-complete-test-terminal-id)
           (input "echo 'Hello from the terminal test'")
           (result (ai-auto-complete-execute-tool "send_to_terminal"
                                                `((terminal_id . ,terminal-id)
                                                  (input . ,input)))))
      (with-current-buffer (get-buffer-create "*Tool Test - send_to_terminal*")
        (erase-buffer)
        (insert "Test: send_to_terminal\n\n")
        (insert "Terminal ID: " terminal-id "\n")
        (insert "Input: " input "\n\n")
        (insert "Result:\n\n")
        (insert (or result "No result"))
        (display-buffer (current-buffer))))))

;; Test function for get_terminal_output
(defun ai-auto-complete-test-get-terminal-output ()
  "Test the get_terminal_output tool."
  (interactive)
  (message "Testing get_terminal_output tool...")
  (if (not (boundp 'ai-auto-complete-test-terminal-id))
      (message "ERROR: No test terminal ID available. Run create_terminal test first.")
    (let* ((terminal-id ai-auto-complete-test-terminal-id)
           (result (ai-auto-complete-execute-tool "get_terminal_output"
                                                `((terminal_id . ,terminal-id)
                                                  (max_lines . 20)
                                                  (delay . 0.5)))))
      (with-current-buffer (get-buffer-create "*Tool Test - get_terminal_output*")
        (erase-buffer)
        (insert "Test: get_terminal_output\n\n")
        (insert "Terminal ID: " terminal-id "\n\n")
        (insert "Result:\n\n")
        (insert (or result "No result"))
        (display-buffer (current-buffer))))))

;; Test function for list_terminals
(defun ai-auto-complete-test-list-terminals ()
  "Test the list_terminals tool."
  (interactive)
  (message "Testing list_terminals tool...")
  (let* ((result (ai-auto-complete-execute-tool "list_terminals" nil)))
    (with-current-buffer (get-buffer-create "*Tool Test - list_terminals*")
      (erase-buffer)
      (insert "Test: list_terminals\n\n")
      (insert "Result:\n\n")
      (insert (or result "No result"))
      (display-buffer (current-buffer)))))

;; Test function for close_terminal
(defun ai-auto-complete-test-close-terminal ()
  "Test the close_terminal tool."
  (interactive)
  (message "Testing close_terminal tool...")
  (if (not (boundp 'ai-auto-complete-test-terminal-id))
      (message "ERROR: No test terminal ID available. Run create_terminal test first.")
    (let* ((terminal-id ai-auto-complete-test-terminal-id)
           (result (ai-auto-complete-execute-tool "close_terminal"
                                                `((terminal_id . ,terminal-id)))))
      (with-current-buffer (get-buffer-create "*Tool Test - close_terminal*")
        (erase-buffer)
        (insert "Test: close_terminal\n\n")
        (insert "Terminal ID: " terminal-id "\n\n")
        (insert "Result:\n\n")
        (insert (or result "No result"))
        (display-buffer (current-buffer))))))

;; Run all tests
(defun ai-auto-complete-test-all-tools ()
  "Run all tool tests."
  (interactive)
  (message "Running all tool tests...")

  ;; Standard tools
  (ai-auto-complete-test-read-file)
  (ai-auto-complete-test-list-directory)
  (ai-auto-complete-test-run-command)
  (ai-auto-complete-test-write-file)
  (ai-auto-complete-test-create-directory)

  ;; Enhanced tools
  (ai-auto-complete-test-run-command-enhanced)
  (ai-auto-complete-test-create-shell-script)
  (ai-auto-complete-test-get-system-info)

  ;; Advanced tools
  (ai-auto-complete-test-search-files)
  (ai-auto-complete-test-list-files)
  (ai-auto-complete-test-list-code-definition-names)
  (ai-auto-complete-test-exact-search-and-replace)
  (ai-auto-complete-test-apply-diff)

  ;; Command tools
  (ai-auto-complete-test-suggest-commands)
  (ai-auto-complete-test-get-command-doc)
  (ai-auto-complete-test-list-command-categories)

  ;; Custom tools
  (ai-auto-complete-test-define-custom-tool)
  (ai-auto-complete-test-list-custom-tools)
  (ai-auto-complete-test-use-custom-tool)
  (ai-auto-complete-test-delete-custom-tool)

  ;; History tools
  (ai-auto-complete-test-get-command-history)
  (ai-auto-complete-test-repeat-command)
  (ai-auto-complete-test-clear-command-history)

  ;; Python tools
  (ai-auto-complete-test-python-execute)
  (ai-auto-complete-test-python-start-repl)
  (ai-auto-complete-test-python-send-input)
  (ai-auto-complete-test-python-get-repl-output)
  (ai-auto-complete-test-python-terminate-repl)
  (ai-auto-complete-test-python-list-repls)
  (ai-auto-complete-test-python-plot)

  ;; Safety tools
  (ai-auto-complete-test-run-command-safe)
  (ai-auto-complete-test-read-file-safe)
  (ai-auto-complete-test-write-file-safe)

  ;; Terminal tools
  (ai-auto-complete-test-create-terminal)
  (ai-auto-complete-test-send-to-terminal)
  (ai-auto-complete-test-get-terminal-output)
  (ai-auto-complete-test-list-terminals)
  (ai-auto-complete-test-close-terminal)

  (message "All tests completed. Check the test buffers for results."))

;; Display registered tools
(defun ai-auto-complete-show-registered-tools ()
  "Show all registered tools."
  (interactive)
  (with-current-buffer (get-buffer-create "*Registered Tools*")
    (erase-buffer)
    (insert "Registered Tools:\n\n")
    (maphash (lambda (name tool)
               (insert (format "- %s: %s\n" name (plist-get tool :description)))
               (let ((params (plist-get tool :parameters)))
                 (when params
                   (insert "  Parameters:\n")
                   (dolist (param params)
                     (insert (format "    - %s: %s\n" (car param) (cdr param)))))))
             ai-auto-complete-tools)
    (display-buffer (current-buffer))))

(provide 'tools/test-tools)
;;; test-tools.el ends here
