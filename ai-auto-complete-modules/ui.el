;;; ui.el --- Enhanced UI for AI Auto Complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides an enhanced UI for AI Auto Complete.
;; It includes markdown rendering, streaming, and interactive elements.

;;; Code:

(require 'cl-lib)

;; Add the UI modules directory to load-path
(add-to-list 'load-path (expand-file-name "ui" (file-name-directory (or load-file-name buffer-file-name))))

;; Load UI components
(require 'ui/markdown-renderer)
(require 'ui/streaming)
(require 'ui/enhanced-chat)
(require 'ui/integration)

;; Customization group for UI
(defgroup ai-auto-complete-ui nil
  "Settings for the enhanced UI in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-ui-")

(defcustom ai-auto-complete-ui-enabled nil
  "Whether the enhanced UI is enabled."
  :type 'boolean
  :group 'ai-auto-complete-ui
  :set (lambda (symbol value)
         (set-default symbol value)
         (if value
             (ai-auto-complete-ui-enable)
           (ai-auto-complete-ui-disable))))

;; Function to enable the enhanced UI
(defun ai-auto-complete-ui-enable ()
  "Enable the enhanced UI."
  (interactive)
  (setq ai-auto-complete-ui-enabled t)
  (ai-auto-complete-ui-integration-enable)
  (message "Enhanced UI enabled"))

;; Function to disable the enhanced UI
(defun ai-auto-complete-ui-disable ()
  "Disable the enhanced UI."
  (interactive)
  (setq ai-auto-complete-ui-enabled nil)
  (ai-auto-complete-ui-integration-disable)
  (message "Enhanced UI disabled"))

;; Function to toggle the enhanced UI
(defun ai-auto-complete-ui-toggle ()
  "Toggle the enhanced UI."
  (interactive)
  (if ai-auto-complete-ui-enabled
      (ai-auto-complete-ui-disable)
    (ai-auto-complete-ui-enable)))

;; Add menu items for the enhanced UI
(defun ai-auto-complete-ui-add-menu-items ()
  "Add menu items for the enhanced UI."
  (when (and (boundp 'ai-auto-complete-menu) ai-auto-complete-menu)
    (easy-menu-add-item ai-auto-complete-menu nil
                       '("Enhanced UI"
                         ["Enable Enhanced UI" ai-auto-complete-ui-toggle
                          :style toggle :selected ai-auto-complete-ui-enabled]
                         ["Show Timestamps" ai-auto-complete-enhanced-chat-toggle-timestamps
                          :style toggle :selected ai-auto-complete-enhanced-chat-show-timestamps
                          :active ai-auto-complete-ui-enabled]
                         ["Show Tool Results" ai-auto-complete-enhanced-chat-toggle-tool-results
                          :style toggle :selected ai-auto-complete-enhanced-chat-show-tool-results
                          :active ai-auto-complete-ui-enabled]
                         "---"
                         ["Select Agent" ai-auto-complete-enhanced-chat-select-agent
                          :active ai-auto-complete-ui-enabled]
                         ["Save Session" ai-auto-complete-enhanced-chat-save-session
                          :active ai-auto-complete-ui-enabled]
                         ["Load Session" ai-auto-complete-enhanced-chat-load-session
                          :active ai-auto-complete-ui-enabled]
                         ["Clear Chat" ai-auto-complete-enhanced-chat-clear
                          :active ai-auto-complete-ui-enabled]))))

;; Add the menu items when this module is loaded
(when (featurep 'ai-auto-complete)
  (ai-auto-complete-ui-add-menu-items))

;; Add a hook to add the menu items when ai-auto-complete is loaded
(eval-after-load 'ai-auto-complete
  '(ai-auto-complete-ui-add-menu-items))

(provide 'ui)
;;; ui.el ends here
