;;; gemini.el --- Gemini provider for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the Gemini provider implementation for the AI Auto Complete package.

;;; Code:

(require 'request)
(require 'json)
(require 's)
(require 'core/backend)
(require 'customization/model-management)

;; Gemini provider implementation
(defun ai-auto-complete-gemini-provider (context history callback model system-prompt &optional agent-name)
  "Request completion from Gemini API with CONTEXT, HISTORY, MODEL, SYSTEM-PROMPT, and optional AGENT-NAME."
  (message "Requesting completion from Gemini API with model %s" model)
  (message "Using system prompt: %s" (substring system-prompt 0 (min 108 (length system-prompt))))
  (message "Passing history with %d messages" (length history))
  (when agent-name
    (message "Request is for agent: %s" agent-name))
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (progn
        (message "Gemini API key is not set")
        (funcall callback "ERROR: Gemini API key is not set. Please set ai-auto-complete-gemini-api-key."))
    (let* (;; Use the model directly - it should already be the correct model name
           ;; from ai-auto-complete-get-correct-model-name in the backend
           (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                       model ai-auto-complete-gemini-api-key))
           (have-images (and (boundp 'ai-auto-complete-enable-images) ai-auto-complete-enable-images))
           (is-chat (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode))
           (data nil))
      ;; Get model attributes
      (let* ((config-attrs (ai-auto-complete-apply-model-attributes 'gemini model (make-hash-table :test 'equal)))
             (temperature (or (and config-attrs (gethash "temperature" config-attrs)) 0.7))
             (max-tokens (or (and config-attrs (gethash "maxOutputTokens" config-attrs)) 1024))
             (top-p (or (and config-attrs (gethash "topP" config-attrs)) 0.9))
             (top-k (or (and config-attrs (gethash "topK" config-attrs)) 40))
             (generation-config `((temperature . ,temperature)
                                 (maxOutputTokens . ,max-tokens)
                                 (topP . ,top-p)
                                 (topK . ,top-k))))

        (message "Using model attributes: temperature=%s, maxOutputTokens=%s, topP=%s, topK=%s"
                 temperature max-tokens top-p top-k)

        (setq data (cond
                    (is-chat
                     (let ((prompt (format "%s\n\n%s" system-prompt context)))
                       (json-encode `((contents . [((role . "user")
                                                 (parts . [((text . ,prompt))]))])
                                    (generationConfig . ,generation-config)))))
                    (have-images ;; Condition for including images
                     (let ((prompt (format "%s\n\n%s" system-prompt context)))
                       (json-encode `((contents . [((role . "user")
                                                 (parts . [((text . ,prompt))]))])
                                    (generationConfig . ,generation-config))))) ;; Result when have-images is true
                    (t ;; Default case
                     (json-encode `((contents . [((role . "user")
                                               (parts . [((text . ,context))]))])
                                  (generationConfig . ,generation-config)))))))
      (setq ai-auto-complete--pending-request t)
      (ai-auto-complete--update-mode-line)
      (request url :type "POST" :headers '(("Content-Type" . "application/json"))
               :data data :parser 'json-read
               :success (lambda (&rest args)
                         (let* ((data (plist-get args :data)))
                          ; (message "Processing :success response for Gemini provider:\nRaw Response:\n%s" data)
                           ;; Check if the response contains an error
                           (if (assoc 'error data)
                               (let* ((error-obj (cdr (assoc 'error data)))
                                      (error-msg (cdr (assoc 'message error-obj))))
                                 (message "Gemini API error - Raw error object: %S" error-obj)
                                 (message "Gemini API error message: %s" error-msg)
                                 (funcall callback (format "ERROR: %s" error-msg)))
                             ;; Normal response processing
                             (let* ((candidates (cdr (assoc 'candidates data)))
                                    (first-candidate (aref candidates 0))
                                    (content (cdr (assoc 'content first-candidate)))
                                    (parts (cdr (assoc 'parts content)))
                                    (first-part (aref parts 0))
                                    (text (cdr (assoc 'text first-part))))
                               ;; Pass the agent-name to the tools processing function
                               (if (and (boundp 'ai-auto-complete-tools-enabled)
                                        ai-auto-complete-tools-enabled
                                        (fboundp 'ai-auto-complete-tools-process-response)
                                        ;; Only process for tools if there are tool calls in the response
                                        (string-match-p "<tool name=" text))
                                   (progn
                                     (message "Processing Gemini response for tools with agent-name: %s"
                                              (or agent-name "nil"))
                                     (ai-auto-complete-tools-process-response text callback agent-name))
                                 (funcall callback text))))))
               :error (lambda (&rest args)
                       (let* ((request-error (plist-get args :error))
                              (response (plist-get args :response))
                              (error-data (plist-get args :data)))
                          (message "Error in Gemini provider - Full error details:")
                          (message "Error: %S" request-error)
                          (message "Response: %S" response)
                          (message "Error Data: %S" error-data)
                          (funcall callback (format "ERROR: %s\nResponse: %S\nData: %S"
                                                  request-error response error-data)))))))) ;; Use renamed variable
;; Test functions for Gemini API

(defun ai-auto-complete-check-gemini-api-key ()
  "Check if the Gemini API key is valid by listing available models."
  (interactive)
  (message "Checking Gemini API key...")
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (message "ERROR: Gemini API key is not set")
    (let ((url (format "https://generativelanguage.googleapis.com/v1/models?key=%s"
                      ai-auto-complete-gemini-api-key)))
      (message "Requesting models list from: %s" url)
      (request url
               :type "GET"
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Gemini API key is valid!")
                           (let* ((models (cdr (assoc 'models data)))
                                  (model-names (mapcar (lambda (model)
                                                        (cdr (assoc 'name model)))
                                                      models)))
                             (message "Available Gemini models: %S" model-names))))
               :error (cl-function
                       (lambda (&key error-thrown response &allow-other-keys)
                         (message "Gemini API key validation ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Your Gemini API key appears to be invalid or has insufficient permissions.")))))
    t))

(defun ai-auto-complete-test-gemini-api ()
  "Test the Gemini API with a simple request and display detailed results."
  (interactive)
  (message "Testing Gemini API...")
  (message "API Key set: %s" (not (string-empty-p ai-auto-complete-gemini-api-key)))
  (let* ((model "gemini-2.0-flash-lite")
         (test-prompt "What is the capital of France?")
         ;; Use the model directly
         (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                     model
                     ai-auto-complete-gemini-api-key))
         (config-attrs (ai-auto-complete-apply-model-attributes 'gemini model (make-hash-table :test 'equal)))
         (temperature (or (and config-attrs (gethash "temperature" config-attrs)) 0.7))
         (max-tokens (or (and config-attrs (gethash "maxOutputTokens" config-attrs)) 1024))
         (top-p (or (and config-attrs (gethash "topP" config-attrs)) 0.9))
         (top-k (or (and config-attrs (gethash "topK" config-attrs)) 40))
         (generation-config `((temperature . ,temperature)
                             (maxOutputTokens . ,max-tokens)
                             (topP . ,top-p)
                             (topK . ,top-k)))
         (data (json-encode `((contents . [((role . "user")
                                         (parts . [((text . ,test-prompt))]))])
                            (generationConfig . ,generation-config)))))
    (message "Testing URL: %s" url)
    (message "Request data: %s" data)
    (request url
             :type "POST"
             :headers '(("Content-Type" . "application/json"))
             :data data
             :parser 'json-read
             :success (cl-function
                       (lambda (&key data &allow-other-keys)
                         (message "Gemini API test SUCCESS")
                         (message "Response data: %S" data)
                         (let* ((candidates (cdr (assoc 'candidates data)))
                                (first-candidate (aref candidates 0))
                                (content (cdr (assoc 'content first-candidate)))
                                (parts (cdr (assoc 'parts content)))
                                (first-part (aref parts 0))
                                (text (cdr (assoc 'text first-part))))
                           (message "Generated text: %s" text))))
             :error (cl-function
                     (lambda (&key error-thrown response data &allow-other-keys)
                       (message "Gemini API test ERROR: %S" error-thrown)
                       (when response
                         (message "Response status: %s" (request-response-status-code response))
                         (message "Response headers: %s" (request-response-headers response))
                         (message "Response data: %s" (request-response-data response)))
                       (message "Full error details: %S" (list :error error-thrown :response response :data data)))))))

(defun ai-auto-complete-test-gemini-model (model)
  "Test if a specific Gemini MODEL is available and working."
  (interactive
   (list (read-string "Enter Gemini model name to test: " "gemini-2.0-flash-lite")))
  (message "Testing Gemini model: %s" model)
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (message "ERROR: Gemini API key is not set")
    (let* (;; Use the model directly
           (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                      model
                      ai-auto-complete-gemini-api-key))
           (test-prompt "What is the capital of France?")
           (data (json-encode `((contents . [((role . "user")
                                           (parts . [((text . ,test-prompt))]))])
                              (generationConfig . ((temperature . 0.7)
                                                  (maxOutputTokens . 1024)
                                                  (topP . 0.9)
                                                  (topK . 40)))))))
      (message "Testing URL: %s" url)
      (request url
               :type "POST"
               :headers '(("Content-Type" . "application/json"))
               :data data
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Model %s is working correctly!" model)
                           (message "Response received successfully")))
               :error (cl-function
                       (lambda (&key error-thrown response &allow-other-keys)
                         (message "Model test ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Model %s appears to be invalid or inaccessible" model)))))))

(defun ai-auto-complete-test-gemini-chat ()
  "Test the Gemini API with a chat request format."
  (interactive)
  (message "Testing Gemini API with chat format...")
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (message "ERROR: Gemini API key is not set")
    (let* ((model "gemini-2.0-flash-lite")
           ;; Use the model directly
           (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                       model
                       ai-auto-complete-gemini-api-key))
           (system-prompt "You are a helpful AI assistant.")
           (user-message "What is the capital of France?")
           ;; Create a chat-like message structure - prepend system prompt to user message
           (messages (list
                      `((role . "user")
                        (parts . [((text . ,(concat "System: " system-prompt "\n\nUser: " user-message)))]))))
           (data (json-encode `((contents . ,(vconcat [] messages))
                              (generationConfig . ((temperature . 0.7)
                                                  (maxOutputTokens . 1024)
                                                  (topP . 0.9)
                                                  (topK . 40)))))))
      (message "Testing URL: %s" url)
      (message "Request data: %s" data)
      (request url
               :type "POST"
               :headers '(("Content-Type" . "application/json"))
               :data data
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Gemini chat test SUCCESS")
                           (message "Response data: %S" data)
                           (let* ((candidates (cdr (assoc 'candidates data)))
                                  (first-candidate (aref candidates 0))
                                  (content (cdr (assoc 'content first-candidate)))
                                  (parts (cdr (assoc 'parts content)))
                                  (first-part (aref parts 0))
                                  (text (cdr (assoc 'text first-part))))
                             (message "Generated text: %s" text))))
               :error (cl-function
                       (lambda (&key error-thrown response data &allow-other-keys)
                         (message "Gemini chat test ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response headers: %s" (request-response-headers response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Full error details: %S" (list :error error-thrown :response response :data data))))))))

(defun ai-auto-complete-debug-gemini-chat-history ()
  "Debug the Gemini chat history format."
  (interactive)
  (if (not (and (boundp 'ai-auto-complete--chat-history)
                (not (null ai-auto-complete--chat-history))))
      (message "No chat history available. Please start a chat first.")
    (let* ((model "gemini-2.0-flash-lite")
           (system-prompt "You are a helpful AI assistant.")
           (context "Hello")
           (messages nil))
      ;; Add current message with system prompt prepended
      (push `((role . "user")
             (parts . [((text . ,(concat "System: " system-prompt "\n\nUser: " context)))])) messages)
      ;; Add history messages (oldest to newest)
      (dolist (msg ai-auto-complete--chat-history) ;; No need to reverse, we want oldest first
        (let ((role (cond ((eq (car msg) 'user) "user")
                         ((eq (car msg) 'agent) "model")
                         ((eq (car msg) 'tool-result) "model")
                         (t "model")))
              (content (cond
                        ((eq (car msg) 'agent) (cdr (cdr msg))) ; Extract the actual message content from agent response
                        ((eq (car msg) 'tool-result) (format "Tool Results: %s" (cdr msg))) ; Format tool results
                        (t (cdr msg)))))
          (message "Chat history entry - Role: %s, Content: %s" role
                   (if (stringp content)
                       (substring content 0 (min 30 (length content)))
                     "<non-string content>"))
          (push `((role . ,role)
                 (parts . [((text . ,content))])) messages)))

      (let* ((url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                        model
                        ai-auto-complete-gemini-api-key))
             (data (json-encode `((contents . ,(vconcat [] (reverse messages)))
                                (generationConfig . ((temperature . 0.7)
                                                    (maxOutputTokens . 1024)
                                                    (topP . 0.9)
                                                    (topK . 40)))))))
        (message "Chat history debug - URL: %s" url)
        (message "Chat history debug - Request data: %s" data)
        (message "Chat history debug - Total messages: %d" (length messages))
        (request url
                 :type "POST"
                 :headers '(("Content-Type" . "application/json"))
                 :data data
                 :parser 'json-read
                 :success (cl-function
                           (lambda (&key data &allow-other-keys)
                             (message "Chat history debug - SUCCESS")
                             (message "Chat history debug - Response received successfully")))
                 :error (cl-function
                         (lambda (&key error-thrown response data &allow-other-keys)
                           (message "Chat history debug - ERROR: %S" error-thrown)
                           (when response
                             (message "Chat history debug - Response status: %s" (request-response-status-code response))
                             (message "Chat history debug - Response data: %s" (request-response-data response)))
                           (message "Chat history debug - Full error details: %S" (list :error error-thrown :response response :data data)))))))))

(defun ai-auto-complete-test-gemini-model-variants ()
  "Test different Gemini model name variants to find which ones work."
  (interactive)
  (message "Testing different Gemini model variants...")
  (let ((models '("gemini-2.0-flash-lite"
                  "gemini-2.0-flash"
                  "gemini-2.0-pro"
                  "gemini-2.5-flash-preview"
                  "gemini-2.5-pro-preview-03-25"
                  "gemini-2.5-pro-exp-03-25"
                  "gemini-pro"
                  "gemini-pro-vision"
                  "gemini-1.5-pro"
                  "gemini-1.5-flash")))
    (dolist (model models)
      (message "Testing model variant: %s" model)
      (let* (;; Use the model directly
             (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                        model
                        ai-auto-complete-gemini-api-key))
             (test-prompt "What is the capital of France?")
             (data (json-encode `((contents . [((role . "user")
                                             (parts . [((text . ,test-prompt))]))])
                                (generationConfig . ((temperature . 0.7)
                                                    (maxOutputTokens . 1024)
                                                    (topP . 0.9)
                                                    (topK . 40)))))))
        (request url
                 :type "POST"
                 :headers '(("Content-Type" . "application/json"))
                 :data data
                 :parser 'json-read
                 :success (cl-function
                           (lambda (&key data &allow-other-keys)
                             (message "Model %s: SUCCESS" model)))
                 :error (cl-function
                         (lambda (&key error-thrown response &allow-other-keys)
                           (message "Model %s: ERROR - %s" model error-thrown)
                           (when response
                             (message "Model %s: Status %s" model (request-response-status-code response))))))))))

;; Register the provider
(ai-auto-complete-register-provider 'gemini #'ai-auto-complete-gemini-provider)

(provide 'providers/gemini)
;;; gemini.el ends here
