;;; gemini.el --- Gemini provider for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the Gemini provider implementation for the AI Auto Complete package.

;;; Code:

(require 'request)
(require 'json)
(require 's)
(require 'core/backend)

;; Gemini provider implementation
(defun ai-auto-complete-gemini-provider (context history callback model system-prompt)
  "Request completion from Gemini API with CONTEXT, HISTORY, MODEL, and SYSTEM-PROMPT."
  (message "Requesting completion from Gemini API with model %s" model)
  (message "Using system prompt: %s" system-prompt)
  (message "Passing history with %d messages" (length history))
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (progn
        (message "Gemini API key is not set")
        (funcall callback "ERROR: Gemini API key is not set. Please set ai-auto-complete-gemini-api-key."))
    (let* ((url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent" model))
           (have-images (and (boundp 'ai-auto-complete-enable-images) ai-auto-complete-enable-images))
           (is-chat (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode))
           (data nil))
      (setq data (cond
                  (is-chat
                   (let ((prompt (format "%s\n\n%s" system-prompt context)))
                     (json-encode `(:model ,model :messages (:role "user" :content ,prompt)))))
                  (have-images ;; Condition for including images
                   (let ((prompt (format "%s\n\n%s" system-prompt context)))
                     (json-encode `(:model ,model :messages (:role "user" :content ,prompt))))) ;; Result when have-images is true
                  (t ;; Default case
                   (json-encode `(:model ,model :messages (:role "user" :content ,context))))))
      (setq ai-auto-complete--pending-request t)
      (ai-auto-complete--update-mode-line)
      (request url :type "POST" :headers '(("Content-Type" . "application/json"))
               :data data :parser 'json-read
               :success (lambda (&rest args)
                         (let* ((data (plist-get args :data)))
                           (message "Processing response for Gemini provider")
                           ;; Check if the response contains an error
                           (if (assoc 'error data)
                               (let* ((error-obj (cdr (assoc 'error data)))
                                      (error-msg (cdr (assoc 'message error-obj))))
                                 (message "Gemini API error: %s" error-msg)
                                 (funcall callback (format "ERROR: %s" error-msg)))
                             ;; Normal response processing
                             (let* ((response (cdr (assoc 'generateContentResponse data)))
                                    (result (cdr (assoc 'textOutputs response)))
                                    (content (cdr (assoc 'text (car result)))))
                               (ai-auto-complete-tools-process-response content callback)))))
               :error (lambda (&rest args)
                       (let* ((request-error (plist-get args :error))) ;; Renamed variable
                          (message "Error in Gemini provider: %s" request-error) ;; Use renamed variable
                          (funcall callback (format "ERROR: %s" request-error)))))))) ;; Use renamed variable
;; Test functions for Gemini API

(defun ai-auto-complete-check-gemini-api-key ()
  "Check if the Gemini API key is valid by listing available models."
  (interactive)
  (message "Checking Gemini API key...")
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (message "ERROR: Gemini API key is not set")
    (let ((url (format "https://generativelanguage.googleapis.com/v1/models?key=%s"
                      ai-auto-complete-gemini-api-key)))
      (message "Requesting models list from: %s" url)
      (request url
               :type "GET"
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Gemini API key is valid!")
                           (let* ((models (cdr (assoc 'models data)))
                                  (model-names (mapcar (lambda (model)
                                                        (cdr (assoc 'name model)))
                                                      models)))
                             (message "Available Gemini models: %S" model-names))))
               :error (cl-function
                       (lambda (&key error-thrown response &allow-other-keys)
                         (message "Gemini API key validation ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Your Gemini API key appears to be invalid or has insufficient permissions.")))))
    t))

(defun ai-auto-complete-test-gemini-api ()
  "Test the Gemini API with a simple request and display detailed results."
  (interactive)
  (message "Testing Gemini API...")
  (message "API Key set: %s" (not (string-empty-p ai-auto-complete-gemini-api-key)))
  (let* ((model "gemini-2.0-flash-lite")
         (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                     model
                     ai-auto-complete-gemini-api-key))
         (test-prompt "What is the capital of France?")
         (data (json-encode `((contents . [((role . "user")
                                         (parts . [((text . ,test-prompt))]))])
                            (generationConfig . ((temperature . 0.7)
                                                (maxOutputTokens . 1024)
                                                (topP . 0.9)
                                                (topK . 40)))))))
    (message "Testing URL: %s" url)
    (message "Request data: %s" data)
    (request url
             :type "POST"
             :headers '(("Content-Type" . "application/json"))
             :data data
             :parser 'json-read
             :success (cl-function
                       (lambda (&key data &allow-other-keys)
                         (message "Gemini API test SUCCESS")
                         (message "Response data: %S" data)
                         (let* ((candidates (cdr (assoc 'candidates data)))
                                (first-candidate (aref candidates 0))
                                (content (cdr (assoc 'content first-candidate)))
                                (parts (cdr (assoc 'parts content)))
                                (first-part (aref parts 0))
                                (text (cdr (assoc 'text first-part))))
                           (message "Generated text: %s" text))))
             :error (cl-function
                     (lambda (&key error-thrown response data &allow-other-keys)
                       (message "Gemini API test ERROR: %S" error-thrown)
                       (when response
                         (message "Response status: %s" (request-response-status-code response))
                         (message "Response headers: %s" (request-response-headers response))
                         (message "Response data: %s" (request-response-data response)))
                       (message "Full error details: %S" (list :error error-thrown :response response :data data)))))))

(defun ai-auto-complete-test-gemini-model (model)
  "Test if a specific Gemini MODEL is available and working."
  (interactive
   (list (read-string "Enter Gemini model name to test: " "gemini-2.0-flash-lite")))
  (message "Testing Gemini model: %s" model)
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (message "ERROR: Gemini API key is not set")
    (let* ((url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                      model
                      ai-auto-complete-gemini-api-key))
           (test-prompt "What is the capital of France?")
           (data (json-encode `((contents . [((role . "user")
                                           (parts . [((text . ,test-prompt))]))])
                              (generationConfig . ((temperature . 0.7)
                                                  (maxOutputTokens . 1024)
                                                  (topP . 0.9)
                                                  (topK . 40)))))))
      (message "Testing URL: %s" url)
      (request url
               :type "POST"
               :headers '(("Content-Type" . "application/json"))
               :data data
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Model %s is working correctly!" model)
                           (message "Response received successfully")))
               :error (cl-function
                       (lambda (&key error-thrown response &allow-other-keys)
                         (message "Model test ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Model %s appears to be invalid or inaccessible" model)))))))

(defun ai-auto-complete-test-gemini-chat ()
  "Test the Gemini API with a chat request format."
  (interactive)
  (message "Testing Gemini API with chat format...")
  (if (string-empty-p ai-auto-complete-gemini-api-key)
      (message "ERROR: Gemini API key is not set")
    (let* ((model "gemini-2.0-flash-lite")
           (url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                       model
                       ai-auto-complete-gemini-api-key))
           (system-prompt "You are a helpful AI assistant.")
           (user-message "What is the capital of France?")
           ;; Create a chat-like message structure - prepend system prompt to user message
           (messages (list
                      `((role . "user")
                        (parts . [((text . ,(concat "System: " system-prompt "\n\nUser: " user-message)))]))))
           (data (json-encode `((contents . ,(vconcat [] messages))
                              (generationConfig . ((temperature . 0.7)
                                                  (maxOutputTokens . 1024)
                                                  (topP . 0.9)
                                                  (topK . 40)))))))
      (message "Testing URL: %s" url)
      (message "Request data: %s" data)
      (request url
               :type "POST"
               :headers '(("Content-Type" . "application/json"))
               :data data
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Gemini chat test SUCCESS")
                           (message "Response data: %S" data)
                           (let* ((candidates (cdr (assoc 'candidates data)))
                                  (first-candidate (aref candidates 0))
                                  (content (cdr (assoc 'content first-candidate)))
                                  (parts (cdr (assoc 'parts content)))
                                  (first-part (aref parts 0))
                                  (text (cdr (assoc 'text first-part))))
                             (message "Generated text: %s" text))))
               :error (cl-function
                       (lambda (&key error-thrown response data &allow-other-keys)
                         (message "Gemini chat test ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response headers: %s" (request-response-headers response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Full error details: %S" (list :error error-thrown :response response :data data))))))))

(defun ai-auto-complete-debug-gemini-chat-history ()
  "Debug the Gemini chat history format."
  (interactive)
  (if (not (and (boundp 'ai-auto-complete--chat-history)
                (not (null ai-auto-complete--chat-history))))
      (message "No chat history available. Please start a chat first.")
    (let* ((model "gemini-2.0-flash-lite")
           (system-prompt "You are a helpful AI assistant.")
           (context "Hello")
           (messages nil))
      ;; Add current message with system prompt prepended
      (push `((role . "user")
             (parts . [((text . ,(concat "System: " system-prompt "\n\nUser: " context)))])) messages)
      ;; Add history messages
      (dolist (msg (reverse ai-auto-complete--chat-history))
        (let ((role (cond ((eq (car msg) 'user) "user")
                         ((eq (car msg) 'agent) "model")
                         (t "model")))
              (content (if (eq (car msg) 'agent)
                           (cdr (cdr msg)) ; Extract the actual message content from agent response
                         (cdr msg))))
          (message "Chat history entry - Role: %s, Content: %s" role
                   (if (stringp content)
                       (substring content 0 (min 30 (length content)))
                     "<non-string content>"))
          (push `((role . ,role)
                 (parts . [((text . ,content))])) messages)))

      (let* ((url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                        model
                        ai-auto-complete-gemini-api-key))
             (data (json-encode `((contents . ,(vconcat [] (reverse messages)))
                                (generationConfig . ((temperature . 0.7)
                                                    (maxOutputTokens . 1024)
                                                    (topP . 0.9)
                                                    (topK . 40)))))))
        (message "Chat history debug - URL: %s" url)
        (message "Chat history debug - Request data: %s" data)
        (message "Chat history debug - Total messages: %d" (length messages))
        (request url
                 :type "POST"
                 :headers '(("Content-Type" . "application/json"))
                 :data data
                 :parser 'json-read
                 :success (cl-function
                           (lambda (&key data &allow-other-keys)
                             (message "Chat history debug - SUCCESS")
                             (message "Chat history debug - Response received successfully")))
                 :error (cl-function
                         (lambda (&key error-thrown response data &allow-other-keys)
                           (message "Chat history debug - ERROR: %S" error-thrown)
                           (when response
                             (message "Chat history debug - Response status: %s" (request-response-status-code response))
                             (message "Chat history debug - Response data: %s" (request-response-data response)))
                           (message "Chat history debug - Full error details: %S" (list :error error-thrown :response response :data data)))))))))

(defun ai-auto-complete-test-gemini-model-variants ()
  "Test different Gemini model name variants to find which ones work."
  (interactive)
  (message "Testing different Gemini model variants...")
  (let ((models '("gemini-2.0-flash-lite"
                  "gemini-2.0-flash"
                  "gemini-2.0-pro"
                  "gemini-2.5-flash-preview"
                  "gemini-2.5-pro-preview-03-25"
                  "gemini-2.5-pro-exp-03-25"
                  "gemini-pro"
                  "gemini-pro-vision"
                  "gemini-1.5-pro"
                  "gemini-1.5-flash")))
    (dolist (model models)
      (message "Testing model variant: %s" model)
      (let* ((url (format "https://generativelanguage.googleapis.com/v1/models/%s:generateContent?key=%s"
                        model
                        ai-auto-complete-gemini-api-key))
             (test-prompt "What is the capital of France?")
             (data (json-encode `((contents . [((role . "user")
                                             (parts . [((text . ,test-prompt))]))])
                                (generationConfig . ((temperature . 0.7)
                                                    (maxOutputTokens . 1024)
                                                    (topP . 0.9)
                                                    (topK . 40)))))))
        (request url
                 :type "POST"
                 :headers '(("Content-Type" . "application/json"))
                 :data data
                 :parser 'json-read
                 :success (cl-function
                           (lambda (&key data &allow-other-keys)
                             (message "Model %s: SUCCESS" model)))
                 :error (cl-function
                         (lambda (&key error-thrown response &allow-other-keys)
                           (message "Model %s: ERROR - %s" model error-thrown)
                           (when response
                             (message "Model %s: Status %s" model (request-response-status-code response))))))))))

;; Register the provider
(ai-auto-complete-register-provider 'gemini #'ai-auto-complete-gemini-provider)

(provide 'providers/gemini)
;;; gemini.el ends here
