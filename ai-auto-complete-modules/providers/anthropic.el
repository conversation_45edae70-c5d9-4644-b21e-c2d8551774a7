;;; anthropic.el --- Anthropic provider for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the Anthropic provider implementation for the AI Auto Complete package.

;;; Code:

(require 'request)
(require 'json)
(require 's)
(require 'core/backend)
(require 'customization/model-management)

;; Anthropic provider implementation
(defun ai-auto-complete-anthropic-provider (context history callback model system-prompt &optional agent-name)
  "Request completion from Anthropic API with CONTEXT, HISTORY, and call CALLBACK with the result.
Uses MODEL and SYSTEM-PROMPT for the request. AGENT-NAME is the optional name of the agent making the request."
  (message "Anthropic provider called with model: %s" model)
  (message "Anthropic API key set: %s" (not (string-empty-p ai-auto-complete-anthropic-api-key)))
  (when agent-name
    (message "Anthropic request is for agent: %s" agent-name))
  (if (string-empty-p ai-auto-complete-anthropic-api-key)
      (progn
        (message "Anthropic API key is not set")
        (funcall callback "ERROR: Anthropic API key is not set. Please set ai-auto-complete-anthropic-api-key to use this backend."))
    (let* ((url "https://api.anthropic.com/v1/messages")
           (headers `(("Content-Type" . "application/json")
                     ("X-API-Key" . ,ai-auto-complete-anthropic-api-key)
                     ("Anthropic-Version" . "2023-06-01")))
           ;; Check if we have shared context images
           (have-images (and (boundp 'ai-auto-complete-context-images)
                            (not (null ai-auto-complete-context-images))))
           ;; Check if we have history
           (is-chat (and history (not (null history))))
           ;; Build the messages array
           (messages (if is-chat
                        ;; Chat mode - build conversation history
                        (let ((msg-array `[]))
                          ;; Add history messages (oldest to newest)
                          (dolist (msg history) ;; No need to reverse, we want oldest first
                            (when (and msg (listp msg)) ;; Ensure msg is a proper list
                              (condition-case err
                                  (let ((role (cond
                                              ((and (consp msg) (eq (car msg) 'user)) "user")
                                              ((and (consp msg) (eq (car msg) 'agent)) "assistant")
                                              ((and (consp msg) (eq (car msg) 'tool-result)) "assistant")
                                              (t "assistant")))
                                        (content (cond
                                                 ((and (consp msg) (eq (car msg) 'agent)) (cddr msg)) ;; Extract the actual response from (agent name . response)
                                                 ((and (consp msg) (eq (car msg) 'tool-result)) (format "Tool Results: %s" (cdr msg))) ;; Format tool results
                                                 ((consp msg) (cdr msg))
                                                 (t "Unknown message format"))))
                                    (setq msg-array (vconcat msg-array
                                                            `[((role . ,role)
                                                              (content . ,content))])))
                                (error
                                 (message "Error processing history message: %s" (error-message-string err))
                                 ;; Skip this message
                                 ))))
                          ;; Add the current message
                          (setq msg-array (vconcat msg-array
                                                  `[((role . "user")
                                                    (content . ,context))]))
                          msg-array)
                      ;; Not chat mode - simple prompt
                      (if have-images
                          ;; We have images - need to format as a multimodal request
                          (let ((content-array `[((type . "text")
                                                (text . ,context))]))
                            ;; Add images to the content array
                            (dolist (img ai-auto-complete-context-images)
                              (let* ((mime-type (plist-get img :mime-type))
                                     (img-content `((type . "image")
                                                   (source .
                                                    ((type . "base64")
                                                     (media_type . ,mime-type)
                                                     (data . ,(plist-get img :data)))))))
                                (setq content-array (vconcat content-array `[,img-content]))))
                            ;; Return the message array with content array
                            `[((role . "user")
                               (content . ,content-array))])
                        ;; No images - simple text prompt
                        `[((role . "user")
                           (content . ,context))])))
           ;; Get model attributes from configuration
           ;; Use the model directly - it should already be the correct model name
           ;; from ai-auto-complete-get-correct-model-name in the backend
           (config-attrs (ai-auto-complete-apply-model-attributes 'anthropic model (make-hash-table :test 'equal)))
           (temperature (or (and config-attrs (gethash "temperature" config-attrs)) 0.7))
           (max-tokens (or (and config-attrs (gethash "max_tokens" config-attrs)) 1024))
           ;; Build the full request data
           (data (json-encode `((model . ,model)
                               (system . ,system-prompt)
                               (messages . ,messages)
                               (temperature . ,temperature)
                               (max_tokens . ,max-tokens))))
           (dummy (message "Anthropic request data: %s" data)))
      (setq ai-auto-complete--pending-request t)
      (ai-auto-complete--update-mode-line)
      (request url
               :type "POST"
               :headers headers
               :data data
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Anthropic API request successful")
                           (setq ai-auto-complete--pending-request nil)
                           (ai-auto-complete--update-mode-line)
                           (message "Response data: %s" data)
                           ;; Check if the response contains an error
                           (if (assoc 'error data)
                               (let* ((error-obj (cdr (assoc 'error data)))
                                      (error-msg (cdr (assoc 'message error-obj))))
                                 (message "Anthropic API error: %s" error-msg)
                                 (funcall callback (format "ERROR: %s" error-msg)))
                             ;; Normal response processing
                             (let* ((content (cdr (assoc 'content data)))
                                    (first-content (aref content 0))
                                    (text (cdr (assoc 'text first-content))))
                               (message "Extracted text: %s" (substring text 0 (min 50 (length text))))
                               ;; Store the completion in a list
                               (setq ai-auto-complete--completions (list text)
                                     ai-auto-complete--current-completion-index 0)
                               ;; Pass the agent-name to the tools processing function
                               (if (and (boundp 'ai-auto-complete-tools-enabled)
                                        ai-auto-complete-tools-enabled
                                        (fboundp 'ai-auto-complete-tools-process-response)
                                        ;; Only process for tools if there are tool calls in the response
                                        (string-match-p "<tool name=" text))
                                   (progn
                                     (message "Processing Anthropic response for tools with agent-name: %s"
                                              (or agent-name "nil"))
                                     (ai-auto-complete-tools-process-response text callback agent-name))
                                 (funcall callback text))))))
               :error (cl-function
                       (lambda (&key error-thrown response &allow-other-keys)
                         (message "Anthropic API request failed")
                         (setq ai-auto-complete--pending-request nil)
                         (ai-auto-complete--update-mode-line)
                         (message "Error: %S" error-thrown)
                         (message "Response status: %s" (request-response-status-code response))
                         (message "Response error: %s" (request-response-error-thrown response))
                         (funcall callback (format "ERROR: %S" error-thrown))))))))

;; Register the provider
(ai-auto-complete-register-provider 'anthropic #'ai-auto-complete-anthropic-provider)

(provide 'providers/anthropic)
;;; anthropic.el ends here
