;;; openai.el --- OpenAI provider for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the OpenAI provider implementation for the AI Auto Complete package.

;;; Code:

(require 'request)
(require 'json)
(require 's)
(require 'core/backend)
(require 'customization/model-management)

;; OpenAI provider implementation
(defun ai-auto-complete-openai-provider (context history callback model system-prompt &optional agent-name)
  "Request completion from OpenAI API with CONTEXT, HISTORY, and call CALLBACK with the result.
Uses MODEL and SYSTEM-PROMPT for the request. AGENT-NAME is the optional name of the agent making the request."
  (when agent-name
    (message "OpenAI request is for agent: %s" agent-name))
  (if (string-empty-p ai-auto-complete-openai-api-key)
      (progn
        (message "OpenAI API key is not set")
        (funcall callback "ERROR: OpenAI API key is not set. Please set ai-auto-complete-openai-api-key to use this backend."))
    (let* ((url "https://api.openai.com/v1/chat/completions")
           (headers `(("Content-Type" . "application/json")
                     ("Authorization" . ,(concat "Bearer " ai-auto-complete-openai-api-key))))
           ;; Check if we have shared context images
           (have-images (and (boundp 'ai-auto-complete-context-images)
                            (not (null ai-auto-complete-context-images))))
           ;; Check if we have history
           (is-chat (and history (not (null history))))
           ;; Build the messages array
           (messages (if is-chat
                        ;; Chat mode - build conversation history
                        (let ((msg-array `[((role . "system")
                                          (content . ,system-prompt))]))
                          (message "DEBUG-OPENAI: Using system prompt (first 100 chars): %s"
                                   (substring system-prompt 0 (min 100 (length system-prompt))))

                          ;; Add history messages (oldest to newest)
                          (dolist (msg history) ;; No need to reverse, we want oldest first
                            (when (and msg (listp msg)) ;; Ensure msg is a proper list
                              (condition-case err
                                  (let ((role (cond
                                              ((and (consp msg) (eq (car msg) 'user)) "user")
                                              ((and (consp msg) (eq (car msg) 'agent)) "assistant")
                                              ((and (consp msg) (eq (car msg) 'tool-result)) "assistant")
                                              (t "assistant")))
                                        (content (cond
                                                 ((and (consp msg) (eq (car msg) 'agent)) (cddr msg)) ;; Extract the actual response from (agent name . response)
                                                 ((and (consp msg) (eq (car msg) 'tool-result)) (format "Tool Results: %s" (cdr msg))) ;; Format tool results
                                                 ((consp msg) (cdr msg))
                                                 (t "Unknown message format"))))

                                    ;; Debug the message being processed
                                    (message "DEBUG-OPENAI: Processing history message - Role: %s, Content: %s"
                                             role
                                             (substring (format "%s" content) 0 (min 50 (length (format "%s" content)))))

                                    (setq msg-array (vconcat msg-array
                                                            `[((role . ,role)
                                                              (content . ,content))])))
                                (error
                                 (message "Error processing history message: %s" (error-message-string err))
                                 ;; Skip this message
                                 ))))
                          ;; Add the current message
                          (message "DEBUG-OPENAI: Adding current message: %s"
                                   (substring context 0 (min 50 (length context))))
                          (setq msg-array (vconcat msg-array
                                                  `[((role . "user")
                                                    (content . ,context))]))
                          msg-array)
                      ;; Not chat mode - simple prompt
                      (if have-images
                          ;; We have images - need to format as a multimodal request
                          (let ((msg-array `[((role . "system")
                                            (content . ,system-prompt))]))
                            ;; Add the user message with content array
                            (let ((content-array `[((type . "text")
                                                  (text . ,context))]))
                              ;; Add images to the content array
                              (dolist (img ai-auto-complete-context-images)
                                (let* ((mime-type (plist-get img :mime-type))
                                       (img-content `((type . "image_url")
                                                     (image_url .
                                                      ((url . ,(concat "data:" mime-type ";base64," (plist-get img :data))))))))
                                  (setq content-array (vconcat content-array `[,img-content]))))
                              ;; Add the user message with content array
                              (setq msg-array (vconcat msg-array
                                                      `[((role . "user")
                                                        (content . ,content-array))])))
                            msg-array)
                        ;; No images - simple text prompt
                        `[((role . "system")
                          (content . ,system-prompt))
                         ((role . "user")
                          (content . ,context))])))
           ;; Get model attributes from configuration
           ;; Use the model directly - it should already be the correct model name
           ;; from ai-auto-complete-get-correct-model-name in the backend
           (config-attrs (ai-auto-complete-apply-model-attributes 'openai model (make-hash-table :test 'equal)))
           (temperature (or (and config-attrs (gethash "temperature" config-attrs)) 0.7))
           (max-tokens (or (and config-attrs (gethash "max_tokens" config-attrs)) 1024))
           (top-p (or (and config-attrs (gethash "top_p" config-attrs)) 0.9))
           (frequency-penalty (or (and config-attrs (gethash "frequency_penalty" config-attrs)) 0.0))
           (presence-penalty (or (and config-attrs (gethash "presence_penalty" config-attrs)) 0.0))
           ;; Build the full request data
           (data (json-encode `((model . ,model)
                               (messages . ,messages)
                               (temperature . ,temperature)
                               (max_tokens . ,max-tokens)
                               (top_p . ,top-p)
                               (frequency_penalty . ,frequency-penalty)
                               (presence_penalty . ,presence-penalty)
                               (n . 1)))))
      (setq ai-auto-complete--pending-request t)
      (ai-auto-complete--update-mode-line)
      (request url
               :type "POST"
               :headers headers
               :data data
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (setq ai-auto-complete--pending-request nil)
                           (ai-auto-complete--update-mode-line)
                           ;; Check if the response contains an error
                           (if (assoc 'error data)
                               (let* ((error-obj (cdr (assoc 'error data)))
                                      (error-msg (cdr (assoc 'message error-obj))))
                                 (message "OpenAI API error: %s" error-msg)
                                 (funcall callback (format "ERROR: %s" error-msg)))
                             ;; Normal response processing
                             (let* ((choice (aref (cdr (assoc 'choices data)) 0)) ; Get the first choice
                                    (message (cdr (assoc 'message choice)))
                                    (content (cdr (assoc 'content message))))
                               ;; Pass the agent-name to the tools processing function
                               (if (and (boundp 'ai-auto-complete-tools-enabled)
                                        ai-auto-complete-tools-enabled
                                        (fboundp 'ai-auto-complete-tools-process-response)
                                        ;; Only process for tools if there are tool calls in the response
                                        (string-match-p "<tool name=" content))
                                   (progn
                                     (message "Processing OpenAI response for tools with agent-name: %s"
                                              (or agent-name "nil"))
                                     (ai-auto-complete-tools-process-response content callback agent-name))
                                 (funcall callback content)))))))
               :error (cl-function
                       (lambda (&key error-thrown &allow-other-keys)
                         (setq ai-auto-complete--pending-request nil)
                         (ai-auto-complete--update-mode-line)
                         (message "Error: %S" error-thrown)
                         (funcall callback (format "ERROR: %S" error-thrown)))))))

;; Register the provider
(ai-auto-complete-register-provider 'openai #'ai-auto-complete-openai-provider)

(provide 'providers/openai)
;;; openai.el ends here
