;;; mcp-server-init.el --- MCP server initialization -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides functions to initialize and manage MCP servers
;; from the user's configured directory.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)

;; Set the default MCP servers directory
(defcustom ai-auto-complete-mcp-user-servers-directory
  "/home/<USER>/Documents/Cline/MCP"
  "Directory where user's MCP servers are stored."
  :type 'directory
  :group 'ai-auto-complete-mcp-general)

;; Check if Python is available
(defun ai-auto-complete-mcp-check-python ()
  "Check if Python is available for MCP servers.
Returns the path to Python executable or nil if not found."
  (let ((python-cmd (or (executable-find ai-auto-complete-mcp-python-command)
                        (executable-find "python3")
                        (executable-find "python"))))
    (if python-cmd
        (progn
          (message "MCP: Found Python at %s" python-cmd)
          python-cmd)
      (message "MCP: No Python executable found. Please install Python.")
      nil)))

;; Check if Node.js is available
(defun ai-auto-complete-mcp-check-node ()
  "Check if Node.js is available for MCP servers.
Returns the path to Node.js executable or nil if not found."
  (let ((node-cmd (or (executable-find "node")
                      (executable-find "nodejs"))))
    (if node-cmd
        (progn
          (message "MCP: Found Node.js at %s" node-cmd)
          node-cmd)
      (message "MCP: No Node.js executable found. Please install Node.js.")
      nil)))

;; Ensure a server is running before calling a tool
(defun ai-auto-complete-mcp-ensure-server-running (server-name)
  "Ensure MCP server with SERVER-NAME is running before calling tools.
Returns t if the server is running, nil otherwise."
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP: Server %s not found" server-name)
          nil)
      (let ((status (plist-get server :status))
            (process (plist-get server :process)))
        (if (and (eq status 'running) process (process-live-p process))
            t  ; Server is already running
          (message "MCP: Starting server %s..." server-name)
          (ai-auto-complete-mcp-start-server server-name))))))

;; Check if a directory contains a package.json file
(defun ai-auto-complete-mcp-directory-has-package-json (dir)
  "Check if DIR contains a package.json file."
  (file-exists-p (expand-file-name "package.json" dir)))

;; Extract Node.js file path from a script command
(defun ai-auto-complete-mcp-extract-node-file-from-script (script-cmd dir)
  "Extract a Node.js file path from SCRIPT-CMD in DIR.
Returns the full path to the file or nil if not found."
  (when (and script-cmd (stringp script-cmd))
    (when ai-auto-complete-mcp-debug-mode
      (message "MCP: Extracting file path from script command: %s" script-cmd))

    ;; Look for patterns like "node path/to/file.js" or "node ./path/to/file.js"
    (if (string-match "\\<node\\s-+\\(\\./\\)?\\([^ \t\n]+\\)" script-cmd)
        (let ((file-path (match-string 2 script-cmd)))
          (when ai-auto-complete-mcp-debug-mode
            (message "MCP: Extracted file path: %s" file-path))

          (when file-path
            (let ((full-path (expand-file-name file-path dir)))
              (when ai-auto-complete-mcp-debug-mode
                (message "MCP: Checking full path: %s (exists: %s)"
                         full-path (if (file-exists-p full-path) "yes" "no")))

              (if (file-exists-p full-path)
                  full-path
                ;; If the file doesn't exist directly, try to find it in common directories
                (let ((alt-paths (list
                                 (expand-file-name file-path (expand-file-name "dist" dir))
                                 (expand-file-name file-path (expand-file-name "build" dir))
                                 (expand-file-name file-path (expand-file-name "lib" dir))
                                 (expand-file-name file-path (expand-file-name "src" dir)))))
                  (when ai-auto-complete-mcp-debug-mode
                    (message "MCP: Checking alternative paths: %s"
                             (mapconcat (lambda (p) (format "%s (exists: %s)"
                                                           p (if (file-exists-p p) "yes" "no")))
                                        alt-paths ", ")))

                  (let ((found-path (cl-find-if #'file-exists-p alt-paths)))
                    (when (and found-path ai-auto-complete-mcp-debug-mode)
                      (message "MCP: Found file at alternative path: %s" found-path))
                    found-path))))))
      (when ai-auto-complete-mcp-debug-mode
        (message "MCP: No node command pattern found in script: %s" script-cmd))
      nil)))

;; Find the main entry point for a Node.js package
(defun ai-auto-complete-mcp-find-node-main-file (dir)
  "Find the main entry point for a Node.js package in DIR.
Returns the path to the main file or nil if not found."
  (let ((package-json-path (expand-file-name "package.json" dir)))
    (when (file-exists-p package-json-path)
      (condition-case nil
          (with-temp-buffer
            (insert-file-contents package-json-path)
            (let* ((json-object-type 'hash-table)
                   (json-array-type 'list)
                   (json-key-type 'string)
                   (pkg-data (json-read))
                   (scripts (gethash "scripts" pkg-data))
                   (main-file nil))

              ;; First, check for script entries in preferred order
              (when scripts
                (when ai-auto-complete-mcp-debug-mode
                  (message "MCP: Found scripts in package.json: %s"
                           (let ((script-list '()))
                             (maphash (lambda (k v) (push (format "%s: %s" k v) script-list)) scripts)
                             (mapconcat 'identity script-list ", "))))

                (let ((script-names '("start" "serve" "dev" "run" "main")))
                  ;; First check exact matches
                  (dolist (name script-names)
                    (when (and (not main-file) (gethash name scripts))
                      (let ((script-cmd (gethash name scripts)))
                        (when ai-auto-complete-mcp-debug-mode
                          (message "MCP: Checking script '%s': %s" name script-cmd))
                        (setq main-file (ai-auto-complete-mcp-extract-node-file-from-script script-cmd dir))
                        (when (and main-file ai-auto-complete-mcp-debug-mode)
                          (message "MCP: Found main file from script '%s': %s" name main-file)))))

                  ;; Then check for prefixed matches like "start:*"
                  (unless main-file
                    (maphash (lambda (key value)
                               (when (and (not main-file)
                                          (cl-some (lambda (prefix)
                                                     (string-prefix-p (concat prefix ":") key))
                                                   script-names))
                                 (when ai-auto-complete-mcp-debug-mode
                                   (message "MCP: Checking prefixed script '%s': %s" key value))
                                 (setq main-file (ai-auto-complete-mcp-extract-node-file-from-script value dir))
                                 (when (and main-file ai-auto-complete-mcp-debug-mode)
                                   (message "MCP: Found main file from prefixed script '%s': %s" key main-file))))
                             scripts))

                  ;; If still not found, try any script that uses node
                  (unless main-file
                    (maphash (lambda (key value)
                               (when (and (not main-file)
                                          (string-match-p "\\<node\\>" value))
                                 (when ai-auto-complete-mcp-debug-mode
                                   (message "MCP: Checking node script '%s': %s" key value))
                                 (setq main-file (ai-auto-complete-mcp-extract-node-file-from-script value dir))
                                 (when (and main-file ai-auto-complete-mcp-debug-mode)
                                   (message "MCP: Found main file from node script '%s': %s" key main-file))))
                             scripts))))

              ;; If no script found, fall back to main/bin fields
              (unless main-file
                (let ((main-entry (or (gethash "main" pkg-data)
                                      (gethash "bin" pkg-data)
                                      "index.js")))
                  (setq main-file (expand-file-name
                                   (if (stringp main-entry)
                                       main-entry
                                     (car (hash-table-values main-entry)))
                                   dir))))

              main-file))
        (error nil)))))

;; Import servers from the user's directory
(defun ai-auto-complete-mcp-import-user-servers ()
  "Import MCP servers from the user's configured directory."
  (interactive)
  (let ((servers-dir ai-auto-complete-mcp-user-servers-directory)
        (python-count 0)
        (node-count 0))
    (if (not (file-exists-p servers-dir))
        (message "MCP: User servers directory %s does not exist" servers-dir)
      (message "MCP: Importing servers from %s" servers-dir)

      ;; Set the servers directory
      (setq ai-auto-complete-mcp-servers-directory servers-dir)

      ;; Scan the directory for servers
      (when (fboundp 'ai-auto-complete-mcp-scan-directory)
        (ai-auto-complete-mcp-scan-directory servers-dir))

      ;; Register each Python file as a server
      (dolist (file (directory-files servers-dir t "\\.py$"))
        (let ((server-name (file-name-base file)))
          (message "MCP: Registering Python server %s from %s" server-name file)
          (ai-auto-complete-mcp-register-server server-name file nil nil "python")
          (setq python-count (1+ python-count))))

      ;; Find and register Node.js packages
      (dolist (item (directory-files servers-dir t))
        (when (and (file-directory-p item)
                   (not (string-match "/\\.$" item))
                   (not (string-match "/\\.\\.$" item))
                   (ai-auto-complete-mcp-directory-has-package-json item))
          (let* ((dir-name (file-name-nondirectory item))
                 (server-name dir-name)
                 (main-file (ai-auto-complete-mcp-find-node-main-file item)))
            (when main-file
              (message "MCP: Registering Node.js server %s from %s" server-name main-file)
              (ai-auto-complete-mcp-register-server server-name main-file 'stdio
                                                   (format "Node.js MCP server: %s" server-name)
                                                   "node")
              (setq node-count (1+ node-count))))))

      (message "MCP: Registered %d Python and %d Node.js servers from %s"
               python-count node-count servers-dir))))

;; Wrapper for calling MCP tools that ensures the server is running
(defun ai-auto-complete-mcp-call-tool-with-server-check (server-name tool-name params callback)
  "Call TOOL-NAME on SERVER-NAME with PARAMS after ensuring the server is running.
CALLBACK will be called with the result."
  (if (ai-auto-complete-mcp-ensure-server-running server-name)
      (ai-auto-complete-mcp-call-tool server-name tool-name params callback)
    (funcall callback (format "Error: Server %s could not be started" server-name))))


;; This is the main entry point afte python bridge and typescript bridge have been installed.
;; This is where shit start to happen. This is loaded from mcp.el and then the function ai-auto-complete-mcp-init-servers is called.
;; But it's not doing much because it's not loading the dirs and settings as it is commented out.
;; Initialize MCP servers
(defun ai-auto-complete-mcp-init-servers ()
  "Initialize MCP servers from the user's directory and settings file."
  (interactive)

  ;; Check if Python and Node interpreters are available
  (let ((python-available (ai-auto-complete-mcp-check-python))
        (node-available (ai-auto-complete-mcp-check-node)))

    ;; Warn if neither runtime is available
    (when (not (or python-available node-available))
      (message "MCP: Neither Python nor Node.js is available. MCP functionality will be limited.")
      ;; Don't return here, as we can still proceed with limited functionality
      )

    ;; Import servers from the user's directory
   ; (ai-auto-complete-mcp-import-user-servers)

    ;; Import servers from settings file
  ;  (when (fboundp 'ai-auto-complete-mcp-init-settings-servers)
  ;    (ai-auto-complete-mcp-init-settings-servers))

    ;; Override the call-tool function to ensure servers are running
    (advice-add 'ai-auto-complete-mcp-call-tool :around
                (lambda (orig-fun server-name tool-name params callback)
                  (if (ai-auto-complete-mcp-ensure-server-running server-name)
                      (funcall orig-fun server-name tool-name params callback)
                    (funcall callback (format "Error: Server %s could not be started" server-name))))
                '((name . "ensure-server-running")))

    ;; Log the initialization with runtime availability
    (message "MCP: Servers initialized from directory %s and settings file (Python: %s, Node.js: %s)"
             ai-auto-complete-mcp-user-servers-directory
             (if python-available "available" "not available")
             (if node-available "available" "not available"))))

;; Initialize on load
(ai-auto-complete-mcp-init-servers)
(provide 'mcp/mcp-server-init)
;;; mcp-server-init.el ends here
