;;; test-settings-server-tools.el --- Test settings-based server with mock tools -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides tests for settings-based server with mock tools.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-server-type)
(require 'mcp/mcp-server-bridge)
(require 'mcp/mcp-settings)

;; Create a temporary settings file for testing
(defvar ai-auto-complete-mcp-test-settings-server-tools-file
  (expand-file-name "mcp-test-settings-server-tools.json" temporary-file-directory)
  "Temporary settings file for MCP settings server with mock tools tests.")

;; Clean up test files
(defun ai-auto-complete-mcp-test-settings-server-tools-cleanup ()
  "Clean up the test settings file."
  (when (file-exists-p ai-auto-complete-mcp-test-settings-server-tools-file)
    (delete-file ai-auto-complete-mcp-test-settings-server-tools-file)))

;; Create a test settings file
(defun ai-auto-complete-mcp-create-test-settings-server-tools-file ()
  "Create a test settings file with sample MCP server configurations."
  (with-temp-file ai-auto-complete-mcp-test-settings-server-tools-file
    (insert "{\n")
    (insert "  \"mcpServers\": {\n")
    (insert "    \"ElevenLabs\": {\n")
    (insert "      \"command\": \"uvx\",\n")
    (insert "      \"args\": [\"elevenlabs-mcp\"],\n")
    (insert "      \"env\": {\n")
    (insert "        \"ELEVENLABS_API_KEY\": \"test-api-key\"\n")
    (insert "      }\n")
    (insert "    }\n")
    (insert "  }\n")
    (insert "}\n")))

;; Set up test environment
(defun ai-auto-complete-mcp-test-settings-server-tools-setup ()
  "Set up the test environment for settings server with mock tools testing."
  ;; Clean up any previous test data
  (ai-auto-complete-mcp-test-settings-server-tools-cleanup)
  
  ;; Create test settings file
  (ai-auto-complete-mcp-create-test-settings-server-tools-file)
  
  ;; Override the settings file path
  (let ((original-file ai-auto-complete-mcp-settings-file))
    (setq ai-auto-complete-mcp-settings-file ai-auto-complete-mcp-test-settings-server-tools-file)
    ;; Return the original file path so we can restore it later
    original-file))

;; Test settings-based server with mock tools
(defun ai-auto-complete-mcp-test-settings-server-tools-handling ()
  "Test settings-based server with mock tools handling."
  (interactive)
  
  ;; Set up test environment
  (let ((original-settings-file (ai-auto-complete-mcp-test-settings-server-tools-setup)))
    
    ;; Clear existing servers
    (maphash (lambda (name _)
               (remhash name ai-auto-complete-mcp-servers))
             ai-auto-complete-mcp-servers)
    
    ;; Enable debug mode
    (setq ai-auto-complete-mcp-debug-mode t)
    
    ;; Register the server bridge transport
    (message "\nRegistering server bridge transport")
    (ai-auto-complete-mcp-register-server-bridge-transport)
    
    ;; Register servers from settings
    (message "\nRegistering servers from settings file: %s" ai-auto-complete-mcp-settings-file)
    (condition-case err
        (let ((count (ai-auto-complete-mcp-register-servers-from-settings)))
          (message "✓ Successfully registered %d servers from settings" count))
      (error
       (message "✗ Error registering servers: %s" (error-message-string err))))
    
    ;; Check if servers were registered
    (let ((servers (ai-auto-complete-mcp-list-servers)))
      (message "\nRegistered servers: %s" servers)
      
      ;; Check ElevenLabs server
      (let ((eleven-server (ai-auto-complete-mcp-get-server "ElevenLabs")))
        (if eleven-server
            (progn
              (message "✓ ElevenLabs server registered successfully")
              (message "  Path: %s" (plist-get eleven-server :path))
              (message "  Runner: %s" (plist-get eleven-server :runner))
              (message "  Transport: %s" (plist-get eleven-server :transport))
              (message "  Server Type: %s" (plist-get eleven-server :server-type))
              (message "  Args: %S" (plist-get eleven-server :args))
              (message "  Env: %S" (plist-get eleven-server :env)))
          (message "✗ Failed to register ElevenLabs server")))
      
      ;; Try to start the server
      (message "\nStarting ElevenLabs server:")
      (condition-case err
          (if (ai-auto-complete-mcp-start-server "ElevenLabs")
              (message "  ✓ ElevenLabs server started successfully")
            (message "  ✗ Failed to start ElevenLabs server"))
        (error (message "  ✗ Error starting ElevenLabs server: %s" (error-message-string err))))
      
      ;; List tools
      (message "\nListing tools from ElevenLabs server:")
      (condition-case err
          (let ((tools (ai-auto-complete-mcp-list-tools "ElevenLabs")))
            (message "  Tools: %S" tools))
        (error (message "  ✗ Error listing tools: %s" (error-message-string err))))
      
      ;; List resources
      (message "\nListing resources from ElevenLabs server:")
      (condition-case err
          (let ((resources (ai-auto-complete-mcp-list-resources "ElevenLabs")))
            (message "  Resources: %S" resources))
        (error (message "  ✗ Error listing resources: %s" (error-message-string err))))
      
      ;; List prompts
      (message "\nListing prompts from ElevenLabs server:")
      (condition-case err
          (let ((prompts (ai-auto-complete-mcp-list-prompts "ElevenLabs")))
            (message "  Prompts: %S" prompts))
        (error (message "  ✗ Error listing prompts: %s" (error-message-string err))))
      
      ;; Call tool
      (message "\nCalling text-to-speech tool on ElevenLabs server:")
      (condition-case err
          (let ((result (ai-auto-complete-mcp-call-tool "ElevenLabs" "text-to-speech" '((text . "Hello, world!") (voice . "Adam")))))
            (message "  Result: %S" result))
        (error (message "  ✗ Error calling tool: %s" (error-message-string err))))
      
      ;; Read resource
      (message "\nReading voices resource from ElevenLabs server:")
      (condition-case err
          (let ((result (ai-auto-complete-mcp-read-resource "ElevenLabs" "voices")))
            (message "  Result: %S" result))
        (error (message "  ✗ Error reading resource: %s" (error-message-string err)))))
    
    ;; Restore original settings file path
    (setq ai-auto-complete-mcp-settings-file original-settings-file)
    
    ;; Clean up
    (ai-auto-complete-mcp-test-settings-server-tools-cleanup)
    
    ;; Disable debug mode
    (setq ai-auto-complete-mcp-debug-mode nil)))

;; Run the test
(defun ai-auto-complete-mcp-run-settings-server-tools-test ()
  "Run the settings server with mock tools test."
  (interactive)
  (ai-auto-complete-mcp-test-settings-server-tools-handling))

(provide 'mcp/tests/test-settings-server-tools)
;;; test-settings-server-tools.el ends here
