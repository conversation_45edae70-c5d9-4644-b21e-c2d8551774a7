;;; test-server-bridge.el --- Test server bridge for MCP servers -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides tests for the server bridge for MCP servers.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-server-type)
(require 'mcp/mcp-server-bridge)
(require 'mcp/mcp-settings)

;; Create a temporary settings file for testing
(defvar ai-auto-complete-mcp-test-server-bridge-file
  (expand-file-name "mcp-test-server-bridge.json" temporary-file-directory)
  "Temporary settings file for MCP server bridge tests.")

;; Clean up test files
(defun ai-auto-complete-mcp-test-server-bridge-cleanup ()
  "Clean up the test settings file."
  (when (file-exists-p ai-auto-complete-mcp-test-server-bridge-file)
    (delete-file ai-auto-complete-mcp-test-server-bridge-file)))

;; Create a test settings file
(defun ai-auto-complete-mcp-create-test-server-bridge-file ()
  "Create a test settings file with sample MCP server configurations."
  (with-temp-file ai-auto-complete-mcp-test-server-bridge-file
    (insert "{\n")
    (insert "  \"mcpServers\": {\n")
    (insert "    \"github.com/modelcontextprotocol/servers/tree/main/src/brave-search\": {\n")
    (insert "      \"command\": \"npx\",\n")
    (insert "      \"args\": [\n")
    (insert "        \"-y\",\n")
    (insert "        \"@modelcontextprotocol/server-brave-search\"\n")
    (insert "      ],\n")
    (insert "      \"env\": {\n")
    (insert "        \"BRAVE_API_KEY\": \"BSAYZHT8BTTufnAI2AZW46b2BNAmaUr\"\n")
    (insert "      },\n")
    (insert "      \"disabled\": false,\n")
    (insert "      \"autoApprove\": []\n")
    (insert "    },\n")
    (insert "    \"ElevenLabs\": {\n")
    (insert "      \"command\": \"uvx\",\n")
    (insert "      \"args\": [\"elevenlabs-mcp\"],\n")
    (insert "      \"env\": {\n")
    (insert "        \"ELEVENLABS_API_KEY\": \"test-api-key\"\n")
    (insert "      }\n")
    (insert "    }\n")
    (insert "  }\n")
    (insert "}\n")))

;; Set up test environment
(defun ai-auto-complete-mcp-test-server-bridge-setup ()
  "Set up the test environment for server bridge testing."
  ;; Clean up any previous test data
  (ai-auto-complete-mcp-test-server-bridge-cleanup)
  
  ;; Create test settings file
  (ai-auto-complete-mcp-create-test-server-bridge-file)
  
  ;; Override the settings file path
  (let ((original-file ai-auto-complete-mcp-settings-file))
    (setq ai-auto-complete-mcp-settings-file ai-auto-complete-mcp-test-server-bridge-file)
    ;; Return the original file path so we can restore it later
    original-file))

;; Test server type detection
(defun ai-auto-complete-mcp-test-server-type-detection ()
  "Test server type detection."
  (message "\nTesting server type detection:")
  
  ;; Test Python runners
  (dolist (runner '("python" "python3" "uvx" "mcp"))
    (let ((server-type (ai-auto-complete-mcp-determine-server-type-from-command runner)))
      (message "  Runner: %s -> Server type: %s" runner server-type)
      (if (eq server-type ai-auto-complete-mcp-server-type-python)
          (message "  ✓ Correctly identified as Python server")
        (message "  ✗ Failed to identify as Python server"))))
  
  ;; Test JavaScript/TypeScript runners
  (dolist (runner '("node" "npx" "ts-node"))
    (let ((server-type (ai-auto-complete-mcp-determine-server-type-from-command runner)))
      (message "  Runner: %s -> Server type: %s" runner server-type)
      (if (eq server-type ai-auto-complete-mcp-server-type-javascript)
          (message "  ✓ Correctly identified as JavaScript server")
        (message "  ✗ Failed to identify as JavaScript server"))))
  
  ;; Test unknown runners
  (dolist (runner '("unknown" "foo" "bar"))
    (let ((server-type (ai-auto-complete-mcp-determine-server-type-from-command runner)))
      (message "  Runner: %s -> Server type: %s" runner server-type)
      (if (eq server-type ai-auto-complete-mcp-server-type-unknown)
          (message "  ✓ Correctly identified as unknown server")
        (message "  ✗ Failed to identify as unknown server")))))

;; Test server bridge
(defun ai-auto-complete-mcp-test-server-bridge-handling ()
  "Test server bridge handling."
  (interactive)
  
  ;; Set up test environment
  (let ((original-settings-file (ai-auto-complete-mcp-test-server-bridge-setup)))
    
    ;; Clear existing servers
    (maphash (lambda (name _)
               (remhash name ai-auto-complete-mcp-servers))
             ai-auto-complete-mcp-servers)
    
    ;; Enable debug mode
    (setq ai-auto-complete-mcp-debug-mode t)
    
    ;; Test server type detection
    (ai-auto-complete-mcp-test-server-type-detection)
    
    ;; Register the server bridge transport
    (message "\nRegistering server bridge transport")
    (ai-auto-complete-mcp-register-server-bridge-transport)
    
    ;; Register servers from settings
    (message "\nRegistering servers from settings file: %s" ai-auto-complete-mcp-settings-file)
    (condition-case err
        (let ((count (ai-auto-complete-mcp-register-servers-from-settings)))
          (message "✓ Successfully registered %d servers from settings" count))
      (error
       (message "✗ Error registering servers: %s" (error-message-string err))))
    
    ;; Check if servers were registered
    (let ((servers (ai-auto-complete-mcp-list-servers)))
      (message "\nRegistered servers: %s" servers)
      
      ;; Check brave-search server
      (let ((brave-server (ai-auto-complete-mcp-get-server "brave-search")))
        (if brave-server
            (progn
              (message "✓ brave-search server registered successfully")
              (message "  Path: %s" (plist-get brave-server :path))
              (message "  Runner: %s" (plist-get brave-server :runner))
              (message "  Transport: %s" (plist-get brave-server :transport))
              (message "  Server Type: %s" (plist-get brave-server :server-type))
              (message "  Args: %S" (plist-get brave-server :args))
              (message "  Env: %S" (plist-get brave-server :env)))
          (message "✗ Failed to register brave-search server")))
      
      ;; Check ElevenLabs server
      (let ((eleven-server (ai-auto-complete-mcp-get-server "ElevenLabs")))
        (if eleven-server
            (progn
              (message "✓ ElevenLabs server registered successfully")
              (message "  Path: %s" (plist-get eleven-server :path))
              (message "  Runner: %s" (plist-get eleven-server :runner))
              (message "  Transport: %s" (plist-get eleven-server :transport))
              (message "  Server Type: %s" (plist-get eleven-server :server-type))
              (message "  Args: %S" (plist-get eleven-server :args))
              (message "  Env: %S" (plist-get eleven-server :env)))
          (message "✗ Failed to register ElevenLabs server")))
      
      ;; Try to start the servers
      (message "\nTrying to start the servers:")
      
      ;; Try to start brave-search server
      (message "\nStarting brave-search server:")
      (condition-case err
          (if (ai-auto-complete-mcp-start-server "brave-search")
              (message "  ✓ brave-search server started successfully")
            (message "  ✗ Failed to start brave-search server"))
        (error (message "  ✗ Error starting brave-search server: %s" (error-message-string err))))
      
      ;; Try to start ElevenLabs server
      (message "\nStarting ElevenLabs server:")
      (condition-case err
          (if (ai-auto-complete-mcp-start-server "ElevenLabs")
              (message "  ✓ ElevenLabs server started successfully")
            (message "  ✗ Failed to start ElevenLabs server"))
        (error (message "  ✗ Error starting ElevenLabs server: %s" (error-message-string err)))))
    
    ;; Restore original settings file path
    (setq ai-auto-complete-mcp-settings-file original-settings-file)
    
    ;; Clean up
    (ai-auto-complete-mcp-test-server-bridge-cleanup)
    
    ;; Disable debug mode
    (setq ai-auto-complete-mcp-debug-mode nil)))

;; Run the test
(defun ai-auto-complete-mcp-run-server-bridge-test ()
  "Run the server bridge test."
  (interactive)
  (ai-auto-complete-mcp-test-server-bridge-handling))

(provide 'mcp/tests/test-server-bridge)
;;; test-server-bridge.el ends here
