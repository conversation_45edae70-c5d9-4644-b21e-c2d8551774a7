;;; test-node-script-detection.el --- Test Node.js script detection -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a test for Node.js MCP server detection with script entries.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-directory)
(require 'mcp/mcp-server-init)

;; Create a temporary directory for testing
(defvar ai-auto-complete-mcp-script-test-dir
  (expand-file-name "mcp-script-test" temporary-file-directory)
  "Temporary directory for MCP script tests.")

;; Clean up test directory
(defun ai-auto-complete-mcp-script-test-cleanup ()
  "Clean up the test directory."
  (when (file-exists-p ai-auto-complete-mcp-script-test-dir)
    (delete-directory ai-auto-complete-mcp-script-test-dir t)))

;; Create a test package.json with script entries
(defun ai-auto-complete-mcp-create-test-package-json ()
  "Create a test package.json file with script entries."
  (let ((test-dir (expand-file-name "weather-server" ai-auto-complete-mcp-script-test-dir))
        (dist-dir (expand-file-name "dist" (expand-file-name "weather-server" ai-auto-complete-mcp-script-test-dir))))
    
    ;; Create directories
    (make-directory test-dir t)
    (make-directory dist-dir t)
    
    ;; Create package.json
    (with-temp-file (expand-file-name "package.json" test-dir)
      (insert "{\n")
      (insert "  \"name\": \"mcp-servers\",\n")
      (insert "  \"version\": \"1.0.0\",\n")
      (insert "  \"description\": \"MCP Servers using the official TypeScript SDK\",\n")
      (insert "  \"type\": \"module\",\n")
      (insert "  \"scripts\": {\n")
      (insert "    \"build\": \"tsc\",\n")
      (insert "    \"start:weather\": \"node dist/typescript-weather-server.js\"\n")
      (insert "  },\n")
      (insert "  \"dependencies\": {\n")
      (insert "    \"@modelcontextprotocol/sdk\": \"^1.11.0\",\n")
      (insert "    \"npx\": \"^10.2.2\",\n")
      (insert "    \"zod\": \"^3.22.4\"\n")
      (insert "  },\n")
      (insert "  \"devDependencies\": {\n")
      (insert "    \"@types/node\": \"^20.11.0\",\n")
      (insert "    \"typescript\": \"^5.3.3\"\n")
      (insert "  }\n")
      (insert "}\n"))
    
    ;; Create the server file
    (with-temp-file (expand-file-name "typescript-weather-server.js" dist-dir)
      (insert "#!/usr/bin/env node\n\n")
      (insert "console.log('Weather MCP Server');\n\n")
      (insert "// This is a mock server file for testing\n")
      (insert "const readline = require('readline');\n\n")
      (insert "const rl = readline.createInterface({\n")
      (insert "  input: process.stdin,\n")
      (insert "  output: process.stdout\n")
      (insert "});\n\n")
      (insert "rl.on('line', (line) => {\n")
      (insert "  if (line.trim()) {\n")
      (insert "    try {\n")
      (insert "      const request = JSON.parse(line);\n")
      (insert "      const response = {\n")
      (insert "        jsonrpc: '2.0',\n")
      (insert "        id: request.id,\n")
      (insert "        result: { serverName: 'weather-server' }\n")
      (insert "      };\n")
      (insert "      console.log(JSON.stringify(response));\n")
      (insert "    } catch (error) {\n")
      (insert "      console.error(`Error: ${error.message}`);\n")
      (insert "    }\n")
      (insert "  }\n")
      (insert "});\n"))
    
    ;; Make the file executable
    (set-file-modes (expand-file-name "typescript-weather-server.js" dist-dir) #o755)))

;; Set up test environment
(defun ai-auto-complete-mcp-script-test-setup ()
  "Set up the test environment."
  ;; Clean up any previous test data
  (ai-auto-complete-mcp-script-test-cleanup)
  
  ;; Create test directory
  (make-directory ai-auto-complete-mcp-script-test-dir t)
  
  ;; Create test package.json with script entries
  (ai-auto-complete-mcp-create-test-package-json))

;; Test script detection
(defun ai-auto-complete-mcp-test-script-detection ()
  "Test MCP server detection for Node.js servers with script entries."
  (interactive)
  
  ;; Set up test environment
  (ai-auto-complete-mcp-script-test-setup)
  
  ;; Clear existing servers
  (maphash (lambda (name _)
             (remhash name ai-auto-complete-mcp-servers))
           ai-auto-complete-mcp-servers)
  
  ;; Set the test directory as the servers directory
  (let ((ai-auto-complete-mcp-servers-directory ai-auto-complete-mcp-script-test-dir))
    
    ;; Scan the directory
    (message "Scanning directory: %s" ai-auto-complete-mcp-script-test-dir)
    (ai-auto-complete-mcp-scan-directory ai-auto-complete-mcp-script-test-dir)
    
    ;; Check if servers were detected
    (let* ((servers (ai-auto-complete-mcp-list-servers))
           (weather-server (ai-auto-complete-mcp-get-server "weather-server")))
      (message "Detected servers: %s" servers)
      
      ;; Check if weather-server was detected
      (if (member "weather-server" servers)
          (progn
            (message "✓ Node.js server with script entry detected successfully")
            ;; Check if the correct path was found
            (let ((server-path (plist-get weather-server :path)))
              (if (and server-path (string-match-p "dist/typescript-weather-server.js$" server-path))
                  (message "✓ Correct script path detected: %s" server-path)
                (message "✗ Incorrect script path detected: %s" server-path))))
        (message "✗ Failed to detect Node.js server with script entry"))))
  
  ;; Clean up
  (ai-auto-complete-mcp-script-test-cleanup))

;; Run the test
(defun ai-auto-complete-mcp-run-script-detection-test ()
  "Run the Node.js MCP server script detection test."
  (interactive)
  (ai-auto-complete-mcp-test-script-detection))

(provide 'mcp/tests/test-node-script-detection)
;;; test-node-script-detection.el ends here
