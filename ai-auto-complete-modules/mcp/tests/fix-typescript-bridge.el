;;; fix-typescript-bridge.el --- Fix TypeScript bridge functions -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides fixes for the TypeScript bridge functions.

;;; Code:

(require 'mcp/transports/mcp-typescript-bridge)

;; Fix the stop-server function
(defun ai-auto-complete-mcp-typescript-bridge-stop-server (server-name &optional path)
  "Stop an MCP server with SERVER-NAME at PATH using the TypeScript bridge."
  (message "MCP typescript-bridge: Stopping server %s" server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          nil)

      ;; Update server status
      (ai-auto-complete-mcp-update-server-status server-name 'stopped)
      (ai-auto-complete-mcp-update-server-process server-name nil)

      (message "MCP typescript-bridge: Server %s stopped" server-name)
      t)))

;; Fix the list-tools function
(defun ai-auto-complete-mcp-typescript-bridge-list-tools (server-name &optional callback)
  "List tools on an MCP server.
SERVER-NAME is the name of the server.
Optional CALLBACK is a function to call with the result."
  (message "MCP typescript-bridge: Listing tools on server %s" server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          (when callback (funcall callback nil))
          nil)

      (let ((process (plist-get server :process)))
        (if (not process)
            (progn
              (message "MCP typescript-bridge: Server %s has no process" server-name)
              (when callback (funcall callback nil))
              nil)

          (let ((request-id (format "req-list-tools-%s" (random 10000)))
                (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                          "listTools"
                          `((serverPath . ,(plist-get server :path)))
                          request-id)))

            ;; Store the callback
            (puthash request-id
                    (lambda (response)
                      (if (plist-get response :error)
                          (progn
                            (message "MCP typescript-bridge: Error listing tools: %s"
                                    (plist-get (plist-get response :error) :message))
                            (when callback (funcall callback nil))
                            nil)
                        (let ((result (plist-get response :result)))
                          (when callback (funcall callback result))
                          result)))
                    ai-auto-complete-mcp-typescript-bridge-callback-table)

            ;; Send the command
            (process-send-string process (concat command "\n"))
            t))))))

;; Fix the list-resources function
(defun ai-auto-complete-mcp-typescript-bridge-list-resources (server-name &optional callback)
  "List resources on an MCP server.
SERVER-NAME is the name of the server.
Optional CALLBACK is a function to call with the result."
  (message "MCP typescript-bridge: Listing resources on server %s" server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          (when callback (funcall callback nil))
          nil)

      (let ((process (plist-get server :process)))
        (if (not process)
            (progn
              (message "MCP typescript-bridge: Server %s has no process" server-name)
              (when callback (funcall callback nil))
              nil)

          (let ((request-id (format "req-list-resources-%s" (random 10000)))
                (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                          "listResources"
                          `((serverPath . ,(plist-get server :path)))
                          request-id)))

            ;; Store the callback
            (puthash request-id
                    (lambda (response)
                      (if (plist-get response :error)
                          (progn
                            (message "MCP typescript-bridge: Error listing resources: %s"
                                    (plist-get (plist-get response :error) :message))
                            (when callback (funcall callback nil))
                            nil)
                        (let ((result (plist-get response :result)))
                          (when callback (funcall callback result))
                          result)))
                    ai-auto-complete-mcp-typescript-bridge-callback-table)

            ;; Send the command
            (process-send-string process (concat command "\n"))
            t))))))

;; Fix the list-prompts function
(defun ai-auto-complete-mcp-typescript-bridge-list-prompts (server-name &optional callback)
  "List prompts on an MCP server.
SERVER-NAME is the name of the server.
Optional CALLBACK is a function to call with the result."
  (message "MCP typescript-bridge: Listing prompts on server %s" server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          (when callback (funcall callback nil))
          nil)

      (let ((process (plist-get server :process)))
        (if (not process)
            (progn
              (message "MCP typescript-bridge: Server %s has no process" server-name)
              (when callback (funcall callback nil))
              nil)

          (let ((request-id (format "req-list-prompts-%s" (random 10000)))
                (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                          "listPrompts"
                          `((serverPath . ,(plist-get server :path)))
                          request-id)))

            ;; Store the callback
            (puthash request-id
                    (lambda (response)
                      (if (plist-get response :error)
                          (progn
                            (message "MCP typescript-bridge: Error listing prompts: %s"
                                    (plist-get (plist-get response :error) :message))
                            (when callback (funcall callback nil))
                            nil)
                        (let ((result (plist-get response :result)))
                          (when callback (funcall callback result))
                          result)))
                    ai-auto-complete-mcp-typescript-bridge-callback-table)

            ;; Send the command
            (process-send-string process (concat command "\n"))
            t))))))

;; Fix the read-resource function
(defun ai-auto-complete-mcp-typescript-bridge-read-resource (server-name uri &optional callback)
  "Read a resource from an MCP server.
SERVER-NAME is the name of the server.
URI is the URI of the resource to read.
Optional CALLBACK is a function to call with the result."
  (message "MCP typescript-bridge: Reading resource %s from server %s" uri server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          (when callback (funcall callback nil))
          nil)

      (let ((process (plist-get server :process)))
        (if (not process)
            (progn
              (message "MCP typescript-bridge: Server %s has no process" server-name)
              (when callback (funcall callback nil))
              nil)

          (let ((request-id (format "req-read-resource-%s" (random 10000)))
                (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                          "readResource"
                          `((serverPath . ,(plist-get server :path))
                            (uri . ,uri))
                          request-id)))

            ;; Store the callback
            (puthash request-id
                    (lambda (response)
                      (if (plist-get response :error)
                          (progn
                            (message "MCP typescript-bridge: Error reading resource %s: %s"
                                    uri (plist-get (plist-get response :error) :message))
                            (when callback (funcall callback nil))
                            nil)
                        (let ((result (plist-get response :result)))
                          (when callback (funcall callback result))
                          result)))
                    ai-auto-complete-mcp-typescript-bridge-callback-table)

            ;; Send the command
            (process-send-string process (concat command "\n"))
            t))))))

;; Fix the get-prompt function
(defun ai-auto-complete-mcp-typescript-bridge-get-prompt (server-name prompt-name params &optional callback)
  "Get a prompt from an MCP server.
SERVER-NAME is the name of the server.
PROMPT-NAME is the name of the prompt to get.
PARAMS is the parameters to pass to the prompt.
Optional CALLBACK is a function to call with the result."
  (message "MCP typescript-bridge: Getting prompt %s from server %s" prompt-name server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          (when callback (funcall callback nil))
          nil)

      (let ((process (plist-get server :process)))
        (if (not process)
            (progn
              (message "MCP typescript-bridge: Server %s has no process" server-name)
              (when callback (funcall callback nil))
              nil)

          (let ((request-id (format "req-get-prompt-%s" (random 10000)))
                (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                          "getPrompt"
                          `((serverPath . ,(plist-get server :path))
                            (promptName . ,prompt-name)
                            (arguments . ,params))
                          request-id)))

            ;; Store the callback
            (puthash request-id
                    (lambda (response)
                      (if (plist-get response :error)
                          (progn
                            (message "MCP typescript-bridge: Error getting prompt %s: %s"
                                    prompt-name (plist-get (plist-get response :error) :message))
                            (when callback (funcall callback nil))
                            nil)
                        (let ((result (plist-get response :result)))
                          (when callback (funcall callback result))
                          result)))
                    ai-auto-complete-mcp-typescript-bridge-callback-table)

            ;; Send the command
            (process-send-string process (concat command "\n"))
            t))))))

;; Fix the call-tool function
(defun ai-auto-complete-mcp-typescript-bridge-call-tool (server-name tool-name params &optional callback)
  "Call a tool on an MCP server.
SERVER-NAME is the name of the server.
TOOL-NAME is the name of the tool to call.
PARAMS is the parameters to pass to the tool.
Optional CALLBACK is a function to call with the result."
  (message "MCP typescript-bridge: Calling tool %s on server %s" tool-name server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          (when callback (funcall callback nil))
          nil)

      (let ((process (plist-get server :process)))
        (if (not process)
            (progn
              (message "MCP typescript-bridge: Server %s has no process" server-name)
              (when callback (funcall callback nil))
              nil)

          (let ((request-id (format "req-call-tool-%s" (random 10000)))
                (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                          "callTool"
                          `((serverPath . ,(plist-get server :path))
                            (toolName . ,tool-name)
                            (arguments . ,params))
                          (format "req-call-tool-%s" (random 10000)))))

            ;; Store the callback
            (puthash request-id
                    (lambda (response)
                      (if (plist-get response :error)
                          (progn
                            (message "MCP typescript-bridge: Error calling tool %s: %s"
                                    tool-name (plist-get (plist-get response :error) :message))
                            (when callback (funcall callback nil))
                            nil)
                        (let ((result (plist-get response :result)))
                          (let ((formatted-result
                                 (if (and result (listp result) (plist-get result :content))
                                     (let ((content (plist-get result :content)))
                                       (if (and (listp content) (> (length content) 0))
                                           (let ((first-item (car content)))
                                             (if (and (listp first-item) (plist-get first-item :text))
                                                 (plist-get first-item :text)
                                               (json-encode result)))
                                         (json-encode result)))
                                   (json-encode result))))
                            (when callback (funcall callback formatted-result))
                            formatted-result))))
                    ai-auto-complete-mcp-typescript-bridge-callback-table)

            ;; Send the command
            (process-send-string process (concat command "\n"))
            t))))))

;; Install the fixed functions
(defun ai-auto-complete-mcp-typescript-bridge-install-fixes ()
  "Install the fixed TypeScript bridge functions."
  (interactive)
  
  ;; Install the fixed functions
  (fset 'ai-auto-complete-mcp-typescript-bridge-stop-server
        (symbol-function 'ai-auto-complete-mcp-typescript-bridge-stop-server))
  
  (fset 'ai-auto-complete-mcp-typescript-bridge-list-tools
        (symbol-function 'ai-auto-complete-mcp-typescript-bridge-list-tools))
  
  (fset 'ai-auto-complete-mcp-typescript-bridge-list-resources
        (symbol-function 'ai-auto-complete-mcp-typescript-bridge-list-resources))
  
  (fset 'ai-auto-complete-mcp-typescript-bridge-list-prompts
        (symbol-function 'ai-auto-complete-mcp-typescript-bridge-list-prompts))
  
  (fset 'ai-auto-complete-mcp-typescript-bridge-read-resource
        (symbol-function 'ai-auto-complete-mcp-typescript-bridge-read-resource))
  
  (fset 'ai-auto-complete-mcp-typescript-bridge-get-prompt
        (symbol-function 'ai-auto-complete-mcp-typescript-bridge-get-prompt))
  
  (fset 'ai-auto-complete-mcp-typescript-bridge-call-tool
        (symbol-function 'ai-auto-complete-mcp-typescript-bridge-call-tool))
  
  ;; Reinstall the TypeScript bridge transport
  (ai-auto-complete-mcp-typescript-bridge-install)
  
  (message "TypeScript bridge fixes installed"))

(provide 'mcp/tests/fix-typescript-bridge)
;;; fix-typescript-bridge.el ends here
