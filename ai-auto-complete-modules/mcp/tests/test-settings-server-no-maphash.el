;;; test-settings-server-no-maphash.el --- Test settings-based server without maphash -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides tests for settings-based server without using maphash.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-settings)

;; Create a temporary settings file for testing
(defvar ai-auto-complete-mcp-test-settings-no-maphash-file
  (expand-file-name "mcp-test-settings-no-maphash.json" temporary-file-directory)
  "Temporary settings file for MCP no-maphash tests.")

;; Clean up test files
(defun ai-auto-complete-mcp-test-settings-no-maphash-cleanup ()
  "Clean up the test settings file."
  (when (file-exists-p ai-auto-complete-mcp-test-settings-no-maphash-file)
    (delete-file ai-auto-complete-mcp-test-settings-no-maphash-file)))

;; Create a test settings file
(defun ai-auto-complete-mcp-create-test-settings-no-maphash-file ()
  "Create a test settings file with sample MCP server configurations."
  (with-temp-file ai-auto-complete-mcp-test-settings-no-maphash-file
    (insert "{\n")
    (insert "  \"mcpServers\": {\n")
    (insert "    \"github.com/modelcontextprotocol/servers/tree/main/src/brave-search\": {\n")
    (insert "      \"command\": \"npx\",\n")
    (insert "      \"args\": [\n")
    (insert "        \"-y\",\n")
    (insert "        \"@modelcontextprotocol/server-brave-search\"\n")
    (insert "      ],\n")
    (insert "      \"env\": {\n")
    (insert "        \"BRAVE_API_KEY\": \"BSAYZHT8BTTufnAI2AZW46b2BNAmaUr\"\n")
    (insert "      },\n")
    (insert "      \"disabled\": false,\n")
    (insert "      \"autoApprove\": []\n")
    (insert "    },\n")
    (insert "    \"ElevenLabs\": {\n")
    (insert "      \"command\": \"uvx\",\n")
    (insert "      \"args\": [\"elevenlabs-mcp\"],\n")
    (insert "      \"env\": {\n")
    (insert "        \"ELEVENLABS_API_KEY\": \"test-api-key\"\n")
    (insert "      }\n")
    (insert "    }\n")
    (insert "  }\n")
    (insert "}\n")))

;; Set up test environment
(defun ai-auto-complete-mcp-test-settings-no-maphash-setup ()
  "Set up the test environment for settings-based server no-maphash testing."
  ;; Clean up any previous test data
  (ai-auto-complete-mcp-test-settings-no-maphash-cleanup)
  
  ;; Create test settings file
  (ai-auto-complete-mcp-create-test-settings-no-maphash-file)
  
  ;; Override the settings file path
  (let ((original-file ai-auto-complete-mcp-settings-file))
    (setq ai-auto-complete-mcp-settings-file ai-auto-complete-mcp-test-settings-no-maphash-file)
    ;; Return the original file path so we can restore it later
    original-file))

;; Test settings-based server without maphash
(defun ai-auto-complete-mcp-test-settings-no-maphash-handling ()
  "Test MCP server handling from settings file without using maphash."
  (interactive)
  
  ;; Set up test environment
  (let ((original-settings-file (ai-auto-complete-mcp-test-settings-no-maphash-setup)))
    
    ;; Clear existing servers
    (maphash (lambda (name _)
               (remhash name ai-auto-complete-mcp-servers))
             ai-auto-complete-mcp-servers)
    
    ;; Enable debug mode
    (setq ai-auto-complete-mcp-debug-mode t)
    
    ;; Register the settings transport
    (message "Registering settings transport")
    (ai-auto-complete-mcp-register-settings-transport)
    
    ;; Register servers from settings
    (message "Registering servers from settings file: %s" ai-auto-complete-mcp-settings-file)
    (condition-case err
        (let ((count (ai-auto-complete-mcp-register-servers-from-settings)))
          (message "✓ Successfully registered %d servers from settings" count))
      (error
       (message "✗ Error registering servers: %s" (error-message-string err))))
    
    ;; Check if servers were registered
    (let ((servers (ai-auto-complete-mcp-list-servers)))
      (message "Registered servers: %s" servers)
      
      ;; Check brave-search server
      (let ((brave-server (ai-auto-complete-mcp-get-server "brave-search")))
        (if brave-server
            (progn
              (message "✓ brave-search server registered successfully")
              (message "  Path: %s" (plist-get brave-server :path))
              (message "  Runner: %s" (plist-get brave-server :runner))
              (message "  Transport: %s" (plist-get brave-server :transport))
              (message "  Args: %S" (plist-get brave-server :args))
              (message "  Env: %S" (plist-get brave-server :env)))
          (message "✗ Failed to register brave-search server")))
      
      ;; Check ElevenLabs server
      (let ((eleven-server (ai-auto-complete-mcp-get-server "ElevenLabs")))
        (if eleven-server
            (progn
              (message "✓ ElevenLabs server registered successfully")
              (message "  Path: %s" (plist-get eleven-server :path))
              (message "  Runner: %s" (plist-get eleven-server :runner))
              (message "  Transport: %s" (plist-get eleven-server :transport))
              (message "  Args: %S" (plist-get eleven-server :args))
              (message "  Env: %S" (plist-get eleven-server :env)))
          (message "✗ Failed to register ElevenLabs server")))
      
      ;; Try to start the servers
      (message "\nTrying to start the servers:")
      
      ;; Try to start brave-search server
      (message "\nStarting brave-search server:")
      (condition-case err
          (if (ai-auto-complete-mcp-start-server "brave-search")
              (message "  ✓ brave-search server started successfully")
            (message "  ✗ Failed to start brave-search server"))
        (error (message "  ✗ Error starting brave-search server: %s" (error-message-string err))))
      
      ;; Try to start ElevenLabs server
      (message "\nStarting ElevenLabs server:")
      (condition-case err
          (if (ai-auto-complete-mcp-start-server "ElevenLabs")
              (message "  ✓ ElevenLabs server started successfully")
            (message "  ✗ Failed to start ElevenLabs server"))
        (error (message "  ✗ Error starting ElevenLabs server: %s" (error-message-string err)))))
    
    ;; Restore original settings file path
    (setq ai-auto-complete-mcp-settings-file original-settings-file)
    
    ;; Clean up
    (ai-auto-complete-mcp-test-settings-no-maphash-cleanup)
    
    ;; Disable debug mode
    (setq ai-auto-complete-mcp-debug-mode nil)))

;; Run the test
(defun ai-auto-complete-mcp-run-settings-no-maphash-test ()
  "Run the settings-based MCP server no-maphash test."
  (interactive)
  (ai-auto-complete-mcp-test-settings-no-maphash-handling))

(provide 'mcp/tests/test-settings-server-no-maphash)
;;; test-settings-server-no-maphash.el ends here
