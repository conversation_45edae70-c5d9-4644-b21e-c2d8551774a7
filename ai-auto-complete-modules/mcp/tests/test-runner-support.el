;;; test-runner-support.el --- Test runner support for MCP servers -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides tests for the runner support in MCP servers.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-directory)
(require 'mcp/mcp-settings)

;; Create a temporary directory for testing
(defvar ai-auto-complete-mcp-runner-test-dir
  (expand-file-name "mcp-runner-test" temporary-file-directory)
  "Temporary directory for MCP runner tests.")

;; Clean up test directory
(defun ai-auto-complete-mcp-runner-test-cleanup ()
  "Clean up the test directory."
  (when (file-exists-p ai-auto-complete-mcp-runner-test-dir)
    (delete-directory ai-auto-complete-mcp-runner-test-dir t)))

;; Set up test environment
(defun ai-auto-complete-mcp-runner-test-setup ()
  "Set up the test environment."
  ;; Clean up any previous test data
  (ai-auto-complete-mcp-runner-test-cleanup)
  
  ;; Create test directory
  (make-directory ai-auto-complete-mcp-runner-test-dir t)
  
  ;; Create a Python MCP server
  (ai-auto-complete-mcp-create-new-server "python-server" ai-auto-complete-mcp-runner-test-dir 'python)
  
  ;; Create a Node.js MCP server
  (ai-auto-complete-mcp-create-new-server "node-server" ai-auto-complete-mcp-runner-test-dir 'node))

;; Test runner support
(defun ai-auto-complete-mcp-test-runner-support ()
  "Test runner support for MCP servers."
  (interactive)
  
  ;; Set up test environment
  (ai-auto-complete-mcp-runner-test-setup)
  
  ;; Clear existing servers
  (maphash (lambda (name _)
             (remhash name ai-auto-complete-mcp-servers))
           ai-auto-complete-mcp-servers)
  
  ;; Set the test directory as the servers directory
  (let ((ai-auto-complete-mcp-servers-directory ai-auto-complete-mcp-runner-test-dir))
    
    ;; Scan the directory
    (message "Scanning directory: %s" ai-auto-complete-mcp-runner-test-dir)
    (ai-auto-complete-mcp-scan-directory ai-auto-complete-mcp-runner-test-dir)
    
    ;; Check if servers were detected
    (let ((servers (ai-auto-complete-mcp-list-servers)))
      (message "Detected servers: %s" servers)
      
      ;; Check Python server
      (let ((python-server (ai-auto-complete-mcp-get-server "python-server")))
        (if python-server
            (progn
              (message "✓ Python server detected successfully")
              (message "  Runner: %s" (plist-get python-server :runner))
              (if (string= (plist-get python-server :runner) "python")
                  (message "  ✓ Correct runner detected")
                (message "  ✗ Incorrect runner detected: %s" (plist-get python-server :runner))))
          (message "✗ Failed to detect Python server")))
      
      ;; Check Node.js server
      (let ((node-server (ai-auto-complete-mcp-get-server "node-server")))
        (if node-server
            (progn
              (message "✓ Node.js server detected successfully")
              (message "  Runner: %s" (plist-get node-server :runner))
              (if (string= (plist-get node-server :runner) "node")
                  (message "  ✓ Correct runner detected")
                (message "  ✗ Incorrect runner detected: %s" (plist-get node-server :runner))))
          (message "✗ Failed to detect Node.js server"))))
    
    ;; Test custom runner
    (let ((custom-server-name "custom-runner-server")
          (custom-server-path (expand-file-name "custom-server.py" ai-auto-complete-mcp-runner-test-dir))
          (custom-runner "custom-python"))
      
      ;; Create a server file
      (with-temp-file custom-server-path
        (insert "print('Custom runner server')"))
      
      ;; Register the server with a custom runner
      (ai-auto-complete-mcp-register-server custom-server-name custom-server-path nil nil custom-runner)
      
      ;; Check if the server was registered with the correct runner
      (let ((custom-server (ai-auto-complete-mcp-get-server custom-server-name)))
        (if custom-server
            (progn
              (message "✓ Custom runner server registered successfully")
              (message "  Runner: %s" (plist-get custom-server :runner))
              (if (string= (plist-get custom-server :runner) custom-runner)
                  (message "  ✓ Correct custom runner detected")
                (message "  ✗ Incorrect custom runner detected: %s" (plist-get custom-server :runner))))
          (message "✗ Failed to register custom runner server")))))
  
  ;; Clean up
  (ai-auto-complete-mcp-runner-test-cleanup))

;; Run the test
(defun ai-auto-complete-mcp-run-runner-support-test ()
  "Run the runner support test."
  (interactive)
  (ai-auto-complete-mcp-test-runner-support))

(provide 'mcp/tests/test-runner-support)
;;; test-runner-support.el ends here
