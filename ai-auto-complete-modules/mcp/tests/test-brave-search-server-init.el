;;; test-brave-search-server-init.el --- Test Brave Search server initialization -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides tests for the Brave Search server initialization.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-server-type)
(require 'mcp/mcp-server-bridge)
(require 'mcp/mcp-settings)

;; Create a temporary settings file for testing
(defvar ai-auto-complete-mcp-test-brave-search-server-init-file
  (expand-file-name "mcp-test-brave-search-server-init.json" temporary-file-directory)
  "Temporary settings file for MCP Brave Search server initialization tests.")

;; Clean up test files
(defun ai-auto-complete-mcp-test-brave-search-server-init-cleanup ()
  "Clean up the test settings file."
  (when (file-exists-p ai-auto-complete-mcp-test-brave-search-server-init-file)
    (delete-file ai-auto-complete-mcp-test-brave-search-server-init-file)))

;; Create a test settings file
(defun ai-auto-complete-mcp-create-test-brave-search-server-init-file ()
  "Create a test settings file with Brave Search server configuration."
  (with-temp-file ai-auto-complete-mcp-test-brave-search-server-init-file
    (insert "{\n")
    (insert "  \"mcpServers\": {\n")
    (insert "    \"brave-search\": {\n")
    (insert "      \"command\": \"npx\",\n")
    (insert "      \"args\": [\n")
    (insert "        \"-y\",\n")
    (insert "        \"@modelcontextprotocol/server-brave-search\"\n")
    (insert "      ],\n")
    (insert "      \"env\": {\n")
    (insert "        \"BRAVE_API_KEY\": \"BSAYZHT8BTTufnAI2AZW46b2BNAmaUr\"\n")
    (insert "      },\n")
    (insert "      \"disabled\": false,\n")
    (insert "      \"autoApprove\": []\n")
    (insert "    }\n")
    (insert "  }\n")
    (insert "}\n")))

;; Set up test environment
(defun ai-auto-complete-mcp-test-brave-search-server-init-setup ()
  "Set up the test environment for Brave Search server initialization testing."
  ;; Clean up any previous test data
  (ai-auto-complete-mcp-test-brave-search-server-init-cleanup)
  
  ;; Create test settings file
  (ai-auto-complete-mcp-create-test-brave-search-server-init-file)
  
  ;; Override the settings file path
  (let ((original-file ai-auto-complete-mcp-settings-file))
    (setq ai-auto-complete-mcp-settings-file ai-auto-complete-mcp-test-brave-search-server-init-file)
    ;; Return the original file path so we can restore it later
    original-file))

;; Test Brave Search server initialization
(defun ai-auto-complete-mcp-test-brave-search-server-init-handling ()
  "Test Brave Search server initialization handling."
  (interactive)
  
  ;; Set up test environment
  (let ((original-settings-file (ai-auto-complete-mcp-test-brave-search-server-init-setup)))
    
    ;; Clear existing servers
    (maphash (lambda (name _)
               (remhash name ai-auto-complete-mcp-servers))
             ai-auto-complete-mcp-servers)
    
    ;; Enable debug mode
    (setq ai-auto-complete-mcp-debug-mode t)
    
    ;; Register the server bridge transport
    (message "\nRegistering server bridge transport")
    (ai-auto-complete-mcp-register-server-bridge-transport)
    
    ;; Register servers from settings
    (message "\nRegistering servers from settings file: %s" ai-auto-complete-mcp-settings-file)
    (condition-case err
        (let ((count (ai-auto-complete-mcp-register-servers-from-settings)))
          (message "✓ Successfully registered %d servers from settings" count))
      (error
       (message "✗ Error registering servers: %s" (error-message-string err))))
    
    ;; Check if servers were registered
    (let ((servers (ai-auto-complete-mcp-list-servers)))
      (message "\nRegistered servers: %s" servers)
      
      ;; Check Brave Search server
      (let ((brave-server (ai-auto-complete-mcp-get-server "brave-search")))
        (if brave-server
            (progn
              (message "✓ Brave Search server registered successfully")
              (message "  Path: %s" (plist-get brave-server :path))
              (message "  Runner: %s" (plist-get brave-server :runner))
              (message "  Transport: %s" (plist-get brave-server :transport))
              (message "  Server Type: %s" (plist-get brave-server :server-type))
              (message "  Args: %S" (plist-get brave-server :args))
              (message "  Env: %S" (plist-get brave-server :env)))
          (message "✗ Failed to register Brave Search server")))
      
      ;; Try to start the server
      (message "\nStarting Brave Search server:")
      (condition-case err
          (if (ai-auto-complete-mcp-start-server "brave-search")
              (message "  ✓ Brave Search server started successfully")
            (message "  ✗ Failed to start Brave Search server"))
        (error (message "  ✗ Error starting Brave Search server: %s" (error-message-string err))))
      
      ;; List tools
      (message "\nListing tools from Brave Search server:")
      (condition-case err
          (let ((tools (ai-auto-complete-mcp-list-tools "brave-search")))
            (message "  Tools: %S" tools))
        (error (message "  ✗ Error listing tools: %s" (error-message-string err))))
      
      ;; Call tool
      (message "\nCalling search tool on Brave Search server:")
      (condition-case err
          (let ((result (ai-auto-complete-mcp-call-tool "brave-search" "search" '((q . "Emacs MCP")))))
            (message "  Result: %S" result))
        (error (message "  ✗ Error calling tool: %s" (error-message-string err)))))
    
    ;; Restore original settings file path
    (setq ai-auto-complete-mcp-settings-file original-settings-file)
    
    ;; Clean up
    (ai-auto-complete-mcp-test-brave-search-server-init-cleanup)
    
    ;; Disable debug mode
    (setq ai-auto-complete-mcp-debug-mode nil)))

;; Run the test
(defun ai-auto-complete-mcp-run-brave-search-server-init-test ()
  "Run the Brave Search server initialization test."
  (interactive)
  (ai-auto-complete-mcp-test-brave-search-server-init-handling))

(provide 'mcp/tests/test-brave-search-server-init)
;;; test-brave-search-server-init.el ends here
