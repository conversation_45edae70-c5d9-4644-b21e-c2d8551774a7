;;; test-node-server-detection.el --- Test Node.js MCP server detection -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a test for Node.js MCP server detection.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-directory)
(require 'mcp/mcp-server-init)

;; Create a temporary directory for testing
(defvar ai-auto-complete-mcp-test-dir
  (expand-file-name "mcp-test" temporary-file-directory)
  "Temporary directory for MCP tests.")

;; Clean up test directory
(defun ai-auto-complete-mcp-test-cleanup ()
  "Clean up the test directory."
  (when (file-exists-p ai-auto-complete-mcp-test-dir)
    (delete-directory ai-auto-complete-mcp-test-dir t)))

;; Set up test environment
(defun ai-auto-complete-mcp-test-setup ()
  "Set up the test environment."
  ;; Clean up any previous test data
  (ai-auto-complete-mcp-test-cleanup)
  
  ;; Create test directory
  (make-directory ai-auto-complete-mcp-test-dir t)
  
  ;; Create a Python MCP server
  (ai-auto-complete-mcp-create-new-server "python-server" ai-auto-complete-mcp-test-dir 'python)
  
  ;; Create a Node.js MCP server
  (ai-auto-complete-mcp-create-new-server "node-server" ai-auto-complete-mcp-test-dir 'node))

;; Test server detection
(defun ai-auto-complete-mcp-test-server-detection ()
  "Test MCP server detection for both Python and Node.js servers."
  (interactive)
  
  ;; Set up test environment
  (ai-auto-complete-mcp-test-setup)
  
  ;; Clear existing servers
  (maphash (lambda (name _)
             (remhash name ai-auto-complete-mcp-servers))
           ai-auto-complete-mcp-servers)
  
  ;; Set the test directory as the servers directory
  (let ((ai-auto-complete-mcp-servers-directory ai-auto-complete-mcp-test-dir))
    
    ;; Scan the directory
    (message "Scanning directory: %s" ai-auto-complete-mcp-test-dir)
    (ai-auto-complete-mcp-scan-directory ai-auto-complete-mcp-test-dir)
    
    ;; Check if servers were detected
    (let ((servers (ai-auto-complete-mcp-list-servers)))
      (message "Detected servers: %s" servers)
      
      ;; Check Python server
      (if (member "python-server" servers)
          (message "✓ Python server detected successfully")
        (message "✗ Failed to detect Python server"))
      
      ;; Check Node.js server
      (if (member "node-server" servers)
          (message "✓ Node.js server detected successfully")
        (message "✗ Failed to detect Node.js server"))))
  
  ;; Clean up
  (ai-auto-complete-mcp-test-cleanup))

;; Run the test
(defun ai-auto-complete-mcp-run-node-detection-test ()
  "Run the Node.js MCP server detection test."
  (interactive)
  (ai-auto-complete-mcp-test-server-detection))

(provide 'mcp/tests/test-node-server-detection)
;;; test-node-server-detection.el ends here
