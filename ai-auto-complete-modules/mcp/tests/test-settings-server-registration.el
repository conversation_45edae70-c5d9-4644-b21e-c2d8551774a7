;;; test-settings-server-registration.el --- Test settings-based server registration -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a test for settings-based MCP server registration.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-settings)

;; Create a temporary settings file for testing
(defvar ai-auto-complete-mcp-test-settings-file
  (expand-file-name "mcp-test-settings.json" temporary-file-directory)
  "Temporary settings file for MCP tests.")

;; Clean up test files
(defun ai-auto-complete-mcp-test-settings-cleanup ()
  "Clean up the test settings file."
  (when (file-exists-p ai-auto-complete-mcp-test-settings-file)
    (delete-file ai-auto-complete-mcp-test-settings-file)))

;; Create a test settings file
(defun ai-auto-complete-mcp-create-test-settings-file ()
  "Create a test settings file with sample MCP server configurations."
  (with-temp-file ai-auto-complete-mcp-test-settings-file
    (insert "{\n")
    (insert "  \"mcpServers\": {\n")
    (insert "    \"github.com/modelcontextprotocol/servers/tree/main/src/brave-search\": {\n")
    (insert "      \"command\": \"npx\",\n")
    (insert "      \"args\": [\n")
    (insert "        \"-y\",\n")
    (insert "        \"@modelcontextprotocol/server-brave-search\"\n")
    (insert "      ],\n")
    (insert "      \"env\": {\n")
    (insert "        \"BRAVE_API_KEY\": \"BSAYZHT8BTTufnAI2AZW46b2BNAmaUr\"\n")
    (insert "      },\n")
    (insert "      \"disabled\": false,\n")
    (insert "      \"autoApprove\": []\n")
    (insert "    },\n")
    (insert "    \"ElevenLabs\": {\n")
    (insert "      \"command\": \"uvx\",\n")
    (insert "      \"args\": [\"elevenlabs-mcp\"],\n")
    (insert "      \"env\": {\n")
    (insert "        \"ELEVENLABS_API_KEY\": \"test-api-key\"\n")
    (insert "      }\n")
    (insert "    }\n")
    (insert "  }\n")
    (insert "}\n")))

;; Set up test environment
(defun ai-auto-complete-mcp-test-settings-setup ()
  "Set up the test environment for settings-based server registration."
  ;; Clean up any previous test data
  (ai-auto-complete-mcp-test-settings-cleanup)

  ;; Create test settings file
  (ai-auto-complete-mcp-create-test-settings-file)

  ;; Override the settings file path
  (let ((original-file ai-auto-complete-mcp-settings-file))
    (setq ai-auto-complete-mcp-settings-file ai-auto-complete-mcp-test-settings-file)
    ;; Return the original file path so we can restore it later
    original-file))

;; Test settings-based server registration
(defun ai-auto-complete-mcp-test-settings-registration ()
  "Test MCP server registration from settings file."
  (interactive)

  ;; Set up test environment
  (let ((original-settings-file (ai-auto-complete-mcp-test-settings-setup)))

    ;; Clear existing servers
    (maphash (lambda (name _)
               (remhash name ai-auto-complete-mcp-servers))
             ai-auto-complete-mcp-servers)

    ;; Register servers from settings
    (message "Registering servers from settings file: %s" ai-auto-complete-mcp-settings-file)
    (ai-auto-complete-mcp-register-servers-from-settings)

    ;; Check if servers were registered
    (let ((servers (ai-auto-complete-mcp-list-servers)))
      (message "Registered servers: %s" servers)

      ;; Check brave-search server
      (let ((brave-server (ai-auto-complete-mcp-get-server "brave-search")))
        (if brave-server
            (progn
              (message "✓ brave-search server registered successfully")
              (message "  Command: %s %s"
                       (plist-get brave-server :command)
                       (mapconcat 'identity (plist-get brave-server :args) " "))
              (message "  Server Path: %s" (plist-get brave-server :server-path))
              (message "  Environment: %s"
                       (let ((env-list '()))
                         (maphash (lambda (k v) (push (format "%s=%s" k v) env-list))
                                  (plist-get brave-server :env))
                         (mapconcat 'identity env-list ", ")))

              ;; Verify that the server path is correctly extracted from args
              (let ((expected-path "-y @modelcontextprotocol/server-brave-search"))
                (if (string= (plist-get brave-server :server-path) expected-path)
                    (message "  ✓ Server path correctly extracted from args")
                  (message "  ✗ Server path incorrect. Expected: %s, Got: %s"
                           expected-path (plist-get brave-server :server-path)))))
          (message "✗ Failed to register brave-search server")))

      ;; Check ElevenLabs server
      (let ((eleven-server (ai-auto-complete-mcp-get-server "ElevenLabs")))
        (if eleven-server
            (progn
              (message "✓ ElevenLabs server registered successfully")
              (message "  Command: %s %s"
                       (plist-get eleven-server :command)
                       (mapconcat 'identity (plist-get eleven-server :args) " "))
              (message "  Server Path: %s" (plist-get eleven-server :server-path))
              (message "  Environment: %s"
                       (let ((env-list '()))
                         (maphash (lambda (k v) (push (format "%s=%s" k v) env-list))
                                  (plist-get eleven-server :env))
                         (mapconcat 'identity env-list ", ")))

              ;; Verify that the server path is correctly extracted from args
              (let ((expected-path "elevenlabs-mcp"))
                (if (string= (plist-get eleven-server :server-path) expected-path)
                    (message "  ✓ Server path correctly extracted from args")
                  (message "  ✗ Server path incorrect. Expected: %s, Got: %s"
                           expected-path (plist-get eleven-server :server-path)))))
          (message "✗ Failed to register ElevenLabs server"))))

    ;; Restore original settings file path
    (setq ai-auto-complete-mcp-settings-file original-settings-file)

    ;; Clean up
    (ai-auto-complete-mcp-test-settings-cleanup)))

;; Run the test
(defun ai-auto-complete-mcp-run-settings-registration-test ()
  "Run the settings-based MCP server registration test."
  (interactive)
  (ai-auto-complete-mcp-test-settings-registration))

(provide 'mcp/tests/test-settings-server-registration)
;;; test-settings-server-registration.el ends here
