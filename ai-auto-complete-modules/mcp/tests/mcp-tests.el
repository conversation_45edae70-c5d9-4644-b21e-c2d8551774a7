;;; mcp-tests.el --- Tests for MCP integration in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides tests for the MCP integration in the AI Auto Complete package.

;;; Code:

(require 'ert)
(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)
(require 'mcp/mcp-tools-bridge)
(require 'mcp/mcp-directory)
(require 'mcp/mcp-resources)
(require 'mcp/mcp-sampling)

;; Test server path
(defvar ai-auto-complete-mcp-test-server-path
  (expand-file-name "test-server.py" (file-name-directory (or load-file-name buffer-file-name)))
  "Path to the test MCP server.")

;; Create a test server file
(defun ai-auto-complete-mcp-create-test-server ()
  "Create a test MCP server file."
  (with-temp-file ai-auto-complete-mcp-test-server-path
    (insert "#!/usr/bin/env python3
from mcp.server.fastmcp import FastMCP

# Create an MCP server
mcp = FastMCP(\"Test Server\")

@mcp.tool()
def hello(name: str) -> str:
    \"\"\"Say hello to someone\"\"\"
    return f\"Hello, {name}!\"

@mcp.tool()
def add(a: float, b: float) -> float:
    \"\"\"Add two numbers\"\"\"
    return a + b

@mcp.resource(\"greeting://{name}\")
def get_greeting(name: str) -> str:
    \"\"\"Get a personalized greeting\"\"\"
    return f\"Welcome, {name}!\"

@mcp.prompt()
def greeting_prompt(name: str) -> str:
    \"\"\"Generate a greeting prompt\"\"\"
    return f\"Write a personalized greeting for {name}.\"

if __name__ == \"__main__\":
    mcp.run()
"))
  
  ;; Make the file executable
  (set-file-modes ai-auto-complete-mcp-test-server-path (logior (file-modes ai-auto-complete-mcp-test-server-path) #o111)))

;; Setup function for tests
(defun ai-auto-complete-mcp-tests-setup ()
  "Setup function for MCP tests."
  ;; Enable MCP
  (setq ai-auto-complete-mcp-enabled t)
  
  ;; Create the test server
  (ai-auto-complete-mcp-create-test-server)
  
  ;; Register the test server
  (ai-auto-complete-mcp-register-server "test-server" ai-auto-complete-mcp-test-server-path 'stdio "Test MCP server"))

;; Teardown function for tests
(defun ai-auto-complete-mcp-tests-teardown ()
  "Teardown function for MCP tests."
  ;; Stop the test server if it's running
  (when (ai-auto-complete-mcp-server-running-p "test-server")
    (ai-auto-complete-mcp-stop-server "test-server"))
  
  ;; Remove the test server
  (remhash "test-server" ai-auto-complete-mcp-servers)
  
  ;; Delete the test server file
  (when (file-exists-p ai-auto-complete-mcp-test-server-path)
    (delete-file ai-auto-complete-mcp-test-server-path)))

;; Test server registration
(ert-deftest ai-auto-complete-mcp-test-server-registration ()
  "Test MCP server registration."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Check if the server is registered
        (should (ai-auto-complete-mcp-server-exists-p "test-server"))
        
        ;; Check server properties
        (let ((server (ai-auto-complete-mcp-get-server "test-server")))
          (should server)
          (should (string= (plist-get server :path) ai-auto-complete-mcp-test-server-path))
          (should (eq (plist-get server :transport) 'stdio))
          (should (string= (plist-get server :description) "Test MCP server"))
          (should (eq (plist-get server :status) 'stopped))))
    (ai-auto-complete-mcp-tests-teardown)))

;; Test server start/stop
(ert-deftest ai-auto-complete-mcp-test-server-start-stop ()
  "Test MCP server start/stop."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Start the server
        (should (ai-auto-complete-mcp-start-server "test-server"))
        
        ;; Check if the server is running
        (should (ai-auto-complete-mcp-server-running-p "test-server"))
        
        ;; Stop the server
        (should (ai-auto-complete-mcp-stop-server "test-server"))
        
        ;; Check if the server is stopped
        (should-not (ai-auto-complete-mcp-server-running-p "test-server")))
    (ai-auto-complete-mcp-tests-teardown)))

;; Test tool call
(ert-deftest ai-auto-complete-mcp-test-tool-call ()
  "Test MCP tool call."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Start the server
        (should (ai-auto-complete-mcp-start-server "test-server"))
        
        ;; Wait for the server to initialize
        (sleep-for 1)
        
        ;; Call the hello tool
        (let ((result-promise (make-hash-table :test 'eq)))
          ;; Set up the promise
          (puthash 'status 'pending result-promise)
          (puthash 'value nil result-promise)
          
          ;; Call the tool
          (ai-auto-complete-mcp-call-tool
           "test-server" "hello" '(("name" . "World"))
           (lambda (result)
             (puthash 'status 'fulfilled result-promise)
             (puthash 'value result result-promise)))
          
          ;; Wait for the result (with timeout)
          (let ((timeout 5)
                (start-time (current-time)))
            (while (and (eq (gethash 'status result-promise) 'pending)
                        (< (float-time (time-since start-time)) timeout))
              (sleep-for 0.1))
            
            ;; Check the result
            (should (eq (gethash 'status result-promise) 'fulfilled))
            (should (string= (gethash 'value result-promise) "Hello, World!")))))
    (ai-auto-complete-mcp-tests-teardown)))

;; Test resource read
(ert-deftest ai-auto-complete-mcp-test-resource-read ()
  "Test MCP resource read."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Start the server
        (should (ai-auto-complete-mcp-start-server "test-server"))
        
        ;; Wait for the server to initialize
        (sleep-for 1)
        
        ;; Read the greeting resource
        (let ((result-promise (make-hash-table :test 'eq)))
          ;; Set up the promise
          (puthash 'status 'pending result-promise)
          (puthash 'value nil result-promise)
          
          ;; Read the resource
          (ai-auto-complete-mcp-read-resource
           "test-server" "greeting://World"
           (lambda (result)
             (puthash 'status 'fulfilled result-promise)
             (puthash 'value result result-promise)))
          
          ;; Wait for the result (with timeout)
          (let ((timeout 5)
                (start-time (current-time)))
            (while (and (eq (gethash 'status result-promise) 'pending)
                        (< (float-time (time-since start-time)) timeout))
              (sleep-for 0.1))
            
            ;; Check the result
            (should (eq (gethash 'status result-promise) 'fulfilled))
            (should (string= (gethash 'value result-promise) "Welcome, World!")))))
    (ai-auto-complete-mcp-tests-teardown)))

;; Test prompt get
(ert-deftest ai-auto-complete-mcp-test-prompt-get ()
  "Test MCP prompt get."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Start the server
        (should (ai-auto-complete-mcp-start-server "test-server"))
        
        ;; Wait for the server to initialize
        (sleep-for 1)
        
        ;; Get the greeting prompt
        (let ((result-promise (make-hash-table :test 'eq)))
          ;; Set up the promise
          (puthash 'status 'pending result-promise)
          (puthash 'value nil result-promise)
          
          ;; Get the prompt
          (ai-auto-complete-mcp-get-prompt
           "test-server" "greeting_prompt" '(("name" . "World"))
           (lambda (result)
             (puthash 'status 'fulfilled result-promise)
             (puthash 'value result result-promise)))
          
          ;; Wait for the result (with timeout)
          (let ((timeout 5)
                (start-time (current-time)))
            (while (and (eq (gethash 'status result-promise) 'pending)
                        (< (float-time (time-since start-time)) timeout))
              (sleep-for 0.1))
            
            ;; Check the result
            (should (eq (gethash 'status result-promise) 'fulfilled))
            (should (string= (gethash 'value result-promise) "Write a personalized greeting for World.")))))
    (ai-auto-complete-mcp-tests-teardown)))

;; Test tools list
(ert-deftest ai-auto-complete-mcp-test-tools-list ()
  "Test MCP tools list."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Start the server
        (should (ai-auto-complete-mcp-start-server "test-server"))
        
        ;; Wait for the server to initialize
        (sleep-for 1)
        
        ;; List the tools
        (let ((result-promise (make-hash-table :test 'eq)))
          ;; Set up the promise
          (puthash 'status 'pending result-promise)
          (puthash 'value nil result-promise)
          
          ;; List the tools
          (ai-auto-complete-mcp-list-tools
           "test-server"
           (lambda (result)
             (puthash 'status 'fulfilled result-promise)
             (puthash 'value result result-promise)))
          
          ;; Wait for the result (with timeout)
          (let ((timeout 5)
                (start-time (current-time)))
            (while (and (eq (gethash 'status result-promise) 'pending)
                        (< (float-time (time-since start-time)) timeout))
              (sleep-for 0.1))
            
            ;; Check the result
            (should (eq (gethash 'status result-promise) 'fulfilled))
            (let ((tools (gethash 'value result-promise)))
              (should (listp tools))
              (should (= (length tools) 2))
              (should (cl-some (lambda (tool) (string= (plist-get tool :name) "hello")) tools))
              (should (cl-some (lambda (tool) (string= (plist-get tool :name) "add")) tools))))))
    (ai-auto-complete-mcp-tests-teardown)))

;; Test resources list
(ert-deftest ai-auto-complete-mcp-test-resources-list ()
  "Test MCP resources list."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Start the server
        (should (ai-auto-complete-mcp-start-server "test-server"))
        
        ;; Wait for the server to initialize
        (sleep-for 1)
        
        ;; List the resources
        (let ((result-promise (make-hash-table :test 'eq)))
          ;; Set up the promise
          (puthash 'status 'pending result-promise)
          (puthash 'value nil result-promise)
          
          ;; List the resources
          (ai-auto-complete-mcp-list-resources
           "test-server"
           (lambda (result)
             (puthash 'status 'fulfilled result-promise)
             (puthash 'value result result-promise)))
          
          ;; Wait for the result (with timeout)
          (let ((timeout 5)
                (start-time (current-time)))
            (while (and (eq (gethash 'status result-promise) 'pending)
                        (< (float-time (time-since start-time)) timeout))
              (sleep-for 0.1))
            
            ;; Check the result
            (should (eq (gethash 'status result-promise) 'fulfilled))
            (let ((resources (gethash 'value result-promise)))
              (should (listp resources))
              (should (= (length resources) 1))
              (should (cl-some (lambda (resource) (string= (plist-get resource :uri) "greeting://{name}")) resources))))))
    (ai-auto-complete-mcp-tests-teardown)))

;; Test prompts list
(ert-deftest ai-auto-complete-mcp-test-prompts-list ()
  "Test MCP prompts list."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Start the server
        (should (ai-auto-complete-mcp-start-server "test-server"))
        
        ;; Wait for the server to initialize
        (sleep-for 1)
        
        ;; List the prompts
        (let ((result-promise (make-hash-table :test 'eq)))
          ;; Set up the promise
          (puthash 'status 'pending result-promise)
          (puthash 'value nil result-promise)
          
          ;; List the prompts
          (ai-auto-complete-mcp-list-prompts
           "test-server"
           (lambda (result)
             (puthash 'status 'fulfilled result-promise)
             (puthash 'value result result-promise)))
          
          ;; Wait for the result (with timeout)
          (let ((timeout 5)
                (start-time (current-time)))
            (while (and (eq (gethash 'status result-promise) 'pending)
                        (< (float-time (time-since start-time)) timeout))
              (sleep-for 0.1))
            
            ;; Check the result
            (should (eq (gethash 'status result-promise) 'fulfilled))
            (let ((prompts (gethash 'value result-promise)))
              (should (listp prompts))
              (should (= (length prompts) 1))
              (should (cl-some (lambda (prompt) (string= (plist-get prompt :name) "greeting_prompt")) prompts))))))
    (ai-auto-complete-mcp-tests-teardown)))

;; Test tool import
(ert-deftest ai-auto-complete-mcp-test-tool-import ()
  "Test MCP tool import."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Start the server
        (should (ai-auto-complete-mcp-start-server "test-server"))
        
        ;; Wait for the server to initialize
        (sleep-for 1)
        
        ;; Import the server as a tool
        (ai-auto-complete-mcp-import-server-as-tool "test-server")
        
        ;; Check if the tools were imported
        (should (gethash "mcp:test-server:hello" ai-auto-complete-tools))
        (should (gethash "mcp:test-server:add" ai-auto-complete-tools)))
    (ai-auto-complete-mcp-tests-teardown)))

;; Test resource sampling
(ert-deftest ai-auto-complete-mcp-test-resource-sampling ()
  "Test MCP resource sampling."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Start the server
        (should (ai-auto-complete-mcp-start-server "test-server"))
        
        ;; Wait for the server to initialize
        (sleep-for 1)
        
        ;; Sample the greeting resource
        (let ((sample (ai-auto-complete-mcp-sample-resource "test-server" "greeting://World")))
          (should sample)
          (should (string= (plist-get sample :uri) "greeting://World"))
          (should (string= (plist-get sample :value) "Welcome, World!"))))
    (ai-auto-complete-mcp-tests-teardown)))

;; Test tool sampling
(ert-deftest ai-auto-complete-mcp-test-tool-sampling ()
  "Test MCP tool sampling."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Start the server
        (should (ai-auto-complete-mcp-start-server "test-server"))
        
        ;; Wait for the server to initialize
        (sleep-for 1)
        
        ;; Sample the hello tool
        (let ((sample (ai-auto-complete-mcp-sample-tool "test-server" "hello" '(("name" . "World")))))
          (should sample)
          (should (string= (plist-get sample :name) "hello"))
          (should (string= (plist-get sample :result) "Hello, World!"))))
    (ai-auto-complete-mcp-tests-teardown)))

;; Test prompt sampling
(ert-deftest ai-auto-complete-mcp-test-prompt-sampling ()
  "Test MCP prompt sampling."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Start the server
        (should (ai-auto-complete-mcp-start-server "test-server"))
        
        ;; Wait for the server to initialize
        (sleep-for 1)
        
        ;; Sample the greeting prompt
        (let ((sample (ai-auto-complete-mcp-sample-prompt "test-server" "greeting_prompt" '(("name" . "World")))))
          (should sample)
          (should (string= (plist-get sample :name) "greeting_prompt"))
          (should (string= (plist-get sample :result) "Write a personalized greeting for World."))))
    (ai-auto-complete-mcp-tests-teardown)))

;; Test examples generation
(ert-deftest ai-auto-complete-mcp-test-examples-generation ()
  "Test MCP examples generation."
  (ai-auto-complete-mcp-tests-setup)
  (unwind-protect
      (progn
        ;; Start the server
        (should (ai-auto-complete-mcp-start-server "test-server"))
        
        ;; Wait for the server to initialize
        (sleep-for 1)
        
        ;; Generate examples
        (let ((examples (ai-auto-complete-mcp-generate-examples "test-server")))
          (should examples)
          (should (plist-get examples :tools))
          (should (plist-get examples :resources))
          (should (plist-get examples :prompts))))
    (ai-auto-complete-mcp-tests-teardown)))

;; Run all tests
(defun ai-auto-complete-mcp-run-tests ()
  "Run all MCP tests."
  (interactive)
  (ert-run-tests-interactively "^ai-auto-complete-mcp-test-"))

(provide 'mcp/tests/mcp-tests)
;;; mcp-tests.el ends here
