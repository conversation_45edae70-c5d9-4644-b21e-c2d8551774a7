;;; mcp-init.el --- MCP initialization -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides initialization for MCP integration.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-server-type)
(require 'mcp/mcp-server-bridge)
(require 'mcp/mcp-settings)
(require 'mcp/transports/mcp-stdio-bridge)
(require 'mcp/transports/mcp-typescript-bridge)

;; Initialize MCP
(defun ai-auto-complete-mcp-init ()
  "Initialize MCP integration."
  (interactive)

  ;; Register transports
  (message "MCP: Registering transports")

  ;; Register stdio transport
  (ai-auto-complete-mcp-register-stdio-transport)

  ;; Register TypeScript bridge transport
  (ai-auto-complete-mcp-register-typescript-bridge-transport)

  ;; Register server bridge transport
  (ai-auto-complete-mcp-register-server-bridge-transport)

  ;; Initialize settings-based servers
  ;(ai-auto-complete-mcp-init-settings-servers)

  ;; Scan for servers in the MCP directory
 ; (if (and (boundp 'ai-auto-complete-mcp-auto-import)
  ;         (not ai-auto-complete-mcp-auto-import))
   ;   (message "MCP: Auto-import is disabled, skipping server scan")
    ;(message "MCP: Scanning for servers")
    ;(ai-auto-complete-mcp-scan-directory ai-auto-complete-mcp-servers-directory))
  (message "NB: Skipped importing from directories for now.")
  (message "MCP: Initialization complete"))

(provide 'mcp/mcp-init)
;;; mcp-init.el ends here
