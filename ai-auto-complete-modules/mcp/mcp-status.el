;;; mcp-status.el --- MCP status checking functions -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides functions to check the status of MCP servers and tools.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)

;; Check MCP status
(defun ai-auto-complete-mcp-check-status ()
  "Check the status of MCP integration and servers.
Returns a plist with status information."
  (interactive)
  (let ((result (list :mcp-enabled nil
                      :servers-directory nil
                      :servers-count 0
                      :running-servers 0
                      :python-available nil
                      :stdio-transport-available nil)))
    
    ;; Check if MCP is enabled
    (setq result (plist-put result :mcp-enabled 
                           (and (boundp 'ai-auto-complete-mcp-enabled)
                                ai-auto-complete-mcp-enabled)))
    
    ;; Check servers directory
    (when (boundp 'ai-auto-complete-mcp-servers-directory)
      (setq result (plist-put result :servers-directory ai-auto-complete-mcp-servers-directory))
      (setq result (plist-put result :servers-directory-exists 
                             (file-exists-p ai-auto-complete-mcp-servers-directory))))
    
    ;; Check user servers directory
    (when (boundp 'ai-auto-complete-mcp-user-servers-directory)
      (setq result (plist-put result :user-servers-directory 
                             ai-auto-complete-mcp-user-servers-directory))
      (setq result (plist-put result :user-servers-directory-exists 
                             (file-exists-p ai-auto-complete-mcp-user-servers-directory))))
    
    ;; Check Python availability
    (let ((python-cmd (or (executable-find ai-auto-complete-mcp-python-command)
                         (executable-find "python3")
                         (executable-find "python"))))
      (setq result (plist-put result :python-available (not (null python-cmd))))
      (when python-cmd
        (setq result (plist-put result :python-command python-cmd))))
    
    ;; Check stdio transport availability
    (setq result (plist-put result :stdio-transport-available 
                           (fboundp 'ai-auto-complete-mcp-stdio-start-server)))
    
    ;; Count servers
    (let ((servers (ai-auto-complete-mcp-list-servers))
          (running-count 0))
      (setq result (plist-put result :servers-count (length servers)))
      (setq result (plist-put result :servers servers))
      
      ;; Count running servers
      (dolist (server-name servers)
        (when (eq (ai-auto-complete-mcp-get-server-status server-name) 'running)
          (setq running-count (1+ running-count))))
      
      (setq result (plist-put result :running-servers running-count)))
    
    ;; Display the result if called interactively
    (when (called-interactively-p 'any)
      (ai-auto-complete-mcp-display-status result))
    
    ;; Return the result
    result))

;; Display MCP status
(defun ai-auto-complete-mcp-display-status (status)
  "Display the STATUS of MCP integration and servers."
  (let ((buffer-name "*MCP Status*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer
        
        ;; Add header
        (insert "MCP Status\n")
        (insert "==========\n\n")
        
        ;; MCP enabled
        (insert (format "MCP Enabled: %s\n" 
                       (if (plist-get status :mcp-enabled) "Yes" "No")))
        
        ;; Python availability
        (insert (format "Python Available: %s\n" 
                       (if (plist-get status :python-available) "Yes" "No")))
        (when (plist-get status :python-available)
          (insert (format "Python Command: %s\n" (plist-get status :python-command))))
        
        ;; Stdio transport
        (insert (format "Stdio Transport Available: %s\n" 
                       (if (plist-get status :stdio-transport-available) "Yes" "No")))
        
        ;; Servers directory
        (insert (format "\nServers Directory: %s\n" (plist-get status :servers-directory)))
        (insert (format "Servers Directory Exists: %s\n" 
                       (if (plist-get status :servers-directory-exists) "Yes" "No")))
        
        ;; User servers directory
        (when (plist-get status :user-servers-directory)
          (insert (format "\nUser Servers Directory: %s\n" 
                         (plist-get status :user-servers-directory)))
          (insert (format "User Servers Directory Exists: %s\n" 
                         (if (plist-get status :user-servers-directory-exists) "Yes" "No"))))
        
        ;; Servers count
        (insert (format "\nRegistered Servers: %d\n" (plist-get status :servers-count)))
        (insert (format "Running Servers: %d\n\n" (plist-get status :running-servers)))
        
        ;; List servers
        (insert "Servers:\n")
        (dolist (server-name (plist-get status :servers))
          (let* ((server (ai-auto-complete-mcp-get-server server-name))
                 (status (plist-get server :status))
                 (path (plist-get server :path))
                 (transport (plist-get server :transport)))
            (insert (format "  - %s (%s, %s): %s\n" 
                           server-name transport status path))))
        
        ;; Add buttons for actions
        (insert "\nActions: ")
        (insert-button "Refresh"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-check-status))
                      'follow-link t)
        (insert " | ")
        (insert-button "Start All Servers"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-start-all-servers)
                               (ai-auto-complete-mcp-check-status))
                      'follow-link t)
        (insert " | ")
        (insert-button "Stop All Servers"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-stop-all-servers)
                               (ai-auto-complete-mcp-check-status))
                      'follow-link t)
        (insert " | ")
        (insert-button "Import User Servers"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-import-user-servers)
                               (ai-auto-complete-mcp-check-status))
                      'follow-link t)))
    
    ;; Display the buffer
    (switch-to-buffer buffer-name)))

;; Stop all MCP servers
(defun ai-auto-complete-mcp-stop-all-servers ()
  "Stop all running MCP servers."
  (interactive)
  (let ((servers (ai-auto-complete-mcp-list-servers))
        (stopped 0))
    (dolist (server-name servers)
      (when (eq (ai-auto-complete-mcp-get-server-status server-name) 'running)
        (when (ai-auto-complete-mcp-stop-server server-name)
          (setq stopped (1+ stopped)))))
    (message "Stopped %d MCP servers" stopped)))

;; Add a command to check MCP server status
(defun ai-auto-complete-mcp-server-status ()
  "Check the status of MCP servers and display it."
  (interactive)
  (ai-auto-complete-mcp-check-status))

(provide 'mcp/mcp-status)
;;; mcp-status.el ends here
