;;; mcp-tools-ui-test.el --- Test functions for MCP tools UI -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides test functions for the MCP tools UI.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-tools-ui)
(require 'mcp/mcp-persistence)
(require 'widget)
(require 'wid-edit)

;; Test function to create a simple widget UI
(defun ai-auto-complete-mcp-test-widget-ui ()
  "Test the widget UI system with a simple form."
  (interactive)
  (let ((buffer-name "*MCP Widget Test*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (kill-all-local-variables)
        (remove-overlays)
        
        ;; Use widget mode
        (widget-setup)
        
        ;; Add header
        (widget-insert "Widget Test Form\n")
        (widget-insert "===============\n\n")
        
        ;; Test field
        (widget-insert "Test Field: ")
        (let ((test-field (widget-create 'editable-field
                                        :size 30
                                        :format "%v"
                                        "Test value")))
          
          ;; Test button
          (widget-insert "\n\n")
          (widget-create 'push-button
                        :notify (lambda (&rest _)
                                  (message "Button clicked with value: %s" (widget-value test-field)))
                        "Test Button")
          
          ;; Cancel button
          (widget-insert " ")
          (widget-create 'push-button
                        :notify (lambda (&rest _)
                                  (kill-buffer buffer-name))
                        "Cancel"))
        
        (use-local-map widget-keymap)
        (widget-setup))
      
      (switch-to-buffer buffer-name))))

;; Test function to create a sample server and open the tools UI
(defun ai-auto-complete-mcp-test-tools-ui ()
  "Create a sample server and open the tools UI."
  (interactive)
  
  ;; Create a sample server if it doesn't exist
  (unless (ai-auto-complete-mcp-server-exists-p "test-server")
    (ai-auto-complete-mcp-register-server
     "test-server" "/path/to/test/server.py" 'stdio "Test server" "python"))
  
  ;; Open the tools management UI
  (ai-auto-complete-mcp-manage-server-tools "test-server"))

;; Test function to add a tool to the sample server
(defun ai-auto-complete-mcp-test-add-tool ()
  "Test adding a tool to the sample server."
  (interactive)
  
  ;; Create a sample server if it doesn't exist
  (unless (ai-auto-complete-mcp-server-exists-p "test-server")
    (ai-auto-complete-mcp-register-server
     "test-server" "/path/to/test/server.py" 'stdio "Test server" "python"))
  
  ;; Open the add tool UI
  (ai-auto-complete-mcp-add-tool "test-server"))

(provide 'mcp/mcp-tools-ui-test)
;;; mcp-tools-ui-test.el ends here
