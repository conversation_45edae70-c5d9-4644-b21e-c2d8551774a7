;;; mcp-error-handling.el --- Error handling for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides error handling and recovery mechanisms for MCP integration in the AI Auto Complete package.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)

;; Error types
(define-error 'ai-auto-complete-mcp-error "MCP error" 'error)
(define-error 'ai-auto-complete-mcp-server-error "MCP server error" 'ai-auto-complete-mcp-error)
(define-error 'ai-auto-complete-mcp-transport-error "MCP transport error" 'ai-auto-complete-mcp-error)
(define-error 'ai-auto-complete-mcp-protocol-error "MCP protocol error" 'ai-auto-complete-mcp-error)
(define-error 'ai-auto-complete-mcp-timeout-error "MCP timeout error" 'ai-auto-complete-mcp-error)

;; Error log
(defvar ai-auto-complete-mcp-error-log '()
  "Log of MCP errors.")

;; Error handling settings are defined in customization/mcp-customization.el

;; Restart attempts counter
(defvar ai-auto-complete-mcp-restart-attempts (make-hash-table :test 'equal)
  "Hash table of MCP server restart attempts.")

;; Log an error
(defun ai-auto-complete-mcp-log-error (error-type server-name message &optional data)
  "Log an error of ERROR-TYPE for SERVER-NAME with MESSAGE and optional DATA."
  (let ((error-entry (list :type error-type
                          :server server-name
                          :message message
                          :data data
                          :timestamp (current-time))))
    ;; Add to the log
    (push error-entry ai-auto-complete-mcp-error-log)

    ;; Trim the log if it's too long
    (when (> (length ai-auto-complete-mcp-error-log) ai-auto-complete-mcp-error-log-max-size)
      (setq ai-auto-complete-mcp-error-log (butlast ai-auto-complete-mcp-error-log)))

    ;; Return the error entry
    error-entry))

;; Clear the error log
(defun ai-auto-complete-mcp-clear-error-log ()
  "Clear the MCP error log."
  (interactive)
  (setq ai-auto-complete-mcp-error-log '())
  (message "Cleared MCP error log"))

;; Display the error log
(defun ai-auto-complete-mcp-display-error-log ()
  "Display the MCP error log."
  (interactive)
  (let ((buffer-name "*MCP Error Log*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert "MCP Error Log\n")
        (insert "=============\n\n")

        ;; Add error entries
        (if (null ai-auto-complete-mcp-error-log)
            (insert "No errors logged.\n")
          (dolist (error-entry (reverse ai-auto-complete-mcp-error-log))
            (let ((error-type (plist-get error-entry :type))
                  (server-name (plist-get error-entry :server))
                  (message (plist-get error-entry :message))
                  (data (plist-get error-entry :data))
                  (timestamp (plist-get error-entry :timestamp)))

              (insert (format "Time: %s\n" (format-time-string "%Y-%m-%d %H:%M:%S" timestamp)))
              (insert (format "Type: %s\n" error-type))
              (insert (format "Server: %s\n" server-name))
              (insert (format "Message: %s\n" message))

              (when data
                (insert "Data:\n")
                (insert (format "%S\n" data)))

              (insert "\n"))))

        ;; Add buttons for actions
        (insert "Actions: ")
        (insert-button "Clear Log"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-clear-error-log)
                               (ai-auto-complete-mcp-display-error-log))
                      'follow-link t)

        (insert " | ")
        (insert-button "Refresh"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-display-error-log))
                      'follow-link t)

        (goto-char (point-min))))

    (switch-to-buffer buffer-name)))

;; Handle a server error
(defun ai-auto-complete-mcp-handle-server-error (server-name message &optional data)
  "Handle a server error for SERVER-NAME with MESSAGE and optional DATA."
  ;; Log the error
  (ai-auto-complete-mcp-log-error 'server-error server-name message data)

  ;; Display a message
  (message "MCP server error: %s - %s" server-name message)

  ;; Check if we should restart the server
  (when ai-auto-complete-mcp-auto-restart-servers
    (let ((attempts (gethash server-name ai-auto-complete-mcp-restart-attempts 0)))
      (if (>= attempts ai-auto-complete-mcp-max-restart-attempts)
          (progn
            (message "MCP server %s has failed too many times, not restarting" server-name)
            (remhash server-name ai-auto-complete-mcp-restart-attempts))
        ;; Increment the restart attempts
        (puthash server-name (1+ attempts) ai-auto-complete-mcp-restart-attempts)

        ;; Restart the server
        (message "Restarting MCP server %s (attempt %d of %d)"
                 server-name (1+ attempts) ai-auto-complete-mcp-max-restart-attempts)
        (ai-auto-complete-mcp-restart-server server-name)))))

;; Handle a transport error
(defun ai-auto-complete-mcp-handle-transport-error (server-name message &optional data)
  "Handle a transport error for SERVER-NAME with MESSAGE and optional DATA."
  ;; Log the error
  (ai-auto-complete-mcp-log-error 'transport-error server-name message data)

  ;; Display a message
  (message "MCP transport error: %s - %s" server-name message))

;; Handle a protocol error
(defun ai-auto-complete-mcp-handle-protocol-error (server-name message &optional data)
  "Handle a protocol error for SERVER-NAME with MESSAGE and optional DATA."
  ;; Log the error
  (ai-auto-complete-mcp-log-error 'protocol-error server-name message data)

  ;; Display a message
  (message "MCP protocol error: %s - %s" server-name message))

;; Handle a timeout error
(defun ai-auto-complete-mcp-handle-timeout-error (server-name message &optional data)
  "Handle a timeout error for SERVER-NAME with MESSAGE and optional DATA."
  ;; Log the error
  (ai-auto-complete-mcp-log-error 'timeout-error server-name message data)

  ;; Display a message
  (message "MCP timeout error: %s - %s" server-name message))

;; Reset restart attempts for a server
(defun ai-auto-complete-mcp-reset-restart-attempts (server-name)
  "Reset restart attempts for SERVER-NAME."
  (remhash server-name ai-auto-complete-mcp-restart-attempts))

;; Advice function to reset restart attempts when a server is started successfully
(defun ai-auto-complete-mcp-advice-start-server-reset-attempts (orig-fun server-name)
  "Advice function to reset restart attempts when a server is started successfully.
Calls ORIG-FUN with SERVER-NAME."
  (let ((result (funcall orig-fun server-name)))
    (when result
      (ai-auto-complete-mcp-reset-restart-attempts server-name))
    result))

;; Advice function to handle errors in stdio process filter
(defun ai-auto-complete-mcp-advice-stdio-process-filter (orig-fun process output)
  "Advice function to handle errors in stdio process filter.
Calls ORIG-FUN with PROCESS and OUTPUT."
  (condition-case err
      (funcall orig-fun process output)
    (error
     (let ((server-name (process-get process 'server-name)))
       (ai-auto-complete-mcp-handle-server-error
        server-name
        (format "Error in stdio process filter: %s" (error-message-string err))
        (list :process process :output output))))))

;; Advice function to handle errors in stdio process sentinel
(defun ai-auto-complete-mcp-advice-stdio-process-sentinel (orig-fun process event)
  "Advice function to handle errors in stdio process sentinel.
Calls ORIG-FUN with PROCESS and EVENT."
  (condition-case err
      (funcall orig-fun process event)
    (error
     (let ((server-name (process-get process 'server-name)))
       (ai-auto-complete-mcp-handle-server-error
        server-name
        (format "Error in stdio process sentinel: %s" (error-message-string err))
        (list :process process :event event))))))

;; Advice function to handle errors in sse process events
(defun ai-auto-complete-mcp-advice-sse-process-events (orig-fun connection data)
  "Advice function to handle errors in sse process events.
Calls ORIG-FUN with CONNECTION and DATA."
  (condition-case err
      (funcall orig-fun connection data)
    (error
     (let ((server-name (ai-auto-complete-mcp-sse-connection-server-name connection)))
       (ai-auto-complete-mcp-handle-server-error
        server-name
        (format "Error in sse process events: %s" (error-message-string err))
        (list :connection connection :data data))))))

;; Advice function to handle errors in tool calls
(defun ai-auto-complete-mcp-advice-call-tool-handle-errors (orig-fun server-name tool-name params callback)
  "Advice function to handle errors in tool calls.
Calls ORIG-FUN with SERVER-NAME, TOOL-NAME, PARAMS, and CALLBACK."
  (condition-case err
      (funcall orig-fun server-name tool-name params callback)
    (error
     (ai-auto-complete-mcp-handle-server-error
      server-name
      (format "Error calling tool %s: %s" tool-name (error-message-string err))
      (list :tool-name tool-name :params params))
     (funcall callback (format "Error: %s" (error-message-string err))))))

;; Advice function to handle errors in resource reads
(defun ai-auto-complete-mcp-advice-read-resource-handle-errors (orig-fun server-name resource-uri callback)
  "Advice function to handle errors in resource reads.
Calls ORIG-FUN with SERVER-NAME, RESOURCE-URI, and CALLBACK."
  (condition-case err
      (funcall orig-fun server-name resource-uri callback)
    (error
     (ai-auto-complete-mcp-handle-server-error
      server-name
      (format "Error reading resource %s: %s" resource-uri (error-message-string err))
      (list :resource-uri resource-uri))
     (funcall callback (format "Error: %s" (error-message-string err))))))

;; Advice function to handle errors in prompt gets
(defun ai-auto-complete-mcp-advice-get-prompt-handle-errors (orig-fun server-name prompt-name params callback)
  "Advice function to handle errors in prompt gets.
Calls ORIG-FUN with SERVER-NAME, PROMPT-NAME, PARAMS, and CALLBACK."
  (condition-case err
      (funcall orig-fun server-name prompt-name params callback)
    (error
     (ai-auto-complete-mcp-handle-server-error
      server-name
      (format "Error getting prompt %s: %s" prompt-name (error-message-string err))
      (list :prompt-name prompt-name :params params))
     (funcall callback (format "Error: %s" (error-message-string err))))))

;; Advice function to handle errors in tools list
(defun ai-auto-complete-mcp-advice-list-tools-handle-errors (orig-fun server-name callback)
  "Advice function to handle errors in tools list.
Calls ORIG-FUN with SERVER-NAME and CALLBACK."
  (condition-case err
      (funcall orig-fun server-name callback)
    (error
     (ai-auto-complete-mcp-handle-server-error
      server-name
      (format "Error listing tools: %s" (error-message-string err))
      nil)
     (funcall callback (format "Error: %s" (error-message-string err))))))

;; Advice function to handle errors in resources list
(defun ai-auto-complete-mcp-advice-list-resources-handle-errors (orig-fun server-name callback)
  "Advice function to handle errors in resources list.
Calls ORIG-FUN with SERVER-NAME and CALLBACK."
  (condition-case err
      (funcall orig-fun server-name callback)
    (error
     (ai-auto-complete-mcp-handle-server-error
      server-name
      (format "Error listing resources: %s" (error-message-string err))
      nil)
     (funcall callback (format "Error: %s" (error-message-string err))))))

;; Advice function to handle errors in prompts list
(defun ai-auto-complete-mcp-advice-list-prompts-handle-errors (orig-fun server-name callback)
  "Advice function to handle errors in prompts list.
Calls ORIG-FUN with SERVER-NAME and CALLBACK."
  (condition-case err
      (funcall orig-fun server-name callback)
    (error
     (ai-auto-complete-mcp-handle-server-error
      server-name
      (format "Error listing prompts: %s" (error-message-string err))
      nil)
     (funcall callback (format "Error: %s" (error-message-string err))))))

;; Setup function to add advice to MCP functions
(defun ai-auto-complete-mcp-error-handling-setup ()
  "Set up MCP error handling."
  ;; Add advice to reset restart attempts when a server is started successfully
  (advice-add 'ai-auto-complete-mcp-start-server :around
              #'ai-auto-complete-mcp-advice-start-server-reset-attempts)

  ;; Add advice to handle errors in stdio process filter
  (advice-add 'ai-auto-complete-mcp-stdio-process-filter :around
              #'ai-auto-complete-mcp-advice-stdio-process-filter)

  ;; Add advice to handle errors in stdio process sentinel
  (advice-add 'ai-auto-complete-mcp-stdio-process-sentinel :around
              #'ai-auto-complete-mcp-advice-stdio-process-sentinel)

  ;; Add advice to handle errors in sse process events
  (when (fboundp 'ai-auto-complete-mcp-sse-process-events)
    (advice-add 'ai-auto-complete-mcp-sse-process-events :around
                #'ai-auto-complete-mcp-advice-sse-process-events))

  ;; Add advice to handle errors in tool calls
  (advice-add 'ai-auto-complete-mcp-call-tool :around
              #'ai-auto-complete-mcp-advice-call-tool-handle-errors)

  ;; Add advice to handle errors in resource reads
  (advice-add 'ai-auto-complete-mcp-read-resource :around
              #'ai-auto-complete-mcp-advice-read-resource-handle-errors)

  ;; Add advice to handle errors in prompt gets
  (advice-add 'ai-auto-complete-mcp-get-prompt :around
              #'ai-auto-complete-mcp-advice-get-prompt-handle-errors)

  ;; Add advice to handle errors in tools list
  (advice-add 'ai-auto-complete-mcp-list-tools :around
              #'ai-auto-complete-mcp-advice-list-tools-handle-errors)

  ;; Add advice to handle errors in resources list
  (advice-add 'ai-auto-complete-mcp-list-resources :around
              #'ai-auto-complete-mcp-advice-list-resources-handle-errors)

  ;; Add advice to handle errors in prompts list
  (advice-add 'ai-auto-complete-mcp-list-prompts :around
              #'ai-auto-complete-mcp-advice-list-prompts-handle-errors))

;; Teardown function to remove advice from MCP functions
(defun ai-auto-complete-mcp-error-handling-teardown ()
  "Remove MCP error handling."
  ;; Remove advice from start server function
  (advice-remove 'ai-auto-complete-mcp-start-server
                 #'ai-auto-complete-mcp-advice-start-server-reset-attempts)

  ;; Remove advice from stdio process filter
  (advice-remove 'ai-auto-complete-mcp-stdio-process-filter
                 #'ai-auto-complete-mcp-advice-stdio-process-filter)

  ;; Remove advice from stdio process sentinel
  (advice-remove 'ai-auto-complete-mcp-stdio-process-sentinel
                 #'ai-auto-complete-mcp-advice-stdio-process-sentinel)

  ;; Remove advice from sse process events
  (when (fboundp 'ai-auto-complete-mcp-sse-process-events)
    (advice-remove 'ai-auto-complete-mcp-sse-process-events
                   #'ai-auto-complete-mcp-advice-sse-process-events))

  ;; Remove advice from tool call function
  (advice-remove 'ai-auto-complete-mcp-call-tool
                 #'ai-auto-complete-mcp-advice-call-tool-handle-errors)

  ;; Remove advice from resource read function
  (advice-remove 'ai-auto-complete-mcp-read-resource
                 #'ai-auto-complete-mcp-advice-read-resource-handle-errors)

  ;; Remove advice from prompt get function
  (advice-remove 'ai-auto-complete-mcp-get-prompt
                 #'ai-auto-complete-mcp-advice-get-prompt-handle-errors)

  ;; Remove advice from tools list function
  (advice-remove 'ai-auto-complete-mcp-list-tools
                 #'ai-auto-complete-mcp-advice-list-tools-handle-errors)

  ;; Remove advice from resources list function
  (advice-remove 'ai-auto-complete-mcp-list-resources
                 #'ai-auto-complete-mcp-advice-list-resources-handle-errors)

  ;; Remove advice from prompts list function
  (advice-remove 'ai-auto-complete-mcp-list-prompts
                 #'ai-auto-complete-mcp-advice-list-prompts-handle-errors))

;; Set up MCP error handling when this module is loaded
(ai-auto-complete-mcp-error-handling-setup)

(provide 'mcp/mcp-error-handling)
;;; mcp-error-handling.el ends here
