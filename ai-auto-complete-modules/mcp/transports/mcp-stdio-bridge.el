;;; mcp-stdio-bridge.el --- MCP bridge-based stdio transport -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a stdio transport for MCP servers that uses a Python bridge
;; to communicate with MCP servers using the MCP Python SDK.

;;; Code:

(require 'mcp/mcp-core)
(declare-function ai-auto-complete-server-types "mcp/mcp-core")

;; Find the last JSON response in the buffer
(defun ai-auto-complete-mcp-stdio-bridge-find-last-json-response (process)
  "Find the last JSON response in the buffer for PROCESS and call the callback."
  (let* ((server-name (process-get process 'server-name))
         (buffer-name (format "*MCP-stdio-bridge-%s*" server-name))
         (buffer (get-buffer-create buffer-name))
         (current-callback (process-get process 'current-callback))
         (last-json nil)
         (request-id (process-get process 'last-request-id)))

    (when current-callback
      (with-current-buffer buffer
        ;; Search for JSON-RPC responses in the buffer
        (goto-char (point-min))
        (while (re-search-forward "{.*\"jsonrpc\"\\s-*:\\s-*\"2\\.0\".*}" nil t)
          (let ((json-str (match-string 0)))
            (condition-case nil
                (let* ((json-object-type 'plist)
                       (json-array-type 'list)
                       (json-key-type 'keyword)
                       (json-msg (json-read-from-string json-str))
                       (response-id (plist-get json-msg :id)))

                  ;; If this response matches our request ID, use it
                  (when (and request-id (equal response-id request-id))
                    (message "MCP stdio-bridge: Found matching JSON response for request ID: %s" request-id)
                    (setq last-json json-msg)
                    (goto-char (point-max))) ; Stop searching

                  ;; Otherwise, keep track of the last valid JSON response
                  (when (and response-id (not last-json))
                    (setq last-json json-msg)))
              (error nil))))

        ;; If we found a JSON response, call the callback
        (when last-json
          (message "MCP stdio-bridge: Calling callback with last JSON response with ID: %s"
                   (or (plist-get last-json :id) "none"))
          (condition-case callback-err
              (progn
                (funcall current-callback last-json)
                (message "MCP stdio-bridge: Callback executed successfully")
                (process-put process 'current-callback nil)
                (process-put process 'last-request-id nil))
            (error
             (message "MCP stdio-bridge: Error in callback: %s" (error-message-string callback-err))
             ;; Clear the callback to avoid getting stuck
             (process-put process 'current-callback nil)
             (process-put process 'last-request-id nil))))))))

;; Process filter for MCP bridge communication
(defun ai-auto-complete-mcp-stdio-bridge-process-filter (process output)
  "Process filter for MCP bridge communication.
PROCESS is the process object, OUTPUT is the output from the process."
  (let* ((server-name (process-get process 'server-name))
         (buffer-name (format "*MCP-stdio-bridge-%s*" server-name))
         (buffer (get-buffer-create buffer-name))
         (callback-table (process-get process 'callback-table))
         (current-callback (process-get process 'current-callback)))

    ;; Log the raw output for debugging - always show this to help diagnose issues
    (message "MCP stdio-bridge: Raw output from bridge (length: %d)" (length output))
    (when (< (length output) 500)
      (message "MCP stdio-bridge: Raw output content: %s" output))

    ;; Append output to buffer
    (with-current-buffer buffer
      (goto-char (point-max))
      (insert output))

    ;; Process complete JSON messages
    (with-current-buffer buffer
      (goto-char (point-min))
      (while (re-search-forward "\n" nil t)
        (let ((line (buffer-substring (point-min) (point))))
          (delete-region (point-min) (point))

          ;; Check for the ready status message
          (when (string-match "\"status\":\\s-*\"ready\"" line)
            (message "MCP stdio-bridge: Bridge is ready")
            (process-put process 'ready t))

          ;; Check for ElevenLabs server ready message
          (when (string-match "Starting MCP serve" line)
            (message "MCP stdio-bridge: ElevenLabs server is ready")
            (process-put process 'elevenlabs-ready t))

          ;; Check for Brave Search server ready message
          (when (string-match "Brave Search MCP Server running on stdio" line)
            (message "MCP stdio-bridge: Brave Search server is ready")
            (process-put process 'brave-ready t))

          ;; Check for bridge waiting for input message
          (when (string-match "MCP Bridge: Waiting for input" line)
            (message "MCP stdio-bridge: Bridge is waiting for input")
            (process-put process 'waiting-for-input t)

            ;; If we have a pending callback, try to find the last JSON response
            (when (process-get process 'current-callback)
              (message "MCP stdio-bridge: Trying to find the last JSON response for pending callback")
              (ai-auto-complete-mcp-stdio-bridge-find-last-json-response process)))

          ;; Parse the JSON message
          (when (and line (not (string-empty-p line)))
            ;; Try to parse the JSON, but handle errors safely
            (let ((json-message nil)
                  (parse-error nil))

              ;; First try to parse the JSON
              (condition-case err
                  (progn
                    ;; Check if the line looks like JSON
                    (when (string-match "^\\s-*{" line)
                      (message "MCP stdio-bridge: Attempting to parse JSON line: %s"
                               (substring line 0 (min 200 (length line)))))

                    (setq json-message (let ((json-object-type 'plist)
                                            (json-array-type 'list)
                                            (json-key-type 'keyword))
                                        (json-read-from-string line)))

                    ;; Successfully parsed JSON
                    (message "MCP stdio-bridge: Successfully parsed JSON with ID: %s"
                             (or (plist-get json-message :id) "none"))

                    ;; Check if this is a listTools response
                    (when (and (plist-get json-message :id)
                               (string-match "^req-tools-" (plist-get json-message :id)))
                      (message "MCP stdio-bridge: This appears to be a listTools response")
                      (let ((result (plist-get json-message :result)))
                        (when result
                          (message "MCP stdio-bridge: Result type: %s" (type-of result))))))
                (error
                 ;; Capture the error message
                 (setq parse-error (error-message-string err))
                 ;; Only show JSON parsing errors for lines that look like JSON
                 (when (string-match "^\\s-*{" line)
                   (message "MCP stdio-bridge: Error parsing JSON: %s" parse-error)
                   (message "MCP stdio-bridge: Problematic line: %s" line))))

              ;; If we successfully parsed the JSON, process it
              (when json-message
                ;; Check if this is a JSON-RPC response with an ID
                (let ((response-id (plist-get json-message :id)))
                  (message "MCP stdio-bridge: JSON message has ID: %s" response-id)

                  ;; Debug: Check if this is an initialization response
                  (when (and response-id (string-match "^req-init-" response-id))
                    (message "MCP stdio-bridge: This appears to be an initialization response"))

                  (if response-id
                      ;; This is a response to a specific request
                      (let ((stored-callback (and callback-table
                                                 (hash-table-p callback-table)
                                                 (gethash response-id callback-table))))
                        ;; Debug: Check if we found a stored callback
                        (if stored-callback
                            (message "MCP stdio-bridge: Found stored callback for request ID: %s" response-id)
                          (message "MCP stdio-bridge: No stored callback found for request ID: %s" response-id))

                        ;; Debug: Check if we have a current callback
                        (when current-callback
                          (message "MCP stdio-bridge: Current callback is available"))

                        (if stored-callback
                            ;; We have a callback for this request ID
                            (condition-case callback-err
                                (progn
                                  (message "MCP stdio-bridge: Processing response for request ID: %s" response-id)
                                  ;; Safely execute the callback with error handling
                                  (condition-case callback-inner-err
                                      (progn
                                        (funcall stored-callback json-message)
                                        (message "MCP stdio-bridge: Stored callback executed successfully"))
                                    (error
                                     (message "MCP stdio-bridge: Inner error in callback for request ID %s: %s"
                                              response-id (error-message-string callback-inner-err))))
                                  ;; Remove the callback from the table
                                  (remhash response-id callback-table))
                              (error
                               (message "MCP stdio-bridge: Error in callback for request ID %s: %s"
                                        response-id (error-message-string callback-err))
                               (message "MCP stdio-bridge: Callback was processing line of length %d"
                                        (length line))
                               ;; Remove the callback to avoid memory leaks
                               (remhash response-id callback-table)))

                          ;; If we don't have a specific callback but have a current callback
                          (when current-callback
                            (condition-case callback-err
                                (progn
                                  (message "MCP stdio-bridge: Using current callback for response ID: %s" response-id)
                                  ;; Debug: Show the JSON message being processed
                                  (message "MCP stdio-bridge: Processing JSON message with ID: %s"
                                           (or (plist-get json-message :id) "none"))
                                  (funcall current-callback json-message)
                                  (message "MCP stdio-bridge: Current callback executed successfully")
                                  (process-put process 'current-callback nil))
                              (error
                               (message "MCP stdio-bridge: Error in current callback: %s"
                                        (error-message-string callback-err))
                               (message "MCP stdio-bridge: Callback was processing line of length %d"
                                        (length line))
                               ;; Clear the callback to avoid getting stuck
                               (process-put process 'current-callback nil))))))

                    ;; No ID, use the current callback if available
                    (when current-callback
                      (message "MCP stdio-bridge: Using current callback for message without ID")
                      ;; Debug: Show the JSON message being processed
                      (message "MCP stdio-bridge: Processing JSON message without ID")
                      (condition-case callback-err
                          (progn
                            (funcall current-callback json-message)
                            (message "MCP stdio-bridge: Current callback executed successfully")
                            (process-put process 'current-callback nil))
                        (error
                         (message "MCP stdio-bridge: Error in callback: %s" (error-message-string callback-err))
                         (message "MCP stdio-bridge: Callback was processing line of length %d"
                                  (length line))
                         ;; Clear the callback to avoid getting stuck
                         (process-put process 'current-callback nil))))))))))))))

;; Process sentinel for MCP bridge communication
(defun ai-auto-complete-mcp-stdio-bridge-process-sentinel (process event)
  "Process sentinel for MCP bridge communication.
PROCESS is the process object, EVENT is the event that occurred."
  (let ((server-name (process-get process 'server-name)))
    (when ai-auto-complete-mcp-debug-mode
      (message "MCP stdio-bridge: Process %s received event: %s" server-name event))

    (when (or (string-prefix-p "exited" event)
              (string-prefix-p "failed" event)
              (string-prefix-p "killed" event))
      ;; Update server status
      (ai-auto-complete-mcp-update-server-status server-name 'stopped)
      (ai-auto-complete-mcp-update-server-process server-name nil)

      (when ai-auto-complete-mcp-debug-mode
        (message "MCP stdio-bridge: Bridge process for server %s stopped" server-name)))))

;; Start the MCP bridge process
(defun ai-auto-complete-mcp-stdio-bridge-start-bridge (&optional server-type server-runner)
  "Start the MCP bridge process for SERVER-TYPE using SERVER-RUNNER.
If SERVER-TYPE is nil, use the default Python bridge.
If SERVER-RUNNER is nil, use the default runner for the server type."
  (let* ((module-dir (file-name-directory (locate-library "mcp/mcp-core")))
         (bridge-script nil)
         (command nil)
         (buffer-name "*MCP-stdio-bridge*")
         (process nil))

    ;; Determine the bridge script and command based on server type
    (if server-type
        ;; Find the bridge script for the specified server type
        (dolist (type ai-auto-complete-mcp-server-types) ;; function from mcp-core.el but it doesn't belong there
          (when (string-equal (plist-get type :name) server-type)
            (setq bridge-script (expand-file-name
                                (concat "bridge/" (plist-get type :bridge-script))
                                module-dir))
            ;; Use the provided runner if available, otherwise use the default
            (setq command (or server-runner (plist-get type :command)))))

      ;; No server type specified, use the default Python bridge
      (let ((bridge-script-paths (list
                                ;; Try the standard path first
                                (expand-file-name "bridge/python-mcp-bridge/main.py" module-dir)
                                ;; Try the direct path in the modules directory
                                (expand-file-name "ai-auto-complete-modules/mcp/bridge/python-mcp-bridge/main.py")
                                ;; Try relative to the current directory
                                "ai-auto-complete-modules/mcp/bridge/python-mcp-bridge/main.py")))

        ;; Find the first existing bridge script
        (dolist (path bridge-script-paths)
          (message "MCP stdio-bridge: Checking bridge script path: %s (exists: %s)"
                  path (if (file-exists-p path) "yes" "no"))
          (when (and (not bridge-script) (file-exists-p path))
            (setq bridge-script path)))

        ;; Use the provided runner if available, otherwise use Python
        (setq command (or server-runner "python"))))

    ;; Check if the bridge script exists
    (if (not bridge-script)
        (progn
          (message "MCP stdio-bridge: Bridge script not found for server type: %s" server-type)
          nil)

      ;; Start the bridge process
      (condition-case err
          (progn
            ;; Create a buffer for the process
            (with-current-buffer (get-buffer-create buffer-name)
              (erase-buffer))

            ;; Start the process
            (let ((executable (executable-find command)))
              (message "MCP stdio-bridge: Using %s executable: %s"
                      (or server-type "Python") executable)

              ;; Check if we should use a virtual environment
              (let ((python-venv (getenv "VIRTUAL_ENV")))
                (when python-venv
                  (message "MCP stdio-bridge: Using Python virtual environment: %s" python-venv)))

              ;; Start the bridge process
              (message "MCP stdio-bridge: Starting bridge process with command: %s %s"
                      executable bridge-script)
              (setq process (start-process "mcp-bridge" buffer-name
                                          executable bridge-script)))

            (message "MCP stdio-bridge: Bridge process started with PID: %s" (process-id process))

            ;; Set up filter and sentinel
            (set-process-filter process #'ai-auto-complete-mcp-stdio-bridge-process-filter)
            (set-process-sentinel process #'ai-auto-complete-mcp-stdio-bridge-process-sentinel)

            ;; Store the server type and runner in the process
            (process-put process 'server-type server-type)
            (process-put process 'server-runner server-runner)

            ;; Wait for the bridge to be ready
            (let ((timeout 30) ; Increase timeout to 30 seconds
                  (start-time (current-time)))
              (message "MCP stdio-bridge: Waiting for bridge to be ready (timeout: %s seconds)" timeout)
              (while (and (not (process-get process 'ready))
                          (< (float-time (time-since start-time)) timeout))
                (accept-process-output process 0.1)
                (when (zerop (mod (truncate (float-time (time-since start-time))) 5))
                  (message "MCP stdio-bridge: Still waiting for bridge to be ready... (%s seconds elapsed)"
                           (truncate (float-time (time-since start-time))))))

              ;; Check if the bridge is ready
              (if (process-get process 'ready)
                  (progn
                    (message "MCP stdio-bridge: Bridge is ready")
                    process)
                (progn
                  (message "MCP stdio-bridge: Timed out waiting for bridge to be ready")
                  (when process
                    (delete-process process))
                  nil))))
        (error
         (message "MCP stdio-bridge: Error starting bridge process: %s" (error-message-string err))
         nil)))))

;; Global bridge process
(defvar ai-auto-complete-mcp-stdio-bridge-process nil
  "The MCP bridge process.")

;; Start an MCP server using the bridge
(defun ai-auto-complete-mcp-stdio-bridge-start-server (server-name path)
  "Start an MCP server with SERVER-NAME at PATH using the bridge."
  (message "MCP stdio-bridge: Starting server %s at %s" server-name path)

  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (server-type (plist-get server :server-type))
         (transport (plist-get server :transport))
         (is-settings-server (eq transport 'server-bridge))
         (args (plist-get server :args))
         (runner (plist-get server :runner))
         (env (plist-get server :env))
         (api-key (or (plist-get server :api-key)
                      (when env
                        (or (plist-get env :OPENAI_API_KEY)
                            (plist-get env :ANTHROPIC_API_KEY)
                            (plist-get env :GEMINI_API_KEY)
                            (plist-get env :ELEVENLABS_API_KEY))))))
    (if (not server)
        (progn
          (message "MCP stdio-bridge: Server %s not found" server-name)
          nil)

      ;; Check if the path exists (skip for settings-based servers)
      (if (and (not is-settings-server)
               (not (file-exists-p path)))
          (progn
            (message "MCP stdio-bridge: Server path %s does not exist" path)
            nil)
        ;; Debug: Show that we're skipping the file existence check for settings-based servers
        (when is-settings-server
          (message "MCP DEBUG: Skipping file existence check in stdio-bridge for settings-based server %s" server-name)
          (message "MCP DEBUG: Using runner: %s, args: %S" runner args)))

        ;; Start the bridge process if not already running or if we need a different type
        (when (or (not ai-auto-complete-mcp-stdio-bridge-process)
                  (not (string-equal (process-get ai-auto-complete-mcp-stdio-bridge-process 'server-type) server-type)))
          ;; If we have a running bridge but it's the wrong type, kill it
          (when (and ai-auto-complete-mcp-stdio-bridge-process
                     (process-live-p ai-auto-complete-mcp-stdio-bridge-process))
            (message "MCP stdio-bridge: Stopping existing bridge process (wrong type)")
            (delete-process ai-auto-complete-mcp-stdio-bridge-process)
            (setq ai-auto-complete-mcp-stdio-bridge-process nil))

          ;; Start a new bridge process with the correct type
          (setq ai-auto-complete-mcp-stdio-bridge-process
                (ai-auto-complete-mcp-stdio-bridge-start-bridge server-type))) ;; self-module funciton; Another important fork that starts the bridge process

        (if (not ai-auto-complete-mcp-stdio-bridge-process)
            (progn
              (message "MCP stdio-bridge: Failed to start bridge process")
              nil)

          ;; Initialize the server through the bridge
          (let ((buffer-name (format "*MCP-stdio-bridge-%s*" server-name))
                (callback-table (make-hash-table :test 'equal))
                (initialized nil))

            ;; Create a buffer for the server
            (with-current-buffer (get-buffer-create buffer-name)
              (erase-buffer))

            ;; Make sure the callback table is a hash table
            (unless (hash-table-p callback-table)
              (setq callback-table (make-hash-table :test 'equal))
              (message "MCP stdio-bridge: Created new callback table for server %s" server-name))

            ;; Set server properties on the bridge process
            (process-put ai-auto-complete-mcp-stdio-bridge-process 'server-name server-name)
            (process-put ai-auto-complete-mcp-stdio-bridge-process 'callback-table callback-table)

            ;; Prepare initialization parameters
            (let* ((init-params (list :serverPath path :apiKey api-key))
                   ;; For settings-based servers, add additional parameters
                   ;; Convert env from plist to hash table for JSON encoding
                   (env-hash (when env
                               (let ((hash (make-hash-table :test 'equal)))
                                 (if (hash-table-p env)
                                     ;; If env is already a hash table, use it directly
                                     env
                                   ;; If env is a plist, convert it to a hash table
                                   (let ((key nil))
                                     (dolist (item env)
                                       (if (keywordp item)
                                           ;; Remove the leading colon from keyword
                                           (setq key (substring (symbol-name item) 1))
                                         (when key
                                           (puthash key item hash)
                                           (setq key nil)))))
                                   hash))))
                   ;; For settings-based servers, add additional parameters
                   (init-params (if is-settings-server
                                   (plist-put
                                    (plist-put
                                     (plist-put
                                      (plist-put init-params :isSettingsServer t)
                                      :serverArgs args)
                                     :runner runner)
                                    :env env-hash)
                                 init-params))
                   ;; Create a unique request ID for initialization
                   (request-id (format "req-init-%s" (random 10000)))
                   ;; Create the JSON-RPC request
                   (command (ai-auto-complete-mcp-stdio-bridge-create-jsonrpc-request
                             "initialize"
                             init-params
                             request-id))
                   (response nil))

              ;; Set the callback for the response
              (message "MCP stdio-bridge: Setting up callback for initialization response with ID: %s" request-id)

              ;; Store the callback in the callback table
              (puthash request-id
                      (lambda (json-message)
                        (message "MCP stdio-bridge: Initialization callback triggered with message: %S" json-message)
                        (setq response json-message)
                        (setq initialized t)
                        (message "MCP stdio-bridge: Initialization completed, initialized=%s" initialized))
                      callback-table)

              ;; Also set it as the current callback as a fallback
              (process-put ai-auto-complete-mcp-stdio-bridge-process 'current-callback
                          (lambda (json-message)
                            (message "MCP stdio-bridge: Initialization fallback callback triggered with message: %S" json-message)
                            (setq response json-message)
                            (setq initialized t)
                            (message "MCP stdio-bridge: Initialization completed via fallback, initialized=%s" initialized)))

              ;; Store the request ID for later use
              (process-put ai-auto-complete-mcp-stdio-bridge-process 'last-request-id request-id)

              (message "MCP stdio-bridge: Sending initialization request with ID: %s" request-id)
              (process-send-string ai-auto-complete-mcp-stdio-bridge-process (concat command "\n"))

              ;; Wait for initialization to complete
              (let ((timeout 30) ;; Increase timeout to 30 seconds
                    (start-time (current-time)))
                (while (and (not initialized)
                            (< (float-time (time-since start-time)) timeout))
                  (accept-process-output ai-auto-complete-mcp-stdio-bridge-process 0.1)

                  ;; Check if the bridge is waiting for input, which indicates it's done processing
                  (when (and (not initialized)
                             (process-get ai-auto-complete-mcp-stdio-bridge-process 'waiting-for-input))
                    ;; Try to find the last JSON response
                    (message "MCP stdio-bridge: Bridge is waiting for input, trying to find the last JSON response")
                    (ai-auto-complete-mcp-stdio-bridge-find-last-json-response ai-auto-complete-mcp-stdio-bridge-process))))

                (if initialized
                    (if (plist-get response :error)
                        (progn
                          (message "MCP stdio-bridge: Error initializing server %s: %s"
                                  server-name (plist-get (plist-get response :error) :message))
                          nil)
                      ;; Check for success in the result
                      (let* ((result (plist-get response :result))
                             (success (and result (plist-get result :success))))
                        (if success
                            (progn
                              ;; Update server status and process
                              (ai-auto-complete-mcp-update-server-status server-name 'running)
                              (ai-auto-complete-mcp-update-server-process server-name ai-auto-complete-mcp-stdio-bridge-process)

                              (message "MCP stdio-bridge: Server %s initialized successfully" server-name)
                              t)
                          ;; No explicit success flag, but no error either - assume success
                          (progn
                            ;; Update server status and process
                            (ai-auto-complete-mcp-update-server-status server-name 'running)
                            (ai-auto-complete-mcp-update-server-process server-name ai-auto-complete-mcp-stdio-bridge-process)

                            (message "MCP stdio-bridge: Server %s initialized (no explicit success flag)" server-name)
                            t))))
                  (progn
                    (message "MCP stdio-bridge: Timed out waiting for server %s to initialize" server-name)
                    nil))))))))

;; Stop an MCP server using the bridge
(defun ai-auto-complete-mcp-stdio-bridge-stop-server (server-name)
  "Stop an MCP server with SERVER-NAME using the bridge."
  (let* ((server (ai-auto-complete-mcp-get-server server-name)))
    (when server
      ;; Update server status
      (ai-auto-complete-mcp-update-server-status server-name 'stopped)
      (ai-auto-complete-mcp-update-server-process server-name nil)

      (message "MCP stdio-bridge: Stopped server %s" server-name)
      t)))

;; Send a command to the bridge
(defun ai-auto-complete-mcp-stdio-bridge-send-command (server-name command callback)
  "Send COMMAND to the bridge for SERVER-NAME.
CALLBACK will be called with the response."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (process (plist-get server :process)))
    (if (and server process (process-live-p process))
        (progn
          ;; Extract the request ID from the command if it's a JSON-RPC request
          (let ((request-id nil)
                (callback-table (process-get process 'callback-table)))

            ;; Try to parse the command as JSON to extract the ID
            (condition-case nil
                (let* ((json-object-type 'plist)
                       (json-array-type 'list)
                       (json-key-type 'keyword)
                       (json-cmd (json-read-from-string command)))
                  (setq request-id (plist-get json-cmd :id)))
              (error nil))

            ;; If we have a request ID and a callback table, store the callback
            (if (and request-id callback-table (hash-table-p callback-table))
                (progn
                  (message "MCP stdio-bridge: Storing callback for request ID: %s" request-id)
                  (puthash request-id callback callback-table)
                  ;; Also store the request ID for the fallback mechanism
                  (process-put process 'last-request-id request-id))

              ;; Otherwise, use the current callback approach
              (progn
                (process-put process 'current-callback callback)
                ;; If we have a request ID, store it for the fallback mechanism
                (when request-id
                  (process-put process 'last-request-id request-id)))))

          ;; Send the command
          (process-send-string process (concat command "\n"))

          (when ai-auto-complete-mcp-debug-mode
            (message "MCP stdio-bridge: Sent command to bridge for server %s: %s"
                     server-name (substring command 0 (min 100 (length command)))))

          t)
      (progn
        (message "MCP stdio-bridge: Server %s not running" server-name)
        nil))))

;; Create a JSON-RPC request
(defun ai-auto-complete-mcp-stdio-bridge-create-jsonrpc-request (method params id)
  "Create a JSON-RPC request with METHOD, PARAMS, and ID."
  (json-encode
   (list :jsonrpc "2.0"
         :method method
         :params params
         :id id)))

;; List tools using the bridge with JSON-RPC
(defun ai-auto-complete-mcp-stdio-bridge-list-tools (server-name callback &rest _args)
  "List tools from SERVER-NAME using the bridge.
CALLBACK will be called with the result.
Optional _ARGS are ignored for compatibility with different calling conventions."
  (message "MCP stdio-bridge: Listing tools from server %s" server-name)

  ;; First, check if the server exists
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP stdio-bridge: Server %s not found" server-name)
          (when (functionp callback)
            (funcall callback (format "Error: Server %s not found" server-name)))
          nil)

      ;; Check if the server is running
      (let ((status (plist-get server :status))
            (process (plist-get server :process)))
        (if (not (eq status 'running))
            ;; Try to start the server
            (if (ai-auto-complete-mcp-stdio-bridge-start-server server-name (plist-get server :path))
                ;; Server started successfully, list tools using JSON-RPC
                (let ((request-id (format "req-tools-%s" (random 10000))))
                  (message "MCP stdio-bridge: Sending listTools request with ID: %s" request-id)
                  (ai-auto-complete-mcp-stdio-bridge-send-command
                   server-name
                   (ai-auto-complete-mcp-stdio-bridge-create-jsonrpc-request
                    "listTools"
                    (list :serverPath (plist-get server :path))
                    request-id)
                   (lambda (response)
                     (let ((error (plist-get response :error))
                           (result (plist-get response :result)))
                       (if error
                           (progn
                             (message "MCP stdio-bridge: Error listing tools: %s" (plist-get error :message))
                             (when (functionp callback)
                               (funcall callback (format "Error: %s" (plist-get error :message)))))
                         (progn
                           (message "MCP stdio-bridge: Successfully received tools list")
                           (when result
                             (message "MCP stdio-bridge: Result type: %s" (type-of result)))

                           ;; Process the result based on its type
                           (let ((processed-result result))
                             ;; If result is a string, try to parse it as a plist or list
                             (when (stringp result)
                               (condition-case display-err
                                   (message "MCP stdio-bridge: Result is a string, attempting to parse: %s"
                                            (substring result 0 (min 100 (length result))))
                                 (error
                                  (message "MCP stdio-bridge: Result is a string (error displaying content)")))

                               (condition-case parse-err
                                   (progn
                                     ;; Check if it looks like a plist string (starts with "(" and contains ":")
                                     (if (and (> (length result) 2)  ;; Make sure string is long enough
                                              (string-match-p "^\\s*(\\s*:" result)
                                              (string-match-p ":\\s*[^\\s]" result))
                                         (progn
                                           (message "MCP stdio-bridge: String looks like a plist, attempting to read")
                                           ;; Try to read it as a plist with error handling
                                           (condition-case read-err
                                               (setq processed-result (read result))
                                             (error
                                              (message "MCP stdio-bridge: Error reading plist: %s"
                                                       (error-message-string read-err)))))

                                       ;; Check if it looks like a list string (starts with "[" or "(")
                                       (when (and (> (length result) 2)  ;; Make sure string is long enough
                                                  (string-match-p "^\\s*[\\[(]" result))
                                         (message "MCP stdio-bridge: String looks like a list, attempting to read")
                                         ;; Try to read it as a list with error handling
                                         (condition-case read-err
                                             (setq processed-result (read result))
                                           (error
                                            (message "MCP stdio-bridge: Error reading list: %s"
                                                     (error-message-string read-err)))))))
                                 (error
                                  (message "MCP stdio-bridge: Error parsing result string: %s"
                                           (error-message-string parse-err))
                                  ;; Keep it as a string if parsing fails
                                  (setq processed-result result))))

                             ;; Safely process the result and call the callback
                             (condition-case process-err
                                 (when (functionp callback)
                                   (message "MCP stdio-bridge: Calling callback with processed result type: %s"
                                            (type-of processed-result))
                                   (funcall callback processed-result))
                               (error
                                (message "MCP stdio-bridge: Error processing tools list: %s"
                                         (error-message-string process-err))
                                (when (functionp callback)
                                  (funcall callback "Error processing tools list")))))))))))
              ;; Failed to start server
              (progn
                (message "MCP stdio-bridge: Failed to start server %s" server-name)
                (when (functionp callback)
                  (funcall callback (format "Error: Failed to start server %s" server-name)))
                nil))

          ;; Server is running, send list tools command using JSON-RPC
          (let ((request-id (format "req-tools-%s" (random 10000))))
            (message "MCP stdio-bridge: Sending listTools request with ID: %s" request-id)
            (ai-auto-complete-mcp-stdio-bridge-send-command
             server-name
             (ai-auto-complete-mcp-stdio-bridge-create-jsonrpc-request
              "listTools"
              (list :serverPath (plist-get server :path))
              request-id)
             (lambda (response)
               (let ((error (plist-get response :error))
                     (result (plist-get response :result)))
                 (if error
                     (progn
                       (message "MCP stdio-bridge: Error listing tools: %s" (plist-get error :message))
                       (when (functionp callback)
                         (funcall callback (format "Error: %s" (plist-get error :message)))))
                   (progn
                     (message "MCP stdio-bridge: Successfully received tools list")
                     (when result
                       (message "MCP stdio-bridge: Result type: %s" (type-of result)))

                     ;; Process the result based on its type
                     (let ((processed-result result))
                       ;; If result is a string, try to parse it as a plist or list
                       (when (stringp result)
                         (condition-case display-err
                             (message "MCP stdio-bridge: Result is a string, attempting to parse: %s"
                                      (substring result 0 (min 100 (length result))))
                           (error
                            (message "MCP stdio-bridge: Result is a string (error displaying content)")))

                         (condition-case parse-err
                             (progn
                               ;; Check if it looks like a plist string (starts with "(" and contains ":")
                               (if (and (> (length result) 2)  ;; Make sure string is long enough
                                        (string-match-p "^\\s*(\\s*:" result)
                                        (string-match-p ":\\s*[^\\s]" result))
                                   (progn
                                     (message "MCP stdio-bridge: String looks like a plist, attempting to read")
                                     ;; Try to read it as a plist with error handling
                                     (condition-case read-err
                                         (setq processed-result (read result))
                                       (error
                                        (message "MCP stdio-bridge: Error reading plist: %s"
                                                 (error-message-string read-err)))))

                                 ;; Check if it looks like a list string (starts with "[" or "(")
                                 (when (and (> (length result) 2)  ;; Make sure string is long enough
                                            (string-match-p "^\\s*[\\[(]" result))
                                   (message "MCP stdio-bridge: String looks like a list, attempting to read")
                                   ;; Try to read it as a list with error handling
                                   (condition-case read-err
                                       (setq processed-result (read result))
                                     (error
                                      (message "MCP stdio-bridge: Error reading list: %s"
                                               (error-message-string read-err)))))))
                           (error
                            (message "MCP stdio-bridge: Error parsing result string: %s"
                                     (error-message-string parse-err))
                            ;; Keep it as a string if parsing fails
                            (setq processed-result result))))

                       ;; Safely process the result and call the callback
                       (condition-case process-err
                           (when (functionp callback)
                             (message "MCP stdio-bridge: Calling callback with processed result type: %s"
                                      (type-of processed-result))
                             (funcall callback processed-result))
                         (error
                          (message "MCP stdio-bridge: Error processing tools list: %s"
                                   (error-message-string process-err))
                          (when (functionp callback)
                            (funcall callback "Error processing tools list"))))))))))))))))

;; List resources using the bridge with JSON-RPC
(defun ai-auto-complete-mcp-stdio-bridge-list-resources (server-name callback &rest _args)
  "List resources from SERVER-NAME using the bridge.
CALLBACK will be called with the result.
Optional _ARGS are ignored for compatibility with different calling conventions."
  (message "MCP stdio-bridge: Listing resources from server %s" server-name)

  ;; First, check if the server exists
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP stdio-bridge: Server %s not found" server-name)
          (when (functionp callback)
            (funcall callback (format "Error: Server %s not found" server-name)))
          nil)

      ;; Check if the server is running
      (let ((status (plist-get server :status))
            (process (plist-get server :process)))
        (if (not (eq status 'running))
            ;; Try to start the server
            (if (ai-auto-complete-mcp-stdio-bridge-start-server server-name (plist-get server :path))
                ;; Server started successfully, list resources using JSON-RPC
                (ai-auto-complete-mcp-stdio-bridge-send-command
                 server-name
                 (ai-auto-complete-mcp-stdio-bridge-create-jsonrpc-request
                  "listResources"
                  (list :serverPath (plist-get server :path))
                  (format "req-resources-%s" (random 10000)))
                 (lambda (response)
                   (let ((error (plist-get response :error))
                         (result (plist-get response :result)))
                     (if error
                         (when (functionp callback)
                           (funcall callback (format "Error: %s" (plist-get error :message))))
                       (when (functionp callback)
                         (funcall callback result))))))
              ;; Failed to start server
              (progn
                (message "MCP stdio-bridge: Failed to start server %s" server-name)
                (when (functionp callback)
                  (funcall callback (format "Error: Failed to start server %s" server-name)))
                nil))

          ;; Server is running, send list resources command using JSON-RPC
          (ai-auto-complete-mcp-stdio-bridge-send-command
           server-name
           (ai-auto-complete-mcp-stdio-bridge-create-jsonrpc-request
            "listResources"
            (list :serverPath (plist-get server :path))
            (format "req-resources-%s" (random 10000)))
           (lambda (response)
             (let ((error (plist-get response :error))
                   (result (plist-get response :result)))
               (if error
                   (when (functionp callback)
                     (funcall callback (format "Error: %s" (plist-get error :message))))
                 (funcall callback result))))))))))

;; List prompts using the bridge with JSON-RPC
(defun ai-auto-complete-mcp-stdio-bridge-list-prompts (server-name callback &rest _args)
  "List prompts from SERVER-NAME using the bridge.
CALLBACK will be called with the result.
Optional _ARGS are ignored for compatibility with different calling conventions."
  (message "MCP stdio-bridge: Listing prompts from server %s" server-name)

  ;; First, check if the server exists
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP stdio-bridge: Server %s not found" server-name)
          (when (functionp callback)
            (funcall callback (format "Error: Server %s not found" server-name)))
          nil)

      ;; Check if the server is running
      (let ((status (plist-get server :status))
            (process (plist-get server :process)))
        (if (not (eq status 'running))
            ;; Try to start the server
            (if (ai-auto-complete-mcp-stdio-bridge-start-server server-name (plist-get server :path))
                ;; Server started successfully, list prompts using JSON-RPC
                (ai-auto-complete-mcp-stdio-bridge-send-command
                 server-name
                 (ai-auto-complete-mcp-stdio-bridge-create-jsonrpc-request
                  "listPrompts"
                  (list :serverPath (plist-get server :path))
                  (format "req-prompts-%s" (random 10000)))
                 (lambda (response)
                   (let ((error (plist-get response :error))
                         (result (plist-get response :result)))
                     (if error
                         (when (functionp callback)
                           (funcall callback (format "Error: %s" (plist-get error :message))))
                       (when (functionp callback)
                         (funcall callback result)))))))
              ;; Failed to start server
              (progn
                (message "MCP stdio-bridge: Failed to start server %s" server-name)
                (when (functionp callback)
                  (funcall callback (format "Error: Failed to start server %s" server-name)))
                nil))

          ;; Server is running, send list prompts command using JSON-RPC
          (ai-auto-complete-mcp-stdio-bridge-send-command
           server-name
           (ai-auto-complete-mcp-stdio-bridge-create-jsonrpc-request
            "listPrompts"
            (list :serverPath (plist-get server :path))
            (format "req-prompts-%s" (random 10000)))
           (lambda (response)
             (let ((error (plist-get response :error))
                   (result (plist-get response :result)))
               (if error
                   (when (functionp callback)
                     (funcall callback (format "Error: %s" (plist-get error :message))))
                 (funcall callback result)))))))))

;; Call a tool using the bridge with JSON-RPC
(defun ai-auto-complete-mcp-stdio-bridge-call-tool (server-name tool-name params callback &rest _args)
  "Call TOOL-NAME on SERVER-NAME with PARAMS using the bridge.
CALLBACK will be called with the result.
Optional _ARGS are ignored for compatibility with different calling conventions."
  (message "MCP stdio-bridge: Calling tool %s on server %s" tool-name server-name)

  ;; First, check if the server exists
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP stdio-bridge: Server %s not found" server-name)
          (when (functionp callback)
            (funcall callback (format "Error: Server %s not found" server-name)))
          nil)

      ;; Check if the server is running
      (let ((status (plist-get server :status))
            (process (plist-get server :process)))
        (if (not (eq status 'running))
            ;; Try to start the server
            (if (ai-auto-complete-mcp-stdio-bridge-start-server server-name (plist-get server :path))
                ;; Server started successfully, call tool using JSON-RPC
                (ai-auto-complete-mcp-stdio-bridge-send-command
                 server-name
                 (ai-auto-complete-mcp-stdio-bridge-create-jsonrpc-request
                  "callTool"
                  (list :serverPath (plist-get server :path)
                        :toolName tool-name
                        :arguments params)
                  (format "req-call-%s" (random 10000)))
                 (lambda (response)
                   (let ((error (plist-get response :error))
                         (result (plist-get response :result)))
                     (if error
                         (when (functionp callback)
                           (funcall callback (format "Error: %s" (plist-get error :message))))
                       (when (functionp callback)
                         (funcall callback result))))))
              ;; Failed to start server
              (progn
                (message "MCP stdio-bridge: Failed to start server %s" server-name)
                (when (functionp callback)
                  (funcall callback (format "Error: Failed to start server %s" server-name)))
                nil))

          ;; Server is running, send call tool command using JSON-RPC
          (ai-auto-complete-mcp-stdio-bridge-send-command
           server-name
           (ai-auto-complete-mcp-stdio-bridge-create-jsonrpc-request
            "callTool"
            (list :serverPath (plist-get server :path)
                  :toolName tool-name
                  :arguments params)
            (format "req-call-%s" (random 10000)))
           (lambda (response)
             (let ((error (plist-get response :error))
                   (result (plist-get response :result)))
               (if error
                   (when (functionp callback)
                     (funcall callback (format "Error: %s" (plist-get error :message))))
                 (funcall callback result))))))))))

;; Read a resource using the bridge with JSON-RPC
(defun ai-auto-complete-mcp-stdio-bridge-read-resource (server-name resource-uri callback &rest _args)
  "Read RESOURCE-URI from SERVER-NAME using the bridge.
CALLBACK will be called with the result.
Optional _ARGS are ignored for compatibility with different calling conventions."
  (message "MCP stdio-bridge: Reading resource %s from server %s" resource-uri server-name)

  ;; First, check if the server exists
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP stdio-bridge: Server %s not found" server-name)
          (funcall callback (format "Error: Server %s not found" server-name))
          nil)

      ;; Check if the server is running
      (let ((status (plist-get server :status))
            (process (plist-get server :process)))
        (if (not (eq status 'running))
            ;; Try to start the server
            (if (ai-auto-complete-mcp-stdio-bridge-start-server server-name (plist-get server :path))
                ;; Server started successfully, read resource using JSON-RPC
                (ai-auto-complete-mcp-stdio-bridge-send-command
                 server-name
                 (ai-auto-complete-mcp-stdio-bridge-create-jsonrpc-request
                  "readResource"
                  (list :serverPath (plist-get server :path)
                        :uri resource-uri)
                  (format "req-read-%s" (random 10000)))
                 (lambda (response)
                   (let ((error (plist-get response :error))
                         (result (plist-get response :result)))
                     (if error
                         (funcall callback (format "Error: %s" (plist-get error :message)))
                       (funcall callback (list :content (plist-get result :content)
                                              :mime-type (plist-get result :mimeType)))))))
              ;; Failed to start server
              (progn
                (message "MCP stdio-bridge: Failed to start server %s" server-name)
                (funcall callback (format "Error: Failed to start server %s" server-name))
                nil))

          ;; Server is running, send read resource command using JSON-RPC
          (ai-auto-complete-mcp-stdio-bridge-send-command
           server-name
           (ai-auto-complete-mcp-stdio-bridge-create-jsonrpc-request
            "readResource"
            (list :serverPath (plist-get server :path)
                  :uri resource-uri)
            (format "req-read-%s" (random 10000)))
           (lambda (response)
             (let ((error (plist-get response :error))
                   (result (plist-get response :result)))
               (if error
                   (funcall callback (format "Error: %s" (plist-get error :message)))
                 (funcall callback (list :content (plist-get result :content)
                                        :mime-type (plist-get result :mimeType))))))))))))

;; Install the MCP bridge transport
(defun ai-auto-complete-mcp-stdio-bridge-install ()
  "Install the MCP bridge transport as the default stdio transport."
  (interactive)

  ;; Define aliases for the standard stdio transport functions
  (defalias 'ai-auto-complete-mcp-stdio-start-server 'ai-auto-complete-mcp-stdio-bridge-start-server)
  (defalias 'ai-auto-complete-mcp-stdio-stop-server 'ai-auto-complete-mcp-stdio-bridge-stop-server)
  (defalias 'ai-auto-complete-mcp-stdio-call-tool 'ai-auto-complete-mcp-stdio-bridge-call-tool)
  (defalias 'ai-auto-complete-mcp-stdio-read-resource 'ai-auto-complete-mcp-stdio-bridge-read-resource)
  (defalias 'ai-auto-complete-mcp-stdio-list-tools 'ai-auto-complete-mcp-stdio-bridge-list-tools)
  (defalias 'ai-auto-complete-mcp-stdio-list-resources 'ai-auto-complete-mcp-stdio-bridge-list-resources)
  (defalias 'ai-auto-complete-mcp-stdio-list-prompts 'ai-auto-complete-mcp-stdio-bridge-list-prompts)

  (message "MCP: Bridge-based python mcp skd stdio transport installed first fxn to be executed from mcp-stdio-bridge.el"))

;; Install the MCP bridge transport on load
(ai-auto-complete-mcp-stdio-bridge-install)

(provide 'mcp/transports/mcp-stdio-bridge)
;;; mcp-stdio-bridge.el ends here
