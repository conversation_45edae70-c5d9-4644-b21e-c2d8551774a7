;;; mcp-stdio.el --- Stdio transport for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the stdio transport for MCP integration in the AI Auto Complete package.
;; It handles communication with MCP servers using standard input/output.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)

;; Process filter for MCP stdio communication
(defun ai-auto-complete-mcp-stdio-process-filter (process output)
  "Process filter for MCP stdio communication.
PROCESS is the process object, OUTPUT is the output from the process."
  (let* ((server-name (process-get process 'server-name))
         (buffer-name (format "*MCP-stdio-%s*" server-name))
         (buffer (get-buffer-create buffer-name))
         (callback-table (process-get process 'callback-table)))

    ;; Append output to buffer
    (with-current-buffer buffer
      (goto-char (point-max))
      (insert output))

    ;; Process complete JSON-RPC messages
    (with-current-buffer buffer
      (goto-char (point-min))
      (while (re-search-forward "\n" nil t)
        (let ((line (buffer-substring (point-min) (point))))
          (delete-region (point-min) (point))

          ;; Parse the JSON-RPC message
          (when (and line (not (string-empty-p line)))
            (let ((json-message (ai-auto-complete-mcp-parse-jsonrpc-response line)))
              (when json-message
                (if (plist-member json-message :id)
                    ;; This is a response to a request
                    (let* ((id (plist-get json-message :id))
                           (callback (gethash id callback-table)))
                      (when callback
                        (when ai-auto-complete-mcp-debug-mode
                          (message "MCP stdio: Processing response for request %s" id))
                        (remhash id callback-table)
                        (funcall callback json-message)))

                  ;; This is a notification
                  (when ai-auto-complete-mcp-debug-mode
                    (message "MCP stdio: Received notification: %s"
                             (plist-get json-message :method))))))))))))

;; Process sentinel for MCP stdio communication
(defun ai-auto-complete-mcp-stdio-process-sentinel (process event)
  "Process sentinel for MCP stdio communication.
PROCESS is the process object, EVENT is the event that occurred."
  (let ((server-name (process-get process 'server-name)))
    (when ai-auto-complete-mcp-debug-mode
      (message "MCP stdio: Process %s received event: %s" server-name event))

    (when (or (string-prefix-p "exited" event)
              (string-prefix-p "failed" event)
              (string-prefix-p "killed" event))
      ;; Update server status
      (ai-auto-complete-mcp-update-server-status server-name 'stopped)
      (ai-auto-complete-mcp-update-server-process server-name nil)

      (when ai-auto-complete-mcp-debug-mode
        (message "MCP stdio: Server %s stopped" server-name)))))

;; Start an MCP server using stdio transport
(defun ai-auto-complete-mcp-stdio-start-server (server-name path)
  "Start an MCP server with SERVER-NAME at PATH using stdio transport."
  (when (fboundp 'ai-auto-complete-mcp-debug-info)
    (ai-auto-complete-mcp-debug-info "Starting MCP server %s at %s using stdio transport"
                                    server-name path))

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (when (fboundp 'ai-auto-complete-mcp-debug-error)
            (ai-auto-complete-mcp-debug-error "Server %s not found" server-name))
          (message "MCP stdio: Server %s not found" server-name)
          nil)

      ;; Check if the path exists
      (if (not (file-exists-p path))
          (progn
            (when (fboundp 'ai-auto-complete-mcp-debug-error)
              (ai-auto-complete-mcp-debug-error "Server path %s does not exist" path))
            (message "MCP stdio: Server path %s does not exist" path)
            nil)

        ;; Check if the file is executable
        (if (not (file-executable-p path))
            (progn
              (when (fboundp 'ai-auto-complete-mcp-debug-error)
                (ai-auto-complete-mcp-debug-error "Server file %s is not executable" path))
              (message "MCP stdio: Server file %s is not executable" path)
              nil)

          (let ((buffer-name (format "*MCP-stdio-%s*" server-name))
                (callback-table (make-hash-table :test 'equal)))

            ;; Create a buffer for the process
            (with-current-buffer (get-buffer-create buffer-name)
              (erase-buffer))

            ;; Start the process
            (condition-case err
                (let* ((runner (or (plist-get server :runner)
                                  ai-auto-complete-mcp-python-command))
                       (process (start-process server-name buffer-name
                                              runner
                                              path)))

                  (when ai-auto-complete-mcp-debug-mode
                    (message "MCP stdio: Starting server %s with runner: %s"
                             server-name runner))

                  ;; Set process properties
                  (process-put process 'server-name server-name)
                  (process-put process 'callback-table callback-table)

                  ;; Set up filter and sentinel
                  (set-process-filter process #'ai-auto-complete-mcp-stdio-process-filter)
                  (set-process-sentinel process #'ai-auto-complete-mcp-stdio-process-sentinel)

                  ;; Update server status and process
                  (ai-auto-complete-mcp-update-server-status server-name 'running)
                  (ai-auto-complete-mcp-update-server-process server-name process)

                  ;; Initialize the server
                  (ai-auto-complete-mcp-stdio-initialize-server server-name)

                  (when (fboundp 'ai-auto-complete-mcp-debug-info)
                    (ai-auto-complete-mcp-debug-info "Successfully started server %s" server-name))

                  (when ai-auto-complete-mcp-debug-mode
                    (message "MCP stdio: Started server %s" server-name))

                  t)
              (error
               (when (fboundp 'ai-auto-complete-mcp-debug-error)
                 (ai-auto-complete-mcp-debug-error "Error starting process: %s"
                                                 (error-message-string err)))
               (message "MCP stdio: Error starting server %s: %s"
                        server-name (error-message-string err))
               nil))))))))

;; Stop an MCP server using stdio transport
(defun ai-auto-complete-mcp-stdio-stop-server (server-name)
  "Stop an MCP server with SERVER-NAME using stdio transport."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (process (plist-get server :process)))
    (when (and server process)
      ;; Kill the process
      (delete-process process)

      ;; Update server status and process
      (ai-auto-complete-mcp-update-server-status server-name 'stopped)
      (ai-auto-complete-mcp-update-server-process server-name nil)

      (when ai-auto-complete-mcp-debug-mode
        (message "MCP stdio: Stopped server %s" server-name))

      t)))

;; Send a message to an MCP server using stdio transport
(defun ai-auto-complete-mcp-stdio-send-message (server-name message &optional callback)
  "Send MESSAGE to MCP server with SERVER-NAME using stdio transport.
If CALLBACK is provided, it will be called with the response."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (process (plist-get server :process)))
    (if (and server process)
        (let ((json-message (ai-auto-complete-mcp-parse-jsonrpc-response message)))
          (when json-message
            ;; If this is a request with an ID and a callback is provided
            (when (and (plist-member json-message :id) callback)
              (let ((id (plist-get json-message :id))
                    (callback-table (process-get process 'callback-table)))
                (puthash id callback callback-table)))

            ;; Send the message
            (process-send-string process (concat message "\n"))

            (when ai-auto-complete-mcp-debug-mode
              (message "MCP stdio: Sent message to server %s: %s"
                       server-name
                       (substring message 0 (min 100 (length message)))))

            t))
      (progn
        (message "MCP stdio: Server %s not running" server-name)
        nil))))

;; Initialize an MCP server
(defun ai-auto-complete-mcp-stdio-initialize-server (server-name)
  "Initialize the MCP server with SERVER-NAME."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (params (list :serverName server-name
                      :serverVersion "1.0.0"
                      :capabilities (list :tools (list :listChanged t)
                                         :resources (list :listChanged t :subscribe t)
                                         :prompts (list :listChanged t)))))

    ;; Create the initialization request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request
                    "initialize" params request-id)))

      ;; Send the request
      (ai-auto-complete-mcp-stdio-send-message
       server-name request
       (lambda (response)
         ;; Process the initialization response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (progn
                 (message "MCP stdio: Error initializing server %s: %s"
                          server-name (plist-get error :message))
                 (ai-auto-complete-mcp-stop-server server-name))

             ;; Update server capabilities
             (when result
               (ai-auto-complete-mcp-update-server-capabilities server-name result)
               (when ai-auto-complete-mcp-debug-mode
                 (message "MCP stdio: Server %s initialized with capabilities: %s"
                          server-name result))))))))))

;; Call a tool using stdio transport
(defun ai-auto-complete-mcp-stdio-call-tool (server-name tool-name params callback)
  "Call TOOL-NAME on SERVER-NAME with PARAMS using stdio transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :name tool-name :arguments params)))

    ;; Create the tool call request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request
                    "callTool" request-params request-id)))

      ;; Send the request
      (ai-auto-complete-mcp-stdio-send-message
       server-name request
       (lambda (response)
         ;; Process the tool call response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; Read a resource using stdio transport
(defun ai-auto-complete-mcp-stdio-read-resource (server-name resource-uri callback)
  "Read RESOURCE-URI from SERVER-NAME using stdio transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :uri resource-uri)))

    ;; Create the resource read request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request
                    "readResource" request-params request-id)))

      ;; Send the request
      (ai-auto-complete-mcp-stdio-send-message
       server-name request
       (lambda (response)
         ;; Process the resource read response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; Get a prompt using stdio transport
(defun ai-auto-complete-mcp-stdio-get-prompt (server-name prompt-name params callback)
  "Get PROMPT-NAME from SERVER-NAME with PARAMS using stdio transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :name prompt-name :arguments params)))

    ;; Create the prompt get request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request
                    "getPrompt" request-params request-id)))

      ;; Send the request
      (ai-auto-complete-mcp-stdio-send-message
       server-name request
       (lambda (response)
         ;; Process the prompt get response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; List tools using stdio transport
(defun ai-auto-complete-mcp-stdio-list-tools (server-name callback)
  "List tools from SERVER-NAME using stdio transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list)))

    ;; Create the list tools request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request
                    "listTools" request-params request-id)))

      ;; Send the request
      (ai-auto-complete-mcp-stdio-send-message
       server-name request
       (lambda (response)
         ;; Process the list tools response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; List resources using stdio transport
(defun ai-auto-complete-mcp-stdio-list-resources (server-name callback)
  "List resources from SERVER-NAME using stdio transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list)))

    ;; Create the list resources request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request
                    "listResources" request-params request-id)))

      ;; Send the request
      (ai-auto-complete-mcp-stdio-send-message
       server-name request
       (lambda (response)
         ;; Process the list resources response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; List prompts using stdio transport
(defun ai-auto-complete-mcp-stdio-list-prompts (server-name callback)
  "List prompts from SERVER-NAME using stdio transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list)))

    ;; Create the list prompts request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request
                    "listPrompts" request-params request-id)))

      ;; Send the request
      (ai-auto-complete-mcp-stdio-send-message
       server-name request
       (lambda (response)
         ;; Process the list prompts response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

(provide 'mcp/transports/mcp-stdio)
;;; mcp-stdio.el ends here
