;;; mcp-websocket.el --- WebSocket transport for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the WebSocket transport for MCP integration in the AI Auto Complete package.
;; It handles communication with MCP servers using WebSockets.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)
(require 'websocket)

;; WebSocket connection structure
(cl-defstruct (ai-auto-complete-mcp-websocket-connection
               (:constructor ai-auto-complete-mcp-websocket-connection-create)
               (:copier nil))
  "Structure for WebSocket connection."
  url                   ; URL of the WebSocket endpoint
  websocket             ; WebSocket connection
  callback-table        ; Hash table of callbacks
  server-name           ; Name of the server
  connected-p           ; Whether the connection is established
  )

;; Start an MCP server using WebSocket transport
(defun ai-auto-complete-mcp-websocket-start-server (server-name url)
  "Start an MCP server with SERVER-NAME at URL using WebSocket transport."
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (when server
      (let ((callback-table (make-hash-table :test 'equal)))
        
        ;; Create the WebSocket connection
        (let* ((ws-url (if (string-prefix-p "ws://" url)
                          url
                        (concat "ws://" url)))
               (connection (ai-auto-complete-mcp-websocket-connection-create
                           :url ws-url
                           :callback-table callback-table
                           :server-name server-name
                           :connected-p nil))
               (websocket (websocket-open
                          ws-url
                          :on-message (lambda (_websocket frame)
                                       (ai-auto-complete-mcp-websocket-on-message connection frame))
                          :on-close (lambda (_websocket)
                                     (ai-auto-complete-mcp-websocket-on-close connection))
                          :on-error (lambda (_websocket error)
                                     (ai-auto-complete-mcp-websocket-on-error connection error))
                          :on-open (lambda (_websocket)
                                    (ai-auto-complete-mcp-websocket-on-open connection)))))
          
          ;; Set the websocket in the connection
          (setf (ai-auto-complete-mcp-websocket-connection-websocket connection) websocket)
          
          ;; Update server status and connection
          (ai-auto-complete-mcp-update-server-status server-name 'running)
          (ai-auto-complete-mcp-update-server-connection server-name connection)
          
          ;; Initialize the server
          (ai-auto-complete-mcp-websocket-initialize-server server-name)
          
          (when ai-auto-complete-mcp-debug-mode
            (message "MCP WebSocket: Started server %s at %s" server-name url))
          
          t)))))

;; Stop an MCP server using WebSocket transport
(defun ai-auto-complete-mcp-websocket-stop-server (server-name)
  "Stop an MCP server with SERVER-NAME using WebSocket transport."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (connection (plist-get server :connection)))
    (when (and server connection)
      ;; Close the connection
      (when (ai-auto-complete-mcp-websocket-connection-websocket connection)
        (websocket-close (ai-auto-complete-mcp-websocket-connection-websocket connection)))
      
      ;; Update server status and connection
      (ai-auto-complete-mcp-update-server-status server-name 'stopped)
      (ai-auto-complete-mcp-update-server-connection server-name nil)
      
      (when ai-auto-complete-mcp-debug-mode
        (message "MCP WebSocket: Stopped server %s" server-name))
      
      t)))

;; WebSocket message handler
(defun ai-auto-complete-mcp-websocket-on-message (connection frame)
  "Handle a WebSocket message for CONNECTION from FRAME."
  (let ((server-name (ai-auto-complete-mcp-websocket-connection-server-name connection))
        (data (websocket-frame-text frame)))
    
    (when ai-auto-complete-mcp-debug-mode
      (message "MCP WebSocket: Received message from server %s: %s" 
               server-name 
               (substring data 0 (min 100 (length data)))))
    
    ;; Parse the JSON message
    (let ((json-message (ai-auto-complete-mcp-parse-jsonrpc-response data)))
      (when json-message
        (if (plist-member json-message :id)
            ;; This is a response to a request
            (let* ((id (plist-get json-message :id))
                   (callback-table (ai-auto-complete-mcp-websocket-connection-callback-table connection))
                   (callback (gethash id callback-table)))
              (when callback
                (when ai-auto-complete-mcp-debug-mode
                  (message "MCP WebSocket: Processing response for request %s" id))
                (remhash id callback-table)
                (funcall callback json-message)))
          
          ;; This is a notification
          (let ((method (plist-get json-message :method)))
            (when ai-auto-complete-mcp-debug-mode
              (message "MCP WebSocket: Received notification %s from server %s" 
                       method server-name))
            
            ;; Process the notification based on its method
            (cond
             ;; Resource changed notification
             ((string= method "resourceChanged")
              (let* ((params (plist-get json-message :params))
                     (uri (plist-get params :uri))
                     (subscription-id (plist-get params :subscriptionId))
                     (value (plist-get params :value)))
                (when ai-auto-complete-mcp-debug-mode
                  (message "MCP WebSocket: Resource changed: %s" uri))
                
                ;; Process the resource change
                (when (fboundp 'ai-auto-complete-mcp-process-resource-change)
                  (ai-auto-complete-mcp-process-resource-change
                   server-name uri subscription-id value))))
             
             ;; Tools changed notification
             ((string= method "toolsChanged")
              (when ai-auto-complete-mcp-debug-mode
                (message "MCP WebSocket: Tools changed")))
             
             ;; Prompts changed notification
             ((string= method "promptsChanged")
              (when ai-auto-complete-mcp-debug-mode
                (message "MCP WebSocket: Prompts changed")))
             
             ;; Other notifications
             (t
              (when ai-auto-complete-mcp-debug-mode
                (message "MCP WebSocket: Received unknown notification: %s" method))))))))))

;; WebSocket close handler
(defun ai-auto-complete-mcp-websocket-on-close (connection)
  "Handle a WebSocket close event for CONNECTION."
  (let ((server-name (ai-auto-complete-mcp-websocket-connection-server-name connection)))
    (when ai-auto-complete-mcp-debug-mode
      (message "MCP WebSocket: Connection closed for server %s" server-name))
    
    ;; Update server status
    (ai-auto-complete-mcp-update-server-status server-name 'stopped)
    (ai-auto-complete-mcp-update-server-connection server-name nil)))

;; WebSocket error handler
(defun ai-auto-complete-mcp-websocket-on-error (connection error)
  "Handle a WebSocket error event for CONNECTION with ERROR."
  (let ((server-name (ai-auto-complete-mcp-websocket-connection-server-name connection)))
    (when ai-auto-complete-mcp-debug-mode
      (message "MCP WebSocket: Error for server %s: %s" server-name error))
    
    ;; Log the error
    (when (fboundp 'ai-auto-complete-mcp-handle-transport-error)
      (ai-auto-complete-mcp-handle-transport-error
       server-name
       (format "WebSocket error: %s" error)))))

;; WebSocket open handler
(defun ai-auto-complete-mcp-websocket-on-open (connection)
  "Handle a WebSocket open event for CONNECTION."
  (let ((server-name (ai-auto-complete-mcp-websocket-connection-server-name connection)))
    (when ai-auto-complete-mcp-debug-mode
      (message "MCP WebSocket: Connection opened for server %s" server-name))
    
    ;; Mark the connection as established
    (setf (ai-auto-complete-mcp-websocket-connection-connected-p connection) t)))

;; Initialize an MCP server using WebSocket
(defun ai-auto-complete-mcp-websocket-initialize-server (server-name)
  "Initialize the MCP server with SERVER-NAME using WebSocket."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (params (list :serverName server-name
                      :serverVersion "1.0.0"
                      :capabilities (list :tools (list :listChanged t)
                                         :resources (list :listChanged t :subscribe t)
                                         :prompts (list :listChanged t)))))
    
    ;; Create the initialization request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "initialize" params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-websocket-send-message
       server-name request
       (lambda (response)
         ;; Process the initialization response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (progn
                 (message "MCP WebSocket: Error initializing server %s: %s" 
                          server-name (plist-get error :message))
                 (ai-auto-complete-mcp-stop-server server-name))
             
             ;; Update server capabilities
             (when result
               (ai-auto-complete-mcp-update-server-capabilities server-name result)
               (when ai-auto-complete-mcp-debug-mode
                 (message "MCP WebSocket: Server %s initialized with capabilities: %s" 
                          server-name result))))))))))

;; Send a message to an MCP server using WebSocket
(defun ai-auto-complete-mcp-websocket-send-message (server-name message &optional callback)
  "Send MESSAGE to MCP server with SERVER-NAME using WebSocket.
If CALLBACK is provided, it will be called with the response."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (connection (plist-get server :connection)))
    (if (and server connection)
        (let ((json-message (ai-auto-complete-mcp-parse-jsonrpc-response message))
              (websocket (ai-auto-complete-mcp-websocket-connection-websocket connection)))
          (when json-message
            ;; If this is a request with an ID and a callback is provided
            (when (and (plist-member json-message :id) callback)
              (let ((id (plist-get json-message :id))
                    (callback-table (ai-auto-complete-mcp-websocket-connection-callback-table connection)))
                (puthash id callback callback-table)))
            
            ;; Send the message
            (websocket-send-text websocket message)
            
            (when ai-auto-complete-mcp-debug-mode
              (message "MCP WebSocket: Sent message to server %s: %s" 
                       server-name 
                       (substring message 0 (min 100 (length message)))))
            
            t))
      (progn
        (message "MCP WebSocket: Server %s not running" server-name)
        nil))))

;; Call a tool using WebSocket transport
(defun ai-auto-complete-mcp-websocket-call-tool (server-name tool-name params callback)
  "Call TOOL-NAME on SERVER-NAME with PARAMS using WebSocket transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :name tool-name :arguments params)))
    
    ;; Create the tool call request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "callTool" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-websocket-send-message
       server-name request
       (lambda (response)
         ;; Process the tool call response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; Read a resource using WebSocket transport
(defun ai-auto-complete-mcp-websocket-read-resource (server-name resource-uri callback)
  "Read RESOURCE-URI from SERVER-NAME using WebSocket transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :uri resource-uri)))
    
    ;; Create the resource read request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "readResource" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-websocket-send-message
       server-name request
       (lambda (response)
         ;; Process the resource read response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; Get a prompt using WebSocket transport
(defun ai-auto-complete-mcp-websocket-get-prompt (server-name prompt-name params callback)
  "Get PROMPT-NAME from SERVER-NAME with PARAMS using WebSocket transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :name prompt-name :arguments params)))
    
    ;; Create the prompt get request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "getPrompt" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-websocket-send-message
       server-name request
       (lambda (response)
         ;; Process the prompt get response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; List tools using WebSocket transport
(defun ai-auto-complete-mcp-websocket-list-tools (server-name callback)
  "List tools from SERVER-NAME using WebSocket transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list)))
    
    ;; Create the list tools request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "listTools" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-websocket-send-message
       server-name request
       (lambda (response)
         ;; Process the list tools response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; List resources using WebSocket transport
(defun ai-auto-complete-mcp-websocket-list-resources (server-name callback)
  "List resources from SERVER-NAME using WebSocket transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list)))
    
    ;; Create the list resources request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "listResources" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-websocket-send-message
       server-name request
       (lambda (response)
         ;; Process the list resources response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; List prompts using WebSocket transport
(defun ai-auto-complete-mcp-websocket-list-prompts (server-name callback)
  "List prompts from SERVER-NAME using WebSocket transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list)))
    
    ;; Create the list prompts request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "listPrompts" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-websocket-send-message
       server-name request
       (lambda (response)
         ;; Process the list prompts response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

(provide 'mcp/transports/mcp-websocket)
;;; mcp-websocket.el ends here
