;;; mcp-grpc.el --- gRPC transport for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the gRPC transport for MCP integration in the AI Auto Complete package.
;; It handles communication with MCP servers using gRPC.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)

;; gRPC connection structure
(cl-defstruct (ai-auto-complete-mcp-grpc-connection
               (:constructor ai-auto-complete-mcp-grpc-connection-create)
               (:copier nil))
  "Structure for gRPC connection."
  url                   ; URL of the gRPC endpoint
  process               ; Process for the gRPC client
  buffer                ; Buffer for the gRPC client
  callback-table        ; Hash table of callbacks
  server-name           ; Name of the server
  connected-p           ; Whether the connection is established
  )

;; Start an MCP server using gRPC transport
(defun ai-auto-complete-mcp-grpc-start-server (server-name url)
  "Start an MCP server with SERVER-NAME at URL using gRPC transport."
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (when server
      (let ((callback-table (make-hash-table :test 'equal))
            (buffer-name (format "*MCP-grpc-%s*" server-name)))
        
        ;; Create a buffer for the connection
        (with-current-buffer (get-buffer-create buffer-name)
          (erase-buffer))
        
        ;; Create the gRPC connection
        (let ((connection (ai-auto-complete-mcp-grpc-connection-create
                          :url url
                          :buffer (get-buffer buffer-name)
                          :callback-table callback-table
                          :server-name server-name
                          :connected-p nil)))
          
          ;; Start the gRPC client process
          (let* ((process-name (format "mcp-grpc-%s" server-name))
                 (grpc-client-path (executable-find "grpc_cli"))
                 (process (if grpc-client-path
                             (start-process
                              process-name buffer-name
                              grpc-client-path "ls" url)
                           (progn
                             (message "gRPC CLI not found. Please install grpc_cli.")
                             nil))))
            
            (if process
                (progn
                  ;; Set up the process
                  (set-process-filter process
                                     (lambda (proc output)
                                       (ai-auto-complete-mcp-grpc-process-filter connection proc output)))
                  (set-process-sentinel process
                                       (lambda (proc event)
                                         (ai-auto-complete-mcp-grpc-process-sentinel connection proc event)))
                  
                  ;; Set the process in the connection
                  (setf (ai-auto-complete-mcp-grpc-connection-process connection) process)
                  
                  ;; Update server status and connection
                  (ai-auto-complete-mcp-update-server-status server-name 'running)
                  (ai-auto-complete-mcp-update-server-connection server-name connection)
                  
                  ;; Initialize the server
                  (ai-auto-complete-mcp-grpc-initialize-server server-name)
                  
                  (when ai-auto-complete-mcp-debug-mode
                    (message "MCP gRPC: Started server %s at %s" server-name url))
                  
                  t)
              (progn
                (message "Failed to start gRPC client for server %s" server-name)
                nil))))))))

;; Stop an MCP server using gRPC transport
(defun ai-auto-complete-mcp-grpc-stop-server (server-name)
  "Stop an MCP server with SERVER-NAME using gRPC transport."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (connection (plist-get server :connection)))
    (when (and server connection)
      ;; Kill the process
      (when (ai-auto-complete-mcp-grpc-connection-process connection)
        (delete-process (ai-auto-complete-mcp-grpc-connection-process connection)))
      
      ;; Update server status and connection
      (ai-auto-complete-mcp-update-server-status server-name 'stopped)
      (ai-auto-complete-mcp-update-server-connection server-name nil)
      
      (when ai-auto-complete-mcp-debug-mode
        (message "MCP gRPC: Stopped server %s" server-name))
      
      t)))

;; Process filter for gRPC client
(defun ai-auto-complete-mcp-grpc-process-filter (connection process output)
  "Process filter for gRPC client.
CONNECTION is the gRPC connection, PROCESS is the gRPC client process,
OUTPUT is the output from the process."
  (let ((server-name (ai-auto-complete-mcp-grpc-connection-server-name connection))
        (buffer (ai-auto-complete-mcp-grpc-connection-buffer connection)))
    
    (when ai-auto-complete-mcp-debug-mode
      (message "MCP gRPC: Received output from server %s: %s" 
               server-name 
               (substring output 0 (min 100 (length output)))))
    
    ;; Append the output to the buffer
    (with-current-buffer buffer
      (goto-char (point-max))
      (insert output))
    
    ;; Process complete messages
    (ai-auto-complete-mcp-grpc-process-messages connection)))

;; Process sentinel for gRPC client
(defun ai-auto-complete-mcp-grpc-process-sentinel (connection process event)
  "Process sentinel for gRPC client.
CONNECTION is the gRPC connection, PROCESS is the gRPC client process,
EVENT is the event from the process."
  (let ((server-name (ai-auto-complete-mcp-grpc-connection-server-name connection)))
    (when ai-auto-complete-mcp-debug-mode
      (message "MCP gRPC: Process event for server %s: %s" server-name event))
    
    ;; Check if the process has exited
    (when (string-match "\\(finished\\|exited\\|killed\\)" event)
      ;; Update server status
      (ai-auto-complete-mcp-update-server-status server-name 'stopped)
      (ai-auto-complete-mcp-update-server-connection server-name nil)
      
      (when ai-auto-complete-mcp-debug-mode
        (message "MCP gRPC: Server %s stopped" server-name)))))

;; Process messages from the gRPC client
(defun ai-auto-complete-mcp-grpc-process-messages (connection)
  "Process messages from the gRPC client for CONNECTION."
  (let ((server-name (ai-auto-complete-mcp-grpc-connection-server-name connection))
        (buffer (ai-auto-complete-mcp-grpc-connection-buffer connection)))
    
    (with-current-buffer buffer
      (goto-char (point-min))
      
      ;; Look for complete messages
      (while (re-search-forward "^\\([0-9a-f]+\\)\n" nil t)
        (let ((message-id (match-string 1)))
          (when (re-search-forward "^\\([0-9a-f]+\\)\n" nil t)
            (let ((message-length (string-to-number (match-string 1) 16)))
              (when (>= (- (point-max) (point)) message-length)
                ;; We have a complete message
                (let ((message-start (point))
                      (message-end (+ (point) message-length)))
                  (let ((message (buffer-substring message-start message-end)))
                    ;; Process the message
                    (ai-auto-complete-mcp-grpc-process-message connection message-id message)
                    
                    ;; Delete the processed message
                    (delete-region (point-min) message-end)))))))))))

;; Process a message from the gRPC client
(defun ai-auto-complete-mcp-grpc-process-message (connection message-id message)
  "Process a message from the gRPC client.
CONNECTION is the gRPC connection, MESSAGE-ID is the ID of the message,
MESSAGE is the message content."
  (let ((server-name (ai-auto-complete-mcp-grpc-connection-server-name connection))
        (callback-table (ai-auto-complete-mcp-grpc-connection-callback-table connection)))
    
    (when ai-auto-complete-mcp-debug-mode
      (message "MCP gRPC: Processing message %s from server %s" message-id server-name))
    
    ;; Parse the message as JSON
    (let ((json-message (ai-auto-complete-mcp-parse-jsonrpc-response message)))
      (when json-message
        (if (plist-member json-message :id)
            ;; This is a response to a request
            (let* ((id (plist-get json-message :id))
                   (callback (gethash id callback-table)))
              (when callback
                (when ai-auto-complete-mcp-debug-mode
                  (message "MCP gRPC: Processing response for request %s" id))
                (remhash id callback-table)
                (funcall callback json-message)))
          
          ;; This is a notification
          (let ((method (plist-get json-message :method)))
            (when ai-auto-complete-mcp-debug-mode
              (message "MCP gRPC: Received notification %s from server %s" 
                       method server-name))
            
            ;; Process the notification based on its method
            (cond
             ;; Resource changed notification
             ((string= method "resourceChanged")
              (let* ((params (plist-get json-message :params))
                     (uri (plist-get params :uri))
                     (subscription-id (plist-get params :subscriptionId))
                     (value (plist-get params :value)))
                (when ai-auto-complete-mcp-debug-mode
                  (message "MCP gRPC: Resource changed: %s" uri))
                
                ;; Process the resource change
                (when (fboundp 'ai-auto-complete-mcp-process-resource-change)
                  (ai-auto-complete-mcp-process-resource-change
                   server-name uri subscription-id value))))
             
             ;; Tools changed notification
             ((string= method "toolsChanged")
              (when ai-auto-complete-mcp-debug-mode
                (message "MCP gRPC: Tools changed")))
             
             ;; Prompts changed notification
             ((string= method "promptsChanged")
              (when ai-auto-complete-mcp-debug-mode
                (message "MCP gRPC: Prompts changed")))
             
             ;; Other notifications
             (t
              (when ai-auto-complete-mcp-debug-mode
                (message "MCP gRPC: Received unknown notification: %s" method))))))))))

;; Initialize an MCP server using gRPC
(defun ai-auto-complete-mcp-grpc-initialize-server (server-name)
  "Initialize the MCP server with SERVER-NAME using gRPC."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (params (list :serverName server-name
                      :serverVersion "1.0.0"
                      :capabilities (list :tools (list :listChanged t)
                                         :resources (list :listChanged t :subscribe t)
                                         :prompts (list :listChanged t)))))
    
    ;; Create the initialization request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "initialize" params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-grpc-send-message
       server-name request
       (lambda (response)
         ;; Process the initialization response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (progn
                 (message "MCP gRPC: Error initializing server %s: %s" 
                          server-name (plist-get error :message))
                 (ai-auto-complete-mcp-stop-server server-name))
             
             ;; Update server capabilities
             (when result
               (ai-auto-complete-mcp-update-server-capabilities server-name result)
               (when ai-auto-complete-mcp-debug-mode
                 (message "MCP gRPC: Server %s initialized with capabilities: %s" 
                          server-name result))))))))))

;; Send a message to an MCP server using gRPC
(defun ai-auto-complete-mcp-grpc-send-message (server-name message &optional callback)
  "Send MESSAGE to MCP server with SERVER-NAME using gRPC.
If CALLBACK is provided, it will be called with the response."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (connection (plist-get server :connection)))
    (if (and server connection)
        (let ((json-message (ai-auto-complete-mcp-parse-jsonrpc-response message))
              (process (ai-auto-complete-mcp-grpc-connection-process connection)))
          (when json-message
            ;; If this is a request with an ID and a callback is provided
            (when (and (plist-member json-message :id) callback)
              (let ((id (plist-get json-message :id))
                    (callback-table (ai-auto-complete-mcp-grpc-connection-callback-table connection)))
                (puthash id callback callback-table)))
            
            ;; Send the message
            (let ((message-with-header (format "%s\n%x\n%s"
                                              (ai-auto-complete-mcp-generate-request-id)
                                              (length message)
                                              message)))
              (process-send-string process message-with-header)
              
              (when ai-auto-complete-mcp-debug-mode
                (message "MCP gRPC: Sent message to server %s: %s" 
                         server-name 
                         (substring message 0 (min 100 (length message)))))
              
              t)))
      (progn
        (message "MCP gRPC: Server %s not running" server-name)
        nil))))

;; Call a tool using gRPC transport
(defun ai-auto-complete-mcp-grpc-call-tool (server-name tool-name params callback)
  "Call TOOL-NAME on SERVER-NAME with PARAMS using gRPC transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :name tool-name :arguments params)))
    
    ;; Create the tool call request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "callTool" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-grpc-send-message
       server-name request
       (lambda (response)
         ;; Process the tool call response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; Read a resource using gRPC transport
(defun ai-auto-complete-mcp-grpc-read-resource (server-name resource-uri callback)
  "Read RESOURCE-URI from SERVER-NAME using gRPC transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :uri resource-uri)))
    
    ;; Create the resource read request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "readResource" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-grpc-send-message
       server-name request
       (lambda (response)
         ;; Process the resource read response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; Get a prompt using gRPC transport
(defun ai-auto-complete-mcp-grpc-get-prompt (server-name prompt-name params callback)
  "Get PROMPT-NAME from SERVER-NAME with PARAMS using gRPC transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :name prompt-name :arguments params)))
    
    ;; Create the prompt get request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "getPrompt" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-grpc-send-message
       server-name request
       (lambda (response)
         ;; Process the prompt get response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; List tools using gRPC transport
(defun ai-auto-complete-mcp-grpc-list-tools (server-name callback)
  "List tools from SERVER-NAME using gRPC transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list)))
    
    ;; Create the list tools request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "listTools" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-grpc-send-message
       server-name request
       (lambda (response)
         ;; Process the list tools response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; List resources using gRPC transport
(defun ai-auto-complete-mcp-grpc-list-resources (server-name callback)
  "List resources from SERVER-NAME using gRPC transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list)))
    
    ;; Create the list resources request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "listResources" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-grpc-send-message
       server-name request
       (lambda (response)
         ;; Process the list resources response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; List prompts using gRPC transport
(defun ai-auto-complete-mcp-grpc-list-prompts (server-name callback)
  "List prompts from SERVER-NAME using gRPC transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list)))
    
    ;; Create the list prompts request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "listPrompts" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-grpc-send-message
       server-name request
       (lambda (response)
         ;; Process the list prompts response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

(provide 'mcp/transports/mcp-grpc)
;;; mcp-grpc.el ends here
