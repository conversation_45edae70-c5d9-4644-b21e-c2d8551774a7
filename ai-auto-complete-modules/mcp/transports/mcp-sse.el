;;; mcp-sse.el --- SSE transport for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the SSE (Server-Sent Events) transport for MCP integration in the AI Auto Complete package.
;; It handles communication with MCP servers using HTTP and SSE.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)
(require 'url)
(require 'url-http)

;; SSE connection structure
(cl-defstruct (ai-auto-complete-mcp-sse-connection
               (:constructor ai-auto-complete-mcp-sse-connection-create)
               (:copier nil))
  "Structure for SSE connection."
  url                   ; URL of the SSE endpoint
  buffer                ; Buffer for the connection
  process               ; Process for the connection
  callback-table        ; Hash table of callbacks
  event-buffer          ; Buffer for accumulating SSE events
  last-event-id         ; Last event ID received
  retry-time            ; Retry time in milliseconds
  server-name           ; Name of the server
  connected-p           ; Whether the connection is established
  )

;; Start an MCP server using SSE transport
(defun ai-auto-complete-mcp-sse-start-server (server-name url)
  "Start an MCP server with SERVER-NAME at URL using SSE transport."
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (when server
      (let ((callback-table (make-hash-table :test 'equal))
            (buffer-name (format "*MCP-sse-%s*" server-name)))
        
        ;; Create a buffer for the connection
        (with-current-buffer (get-buffer-create buffer-name)
          (erase-buffer))
        
        ;; Create the SSE connection
        (let ((connection (ai-auto-complete-mcp-sse-connection-create
                          :url url
                          :buffer (get-buffer buffer-name)
                          :callback-table callback-table
                          :event-buffer ""
                          :server-name server-name
                          :connected-p nil)))
          
          ;; Update server status and connection
          (ai-auto-complete-mcp-update-server-status server-name 'running)
          (ai-auto-complete-mcp-update-server-connection server-name connection)
          
          ;; Initialize the server
          (ai-auto-complete-mcp-sse-initialize-server server-name)
          
          (when ai-auto-complete-mcp-debug-mode
            (message "MCP SSE: Started server %s at %s" server-name url))
          
          t)))))

;; Stop an MCP server using SSE transport
(defun ai-auto-complete-mcp-sse-stop-server (server-name)
  "Stop an MCP server with SERVER-NAME using SSE transport."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (connection (plist-get server :connection)))
    (when (and server connection)
      ;; Close the connection
      (when (ai-auto-complete-mcp-sse-connection-process connection)
        (delete-process (ai-auto-complete-mcp-sse-connection-process connection)))
      
      ;; Update server status and connection
      (ai-auto-complete-mcp-update-server-status server-name 'stopped)
      (ai-auto-complete-mcp-update-server-connection server-name nil)
      
      (when ai-auto-complete-mcp-debug-mode
        (message "MCP SSE: Stopped server %s" server-name))
      
      t)))

;; Initialize an MCP server using SSE
(defun ai-auto-complete-mcp-sse-initialize-server (server-name)
  "Initialize the MCP server with SERVER-NAME using SSE."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (params (list :serverName server-name
                      :serverVersion "1.0.0"
                      :capabilities (list :tools (list :listChanged t)
                                         :resources (list :listChanged t :subscribe t)
                                         :prompts (list :listChanged t)))))
    
    ;; Create the initialization request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "initialize" params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-sse-send-message
       server-name request
       (lambda (response)
         ;; Process the initialization response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (progn
                 (message "MCP SSE: Error initializing server %s: %s" 
                          server-name (plist-get error :message))
                 (ai-auto-complete-mcp-stop-server server-name))
             
             ;; Update server capabilities
             (when result
               (ai-auto-complete-mcp-update-server-capabilities server-name result)
               (when ai-auto-complete-mcp-debug-mode
                 (message "MCP SSE: Server %s initialized with capabilities: %s" 
                          server-name result))))))))))

;; Send a message to an MCP server using SSE
(defun ai-auto-complete-mcp-sse-send-message (server-name message &optional callback)
  "Send MESSAGE to MCP server with SERVER-NAME using SSE.
If CALLBACK is provided, it will be called with the response."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (connection (plist-get server :connection)))
    (if (and server connection)
        (let ((json-message (ai-auto-complete-mcp-parse-jsonrpc-response message))
              (url (ai-auto-complete-mcp-sse-connection-url connection)))
          (when json-message
            ;; If this is a request with an ID and a callback is provided
            (when (and (plist-member json-message :id) callback)
              (let ((id (plist-get json-message :id))
                    (callback-table (ai-auto-complete-mcp-sse-connection-callback-table connection)))
                (puthash id callback callback-table)))
            
            ;; Send the message using HTTP POST
            (let ((url-request-method "POST")
                  (url-request-extra-headers
                   '(("Content-Type" . "application/json")))
                  (url-request-data message))
              
              (url-retrieve
               url
               (lambda (status)
                 ;; Process the response
                 (if (plist-get status :error)
                     (let ((error-details (plist-get status :error)))
                       (message "MCP SSE: Error sending message to %s: %s" 
                                server-name error-details))
                   
                   ;; Parse the response
                   (goto-char (point-min))
                   (when (re-search-forward "^$" nil t)
                     (let ((response (buffer-substring (1+ (point)) (point-max))))
                       ;; Parse the JSON response
                       (let ((json-response (ai-auto-complete-mcp-parse-jsonrpc-response response)))
                         (when json-response
                           ;; If this is a response to a request with an ID
                           (when (plist-member json-response :id)
                             (let ((id (plist-get json-response :id))
                                   (callback-table (ai-auto-complete-mcp-sse-connection-callback-table connection)))
                               (let ((callback (gethash id callback-table)))
                                 (when callback
                                   (remhash id callback-table)
                                   (funcall callback json-response))))))))))
                 
                 ;; Kill the buffer
                 (kill-buffer (current-buffer))))
              
              (when ai-auto-complete-mcp-debug-mode
                (message "MCP SSE: Sent message to server %s: %s" 
                         server-name 
                         (substring message 0 (min 100 (length message)))))
              
              t)))
      (progn
        (message "MCP SSE: Server %s not running" server-name)
        nil))))

;; Process SSE events
(defun ai-auto-complete-mcp-sse-process-events (connection data)
  "Process SSE events in DATA for CONNECTION."
  (let ((event-buffer (concat (ai-auto-complete-mcp-sse-connection-event-buffer connection) data))
        (events '()))
    
    ;; Update the event buffer
    (setf (ai-auto-complete-mcp-sse-connection-event-buffer connection) event-buffer)
    
    ;; Process complete events
    (while (string-match "\\(.*?\\)\n\n" event-buffer)
      (let ((event (match-string 1 event-buffer)))
        ;; Remove the event from the buffer
        (setq event-buffer (substring event-buffer (match-end 0)))
        ;; Add the event to the list
        (push event events)))
    
    ;; Update the event buffer
    (setf (ai-auto-complete-mcp-sse-connection-event-buffer connection) event-buffer)
    
    ;; Process the events
    (dolist (event (nreverse events))
      (ai-auto-complete-mcp-sse-process-event connection event))))

;; Process a single SSE event
(defun ai-auto-complete-mcp-sse-process-event (connection event)
  "Process a single SSE EVENT for CONNECTION."
  (let ((lines (split-string event "\n"))
        (event-type "message")
        (event-id nil)
        (event-data ""))
    
    ;; Parse the event
    (dolist (line lines)
      (cond
       ;; Event type
       ((string-match "^event:\\s-*\\(.*\\)" line)
        (setq event-type (match-string 1 line)))
       
       ;; Event ID
       ((string-match "^id:\\s-*\\(.*\\)" line)
        (setq event-id (match-string 1 line)))
       
       ;; Event data
       ((string-match "^data:\\s-*\\(.*\\)" line)
        (setq event-data (concat event-data (match-string 1 line) "\n")))
       
       ;; Retry time
       ((string-match "^retry:\\s-*\\(.*\\)" line)
        (let ((retry-time (string-to-number (match-string 1 line))))
          (setf (ai-auto-complete-mcp-sse-connection-retry-time connection) retry-time)))))
    
    ;; Update the last event ID
    (when event-id
      (setf (ai-auto-complete-mcp-sse-connection-last-event-id connection) event-id))
    
    ;; Process the event based on its type
    (cond
     ;; Message event
     ((string= event-type "message")
      (when (not (string-empty-p event-data))
        ;; Parse the JSON message
        (let ((json-message (ai-auto-complete-mcp-parse-jsonrpc-response event-data)))
          (when json-message
            ;; If this is a notification
            (when (and (plist-member json-message :method)
                       (not (plist-member json-message :id)))
              (ai-auto-complete-mcp-sse-process-notification connection json-message))))))
     
     ;; Other event types
     (t
      (when ai-auto-complete-mcp-debug-mode
        (message "MCP SSE: Received event of type %s: %s" 
                 event-type event-data))))))

;; Process an SSE notification
(defun ai-auto-complete-mcp-sse-process-notification (connection notification)
  "Process an SSE NOTIFICATION for CONNECTION."
  (let ((method (plist-get notification :method))
        (params (plist-get notification :params))
        (server-name (ai-auto-complete-mcp-sse-connection-server-name connection)))
    
    (when ai-auto-complete-mcp-debug-mode
      (message "MCP SSE: Received notification %s from server %s" 
               method server-name))
    
    ;; Process the notification based on its method
    (cond
     ;; Resource changed notification
     ((string= method "resourceChanged")
      (let ((uri (plist-get params :uri)))
        (when ai-auto-complete-mcp-debug-mode
          (message "MCP SSE: Resource changed: %s" uri))))
     
     ;; Tools changed notification
     ((string= method "toolsChanged")
      (when ai-auto-complete-mcp-debug-mode
        (message "MCP SSE: Tools changed")))
     
     ;; Prompts changed notification
     ((string= method "promptsChanged")
      (when ai-auto-complete-mcp-debug-mode
        (message "MCP SSE: Prompts changed")))
     
     ;; Other notifications
     (t
      (when ai-auto-complete-mcp-debug-mode
        (message "MCP SSE: Received unknown notification: %s" method))))))

;; Call a tool using SSE transport
(defun ai-auto-complete-mcp-sse-call-tool (server-name tool-name params callback)
  "Call TOOL-NAME on SERVER-NAME with PARAMS using SSE transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :name tool-name :arguments params)))
    
    ;; Create the tool call request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "callTool" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-sse-send-message
       server-name request
       (lambda (response)
         ;; Process the tool call response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; Read a resource using SSE transport
(defun ai-auto-complete-mcp-sse-read-resource (server-name resource-uri callback)
  "Read RESOURCE-URI from SERVER-NAME using SSE transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :uri resource-uri)))
    
    ;; Create the resource read request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "readResource" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-sse-send-message
       server-name request
       (lambda (response)
         ;; Process the resource read response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; Get a prompt using SSE transport
(defun ai-auto-complete-mcp-sse-get-prompt (server-name prompt-name params callback)
  "Get PROMPT-NAME from SERVER-NAME with PARAMS using SSE transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :name prompt-name :arguments params)))
    
    ;; Create the prompt get request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "getPrompt" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-sse-send-message
       server-name request
       (lambda (response)
         ;; Process the prompt get response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; List tools using SSE transport
(defun ai-auto-complete-mcp-sse-list-tools (server-name callback)
  "List tools from SERVER-NAME using SSE transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list)))
    
    ;; Create the list tools request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "listTools" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-sse-send-message
       server-name request
       (lambda (response)
         ;; Process the list tools response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; List resources using SSE transport
(defun ai-auto-complete-mcp-sse-list-resources (server-name callback)
  "List resources from SERVER-NAME using SSE transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list)))
    
    ;; Create the list resources request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "listResources" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-sse-send-message
       server-name request
       (lambda (response)
         ;; Process the list resources response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

;; List prompts using SSE transport
(defun ai-auto-complete-mcp-sse-list-prompts (server-name callback)
  "List prompts from SERVER-NAME using SSE transport.
CALLBACK will be called with the result."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list)))
    
    ;; Create the list prompts request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "listPrompts" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-sse-send-message
       server-name request
       (lambda (response)
         ;; Process the list prompts response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (funcall callback (format "Error: %s" (plist-get error :message)))
             (funcall callback result))))))))

(provide 'mcp/transports/mcp-sse)
;;; mcp-sse.el ends here
