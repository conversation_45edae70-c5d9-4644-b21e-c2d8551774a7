;;; mcp-stdio-loader.el --- Stdio transport loader for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a unified loader for stdio transport modules in the MCP integration.
;; It ensures that at least one stdio transport is available and properly loaded.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-python-check)

;; Forward declarations to avoid circular dependencies
(declare-function ai-auto-complete-mcp-stdio-improved-start-server "mcp/transports/mcp-stdio-improved")
(declare-function ai-auto-complete-mcp-stdio-simple-start-server "mcp/transports/mcp-stdio-simple")
(declare-function ai-auto-complete-mcp-stdio-start-server "mcp/transports/mcp-stdio")

;; Track which stdio transport is active
(defvar ai-auto-complete-mcp-stdio-active-transport nil
  "The currently active stdio transport for MCP.")

;; Load the best available stdio transport
(defun ai-auto-complete-mcp-stdio-load-transport ()
  "Load the best available stdio transport for MCP.
Returns the name of the loaded transport, or nil if none could be loaded."
  (interactive)
  (message "Loading MCP stdio transport...")
  
  ;; Reset the active transport
  (setq ai-auto-complete-mcp-stdio-active-transport nil)
  
  ;; Try to load the improved stdio transport
  (when (and (boundp 'ai-auto-complete-mcp-use-improved-stdio)
             ai-auto-complete-mcp-use-improved-stdio)
    (condition-case err
        (progn
          (require 'mcp/transports/mcp-stdio-improved)
          (when (fboundp 'ai-auto-complete-mcp-stdio-improved-start-server)
            (message "Loaded improved stdio transport")
            (setq ai-auto-complete-mcp-stdio-active-transport 'improved)
            (defalias 'ai-auto-complete-mcp-stdio-start-server 
              'ai-auto-complete-mcp-stdio-improved-start-server)
            (defalias 'ai-auto-complete-mcp-stdio-stop-server 
              'ai-auto-complete-mcp-stdio-improved-stop-server)
            (defalias 'ai-auto-complete-mcp-stdio-call-tool 
              'ai-auto-complete-mcp-stdio-improved-call-tool)
            (defalias 'ai-auto-complete-mcp-stdio-read-resource 
              'ai-auto-complete-mcp-stdio-improved-read-resource)
            (defalias 'ai-auto-complete-mcp-stdio-list-tools 
              'ai-auto-complete-mcp-stdio-improved-list-tools)
            (defalias 'ai-auto-complete-mcp-stdio-list-resources 
              'ai-auto-complete-mcp-stdio-improved-list-resources)))
      (error
       (message "Error loading improved stdio transport: %s" (error-message-string err)))))
  
  ;; If improved transport couldn't be loaded, try the simplified transport
  (when (and (not ai-auto-complete-mcp-stdio-active-transport)
             (boundp 'ai-auto-complete-mcp-use-simplified-stdio)
             ai-auto-complete-mcp-use-simplified-stdio)
    (condition-case err
        (progn
          (require 'mcp/transports/mcp-stdio-simple)
          (when (fboundp 'ai-auto-complete-mcp-stdio-simple-start-server)
            (message "Loaded simplified stdio transport")
            (setq ai-auto-complete-mcp-stdio-active-transport 'simple)
            (defalias 'ai-auto-complete-mcp-stdio-start-server 
              'ai-auto-complete-mcp-stdio-simple-start-server)
            (defalias 'ai-auto-complete-mcp-stdio-stop-server 
              'ai-auto-complete-mcp-stdio-simple-stop-server)
            (defalias 'ai-auto-complete-mcp-stdio-call-tool 
              'ai-auto-complete-mcp-stdio-simple-call-tool)
            (defalias 'ai-auto-complete-mcp-stdio-read-resource 
              'ai-auto-complete-mcp-stdio-simple-read-resource)
            (defalias 'ai-auto-complete-mcp-stdio-list-tools 
              'ai-auto-complete-mcp-stdio-simple-list-tools)
            (defalias 'ai-auto-complete-mcp-stdio-list-resources 
              'ai-auto-complete-mcp-stdio-simple-list-resources)))
      (error
       (message "Error loading simplified stdio transport: %s" (error-message-string err)))))
  
  ;; If neither improved nor simplified transport could be loaded, try the standard transport
  (when (not ai-auto-complete-mcp-stdio-active-transport)
    (condition-case err
        (progn
          (require 'mcp/transports/mcp-stdio)
          (when (fboundp 'ai-auto-complete-mcp-stdio-start-server)
            (message "Loaded standard stdio transport")
            (setq ai-auto-complete-mcp-stdio-active-transport 'standard)))
      (error
       (message "Error loading standard stdio transport: %s" (error-message-string err)))))
  
  ;; If no transport could be loaded, try the fixed transport
  (when (not ai-auto-complete-mcp-stdio-active-transport)
    (condition-case err
        (progn
          (require 'mcp/transports/mcp-stdio-fixed)
          (when (fboundp 'ai-auto-complete-mcp-stdio-start-server)
            (message "Loaded fixed stdio transport")
            (setq ai-auto-complete-mcp-stdio-active-transport 'fixed)))
      (error
       (message "Error loading fixed stdio transport: %s" (error-message-string err)))))
  
  ;; Return the active transport
  ai-auto-complete-mcp-stdio-active-transport)

;; Ensure stdio transport is available
(defun ai-auto-complete-mcp-stdio-ensure-transport ()
  "Ensure that a stdio transport is available for MCP.
Returns t if a transport is available, nil otherwise."
  (interactive)
  (if ai-auto-complete-mcp-stdio-active-transport
      t
    (ai-auto-complete-mcp-stdio-load-transport)))

;; Start an MCP server using the best available stdio transport
(defun ai-auto-complete-mcp-stdio-start-server-wrapper (server-name path)
  "Start an MCP server with SERVER-NAME at PATH using the best available stdio transport."
  (if (not (ai-auto-complete-mcp-stdio-ensure-transport))
      (progn
        (message "No stdio transport available")
        nil)
    (if (not (fboundp 'ai-auto-complete-mcp-stdio-start-server))
        (progn
          (message "Stdio transport function not available")
          nil)
      (ai-auto-complete-mcp-stdio-start-server server-name path))))

;; Initialize the stdio transport loader
(defun ai-auto-complete-mcp-stdio-loader-initialize ()
  "Initialize the stdio transport loader."
  (message "Initializing MCP stdio transport loader")
  
  ;; Check Python availability
  (ai-auto-complete-mcp-check-python)
  
  ;; Load the best available transport
  (ai-auto-complete-mcp-stdio-load-transport)
  
  ;; Provide wrapper functions if needed
  (unless (fboundp 'ai-auto-complete-mcp-stdio-start-server)
    (defalias 'ai-auto-complete-mcp-stdio-start-server 
      'ai-auto-complete-mcp-stdio-start-server-wrapper)))

;; Initialize the loader
(ai-auto-complete-mcp-stdio-loader-initialize)

(provide 'mcp/transports/mcp-stdio-loader)
;;; mcp-stdio-loader.el ends here
