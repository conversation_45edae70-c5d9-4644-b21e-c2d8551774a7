;; mcp-typescript-bridge.el --- MCP TypeScript Bridge Transport -*- lexical-binding: t; -*-

;;; Commentary:
;; This file provides a transport for MCP using the TypeScript Bridge.

;;; Code:

(require 'mcp/mcp-core)
(require 'json)

;; Define a variable for the Node.js executable path
(defvar ai-auto-complete-mcp-node-command "node"
  "The Node.js executable to use for MCP servers.")

(defvar ai-auto-complete-mcp-typescript-bridge-process nil
  "The TypeScript bridge process.")

(defvar ai-auto-complete-mcp-typescript-bridge-buffer "*MCP-typescript-bridge*"
  "Buffer for the TypeScript bridge process.")

;; We don't need global callback tables anymore, as we store them per-process
;; Each process has its own 'callback-table and 'current-callback properties

(defun ai-auto-complete-mcp-typescript-bridge-find-bridge-script ()
  "Find the TypeScript bridge script."
  (let* ((bridge-script-paths (list
                              ;; Try the standard path first
                              (expand-file-name "bridge/typescript-mcp-bridge/dist/index.js"
                                              (file-name-directory (locate-library "mcp/mcp-core")))
                              ;; Try the direct path in the modules directory
                              (expand-file-name "ai-auto-complete-modules/mcp/bridge/typescript-mcp-bridge/dist/index.js")
                              ;; Try relative to the current directory
                              "ai-auto-complete-modules/mcp/bridge/typescript-mcp-bridge/dist/index.js"))
         (bridge-script nil))

    ;; Find the first existing bridge script
    (dolist (path bridge-script-paths)
      (message "MCP typescript-bridge: Checking bridge script path: %s (exists: %s)"
               path (if (file-exists-p path) "yes" "no"))
      (when (and (not bridge-script) (file-exists-p path))
        (setq bridge-script path)))

    bridge-script))

(defun ai-auto-complete-mcp-typescript-bridge-process-filter (process output)
  "Process filter for the TypeScript bridge process.
PROCESS is the bridge process.
OUTPUT is the output from the process."
  (when (buffer-live-p (process-buffer process))
    (with-current-buffer (process-buffer process)
      (let ((moving (= (point) (process-mark process))))
        (save-excursion
          ;; Insert the output at the process mark
          (goto-char (process-mark process))
          (insert output)
          (set-marker (process-mark process) (point))

          ;; Log the raw output for debugging
          (when ai-auto-complete-mcp-debug-mode
            (message "MCP typescript-bridge: Raw output from bridge (length: %d)" (length output))
            (when (< (length output) 500)
              (message "MCP typescript-bridge: Raw output content: %s" output)))

          ;; Process each line
          (goto-char (point-min))
          (while (re-search-forward "^\\({.*\\)$" nil t)
            (let ((json-string (match-string 1)))
              (delete-region (line-beginning-position) (line-end-position))
              (delete-char 1) ; Delete the newline

              ;; Parse the JSON
              (condition-case err
                  (let* ((json-object-type 'plist)
                         (json-array-type 'list)
                         (json-key-type 'keyword)
                         (json-message (json-read-from-string json-string)))

                    ;; Check if this is a status message
                    (when (plist-get json-message :status)
                      (let ((status (plist-get json-message :status)))
                        (cond
                         ((string= status "starting")
                          (message "MCP typescript-bridge: Bridge is starting"))
                         ((string= status "ready")
                          (message "MCP typescript-bridge: Bridge is ready")
                          (process-put process 'status 'ready)))))

                    ;; Check if this is a response to a request
                    (let ((response-id (plist-get json-message :id))
                          (callback-table (process-get process 'callback-table)))
                      (when (and response-id
                                 (hash-table-p callback-table)
                                 (gethash response-id callback-table))
                        (let ((callback (gethash response-id callback-table)))
                          (message "MCP typescript-bridge: Found callback for request ID: %s" response-id)
                          (condition-case callback-err
                              (progn
                                (message "MCP typescript-bridge: Processing response for request ID: %s" response-id)
                                (funcall callback json-message)
                                (message "MCP typescript-bridge: Callback executed successfully for request ID: %s" response-id)
                                (remhash response-id callback-table))
                            (error
                             (message "MCP typescript-bridge: Error in callback for request ID %s: %s"
                                      response-id (error-message-string callback-err))
                             ;; Remove the callback to avoid memory leaks
                             (remhash response-id callback-table))))))

                    ;; Check if this is a response to the current callback
                    (let ((response-id (plist-get json-message :id))
                          (current-callback (process-get process 'current-callback)))
                      (when (and response-id current-callback)
                        (message "MCP typescript-bridge: Using current callback for response ID: %s" response-id)
                        (condition-case callback-err
                            (progn
                              (funcall current-callback json-message)
                              (message "MCP typescript-bridge: Current callback executed successfully")
                              (process-put process 'current-callback nil))
                          (error
                           (message "MCP typescript-bridge: Error in current callback: %s"
                                    (error-message-string callback-err))
                           ;; Clear the callback to avoid getting stuck
                           (process-put process 'current-callback nil))))))
                (error
                 (message "MCP typescript-bridge: Error parsing JSON: %s" (error-message-string err))
                 (message "JSON string: %s" json-string)))))

          ;; Check for specific server ready messages in the buffer
          (goto-char (point-min))
          (let ((server-name (process-get process 'server-name)))
            (when server-name
              ;; Check for ElevenLabs server ready message
              (when (re-search-forward "Starting MCP serve" nil t)
                (message "MCP typescript-bridge: ElevenLabs server is ready")
                (process-put process 'elevenlabs-ready t))

              ;; Check for Brave Search server ready message
              (goto-char (point-min))
              (when (re-search-forward "Brave Search MCP Server running on stdio" nil t)
                (message "MCP typescript-bridge: Brave Search server is ready")
                (process-put process 'brave-ready t))

              ;; Check for bridge waiting for input message
              (goto-char (point-min))
              (when (re-search-forward "TypeScript MCP Bridge: Waiting for input" nil t)
                (message "MCP typescript-bridge: Bridge is waiting for input")
                (process-put process 'waiting-for-input t))))

          ;; Move the point if it was at the end
          (when moving
            (goto-char (process-mark process))))))))

(defun ai-auto-complete-mcp-typescript-bridge-process-sentinel (process event)
  "Process sentinel for the TypeScript bridge process.
PROCESS is the bridge process.
EVENT is the event that occurred."
  (let ((status (process-status process)))
    (message "MCP typescript-bridge: Bridge process status changed to %s (%s)" status event)
    (when (memq status '(exit signal))
      (message "MCP typescript-bridge: Bridge process exited")
      (setq ai-auto-complete-mcp-typescript-bridge-process nil))))

(defun ai-auto-complete-mcp-typescript-bridge-start-bridge ()
  "Start the TypeScript bridge process."
  (let* ((bridge-script (ai-auto-complete-mcp-typescript-bridge-find-bridge-script))
         (buffer-name "*MCP-typescript-bridge*")
         (process nil))

    ;; Check if the bridge script exists
    (if (not bridge-script)
        (progn
          (message "MCP typescript-bridge: Bridge script not found")
          nil)

      ;; Check if Node.js is available
      (let ((node-exe (ai-auto-complete-mcp-check-node)))
        (if (not node-exe)
            (progn
              (message "MCP typescript-bridge: Node.js is not available. Please install Node.js.")
              nil)

          ;; Check if the MCP TypeScript SDK is available
          (unless (ai-auto-complete-mcp-check-mcp-typescript-package)
            (message "MCP typescript-bridge: MCP TypeScript SDK not found. Attempting to install...")
            (let ((default-directory (file-name-directory bridge-script)))
              (message "MCP typescript-bridge: Installing dependencies in %s" default-directory)
              (shell-command "npm install")))

          ;; Start the bridge process
          (condition-case err
              (progn
                ;; Create a buffer for the process
                (with-current-buffer (get-buffer-create buffer-name)
                  (erase-buffer))

                ;; Check if we should use a Node.js environment
                (let ((node-env (getenv "NODE_ENV")))
                  (when node-env
                    (message "MCP typescript-bridge: Using Node.js environment: %s" node-env)))

                ;; Start the bridge process
                (message "MCP typescript-bridge: Using Node.js executable: %s" node-exe)
                (message "MCP typescript-bridge: Starting bridge process with command: %s %s" node-exe bridge-script)
                (setq process (start-process "mcp-typescript-bridge" buffer-name
                                            node-exe bridge-script))

                (message "MCP typescript-bridge: Bridge process started with PID: %s" (process-id process))

                ;; Set up filter and sentinel
                (set-process-filter process #'ai-auto-complete-mcp-typescript-bridge-process-filter)
                (set-process-sentinel process #'ai-auto-complete-mcp-typescript-bridge-process-sentinel)

                ;; Wait for the bridge to be ready
                (let ((timeout 30) ; Increase timeout to 30 seconds
                      (start-time (current-time)))
                  (message "MCP typescript-bridge: Waiting for bridge to be ready (timeout: %s seconds)" timeout)
                  (while (and (not (eq (process-get process 'status) 'ready))
                              (< (float-time (time-since start-time)) timeout))
                    (accept-process-output process 0.1)
                    (when (zerop (mod (truncate (float-time (time-since start-time))) 5))
                      (message "MCP typescript-bridge: Still waiting for bridge to be ready... (%s seconds elapsed)"
                               (truncate (float-time (time-since start-time))))))

                  ;; Check if the bridge is ready
                  (if (eq (process-get process 'status) 'ready)
                      (progn
                        (message "MCP typescript-bridge: Bridge is ready")
                        (setq ai-auto-complete-mcp-typescript-bridge-process process)
                        process)
                    (progn
                      (message "MCP typescript-bridge: Timed out waiting for bridge to be ready")
                      (when process
                        (delete-process process))
                      nil))))
        (error
         (message "MCP typescript-bridge: Error starting bridge process: %s" (error-message-string err))
         nil)))))))

(defun ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request (method params id)
  "Create a JSON-RPC request.
METHOD is the method to call.
PARAMS is the parameters to pass.
ID is the request ID."
  (json-encode
   `((jsonrpc . "2.0")
     (method . ,method)
     (params . ,params)
     (id . ,id))))

(defun ai-auto-complete-mcp-typescript-bridge-initialize-server (server-name server-path process)
  "Initialize an MCP server with SERVER-NAME at SERVER-PATH using PROCESS."
  (message "MCP typescript-bridge: Initializing server %s at %s" server-name server-path)

  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (transport (plist-get server :transport))
         (is-settings-server (eq transport 'server-bridge))
         (args (plist-get server :serverArgs))
         ;; Extract API key from server configuration
         (api-key (or (plist-get server :api-key)
                      (when (plist-get server :env)
                        (or (plist-get (plist-get server :env) :ELEVENLABS_API_KEY)
                            (plist-get (plist-get server :env) :BRAVE_API_KEY)))))

         ;; Prepare initialization parameters
         (init-params `((serverPath . ,server-path)
                        (protocolVersion . "0.6.0")
                        (clientInfo . ((name . "ai-auto-complete-mcp-bridge")
                                      (version . "1.0.0")))))

         ;; Add API key to initialization parameters if available
         (init-params (if api-key
                         (append init-params `((apiKey . ,api-key)))
                       init-params))

         ;; For settings-based servers, add additional parameters
         (init-params (if is-settings-server
                         (let* ((env (plist-get server :env))
                                ;; Convert env plist to alist for JSON encoding
                                (env-alist (when env
                                             (let ((result nil))
                                               (cond
                                                ;; If env is a hash table, convert it to an alist
                                                ((hash-table-p env)
                                                 (maphash (lambda (k v)
                                                            (push (cons k v) result))
                                                          env))

                                                ;; If env is a plist, convert it to an alist
                                                ((listp env)
                                                 (let ((keys (cl-loop for (k _) on env by #'cddr collect k)))
                                                   (dolist (key keys)
                                                     (when (symbolp key)
                                                       (push (cons (substring (symbol-name key) 1)
                                                                  (plist-get env key))
                                                             result)))))

                                                ;; Handle other cases
                                                (t
                                                 (message "MCP typescript-bridge: Unsupported env type: %s" (type-of env))))
                                               result))))

                           ;; Log environment variables for debugging
                           (when ai-auto-complete-mcp-debug-mode
                             (message "MCP typescript-bridge: Environment variables for server %s: %S"
                                      server-name env-alist))

                           (append init-params `((isSettingsServer . t)
                                                (serverArgs . ,args)
                                                (runner . ,(plist-get server :runner))
                                                ,@(when env-alist `((env . ,env-alist))))))
                       init-params))

         ;; Skip debug logging to avoid syntax errors
         ;; Create the JSON-RPC request
         (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                   "initialize"
                   init-params
                   (format "req-init-%s" (random 10000))))
         (initialized nil)
         (response nil)
         (server-ready nil)
         (buffer (process-buffer process)))

    ;; Set up a filter to detect server readiness from output
    (let ((original-filter (process-filter process)))
      (set-process-filter
       process
       (lambda (proc output)
         ;; Call the original filter
         (when original-filter
           (funcall original-filter proc output))

         ;; Log the output for debugging
         (when ai-auto-complete-mcp-debug-mode
           (message "MCP typescript-bridge: Server output: %s"
                    (substring output 0 (min 100 (length output)))))

         ;; Check for server ready messages in the output
         (when (and (not server-ready)
                    (or (string-match-p "Brave Search MCP server running on stdio" output)
                        (string-match-p "Starting MCP serve" output)
                        (string-match-p "MCP server ready" output)
                        (string-match-p "Server ready" output)
                        (string-match-p "TypeScript MCP Bridge: Ready to process commands" output)
                        (string-match-p "TypeScript MCP Bridge: Waiting for input" output)))
           (message "MCP typescript-bridge: Detected server ready message for %s" server-name)
           (setq server-ready t)))))

    ;; Set up callback for JSON-RPC response
    (process-put process 'current-callback
                (lambda (json-message)
                  (message "MCP typescript-bridge: Received JSON-RPC response: %S" json-message)
                  (setq response json-message)
                  (setq initialized t)))

    ;; Store the callback in the callback table as well
    (let* ((request-id (plist-get (json-read-from-string command) :id))
           (callback-table (process-get process 'callback-table)))
      (when (and request-id callback-table)
        (message "MCP typescript-bridge: Storing callback for request ID: %s" request-id)
        (puthash request-id
                (lambda (json-message)
                  (message "MCP typescript-bridge: Received JSON-RPC response for ID %s: %S"
                           request-id json-message)
                  (setq response json-message)
                  (setq initialized t))
                callback-table)))

    ;; Send the initialize request
    (message "MCP typescript-bridge: Sending initialization request: %s"
             (substring command 0 (min 100 (length command))))
    (process-send-string process (concat command "\n"))

    ;; Wait for initialization to complete or server ready message
    (let ((timeout 30) ;; Increase timeout to 30 seconds to match stdio-bridge
          (start-time (current-time)))
      (message "MCP typescript-bridge: Waiting for server initialization (timeout: %s seconds)" timeout)
      (while (and (not initialized)
                  (not server-ready)
                  (< (float-time (time-since start-time)) timeout))
        (accept-process-output process 0.1)
        (when (zerop (mod (truncate (float-time (time-since start-time))) 5))
          (message "MCP typescript-bridge: Still waiting for server initialization... (%s seconds elapsed)"
                   (truncate (float-time (time-since start-time))))))

      (cond
       ;; If we got a JSON-RPC response
       (initialized
        (if (plist-get response :error)
            (progn
              (message "MCP typescript-bridge: Error initializing server %s: %s"
                      server-name (plist-get (plist-get response :error) :message))
              nil)
          (progn
            (message "MCP typescript-bridge: Server %s initialized successfully via JSON-RPC" server-name)
            t)))

       ;; If we detected a server ready message
       (server-ready
        (message "MCP typescript-bridge: Server %s initialized successfully via ready message" server-name)
        t)

       ;; For settings-based servers, assume success after timeout
       (is-settings-server
        (message "MCP typescript-bridge: Assuming server %s is initialized (settings-based server)" server-name)
        t)

       ;; Otherwise, report timeout
       (t
        (message "MCP typescript-bridge: Timed out waiting for server %s to initialize" server-name)
        nil)))))

(defun ai-auto-complete-mcp-typescript-bridge-start-server (server-name path)
  "Start an MCP server with SERVER-NAME at PATH using the TypeScript bridge."
  (message "MCP typescript-bridge: Starting server %s at %s" server-name path)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          nil)

      ;; Check if this is a settings-based server
      (let ((transport (plist-get server :transport))
            (is-settings-server (eq (plist-get server :transport) 'server-bridge)))

        ;; Check if the path exists (skip for settings-based servers)
        (if (and (not is-settings-server)
                 (not (file-exists-p path)))
            (progn
              (message "MCP typescript-bridge: Server path %s does not exist" path)
              nil)

          ;; Debug: Show that we're skipping the file existence check for settings-based servers
          (when is-settings-server
            (message "MCP typescript-bridge: Skipping file existence check for settings-based server %s" server-name)
            (message "MCP typescript-bridge: Using runner: %s, args: %S"
                     (plist-get server :runner)
                     (plist-get server :args)))

          ;; Start the bridge process if not already running
          (unless ai-auto-complete-mcp-typescript-bridge-process
            (setq ai-auto-complete-mcp-typescript-bridge-process
                  (ai-auto-complete-mcp-typescript-bridge-start-bridge)))

          (if (not ai-auto-complete-mcp-typescript-bridge-process)
              (progn
                (message "MCP typescript-bridge: Failed to start bridge process")
                nil)

            ;; Initialize the server through the bridge
            (let ((buffer-name (format "*MCP-typescript-bridge-%s*" server-name))
                  (callback-table (make-hash-table :test 'equal))
                  (initialized nil))

              ;; Create a buffer for the server
              (with-current-buffer (get-buffer-create buffer-name)
                (erase-buffer))

              ;; Set server properties on the bridge process
              (process-put ai-auto-complete-mcp-typescript-bridge-process 'server-name server-name)
              (process-put ai-auto-complete-mcp-typescript-bridge-process 'callback-table callback-table)

              ;; Store server type information
              (process-put ai-auto-complete-mcp-typescript-bridge-process 'server-type
                          (plist-get server :server-type))
              (process-put ai-auto-complete-mcp-typescript-bridge-process 'is-settings-server
                          is-settings-server)

              ;; Initialize the server
              (if (ai-auto-complete-mcp-typescript-bridge-initialize-server server-name path ai-auto-complete-mcp-typescript-bridge-process)
                  (progn
                    ;; Update server status and process
                    (ai-auto-complete-mcp-update-server-status server-name 'running)
                    (ai-auto-complete-mcp-update-server-process server-name ai-auto-complete-mcp-typescript-bridge-process)

                    ;; Store the server in the process for later reference
                    (process-put ai-auto-complete-mcp-typescript-bridge-process 'server server)

                    (message "MCP typescript-bridge: Server %s started successfully" server-name)
                    t)
                (progn
                  (message "MCP typescript-bridge: Failed to initialize server %s" server-name)
                  nil)))))))))

(defun ai-auto-complete-mcp-typescript-bridge-stop-server (server-name &optional path)
  "Stop an MCP server with SERVER-NAME at PATH using the TypeScript bridge."
  (message "MCP typescript-bridge: Stopping server %s" server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          nil)

      (let ((process (plist-get server :process)))
        ;; If we have a process, send a stop command
        (when (and process (process-live-p process))
          (message "MCP typescript-bridge: Sending stop command to server %s" server-name)

          ;; Try to send a stop command via JSON-RPC
          (condition-case err
              (let ((request-id (format "req-stop-%s" (random 10000)))
                    (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                              "stopServer"
                              `((serverName . ,server-name))
                              request-id)))
                (process-send-string process (concat command "\n")))
            (error
             (message "MCP typescript-bridge: Error sending stop command: %s"
                      (error-message-string err)))))

        ;; Update server status
        (ai-auto-complete-mcp-update-server-status server-name 'stopped)
        (ai-auto-complete-mcp-update-server-process server-name nil)

        (message "MCP typescript-bridge: Server %s stopped" server-name)
        t))))

(defun ai-auto-complete-mcp-typescript-bridge-call-tool (server-name tool-name params &optional callback &rest _args)
  "Call a tool on an MCP server.
SERVER-NAME is the name of the server.
TOOL-NAME is the name of the tool to call.
PARAMS is the parameters to pass to the tool.
Optional CALLBACK is a function to call with the result.
Optional _ARGS are ignored for compatibility with different calling conventions."
  (message "MCP typescript-bridge: Calling tool %s on server %s" tool-name server-name)

  ;; First, check if the server exists
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          (when (functionp callback)
            (funcall callback (format "Error: Server %s not found" server-name)))
          nil)

      ;; Check if the server is running
      (let ((status (plist-get server :status))
            (process (plist-get server :process)))
        (if (not (eq status 'running))
            ;; Try to start the server
            (if (ai-auto-complete-mcp-typescript-bridge-start-server server-name (plist-get server :path))
                ;; Server started successfully, call tool
                (progn
                  (message "MCP typescript-bridge: Server %s started successfully, calling tool %s"
                           server-name tool-name)
                  ;; Get the updated process after starting the server
                  (setq server (ai-auto-complete-mcp-get-server server-name))
                  (setq process (plist-get server :process))

                  (let* ((request-id (format "req-call-tool-%s" (random 10000)))
                         (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                                   "callTool"
                                   `((serverPath . ,(plist-get server :path))
                                     (toolName . ,tool-name)
                                     (arguments . ,params))
                                   request-id)))

                    ;; Store the callback
                    (puthash request-id
                            (lambda (response)
                              (if (plist-get response :error)
                                  (progn
                                    (message "MCP typescript-bridge: Error calling tool %s: %s"
                                            tool-name (plist-get (plist-get response :error) :message))
                                    (when (functionp callback)
                                      (funcall callback (format "Error: %s"
                                                               (plist-get (plist-get response :error) :message)))))
                                (let ((result (plist-get response :result)))
                                  (when (functionp callback)
                                    (funcall callback (if (and result (listp result) (plist-get result :content))
                                                       (let ((content (plist-get result :content)))
                                                         (if (and (listp content) (> (length content) 0))
                                                             (let ((first-item (car content)))
                                                               (if (and (listp first-item) (plist-get first-item :text))
                                                                   (plist-get first-item :text)
                                                                 (json-encode result)))
                                                           (json-encode result)))
                                                     (json-encode result)))))))
                            (process-get process 'callback-table))

                    ;; Send the command
                    (process-send-string process (concat command "\n"))))

              ;; Failed to start server
              (progn
                (message "MCP typescript-bridge: Failed to start server %s" server-name)
                (when (functionp callback)
                  (funcall callback (format "Error: Failed to start server %s" server-name)))
                nil))

          ;; Server is already running, call tool
          (let* ((request-id (format "req-call-tool-%s" (random 10000)))
                 (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                           "callTool"
                           `((serverPath . ,(plist-get server :path))
                             (toolName . ,tool-name)
                             (arguments . ,params))
                           request-id)))

            ;; Store the callback
            (puthash request-id
                    (lambda (response)
                      (if (plist-get response :error)
                          (progn
                            (message "MCP typescript-bridge: Error calling tool %s: %s"
                                    tool-name (plist-get (plist-get response :error) :message))
                            (when (functionp callback)
                              (funcall callback (format "Error: %s"
                                                       (plist-get (plist-get response :error) :message)))))
                        (let ((result (plist-get response :result)))
                          (when (functionp callback)
                            (funcall callback (if (and result (listp result) (plist-get result :content))
                                               (let ((content (plist-get result :content)))
                                                 (if (and (listp content) (> (length content) 0))
                                                     (let ((first-item (car content)))
                                                       (if (and (listp first-item) (plist-get first-item :text))
                                                           (plist-get first-item :text)
                                                         (json-encode result)))
                                                   (json-encode result)))
                                             (json-encode result)))))))
                    (process-get process 'callback-table))

            ;; Send the command
            (process-send-string process (concat command "\n"))))))))

(defun ai-auto-complete-mcp-typescript-bridge-list-tools (server-name &optional callback &rest _args)
  "List tools on an MCP server.
SERVER-NAME is the name of the server.
Optional CALLBACK is a function to call with the result.
Optional _ARGS are ignored for compatibility with different calling conventions."
  (message "MCP typescript-bridge: Listing tools on server %s" server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          (when (functionp callback) (funcall callback nil))
          nil)

      (let ((process (plist-get server :process)))
        (if (not process)
            (progn
              (message "MCP typescript-bridge: Server %s has no process" server-name)
              (when (functionp callback) (funcall callback nil))
              nil)

          (let* ((request-id (format "req-list-tools-%s" (random 10000)))
                 (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                           "listTools"
                           `((serverPath . ,(plist-get server :path)))
                           request-id)))

            ;; Store the callback
            (puthash request-id
                    (lambda (response)
                      (if (plist-get response :error)
                          (progn
                            (message "MCP typescript-bridge: Error listing tools: %s"
                                    (plist-get (plist-get response :error) :message))
                            (when (functionp callback) (funcall callback nil))
                            nil)
                        (let ((result (plist-get response :result)))
                          (message "MCP typescript-bridge: Successfully received tools list")
                          (when result
                            (message "MCP typescript-bridge: Result type: %s" (type-of result)))

                          ;; Process the result based on its type
                          (let ((processed-result result))
                            ;; If result is a string, try to parse it as a plist or list
                            (when (stringp result)
                              (condition-case display-err
                                  (message "MCP typescript-bridge: Result is a string, attempting to parse: %s"
                                           (substring result 0 (min 100 (length result))))
                                (error
                                 (message "MCP typescript-bridge: Result is a string (error displaying content)")))

                              (condition-case parse-err
                                  (progn
                                    ;; Check if it looks like a plist string (starts with "(" and contains ":")
                                    (if (and (> (length result) 2)  ;; Make sure string is long enough
                                             (string-match-p "^\\s*(\\s*:" result)
                                             (string-match-p ":\\s*[^\\s]" result))
                                        (progn
                                          (message "MCP typescript-bridge: String looks like a plist, attempting to read")
                                          ;; Try to read it as a plist with error handling
                                          (condition-case read-err
                                              (setq processed-result (read result))
                                            (error
                                             (message "MCP typescript-bridge: Error reading plist: %s"
                                                      (error-message-string read-err)))))

                                      ;; Check if it looks like a list string (starts with "[" or "(")
                                      (when (and (> (length result) 2)  ;; Make sure string is long enough
                                                 (string-match-p "^\\s*[\\[(]" result))
                                        (message "MCP typescript-bridge: String looks like a list, attempting to read")
                                        ;; Try to read it as a list with error handling
                                        (condition-case read-err
                                            (setq processed-result (read result))
                                          (error
                                           (message "MCP typescript-bridge: Error reading list: %s"
                                                    (error-message-string read-err)))))))
                                (error
                                 (message "MCP typescript-bridge: Error parsing result string: %s"
                                          (error-message-string parse-err))
                                 ;; Keep it as a string if parsing fails
                                 (setq processed-result result))))

                            ;; Safely process the result and call the callback
                            (condition-case process-err
                                (when (functionp callback)
                                  (message "MCP typescript-bridge: Calling callback with processed result type: %s"
                                           (type-of processed-result))
                                  (funcall callback processed-result))
                              (error
                               (message "MCP typescript-bridge: Error processing tools list: %s"
                                        (error-message-string process-err))
                               (when (functionp callback)
                                 (funcall callback "Error processing tools list")))))))
                    (process-get process 'callback-table))

            ;; Send the command
            (process-send-string process (concat command "\n")))))))))

(defun ai-auto-complete-mcp-typescript-bridge-list-resources (server-name callback &rest _args)
  "List resources on an MCP server.
SERVER-NAME is the name of the server.
CALLBACK is a function to call with the result.
Optional _ARGS are ignored for compatibility with different calling conventions."
  (message "MCP typescript-bridge: Listing resources on server %s" server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          (when (functionp callback) (funcall callback nil)))

      (let ((process (plist-get server :process)))
        (if (not process)
            (progn
              (message "MCP typescript-bridge: Server %s has no process" server-name)
              (when (functionp callback) (funcall callback nil)))

          (let* ((request-id (format "req-list-resources-%s" (random 10000)))
                 (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                           "listResources"
                           `((serverPath . ,(plist-get server :path)))
                           request-id)))

            ;; Store the callback
            (puthash request-id
                    (lambda (response)
                      (if (plist-get response :error)
                          (progn
                            (message "MCP typescript-bridge: Error listing resources: %s"
                                    (plist-get (plist-get response :error) :message))
                            (when (functionp callback) (funcall callback nil)))
                        (when (functionp callback) (funcall callback (plist-get response :result))))))
                    (process-get process 'callback-table))

            ;; Send the command
            (process-send-string process (concat command "\n")))))))

(defun ai-auto-complete-mcp-typescript-bridge-list-prompts (server-name callback &rest _args)
  "List prompts on an MCP server.
SERVER-NAME is the name of the server.
CALLBACK is a function to call with the result.
Optional _ARGS are ignored for compatibility with different calling conventions."
  (message "MCP typescript-bridge: Listing prompts on server %s" server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          (when (functionp callback) (funcall callback nil)))

      (let ((process (plist-get server :process)))
        (if (not process)
            (progn
              (message "MCP typescript-bridge: Server %s has no process" server-name)
              (when (functionp callback) (funcall callback nil)))

          (let* ((request-id (format "req-list-prompts-%s" (random 10000)))
                 (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                           "listPrompts"
                           `((serverPath . ,(plist-get server :path)))
                           request-id)))

            ;; Store the callback
            (puthash request-id
                    (lambda (response)
                      (if (plist-get response :error)
                          (progn
                            (message "MCP typescript-bridge: Error listing prompts: %s"
                                    (plist-get (plist-get response :error) :message))
                            (when (functionp callback) (funcall callback nil)))
                        (when (functionp callback) (funcall callback (plist-get response :result))))))
                    (process-get process 'callback-table))

            ;; Send the command
            (process-send-string process (concat command "\n")))))))

(defun ai-auto-complete-mcp-typescript-bridge-read-resource (server-name uri callback &rest _args)
  "Read a resource from an MCP server.
SERVER-NAME is the name of the server.
URI is the URI of the resource to read.
CALLBACK is a function to call with the result.
Optional _ARGS are ignored for compatibility with different calling conventions."
  (message "MCP typescript-bridge: Reading resource %s from server %s" uri server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          (when (functionp callback) (funcall callback nil)))

      (let ((process (plist-get server :process)))
        (if (not process)
            (progn
              (message "MCP typescript-bridge: Server %s has no process" server-name)
              (when (functionp callback) (funcall callback nil)))

          (let* ((request-id (format "req-read-resource-%s" (random 10000)))
                 (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                           "readResource"
                           `((serverPath . ,(plist-get server :path))
                             (uri . ,uri))
                           request-id)))

            ;; Store the callback
            (puthash request-id
                    (lambda (response)
                      (if (plist-get response :error)
                          (progn
                            (message "MCP typescript-bridge: Error reading resource %s: %s"
                                    uri (plist-get (plist-get response :error) :message))
                            (when (functionp callback) (funcall callback nil)))
                        (when (functionp callback) (funcall callback (plist-get response :result))))))
                    (process-get process 'callback-table))

            ;; Send the command
            (process-send-string process (concat command "\n")))))))

(defun ai-auto-complete-mcp-typescript-bridge-get-prompt (server-name prompt-name params callback &rest _args)
  "Get a prompt from an MCP server.
SERVER-NAME is the name of the server.
PROMPT-NAME is the name of the prompt to get.
PARAMS is the parameters to pass to the prompt.
CALLBACK is a function to call with the result.
Optional _ARGS are ignored for compatibility with different calling conventions."
  (message "MCP typescript-bridge: Getting prompt %s from server %s" prompt-name server-name)

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP typescript-bridge: Server %s not found" server-name)
          (when (functionp callback) (funcall callback nil)))

      (let ((process (plist-get server :process)))
        (if (not process)
            (progn
              (message "MCP typescript-bridge: Server %s has no process" server-name)
              (when (functionp callback) (funcall callback nil)))

          (let* ((request-id (format "req-get-prompt-%s" (random 10000)))
                 (command (ai-auto-complete-mcp-typescript-bridge-create-jsonrpc-request
                           "getPrompt"
                           `((serverPath . ,(plist-get server :path))
                             (promptName . ,prompt-name)
                             (arguments . ,params))
                           request-id)))

            ;; Store the callback
            (puthash request-id
                    (lambda (response)
                      (if (plist-get response :error)
                          (progn
                            (message "MCP typescript-bridge: Error getting prompt %s: %s"
                                    prompt-name (plist-get (plist-get response :error) :message))
                            (when (functionp callback) (funcall callback nil)))
                        (when (functionp callback) (funcall callback (plist-get response :result))))))
                    (process-get process 'callback-table))

            ;; Send the command
            (process-send-string process (concat command "\n")))))))

;; Install the TypeScript bridge transport
(defun ai-auto-complete-mcp-typescript-bridge-install ()
  "Install the TypeScript bridge transport."
  (interactive)

  ;; Register the transport
  (when (fboundp 'ai-auto-complete-mcp-register-transport)
    (ai-auto-complete-mcp-register-transport 'typescript-bridge
                                            #'ai-auto-complete-mcp-typescript-bridge-start-server
                                            #'ai-auto-complete-mcp-typescript-bridge-stop-server
                                            #'ai-auto-complete-mcp-typescript-bridge-call-tool
                                            #'ai-auto-complete-mcp-typescript-bridge-list-tools
                                            #'ai-auto-complete-mcp-typescript-bridge-list-resources
                                            #'ai-auto-complete-mcp-typescript-bridge-list-prompts
                                            #'ai-auto-complete-mcp-typescript-bridge-read-resource
                                            #'ai-auto-complete-mcp-typescript-bridge-get-prompt)
    (message "TypeScript-based bridge transport installed from mcp-typescript-bridge.el"))

  ;; If the function doesn't exist, print a warning
  (unless (fboundp 'ai-auto-complete-mcp-register-transport)
    (message "Warning: ai-auto-complete-mcp-register-transport function not found. TypeScript bridge transport not installed.")))

;; Check if Node.js is available
(defun ai-auto-complete-mcp-check-node ()
  "Check if Node.js is available for MCP servers.
Returns the path to Node.js executable or nil if not found."
  (interactive)
  (let ((node-cmd (executable-find ai-auto-complete-mcp-node-command)))
    (if node-cmd
        (progn
          (message "MCP: Found Node.js at %s" node-cmd)
          (when (called-interactively-p 'any)
            (let ((version (shell-command-to-string (format "%s --version" node-cmd))))
              (message "Node.js version: %s" (string-trim version))))
          node-cmd)
      (message "MCP: No Node.js executable found. Please install Node.js.")
      nil)))

;; Set Node.js path
(defun ai-auto-complete-mcp-set-node-path (path)
  "Set the Node.js path for MCP to PATH."
  (interactive "fNode.js executable path: ")
  (when (file-exists-p path)
    (setq ai-auto-complete-mcp-node-command path)
    (message "MCP: Node.js path set to %s" path)
    (ai-auto-complete-mcp-check-node)))

;; Check if MCP package is available
(defun ai-auto-complete-mcp-check-mcp-typescript-package ()
  "Check if the MCP TypeScript SDK package is available.
Returns t if available, nil otherwise."
  (interactive)
  (let ((node-cmd (ai-auto-complete-mcp-check-node)))
    (if (not node-cmd)
        (progn
          (message "MCP: Node.js is not available")
          nil)
      (let ((output (shell-command-to-string
                    (format "%s -e \"try { require('@modelcontextprotocol/sdk'); console.log('MCP package available'); } catch(e) { console.log('MCP package not available: ' + e.message); }\""
                           node-cmd))))
        (if (string-match-p "MCP package available" output)
            (progn
              (message "MCP: MCP TypeScript SDK package is available")
              t)
          (message "MCP: MCP TypeScript SDK package is not available: %s" (string-trim output))
          nil)))))

;; Display Node.js information
(defun ai-auto-complete-mcp-node-info ()
  "Display information about Node.js and MCP package availability."
  (interactive)
  (let ((buffer-name "*MCP Node.js Info*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert "MCP Node.js Information\n")
        (insert "=====================\n\n")

        ;; Node.js information
        (let ((node-cmd (ai-auto-complete-mcp-check-node)))
          (if node-cmd
              (progn
                (insert (format "Node.js executable: %s\n" node-cmd))
                (let ((version (string-trim (shell-command-to-string (format "%s --version" node-cmd)))))
                  (insert (format "Node.js version: %s\n" version)))

                ;; Check module paths
                (insert "\nNode.js module paths:\n")
                (let ((module-paths (shell-command-to-string
                                    (format "%s -e \"console.log(require.resolve.paths('@modelcontextprotocol/sdk').join('\\n'))\"" node-cmd))))
                  (dolist (path (split-string module-paths "\n"))
                    (when (not (string-empty-p path))
                      (insert (format "  - %s\n" path)))))

                ;; Check MCP package
                (insert "\nMCP TypeScript SDK Package:\n")
                (let ((mcp-output (shell-command-to-string
                                  (format "%s -e \"try { const sdk = require('@modelcontextprotocol/sdk'); console.log('Available: ' + require.resolve('@modelcontextprotocol/sdk')); } catch(e) { console.log('Not available: ' + e.message); }\""
                                         node-cmd))))
                  (insert (format "  %s\n" (string-trim mcp-output)))))

            (insert "Node.js is not available. Please install Node.js.\n")))

        ;; Add buttons for actions
        (insert "\nActions: ")
        (insert-button "Refresh"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-node-info))
                      'follow-link t)
        (insert " | ")
        (insert-button "Set Node.js Path"
                      'action (lambda (_)
                               (call-interactively 'ai-auto-complete-mcp-set-node-path)
                               (ai-auto-complete-mcp-node-info))
                      'follow-link t)
        (insert " | ")
        (insert-button "Install MCP SDK"
                      'action (lambda (_)
                               (when (y-or-n-p "Install MCP TypeScript SDK globally? ")
                                 (let ((default-directory (expand-file-name "ai-auto-complete-modules/mcp/bridge/typescript-mcp-bridge")))
                                   (shell-command "npm install")
                                   (ai-auto-complete-mcp-node-info))))
                      'follow-link t)))

    ;; Display the buffer
    (switch-to-buffer buffer-name)))

;; Install the transport when the file is loaded
(ai-auto-complete-mcp-typescript-bridge-install)

(provide 'mcp/transports/mcp-typescript-bridge)
;;; mcp-typescript-bridge.el ends here
