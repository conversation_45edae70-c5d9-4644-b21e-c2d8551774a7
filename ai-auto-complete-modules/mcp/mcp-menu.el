;;; mcp-menu.el --- Menu integration for MCP -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides menu integration for MCP commands.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)
(require 'mcp/mcp-status)
(require 'mcp/mcp-test-command)
(require 'mcp/mcp-server-init)
(require 'mcp/mcp-ui)
(require 'mcp/mcp-tools-bridge)
(require 'mcp/mcp-persistence)
(require 'mcp/mcp-tools-ui)

;; Create a menu for MCP commands
(defun ai-auto-complete-mcp-create-menu ()
  "Create a menu for MCP commands."
  (interactive)

  ;; Create the MCP menu
  (easy-menu-define ai-auto-complete-mcp-menu nil
    "Menu for MCP commands"
    '("MCP"
      ["Check Status" ai-auto-complete-mcp-server-status t]
      ["List Servers" ai-auto-complete-mcp-list-servers-ui t]
      ["Import User Servers" ai-auto-complete-mcp-import-user-servers t]
      ["Start All Servers" ai-auto-complete-mcp-start-all-servers t]
      ["Stop All Servers" ai-auto-complete-mcp-stop-all-servers t]
      "--"
      ["Import All Servers as Tools" ai-auto-complete-mcp-import-all-servers-as-tools t]
      ["Manage Server Tools" (lambda () (interactive)
                              (let ((server-name (completing-read "Select MCP server: "
                                                                 (ai-auto-complete-mcp-list-servers))))
                                (ai-auto-complete-mcp-manage-server-tools server-name))) t]
      ["Save All Server Configurations" ai-auto-complete-mcp-save-servers t]
      ["Load Saved Server Configurations" ai-auto-complete-mcp-load-servers t]
      "--"
      ["Test Echo" (lambda () (interactive) (call-interactively 'ai-auto-complete-mcp-test-echo)) t]
      ["Test Direct Execution" (lambda () (interactive) (call-interactively 'ai-auto-complete-mcp-test-direct-execution)) t]
      ["Test Tool Integration" ai-auto-complete-mcp-test-tool-integration t]
      "--"
      ["Check Python" ai-auto-complete-mcp-python-info t]
      ["Set Python Path" (lambda () (interactive) (call-interactively 'ai-auto-complete-mcp-set-python-path)) t]
      "--"
      ["Enable MCP" (lambda () (interactive) (setq ai-auto-complete-mcp-enabled t) (message "MCP enabled")) t]
      ["Disable MCP" (lambda () (interactive) (setq ai-auto-complete-mcp-enabled nil) (message "MCP disabled")) t]))

  ;; Add the MCP menu directly to the menu bar
  (define-key-after global-map [menu-bar mcp] (cons "MCP" ai-auto-complete-mcp-menu)))

;; Initialize the menu
(defun ai-auto-complete-mcp-menu-initialize ()
  "Initialize the MCP menu."
  (message "Initializing MCP menu")
  (ai-auto-complete-mcp-create-menu))

;; Initialize the menu on load
(add-hook 'after-init-hook 'ai-auto-complete-mcp-menu-initialize)

(provide 'mcp/mcp-menu)
;;; mcp-menu.el ends here
