;;; mcp-server-type.el --- Server type detection for MCP servers -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides functions for detecting the type of MCP servers.

;;; Code:

(require 'mcp/mcp-core)

;; Define server type constants
(defconst ai-auto-complete-mcp-server-type-python "python"
  "Server type for Python-based MCP servers.")

(defconst ai-auto-complete-mcp-server-type-javascript "javascript"
  "Server type for JavaScript/TypeScript-based MCP servers.")

(defconst ai-auto-complete-mcp-server-type-unknown "unknown"
  "Server type for unknown MCP servers.")

;; Python runners
(defconst ai-auto-complete-mcp-python-runners
  '("python" "python3" "uvx" "mcp" "python2" "uvicorn")
  "List of runners for Python-based MCP servers.")

;; JavaScript/TypeScript runners
(defconst ai-auto-complete-mcp-javascript-runners
  '("node" "npx" "ts-node" "npm" "yarn" "deno")
  "List of runners for JavaScript/TypeScript-based MCP servers.")

;; Determine server type from command
(defun ai-auto-complete-mcp-determine-server-type-from-command (command)
  "Determine server type (Python or JS/TS) based on COMMAND."
  (cond
   ((member command ai-auto-complete-mcp-python-runners)
    ai-auto-complete-mcp-server-type-python)
   ((member command ai-auto-complete-mcp-javascript-runners)
    ai-auto-complete-mcp-server-type-javascript)
   (t
    (if (and command (string-match-p "\\.py$" command))
        ai-auto-complete-mcp-server-type-python
      (if (and command (string-match-p "\\.(js|ts|mjs|cjs)$" command))
          ai-auto-complete-mcp-server-type-javascript
        ai-auto-complete-mcp-server-type-unknown)))))

;; Determine server type from file extension
(defun ai-auto-complete-mcp-determine-server-type-from-path (path)
  "Determine server type (Python or JS/TS) based on PATH."
  (cond
   ((string-match-p "\\.py$" path)
    ai-auto-complete-mcp-server-type-python)
   ((string-match-p "\\.(js|ts|mjs|cjs)$" path)
    ai-auto-complete-mcp-server-type-javascript)
   (t
    ai-auto-complete-mcp-server-type-unknown)))

;; Determine server type from server definition
(defun ai-auto-complete-mcp-determine-server-type (server)
  "Determine server type (Python or JS/TS) based on SERVER definition."
  (let ((command (plist-get server :command))
        (runner (plist-get server :runner))
        (path (plist-get server :path)))
    (cond
     ;; If server-type is already set, use it
     ((plist-get server :server-type)
      (plist-get server :server-type))
     ;; Try to determine from runner
     ((and runner (not (eq runner 'unknown)))
      (ai-auto-complete-mcp-determine-server-type-from-command runner))
     ;; Try to determine from command
     ((and command (not (eq command 'unknown)))
      (ai-auto-complete-mcp-determine-server-type-from-command command))
     ;; Try to determine from path
     ((and path (not (eq path 'unknown)))
      (ai-auto-complete-mcp-determine-server-type-from-path path))
     ;; Default to unknown
     (t
      ai-auto-complete-mcp-server-type-unknown))))

;; Get appropriate transport for server type
(defun ai-auto-complete-mcp-get-transport-for-server-type (server-type)
  "Get appropriate transport for SERVER-TYPE."
  (cond
   ((string-equal server-type ai-auto-complete-mcp-server-type-python)
    'stdio)
   ((string-equal server-type ai-auto-complete-mcp-server-type-javascript)
    'typescript-bridge)
   (t
    'stdio))) ; Default to stdio for unknown server types

;; Get appropriate transport for server
(defun ai-auto-complete-mcp-get-transport-for-server (server)
  "Get appropriate transport for SERVER."
  (let ((server-type (ai-auto-complete-mcp-determine-server-type server)))
    (ai-auto-complete-mcp-get-transport-for-server-type server-type)))

(provide 'mcp/mcp-server-type)
;;; mcp-server-type.el ends here
