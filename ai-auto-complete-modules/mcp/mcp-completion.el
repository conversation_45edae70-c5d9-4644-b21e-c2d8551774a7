;;; mcp-completion.el --- Completion functionality for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides completion functionality for MCP integration in the AI Auto Complete package.
;; It enhances the completion system to support MCP-specific completions.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-client)

;; MCP completion candidates
(defun ai-auto-complete-mcp-completion-candidates ()
  "Get MCP completion candidates."
  (unless ai-auto-complete-mcp-enabled
    (cl-return-from ai-auto-complete-mcp-completion-candidates nil))
  
  (let ((candidates '())
        (servers (ai-auto-complete-mcp-list-servers)))
    
    ;; Add server names
    (dolist (server-name servers)
      (let ((server (ai-auto-complete-mcp-get-server server-name)))
        (when (eq (plist-get server :status) 'running)
          (push (format "<mcp-tool server=\"%s\" name=\"" server-name) candidates))))
    
    ;; Add tool names for each server
    (dolist (server-name servers)
      (let ((server (ai-auto-complete-mcp-get-server server-name)))
        (when (eq (plist-get server :status) 'running)
          ;; Create a promise to get the tools
          (let ((tools-promise (make-hash-table :test 'eq)))
            ;; Set up the promise
            (puthash 'status 'pending tools-promise)
            (puthash 'value nil tools-promise)
            
            ;; List tools
            (ai-auto-complete-mcp-list-tools
             server-name
             (lambda (tools)
               (puthash 'status 'fulfilled tools-promise)
               (puthash 'value tools tools-promise)))
            
            ;; Wait for the result (with timeout)
            (let ((timeout 5)
                  (start-time (current-time)))
              (while (and (eq (gethash 'status tools-promise) 'pending)
                          (< (float-time (time-since start-time)) timeout))
                (sleep-for 0.1))
              
              ;; Add tool names
              (let ((tools (gethash 'value tools-promise)))
                (when (and tools (not (stringp tools)))
                  (dolist (tool tools)
                    (let ((tool-name (plist-get tool :name)))
                      (push (format "<mcp-tool server=\"%s\" name=\"%s\">"
                                   server-name tool-name)
                            candidates)
                      (push (format "<mcp-tool server=\"%s\" name=\"%s\">\n<parameters>\n{\n  \n}\n</parameters>\n</mcp-tool>"
                                   server-name tool-name)
                            candidates))))))))))
    
    (nreverse candidates)))

;; MCP completion at point function
(defun ai-auto-complete-mcp-completion-at-point ()
  "MCP completion at point function."
  (unless ai-auto-complete-mcp-enabled
    (cl-return-from ai-auto-complete-mcp-completion-at-point nil))
  
  (let ((bounds (bounds-of-thing-at-point 'symbol)))
    (when bounds
      (let ((start (car bounds))
            (end (cdr bounds))
            (prefix (thing-at-point 'symbol)))
        (when (and prefix (string-prefix-p "<mcp" prefix))
          (list start end (ai-auto-complete-mcp-completion-candidates) :exclusive 'no))))))

;; Advice function to add MCP completion
(defun ai-auto-complete-mcp-advice-completion-at-point-functions (functions)
  "Advice function to add MCP completion to FUNCTIONS."
  (if (not ai-auto-complete-mcp-enabled)
      functions
    (cons 'ai-auto-complete-mcp-completion-at-point functions)))

;; Setup function to add advice to completion-at-point-functions
(defun ai-auto-complete-mcp-completion-setup ()
  "Set up MCP completion integration."
  (advice-add 'completion-at-point-functions :filter-return
              #'ai-auto-complete-mcp-advice-completion-at-point-functions))

;; Teardown function to remove advice from completion-at-point-functions
(defun ai-auto-complete-mcp-completion-teardown ()
  "Remove MCP completion integration."
  (advice-remove 'completion-at-point-functions
                 #'ai-auto-complete-mcp-advice-completion-at-point-functions))

;; Set up MCP completion integration when this module is loaded
(ai-auto-complete-mcp-completion-setup)

(provide 'mcp/mcp-completion)
;;; mcp-completion.el ends here
