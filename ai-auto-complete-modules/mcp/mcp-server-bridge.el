;;; mcp-server-bridge.el --- Bridge for MCP servers -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a bridge for MCP servers, delegating to the appropriate transport
;; based on server type.

;;; Code:

(require 'mcp/mcp-core)
;(require 'mcp/mcp-server)
(require 'mcp/mcp-server-type)
(require 'mcp/transports/mcp-stdio-bridge)
(require 'mcp/transports/mcp-typescript-bridge)

;; Start an MCP server using the appropriate bridge
(defun ai-auto-complete-mcp-server-bridge-start-server (name &optional path)
  "Start MCP server with NAME at PATH using the appropriate bridge based on server type."
  (let* ((server (ai-auto-complete-mcp-get-server name))
         (server-type (ai-auto-complete-mcp-determine-server-type server)))

    (message "MCP DEBUG: Starting server %s with type %s" name server-type)

    (cond
     ;; Python server
     ((string-equal server-type ai-auto-complete-mcp-server-type-python)
      (message "MCP DEBUG: Using stdio bridge for Python server %s" name)
      (ai-auto-complete-mcp-stdio-start-server name (or path (plist-get server :path))))

     ;; JavaScript/TypeScript server
     ((string-equal server-type ai-auto-complete-mcp-server-type-javascript)
      (message "MCP DEBUG: Using TypeScript bridge for JavaScript server %s" name)
      (ai-auto-complete-mcp-typescript-bridge-start-server name (or path (plist-get server :path))))

     ;; Unknown server type, fall back to stdio
     (t
      (message "MCP DEBUG: Unknown server type for %s, falling back to stdio bridge" name)
      (ai-auto-complete-mcp-stdio-start-server name (or path (plist-get server :path)))))))

;; Stop an MCP server using the appropriate bridge
(defun ai-auto-complete-mcp-server-bridge-stop-server (name &optional path)
  "Stop MCP server with NAME at PATH using the appropriate bridge based on server type."
  (let* ((server (ai-auto-complete-mcp-get-server name))
         (server-type (ai-auto-complete-mcp-determine-server-type server)))

    (message "MCP DEBUG: Stopping server %s with type %s" name server-type)

    (cond
     ;; Python server
     ((string-equal server-type ai-auto-complete-mcp-server-type-python)
      (message "MCP DEBUG: Using stdio bridge for Python server %s" name)
      (ai-auto-complete-mcp-stdio-stop-server name (or path (plist-get server :path))))

     ;; JavaScript/TypeScript server
     ((string-equal server-type ai-auto-complete-mcp-server-type-javascript)
      (message "MCP DEBUG: Using TypeScript bridge for JavaScript server %s" name)
      (ai-auto-complete-mcp-typescript-bridge-stop-server name (or path (plist-get server :path))))

     ;; Unknown server type, fall back to stdio
     (t
      (message "MCP DEBUG: Unknown server type for %s, falling back to stdio bridge" name)
      (ai-auto-complete-mcp-stdio-stop-server name (or path (plist-get server :path)))))))

;; Call a tool on an MCP server using the appropriate bridge
(defun ai-auto-complete-mcp-server-bridge-call-tool (name tool-name params &optional callback)
  "Call tool TOOL-NAME with PARAMS on server NAME using the appropriate bridge.
Optional CALLBACK is a function to call with the result."
  (let* ((server (ai-auto-complete-mcp-get-server name))
         (server-type (ai-auto-complete-mcp-determine-server-type server)))

    (message "MCP DEBUG: Calling tool %s on server %s with type %s" tool-name name server-type)

    (cond
     ;; Python server
     ((string-equal server-type ai-auto-complete-mcp-server-type-python)
      (message "MCP DEBUG: Using stdio bridge for Python server %s" name)
      (ai-auto-complete-mcp-stdio-call-tool name tool-name params callback))

     ;; JavaScript/TypeScript server
     ((string-equal server-type ai-auto-complete-mcp-server-type-javascript)
      (message "MCP DEBUG: Using TypeScript bridge for JavaScript server %s" name)
      (ai-auto-complete-mcp-typescript-bridge-call-tool name tool-name params callback))

     ;; Unknown server type, fall back to stdio
     (t
      (message "MCP DEBUG: Unknown server type for %s, falling back to stdio bridge" name)
      (ai-auto-complete-mcp-stdio-call-tool name tool-name params callback)))))

;; List tools on an MCP server using the appropriate bridge
(defun ai-auto-complete-mcp-server-bridge-list-tools (name)
  "List tools on server NAME using the appropriate bridge."
  (let* ((server (ai-auto-complete-mcp-get-server name))
         (server-type (ai-auto-complete-mcp-determine-server-type server)))

    (message "MCP DEBUG: Listing tools on server %s with type %s" name server-type)

    (cond
     ;; Python server
     ((string-equal server-type ai-auto-complete-mcp-server-type-python)
      (message "MCP DEBUG: Using stdio bridge for Python server %s" name)
      (ai-auto-complete-mcp-stdio-list-tools name))

     ;; JavaScript/TypeScript server
     ((string-equal server-type ai-auto-complete-mcp-server-type-javascript)
      (message "MCP DEBUG: Using TypeScript bridge for JavaScript server %s" name)
      (ai-auto-complete-mcp-typescript-bridge-list-tools name))

     ;; Unknown server type, fall back to stdio
     (t
      (message "MCP DEBUG: Unknown server type for %s, falling back to stdio bridge" name)
      (ai-auto-complete-mcp-stdio-list-tools name)))))

;; List resources on an MCP server using the appropriate bridge
(defun ai-auto-complete-mcp-server-bridge-list-resources (name)
  "List resources on server NAME using the appropriate bridge."
  (let* ((server (ai-auto-complete-mcp-get-server name))
         (server-type (ai-auto-complete-mcp-determine-server-type server)))

    (message "MCP DEBUG: Listing resources on server %s with type %s" name server-type)

    (cond
     ;; Python server
     ((string-equal server-type ai-auto-complete-mcp-server-type-python)
      (message "MCP DEBUG: Using stdio bridge for Python server %s" name)
      (ai-auto-complete-mcp-stdio-list-resources name))

     ;; JavaScript/TypeScript server
     ((string-equal server-type ai-auto-complete-mcp-server-type-javascript)
      (message "MCP DEBUG: Using TypeScript bridge for JavaScript server %s" name)
      (ai-auto-complete-mcp-typescript-bridge-list-resources name))

     ;; Unknown server type, fall back to stdio
     (t
      (message "MCP DEBUG: Unknown server type for %s, falling back to stdio bridge" name)
      (ai-auto-complete-mcp-stdio-list-resources name)))))

;; List prompts on an MCP server using the appropriate bridge
(defun ai-auto-complete-mcp-server-bridge-list-prompts (name)
  "List prompts on server NAME using the appropriate bridge."
  (let* ((server (ai-auto-complete-mcp-get-server name))
         (server-type (ai-auto-complete-mcp-determine-server-type server)))

    (message "MCP DEBUG: Listing prompts on server %s with type %s" name server-type)

    (cond
     ;; Python server
     ((string-equal server-type ai-auto-complete-mcp-server-type-python)
      (message "MCP DEBUG: Using stdio bridge for Python server %s" name)
      (ai-auto-complete-mcp-stdio-list-prompts name))

     ;; JavaScript/TypeScript server
     ((string-equal server-type ai-auto-complete-mcp-server-type-javascript)
      (message "MCP DEBUG: Using TypeScript bridge for JavaScript server %s" name)
      (ai-auto-complete-mcp-typescript-bridge-list-prompts name))

     ;; Unknown server type, fall back to stdio
     (t
      (message "MCP DEBUG: Unknown server type for %s, falling back to stdio bridge" name)
      (ai-auto-complete-mcp-stdio-list-prompts name)))))

;; Read a resource on an MCP server using the appropriate bridge
(defun ai-auto-complete-mcp-server-bridge-read-resource (name resource-id)
  "Read resource RESOURCE-ID on server NAME using the appropriate bridge."
  (let* ((server (ai-auto-complete-mcp-get-server name))
         (server-type (ai-auto-complete-mcp-determine-server-type server)))

    (message "MCP DEBUG: Reading resource %s on server %s with type %s" resource-id name server-type)

    (cond
     ;; Python server
     ((string-equal server-type ai-auto-complete-mcp-server-type-python)
      (message "MCP DEBUG: Using stdio bridge for Python server %s" name)
      (ai-auto-complete-mcp-stdio-read-resource name resource-id))

     ;; JavaScript/TypeScript server
     ((string-equal server-type ai-auto-complete-mcp-server-type-javascript)
      (message "MCP DEBUG: Using TypeScript bridge for JavaScript server %s" name)
      (ai-auto-complete-mcp-typescript-bridge-read-resource name resource-id))

     ;; Unknown server type, fall back to stdio
     (t
      (message "MCP DEBUG: Unknown server type for %s, falling back to stdio bridge" name)
      (ai-auto-complete-mcp-stdio-read-resource name resource-id)))))

;; Get a prompt on an MCP server using the appropriate bridge
(defun ai-auto-complete-mcp-server-bridge-get-prompt (name prompt-id)
  "Get prompt PROMPT-ID on server NAME using the appropriate bridge."
  (let* ((server (ai-auto-complete-mcp-get-server name))
         (server-type (ai-auto-complete-mcp-determine-server-type server)))

    (message "MCP DEBUG: Getting prompt %s on server %s with type %s" prompt-id name server-type)

    (cond
     ;; Python server
     ((string-equal server-type ai-auto-complete-mcp-server-type-python)
      (message "MCP DEBUG: Using stdio bridge for Python server %s" name)
      (ai-auto-complete-mcp-stdio-get-prompt name prompt-id))

     ;; JavaScript/TypeScript server
     ((string-equal server-type ai-auto-complete-mcp-server-type-javascript)
      (message "MCP DEBUG: Using TypeScript bridge for JavaScript server %s" name)
      (ai-auto-complete-mcp-typescript-bridge-get-prompt name prompt-id))

     ;; Unknown server type, fall back to stdio
     (t
      (message "MCP DEBUG: Unknown server type for %s, falling back to stdio bridge" name)
      (ai-auto-complete-mcp-stdio-get-prompt name prompt-id)))))

;; Register the server bridge transport
(defun ai-auto-complete-mcp-register-server-bridge-transport ()
  "Register the server bridge transport for MCP servers."
  (ai-auto-complete-mcp-register-transport
   'server-bridge
   'ai-auto-complete-mcp-server-bridge-start-server
   'ai-auto-complete-mcp-server-bridge-stop-server
   'ai-auto-complete-mcp-server-bridge-call-tool
   'ai-auto-complete-mcp-server-bridge-list-tools
   'ai-auto-complete-mcp-server-bridge-list-resources
   'ai-auto-complete-mcp-server-bridge-list-prompts
   'ai-auto-complete-mcp-server-bridge-read-resource
   'ai-auto-complete-mcp-server-bridge-get-prompt))


;; register the transport as the module initialzies
(ai-auto-complete-mcp-register-server-bridge-transport)
(message "MCP: Server bridge transport registered")

(provide 'mcp/mcp-server-bridge)
;;; mcp-server-bridge.el ends here
