;;; mcp-ui.el --- UI components for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides UI components for MCP integration in the AI Auto Complete package.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)
(require 'mcp/mcp-tools-bridge)
(require 'mcp/mcp-directory)
(require 'mcp/mcp-persistence)
(require 'mcp/mcp-tools-ui)

;; Helper function to edit MCP server attribute
(defun ai-auto-complete-mcp-edit-server-attribute (server-name attribute)
  "Edit ATTRIBUTE of MCP server with SERVER-NAME."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (current-value (plist-get server attribute))
         (prompt (format "Edit %s for server %s: " (substring (symbol-name attribute) 1) server-name))
         (new-value nil))

    ;; Handle different attribute types
    (setq new-value
          (cond
           ;; For environment variables (hash table)
           ((and (eq attribute :env) (hash-table-p current-value))
            (let* ((env-var (completing-read "Select environment variable to edit (or enter new): "
                                            (append (hash-table-keys current-value) '("Add new..."))))
                   (env-value nil))
              (if (string= env-var "Add new...")
                  (let ((new-var (read-string "New environment variable name: "))
                        (new-val (read-string "Value: ")))
                    (when (and new-var (not (string-empty-p new-var)))
                      (puthash new-var new-val current-value)))
                (setq env-value (read-string (format "Value for %s: " env-var)
                                            (gethash env-var current-value)))
                (puthash env-var env-value current-value))
              current-value))

           ;; For args (list)
           ((and (eq attribute :args) (listp current-value))
            (let ((action (completing-read "Action: " '("Add argument" "Edit argument" "Remove argument" "Replace all"))))
              (cond
               ((string= action "Add argument")
                (let ((new-arg (read-string "New argument: ")))
                  (append current-value (list new-arg))))
               ((string= action "Edit argument")
                (let* ((index (read-number "Argument index to edit: " 0))
                       (new-arg (read-string "New value: " (nth index current-value))))
                  (setf (nth index current-value) new-arg)
                  current-value))
               ((string= action "Remove argument")
                (let ((index (read-number "Argument index to remove: " 0)))
                  (append (seq-subseq current-value 0 index)
                          (seq-subseq current-value (1+ index)))))
               ((string= action "Replace all")
                (read-string "New arguments (space-separated): "
                            (mapconcat #'identity current-value " "))))))

           ;; For simple string values
           ((stringp current-value)
            (read-string prompt current-value))

           ;; For symbol values
           ((symbolp current-value)
            (intern (completing-read prompt
                                    (mapcar #'symbol-name
                                            '(stdio sse websocket grpc typescript-bridge settings running stopped))
                                    nil nil (symbol-name current-value))))

           ;; Default case
           (t
            (read-string prompt (format "%S" current-value)))))

    ;; Update the server with the new value
    (when new-value
      (puthash server-name (plist-put server attribute new-value) ai-auto-complete-mcp-servers)
      (message "Updated %s for server %s" (substring (symbol-name attribute) 1) server-name))))

;; Helper function to display a tool's details
(defun ai-auto-complete-mcp-display-tool-details (server-name tool)
  "Display details of TOOL from SERVER-NAME in a buffer."
  (let* ((tool-name (plist-get tool :name))
         (description (plist-get tool :description))
         (parameters (plist-get tool :parameters))
         (input-schema (plist-get tool :inputSchema))
         (buffer-name (format "*MCP Tool: %s - %s*" server-name tool-name)))

    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert (format "MCP Tool: %s - %s\n" server-name tool-name))
        (insert "======================\n\n")

        ;; Add tool details
        (insert (format "Description: %s\n\n" description))

        ;; Add parameters
        (insert "Parameters:\n")
        (insert "----------\n")
        (if parameters
            (dolist (param parameters)
              (let ((param-name (plist-get param :name))
                    (param-description (plist-get param :description))
                    (param-required (plist-get param :required)))

                (insert (format "  %s: %s%s\n"
                               param-name
                               param-description
                               (if param-required " (required)" "")))))
          (if input-schema
              (insert (format "  %s\n" (pp-to-string input-schema)))
            (insert "  No parameters defined.\n")))

        ;; Add buttons for actions
        (insert "\nActions:\n")
        (insert-button "Test Tool"
                      'action (lambda (_)
                               (let ((params (read-string "Parameters (JSON object): " "{}")))
                                 (ai-auto-complete-mcp-sample-tool
                                  server-name tool-name (json-read-from-string params))))
                      'follow-link t)

        (insert " | ")
        (insert-button "Close"
                      'action (lambda (_)
                               (kill-buffer buffer-name))
                      'follow-link t)

        (goto-char (point-min)))

    (switch-to-buffer buffer-name))))

;; Display a list of MCP servers with enhanced details and editing capabilities
(defun ai-auto-complete-mcp-list-servers-ui ()
  "Display an enhanced UI for managing MCP servers with detailed information and editing capabilities."
  (interactive)

  (when ai-auto-complete-mcp-debug-mode
    (message "MCP DEBUG: Starting enhanced server listing UI"))

  (let ((buffer-name "*MCP Servers*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert "MCP Servers - Enhanced View\n")
        (insert "=========================\n\n")

        ;; Add server list
        (let ((servers (ai-auto-complete-mcp-list-servers)))
          (if (null servers)
              (insert "No MCP servers registered.\n")
            (dolist (name servers)
              (let* ((server (ai-auto-complete-mcp-get-server name))
                     (status (plist-get server :status))
                     (transport (plist-get server :transport))
                     (path (plist-get server :path))
                     (description (plist-get server :description))
                     (runner (plist-get server :runner))
                     (server-type (plist-get server :server-type))
                     (args (plist-get server :args))
                     (env (plist-get server :env))
                     (command (plist-get server :command))
                     (capabilities (plist-get server :capabilities)))

                ;; Server header with collapsible section
                (insert (propertize (format "Server: %s" name)
                                   'face 'bold))

                ;; Status indicator with color
                (insert " - ")
                (insert (propertize (format "Status: %s" status)
                                   'face (if (eq status 'running)
                                            '(:foreground "green")
                                          '(:foreground "red"))))
                (insert "\n")

                ;; Basic information section
                (insert "  Basic Information:\n")

                ;; Make attributes editable with buttons
                (insert (format "    Description: %s " description))
                (insert-button "[Edit]"
                              'action (lambda (_)
                                       (ai-auto-complete-mcp-edit-server-attribute name :description)
                                       (ai-auto-complete-mcp-list-servers-ui))
                              'follow-link t)
                (insert "\n")

                (insert (format "    Transport: %s " transport))
                (insert-button "[Edit]"
                              'action (lambda (_)
                                       (ai-auto-complete-mcp-edit-server-attribute name :transport)
                                       (ai-auto-complete-mcp-list-servers-ui))
                              'follow-link t)
                (insert "\n")

                (insert (format "    Path: %s " path))
                (insert-button "[Edit]"
                              'action (lambda (_)
                                       (ai-auto-complete-mcp-edit-server-attribute name :path)
                                       (ai-auto-complete-mcp-list-servers-ui))
                              'follow-link t)
                (insert "\n")

                (insert (format "    Runner: %s " (or runner "Not specified")))
                (insert-button "[Edit]"
                              'action (lambda (_)
                                       (ai-auto-complete-mcp-edit-server-attribute name :runner)
                                       (ai-auto-complete-mcp-list-servers-ui))
                              'follow-link t)
                (insert "\n")

                (when server-type
                  (insert (format "    Server Type: %s\n" server-type)))

                ;; Advanced configuration section
                (insert "  Advanced Configuration:\n")

                ;; Command (if available)
                (when command
                  (insert (format "    Command: %s " command))
                  (insert-button "[Edit]"
                                'action (lambda (_)
                                         (ai-auto-complete-mcp-edit-server-attribute name :command)
                                         (ai-auto-complete-mcp-list-servers-ui))
                                'follow-link t)
                  (insert "\n"))

                ;; Arguments (if available)
                (when args
                  (insert "    Arguments: ")
                  (if (listp args)
                      (insert (mapconcat #'identity args " "))
                    (insert (format "%S" args)))
                  (insert " ")
                  (insert-button "[Edit]"
                                'action (lambda (_)
                                         (ai-auto-complete-mcp-edit-server-attribute name :args)
                                         (ai-auto-complete-mcp-list-servers-ui))
                                'follow-link t)
                  (insert "\n"))

                ;; Environment variables (if available)
                (when env
                  (insert "    Environment Variables:\n")
                  (if (hash-table-p env)
                      (maphash (lambda (var val)
                                 (insert (format "      %s: %s\n" var val)))
                               env)
                    (insert (format "      %S\n" env)))
                  (insert "    ")
                  (insert-button "[Edit Environment Variables]"
                                'action (lambda (_)
                                         (ai-auto-complete-mcp-edit-server-attribute name :env)
                                         (ai-auto-complete-mcp-list-servers-ui))
                                'follow-link t)
                  (insert "\n"))

                ;; Capabilities (if available and server is running)
                (when (and capabilities (eq status 'running))
                  (insert "    Capabilities:\n")
                  (insert (format "      %S\n" capabilities)))

                ;; Tools section (if server is running)
                (when (eq status 'running)
                  (insert "  Tools:\n")
                  (let ((tools-loaded nil))
                    ;; Use a promise to get tools
                    (let ((tools-promise (make-hash-table :test 'eq)))
                      ;; Set up the promise
                      (puthash 'status 'pending tools-promise)
                      (puthash 'value nil tools-promise)

                      ;; List tools
                      (ai-auto-complete-mcp-list-tools
                       name
                       (lambda (tools)
                         (puthash 'status 'fulfilled tools-promise)
                         (puthash 'value tools tools-promise)))

                      ;; Wait for the result (with timeout)
                      (let ((timeout 3) ;; Short timeout to keep UI responsive
                            (start-time (current-time)))
                        (while (and (eq (gethash 'status tools-promise) 'pending)
                                    (< (float-time (time-since start-time)) timeout))
                          (sleep-for 0.1))

                        ;; Display tools
                        (let ((tools (gethash 'value tools-promise)))
                          (if (and tools (not (stringp tools)))
                              (progn
                                (setq tools-loaded t)
                                (dolist (tool tools)
                                  (let ((tool-name (plist-get tool :name)))
                                    (insert (format "    • %s " tool-name))
                                    (insert-button "[Details]"
                                                  'action (lambda (_)
                                                           (ai-auto-complete-mcp-display-tool-details name tool))
                                                  'follow-link t)
                                    (insert " ")
                                    (insert-button "[Test]"
                                                  'action (lambda (_)
                                                           (let ((params (read-string "Parameters (JSON object): " "{}")))
                                                             (ai-auto-complete-mcp-sample-tool
                                                              name tool-name (json-read-from-string params))))
                                                  'follow-link t)
                                    (insert "\n"))))
                            (insert "    Tools information not available. ")
                            (insert-button "[Refresh]"
                                          'action (lambda (_)
                                                   (ai-auto-complete-mcp-list-servers-ui))
                                          'follow-link t)
                            (insert "\n")))))))

                ;; Add buttons for server actions
                (insert "  Actions: ")
                (if (eq status 'running)
                    (insert-button "Stop"
                                  'action (lambda (_)
                                           (ai-auto-complete-mcp-stop-server name)
                                           (ai-auto-complete-mcp-list-servers-ui))
                                  'follow-link t)
                  (insert-button "Start"
                                'action (lambda (_)
                                         (ai-auto-complete-mcp-start-server name)
                                         (ai-auto-complete-mcp-list-servers-ui))
                                'follow-link t))

                (insert " | ")
                (insert-button "Restart"
                              'action (lambda (_)
                                       (ai-auto-complete-mcp-restart-server name)
                                       (ai-auto-complete-mcp-list-servers-ui))
                              'follow-link t)

                (insert " | ")
                (insert-button "Delete"
                              'action (lambda (_)
                                       (when (yes-or-no-p (format "Delete server %s permanently? " name))
                                         (let ((delete-file (y-or-n-p (format "Also delete the server file for %s? " name))))
                                           ;; Delete the server
                                           (ai-auto-complete-mcp-delete-server name delete-file)
                                           ;; Explicitly save the configuration to ensure it's not loaded again
                                           (when (fboundp 'ai-auto-complete-mcp-save-servers)
                                             (ai-auto-complete-mcp-save-servers))
                                           ;; Refresh the UI
                                           (ai-auto-complete-mcp-list-servers-ui))))
                              'follow-link t)

                (insert " | ")
                (insert-button "Import as Tool"
                              'action (lambda (_)
                                       (message "Importing MCP server %s as tool..." name)
                                       (ai-auto-complete-mcp-import-server-as-tool name)
                                       (ai-auto-complete-mcp-list-servers-ui))
                              'follow-link t)

                (insert " | ")
                (insert-button "Manage Tools"
                              'action (lambda (_)
                                       (ai-auto-complete-mcp-manage-server-tools name))
                              'follow-link t)

                (insert " | ")
                (insert-button "Debug Import"
                              'action (lambda (_)
                                       (ai-auto-complete-mcp-debug-tool-import name))
                              'follow-link t)

                (insert "\n\n")))))

        ;; Add buttons for global actions
        (insert "Global Actions:\n")
        (insert-button "Add Server"
                      'action (lambda (_)
                               (call-interactively 'ai-auto-complete-mcp-add-server-ui))
                      'follow-link t)

        (insert " | ")
        (insert-button "Scan Directory"
                      'action (lambda (_)
                               (call-interactively 'ai-auto-complete-mcp-scan-directory)
                               (ai-auto-complete-mcp-list-servers-ui))
                      'follow-link t)

        (insert " | ")
        (insert-button "Start All"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-start-all-servers)
                               (ai-auto-complete-mcp-list-servers-ui))
                      'follow-link t)

        (insert " | ")
        (insert-button "Stop All"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-stop-all-servers)
                               (ai-auto-complete-mcp-list-servers-ui))
                      'follow-link t)

        (insert " | ")
        (insert-button "Import All as Tools"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-import-all-servers-as-tools)
                               (ai-auto-complete-mcp-list-servers-ui))
                      'follow-link t)

        (insert " | ")
        (insert-button "List Tools"
                      'action (lambda (_)
                               (ai-auto-complete-tools-list))
                      'follow-link t)

        (insert " | ")
        (insert-button "Create New Server"
                      'action (lambda (_)
                               (call-interactively 'ai-auto-complete-mcp-create-new-server)
                               (ai-auto-complete-mcp-list-servers-ui))
                      'follow-link t)

        (insert " | ")
        (insert-button "Set Servers Directory"
                      'action (lambda (_)
                               (call-interactively 'ai-auto-complete-mcp-set-servers-directory)
                               (ai-auto-complete-mcp-list-servers-ui))
                      'follow-link t)

        (insert " | ")
        (insert-button "Save All Servers"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-save-servers)
                               (message "Saved all MCP server configurations")
                               (ai-auto-complete-mcp-list-servers-ui))
                      'follow-link t)

        (insert " | ")
        (insert-button "Load Saved Servers"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-load-servers)
                               (ai-auto-complete-mcp-list-servers-ui))
                      'follow-link t)

        (insert "\n\n")
        (insert "MCP Status: ")
        (if ai-auto-complete-mcp-enabled
            (insert-button "Enabled (Click to Disable)"
                          'action (lambda (_)
                                   (ai-auto-complete-mcp-toggle)
                                   (ai-auto-complete-mcp-list-servers-ui))
                          'follow-link t)
          (insert-button "Disabled (Click to Enable)"
                        'action (lambda (_)
                                 (ai-auto-complete-mcp-toggle)
                                 (ai-auto-complete-mcp-list-servers-ui))
                        'follow-link t))

        ;; Add buttons for advanced features
        (insert "\n\nAdvanced Features:\n")
        (insert-button "Generate Examples"
                      'action (lambda (_)
                               (call-interactively 'ai-auto-complete-mcp-display-examples))
                      'follow-link t)

        (insert " | ")
        (insert-button "Subscribe to Resource"
                      'action (lambda (_)
                               (let* ((server-name (completing-read "Select MCP server: "
                                                                  (ai-auto-complete-mcp-list-servers)))
                                      (resource-uri (read-string "Resource URI: ")))
                                 (ai-auto-complete-mcp-subscribe-to-resource
                                  server-name resource-uri
                                  (lambda (value)
                                    (message "Resource %s changed: %s"
                                             resource-uri
                                             (substring value 0 (min 100 (length value))))))))
                      'follow-link t)

        (insert " | ")
        (insert-button "Install MCP SDK"
                      'action (lambda (_)
                               (async-shell-command
                                (format "cd %s && ./install-mcp-sdk.sh"
                                        (expand-file-name "scripts"
                                                         (file-name-directory
                                                          (locate-library "mcp/mcp"))))))
                      'follow-link t)

        ;; Add debug information section
        (when ai-auto-complete-mcp-debug-mode
          (insert "\n\nDebug Information:\n")
          (insert "=================\n")
          (insert "This section is only visible in debug mode.\n\n")

          ;; Show server registry information
          (insert "Server Registry Information:\n")
          (insert (format "  Number of registered servers: %d\n"
                         (hash-table-count ai-auto-complete-mcp-servers)))

          ;; Show server attributes
          (insert "\nServer Attributes:\n")
          (dolist (name (ai-auto-complete-mcp-list-servers))
            (let ((server (ai-auto-complete-mcp-get-server name)))
              (insert (format "  Server: %s\n" name))
              (insert "    Attributes:\n")
              (let ((attributes '(:path :transport :description :process :connection
                                       :capabilities :status :server-type :runner
                                       :command :args :env :server-path)))
                (dolist (attr attributes)
                  (let ((value (plist-get server attr)))
                    (when value
                      (insert (format "      %s: %S\n"
                                     (substring (symbol-name attr) 1)
                                     value))))))))

          ;; Add refresh button for debug info
          (insert "\n")
          (insert-button "Refresh Debug Info"
                        'action (lambda (_)
                                 (ai-auto-complete-mcp-list-servers-ui))
                        'follow-link t))

        (goto-char (point-min)))

    (switch-to-buffer buffer-name))))

;; Add a new MCP server
(defun ai-auto-complete-mcp-add-server-ui ()
  "Display a UI for adding a new MCP server."
  (interactive)

  (let* ((name (read-string "Server name: "))
         (path (read-file-name "Server path: " ai-auto-complete-mcp-servers-directory))
         (transport (completing-read "Transport: " '("stdio" "sse" "websocket" "grpc") nil t nil nil "stdio"))
         (description (read-string "Description: " (format "MCP server: %s" name))))

    ;; Register the server
    (ai-auto-complete-mcp-register-server
     name path
     (intern transport)
     description)

    (message "Added MCP server %s at %s" name path)

    ;; Refresh the servers UI if it's open
    (when (get-buffer "*MCP Servers*")
      (ai-auto-complete-mcp-list-servers-ui))))

;; Customize MCP settings
(defun ai-auto-complete-customize-mcp ()
  "Open the customization interface for MCP settings."
  (interactive)
  (customize-group 'ai-auto-complete-mcp))

;; Import MCP servers from a directory
(defun ai-auto-complete-mcp-import-server-ui ()
  "Display a UI for importing MCP servers from a directory."
  (interactive)

  (let ((directory (read-directory-name "Import MCP servers from directory: "
                                       ai-auto-complete-mcp-servers-directory))
        (recursive (y-or-n-p "Scan subdirectories recursively? ")))

    ;; Scan the directory
    (if recursive
        (ai-auto-complete-mcp-scan-subdirectories directory)
      (ai-auto-complete-mcp-scan-directory directory))

    ;; Refresh the servers UI if it's open
    (when (get-buffer "*MCP Servers*")
      (ai-auto-complete-mcp-list-servers-ui))))

;; Convert a tool to an MCP server
(defun ai-auto-complete-mcp-tool-to-server-ui ()
  "Display a UI for converting a tool to an MCP server."
  (interactive)

  (let* ((tool-names (completing-read-multiple
                     "Select tools to convert (comma-separated): "
                     (hash-table-keys ai-auto-complete-tools)))
         (server-name (read-string "Server name: "
                                  (if (= (length tool-names) 1)
                                      (format "tool-%s" (car tool-names))
                                    "multi-tool-server")))
         (directory (read-directory-name "Save server to: "
                                        ai-auto-complete-mcp-servers-directory)))

    ;; Export the tools as a server
    (ai-auto-complete-mcp-export-tools-as-server tool-names server-name directory)

    ;; Refresh the servers UI if it's open
    (when (get-buffer "*MCP Servers*")
      (ai-auto-complete-mcp-list-servers-ui))))

;; Convert an MCP server to a tool
(defun ai-auto-complete-mcp-server-to-tool-ui ()
  "Display a UI for converting an MCP server to a tool."
  (interactive)

  (let ((server-name (completing-read "Select MCP server to import as tool: "
                                     (ai-auto-complete-mcp-list-servers))))

    ;; Import the server as a tool
    (message "Importing MCP server %s as tool..." server-name)
    (ai-auto-complete-mcp-import-server-as-tool server-name)

    ;; Refresh the servers UI if it's open
    (when (get-buffer "*MCP Servers*")
      (ai-auto-complete-mcp-list-servers-ui))))

;; Display MCP server details
(defun ai-auto-complete-mcp-server-details (server-name)
  "Display detailed information about MCP server with SERVER-NAME."
  (interactive
   (list (completing-read "Select MCP server: " (ai-auto-complete-mcp-list-servers))))

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (message "MCP server %s not found" server-name)
      (let ((buffer-name (format "*MCP Server: %s*" server-name)))
        (with-current-buffer (get-buffer-create buffer-name)
          (let ((inhibit-read-only t))
            (erase-buffer)
            (special-mode) ;; Use special-mode for the buffer

            ;; Add header
            (insert (format "MCP Server: %s\n" server-name))
            (insert "=================\n\n")

            ;; Add server details
            (let ((status (plist-get server :status))
                  (transport (plist-get server :transport))
                  (path (plist-get server :path))
                  (description (plist-get server :description))
                  (capabilities (plist-get server :capabilities)))

              (insert (format "Status: %s\n" status))
              (insert (format "Transport: %s\n" transport))
              (insert (format "Path: %s\n" path))
              (insert (format "Description: %s\n\n" description))

              ;; Add capabilities if available
              (when capabilities
                (insert "Capabilities:\n")
                (insert (format "%S\n\n" capabilities)))

              ;; If the server is running, list its tools, resources, and prompts
              (when (eq status 'running)
                ;; List tools
                (insert "Tools:\n")
                (insert "------\n")
                (ai-auto-complete-mcp-list-tools
                 server-name
                 (lambda (tools)
                   (if (stringp tools)
                       (insert (format "Error: %s\n\n" tools))
                     (if (null tools)
                         (insert "No tools available.\n\n")
                       (dolist (tool tools)
                         (let ((tool-name (plist-get tool :name))
                               (description (plist-get tool :description))
                               (parameters (plist-get tool :parameters)))

                           (insert (format "Tool: %s\n" tool-name))
                           (insert (format "  Description: %s\n" description))
                           (insert "  Parameters:\n")

                           (dolist (param parameters)
                             (let ((param-name (plist-get param :name))
                                   (param-description (plist-get param :description))
                                   (param-required (plist-get param :required)))

                               (insert (format "    %s: %s%s\n"
                                              param-name
                                              param-description
                                              (if param-required " (required)" "")))))

                           (insert "\n")))))))

                ;; List resources
                (insert "Resources:\n")
                (insert "---------\n")
                (ai-auto-complete-mcp-list-resources
                 server-name
                 (lambda (resources)
                   (if (stringp resources)
                       (insert (format "Error: %s\n\n" resources))
                     (if (null resources)
                         (insert "No resources available.\n\n")
                       (dolist (resource resources)
                         (let ((uri (plist-get resource :uri))
                               (description (plist-get resource :description)))

                           (insert (format "Resource: %s\n" uri))
                           (insert (format "  Description: %s\n\n" description))))))))

                ;; List prompts
                (insert "Prompts:\n")
                (insert "-------\n")
                (ai-auto-complete-mcp-list-prompts
                 server-name
                 (lambda (prompts)
                   (if (stringp prompts)
                       (insert (format "Error: %s\n\n" prompts))
                     (if (null prompts)
                         (insert "No prompts available.\n\n")
                       (dolist (prompt prompts)
                         (let ((name (plist-get prompt :name))
                               (description (plist-get prompt :description))
                               (arguments (plist-get prompt :arguments)))

                           (insert (format "Prompt: %s\n" name))
                           (insert (format "  Description: %s\n" description))
                           (insert "  Arguments:\n")

                           (dolist (arg arguments)
                             (let ((arg-name (plist-get arg :name))
                                   (arg-description (plist-get arg :description))
                                   (arg-required (plist-get arg :required)))

                               (insert (format "    %s: %s%s\n"
                                              arg-name
                                              arg-description
                                              (if arg-required " (required)" "")))))

                           (insert "\n")))))))))))

          (goto-char (point-min)))

        (switch-to-buffer buffer-name))))

;; Add MCP menu items
(defun ai-auto-complete-mcp-add-menu-items ()
  "Add MCP menu items to the AI Auto Complete menu."
  (when (and (boundp 'ai-auto-complete-menu) ai-auto-complete-menu)
    (easy-menu-add-item ai-auto-complete-menu nil
                       '("MCP"
                         ["Enable MCP" ai-auto-complete-mcp-toggle
                          :style toggle :selected ai-auto-complete-mcp-enabled]
                         ["Use Improved Stdio Transport" (lambda ()
                                                        (interactive)
                                                        (setq ai-auto-complete-mcp-use-improved-stdio
                                                              (not ai-auto-complete-mcp-use-improved-stdio))
                                                        (message "Improved stdio transport %s"
                                                                 (if ai-auto-complete-mcp-use-improved-stdio "enabled" "disabled")))
                          :style toggle :selected ai-auto-complete-mcp-use-improved-stdio]
                         ["Use Simplified Stdio Transport" (lambda ()
                                                          (interactive)
                                                          (setq ai-auto-complete-mcp-use-simplified-stdio
                                                                (not ai-auto-complete-mcp-use-simplified-stdio))
                                                          (message "Simplified stdio transport %s"
                                                                   (if ai-auto-complete-mcp-use-simplified-stdio "enabled" "disabled")))
                          :style toggle :selected ai-auto-complete-mcp-use-simplified-stdio]

                         ;; Server Management submenu
                         ("Server Management"
                          ["Find Python Executable" ai-auto-complete-mcp-find-python-executable t]
                          ["List MCP Servers" ai-auto-complete-mcp-list-servers-ui t]
                          ["Add MCP Server" ai-auto-complete-mcp-add-server-ui t]
                          ["Import MCP Servers" ai-auto-complete-mcp-import-server-ui t]
                          ["Import Example MCPs" ai-auto-complete-mcp-import-examples t]
                          ["Create New Server" ai-auto-complete-mcp-create-new-server t]
                          "---"
                          ["Start All Servers" ai-auto-complete-mcp-start-all-servers t]
                          ["Stop All Servers" ai-auto-complete-mcp-stop-all-servers t]
                          ["Scan Directory" (lambda ()
                                             (interactive)
                                             (call-interactively 'ai-auto-complete-mcp-scan-directory)) t])

                         ;; Tool Integration submenu
                         ("Tool Integration"
                          ["Convert Tool to MCP Server" ai-auto-complete-mcp-tool-to-server-ui t]
                          ["Import MCP Server as Tool" ai-auto-complete-mcp-server-to-tool-ui t]
                          ["Import All Servers as Tools" ai-auto-complete-mcp-import-all-servers-as-tools t]
                          ["Import All Servers as Tools (Sync)" ai-auto-complete-mcp-import-all-servers-as-tools-sync t]
                          ["Register Tools Directly" ai-auto-complete-mcp-register-tools-directly t]
                          ["Register Tools with Simplified Approach" ai-auto-complete-mcp-simplified-register-tools-directly t]
                          ["Test Direct Tool Execution" (lambda ()
                                                       (interactive)
                                                       (when (fboundp 'ai-auto-complete-mcp-stdio-improved-direct-call-tool)
                                                         (let* ((server-name (completing-read "Server: " (mapcar #'car (ai-auto-complete-mcp-get-servers))))
                                                                (server (ai-auto-complete-mcp-get-server server-name))
                                                                (tool-name (completing-read "Tool: " '("hello" "add" "fetch_url")))
                                                                (params (cond
                                                                         ((string= tool-name "hello") '(("name" . "Emacs User")))
                                                                         ((string= tool-name "add") '(("a" . 5) ("b" . 7)))
                                                                         ((string= tool-name "fetch_url") '(("url" . "http://example.com"))))))
                                                           (message "Executing tool %s on server %s with params %s"
                                                                    tool-name server-name params)
                                                           (let ((result (ai-auto-complete-mcp-stdio-improved-direct-call-tool
                                                                          server-name tool-name params)))
                                                             (message "Result: %s" result)
                                                             (with-current-buffer (get-buffer-create "*MCP Direct Tool Result*")
                                                               (erase-buffer)
                                                               (insert (format "Tool: %s\nServer: %s\nParams: %S\n\nResult:\n%s"
                                                                               tool-name server-name params result))
                                                               (display-buffer (current-buffer))))))) t]
                          ["Verify Tools Registration" ai-auto-complete-mcp-verify-tools-registration t]
                          ["Refresh Tool Definitions" ai-auto-complete-mcp-refresh-tool-definitions t]
                          ["List Available Tools" ai-auto-complete-tools-list t]
                          ["Test Tool Integration with LLM" ai-auto-complete-mcp-test-tool-integration t])

                         ;; Resource Management submenu
                         ("Resource Management"
                          ["Subscribe to Resource" (lambda ()
                                                   (interactive)
                                                   (let* ((server-name (completing-read "Select MCP server: "
                                                                                      (ai-auto-complete-mcp-list-servers)))
                                                          (resource-uri (read-string "Resource URI: ")))
                                                     (ai-auto-complete-mcp-subscribe-to-resource
                                                      server-name resource-uri
                                                      (lambda (value)
                                                        (message "Resource %s changed: %s"
                                                                 resource-uri
                                                                 (substring value 0 (min 100 (length value)))))))) t])

                         ;; Agent Integration submenu
                         ("Agent Integration"
                          ["Create Agent from Server" (lambda ()
                                                      (interactive)
                                                      (let ((server-name (completing-read "Select MCP server: "
                                                                                         (ai-auto-complete-mcp-list-servers))))
                                                        (ai-auto-complete-mcp-create-agent-from-server server-name))) t]
                          ["Register Server with Agent" (lambda ()
                                                        (interactive)
                                                        (call-interactively 'ai-auto-complete-mcp-register-server-with-agent)) t]
                          ["Create Master Agent" ai-auto-complete-mcp-create-master-agent t])

                         ;; Examples & Sampling submenu
                         ("Examples & Sampling"
                          ["Generate Examples" ai-auto-complete-mcp-display-examples t]
                          ["Sample Tool" (lambda ()
                                         (interactive)
                                         (let* ((server-name (completing-read "Select MCP server: "
                                                                            (ai-auto-complete-mcp-list-servers)))
                                                (tools-promise (make-hash-table :test 'eq))
                                                (tool-name nil)
                                                (params nil))
                                           ;; Set up the promise
                                           (puthash 'status 'pending tools-promise)
                                           (puthash 'value nil tools-promise)

                                           ;; List tools
                                           (ai-auto-complete-mcp-list-tools
                                            server-name
                                            (lambda (tools)
                                              (puthash 'status 'fulfilled tools-promise)
                                              (puthash 'value tools tools-promise)))

                                           ;; Wait for the result (with timeout)
                                           (let ((timeout 5)
                                                 (start-time (current-time)))
                                             (while (and (eq (gethash 'status tools-promise) 'pending)
                                                         (< (float-time (time-since start-time)) timeout))
                                               (sleep-for 0.1))

                                             ;; Select a tool
                                             (let ((tools (gethash 'value tools-promise)))
                                               (if (or (not tools) (stringp tools))
                                                   (message "Error listing tools for server %s" server-name)
                                                 (let ((tool-names (mapcar (lambda (tool) (plist-get tool :name)) tools)))
                                                   (setq tool-name (completing-read "Select tool: " tool-names))
                                                   (setq params (read-string "Parameters (JSON object): " "{}"))
                                                   (ai-auto-complete-mcp-sample-tool server-name tool-name (json-read-from-string params)))))))) t]
                          ["Sample Resource" (lambda ()
                                            (interactive)
                                            (let* ((server-name (completing-read "Select MCP server: "
                                                                               (ai-auto-complete-mcp-list-servers)))
                                                   (resources-promise (make-hash-table :test 'eq))
                                                   (resource-uri nil))
                                              ;; Set up the promise
                                              (puthash 'status 'pending resources-promise)
                                              (puthash 'value nil resources-promise)

                                              ;; List resources
                                              (ai-auto-complete-mcp-list-resources
                                               server-name
                                               (lambda (resources)
                                                 (puthash 'status 'fulfilled resources-promise)
                                                 (puthash 'value resources resources-promise)))

                                              ;; Wait for the result (with timeout)
                                              (let ((timeout 5)
                                                    (start-time (current-time)))
                                                (while (and (eq (gethash 'status resources-promise) 'pending)
                                                            (< (float-time (time-since start-time)) timeout))
                                                  (sleep-for 0.1))

                                                ;; Select a resource
                                                (let ((resources (gethash 'value resources-promise)))
                                                  (if (or (not resources) (stringp resources))
                                                      (message "Error listing resources for server %s" server-name)
                                                    (let ((resource-uris (mapcar (lambda (resource) (plist-get resource :uri)) resources)))
                                                      (setq resource-uri (completing-read "Select resource: " resource-uris))
                                                      (ai-auto-complete-mcp-sample-resource server-name resource-uri))))))) t])

                         ;; Debugging & Troubleshooting submenu
                         ("Debugging & Troubleshooting"
                          ["Display Status" ai-auto-complete-mcp-display-status t]
                          ["Display Error Log" ai-auto-complete-mcp-display-error-log t]
                          ["Clear Error Log" ai-auto-complete-mcp-clear-error-log t]
                          ["Clear Caches" ai-auto-complete-mcp-clear-caches t])

                         ;; Installation & Setup submenu
                         ("Installation & Setup"
                          ["Set MCP Servers Directory" ai-auto-complete-mcp-set-servers-directory t]
                          ["Initialize with Custom Directory" (lambda ()
                                                             (interactive)
                                                             (when (fboundp 'ai-auto-complete-initialize-mcp-with-custom-directory)
                                                               (call-interactively 'ai-auto-complete-initialize-mcp-with-custom-directory))) t]
                          ["Install MCP SDK" (lambda ()
                                             (async-shell-command
                                              (format "cd %s && ./install-mcp-sdk.sh"
                                                      (expand-file-name "scripts"
                                                                       (file-name-directory
                                                                        (locate-library "mcp/mcp")))))) t])

                         ;; Settings submenu
                         ("Settings"
                          ["Customize MCP Settings" ai-auto-complete-customize-mcp t]
                          "---"
                          ["Debug Mode" ai-auto-complete-mcp-toggle-debug-mode
                           :style toggle :selected ai-auto-complete-mcp-debug-mode]
                          ["Enable Caching" ai-auto-complete-mcp-toggle-caching
                           :style toggle :selected ai-auto-complete-mcp-enable-caching]
                          ["Auto-Restart Servers" ai-auto-complete-mcp-toggle-auto-restart-servers
                           :style toggle :selected ai-auto-complete-mcp-auto-restart-servers]
                          ["Enable Context Integration" ai-auto-complete-mcp-toggle-context-integration
                           :style toggle :selected ai-auto-complete-mcp-enable-context-integration]
                          ["Enable Session Integration" ai-auto-complete-mcp-toggle-session-integration
                           :style toggle :selected ai-auto-complete-mcp-enable-session-integration])

                         "---"
                         ["Help" ai-auto-complete-mcp-help t]))))

;; Display MCP status
(defun ai-auto-complete-mcp-display-status ()
  "Display status of MCP servers."
  (interactive)
  (let ((buffer-name "*MCP Status*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert "MCP Status\n")
        (insert "==========\n\n")

        ;; Add global status
        (insert (format "MCP Enabled: %s\n" (if (and (boundp 'ai-auto-complete-mcp-enabled) ai-auto-complete-mcp-enabled) "Yes" "No")))
        (insert (format "Debug Mode: %s\n" (if (and (boundp 'ai-auto-complete-mcp-debug-mode) ai-auto-complete-mcp-debug-mode) "Enabled" "Disabled")))
        (insert (format "Caching: %s\n" (if (and (boundp 'ai-auto-complete-mcp-enable-caching) ai-auto-complete-mcp-enable-caching) "Enabled" "Disabled")))
        (insert (format "Auto-Restart Servers: %s\n" (if (and (boundp 'ai-auto-complete-mcp-auto-restart-servers) ai-auto-complete-mcp-auto-restart-servers) "Enabled" "Disabled")))
        (insert (format "Context Integration: %s\n" (if (and (boundp 'ai-auto-complete-mcp-enable-context-integration) ai-auto-complete-mcp-enable-context-integration) "Enabled" "Disabled")))
        (insert (format "Session Integration: %s\n" (if (and (boundp 'ai-auto-complete-mcp-enable-session-integration) ai-auto-complete-mcp-enable-session-integration) "Enabled" "Disabled")))
        (insert "\n")

        ;; Add server status
        (insert "Server Status\n")
        (insert "------------\n\n")

        (if (and (boundp 'ai-auto-complete-mcp-servers) ai-auto-complete-mcp-servers)
            (if (hash-table-empty-p ai-auto-complete-mcp-servers)
                (insert "No MCP servers registered.\n")
              (maphash (lambda (name server)
                         (insert (format "Server: %s\n" name))
                         (insert (format "  Path: %s\n" (plist-get server :path)))
                         (insert (format "  Transport: %s\n" (plist-get server :transport)))
                         (insert (format "  Status: %s\n" (plist-get server :status)))
                         (insert "\n"))
                       ai-auto-complete-mcp-servers))
          (insert "MCP servers not initialized.\n"))

        ;; Add buttons for actions
        (insert "Actions: ")
        (insert-button "Refresh"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-display-status))
                      'follow-link t)

        (insert " | ")
        (insert-button "Start All Servers"
                      'action (lambda (_)
                               (when (fboundp 'ai-auto-complete-mcp-start-all-servers)
                                 (ai-auto-complete-mcp-start-all-servers)
                                 (ai-auto-complete-mcp-display-status)))
                      'follow-link t)

        (insert " | ")
        (insert-button "Stop All Servers"
                      'action (lambda (_)
                               (when (fboundp 'ai-auto-complete-mcp-stop-all-servers)
                                 (ai-auto-complete-mcp-stop-all-servers)
                                 (ai-auto-complete-mcp-display-status)))
                      'follow-link t)

        (goto-char (point-min))))

    (switch-to-buffer buffer-name)))

;; Clear MCP caches
(defun ai-auto-complete-mcp-clear-caches ()
  "Clear all MCP caches."
  (interactive)
  (when (and (boundp 'ai-auto-complete-mcp-tool-cache) ai-auto-complete-mcp-tool-cache)
    (clrhash ai-auto-complete-mcp-tool-cache))
  (when (and (boundp 'ai-auto-complete-mcp-resource-cache) ai-auto-complete-mcp-resource-cache)
    (clrhash ai-auto-complete-mcp-resource-cache))
  (when (and (boundp 'ai-auto-complete-mcp-prompt-cache) ai-auto-complete-mcp-prompt-cache)
    (clrhash ai-auto-complete-mcp-prompt-cache))
  (message "Cleared all MCP caches"))

;; Display help for MCP
(defun ai-auto-complete-mcp-help ()
  "Display help for MCP."
  (interactive)
  (let ((buffer-name "*MCP Help*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert "MCP Help\n")
        (insert "========\n\n")

        ;; Add help text
        (insert "MCP (Model Context Protocol) is an open protocol developed by Anthropic that standardizes how applications provide context to LLMs.\n\n")

        ;; Add sections
        (insert "Servers\n")
        (insert "-------\n\n")
        (insert "MCP servers are Python scripts that implement the MCP protocol. They provide tools, resources, and prompts that can be used by AI Auto Complete.\n\n")

        ;; Add more sections
        (insert "Tools\n")
        (insert "-----\n\n")
        (insert "Tools are functions that can be called by AI Auto Complete. They can be used to perform actions like searching the web, running code, or accessing external data.\n\n")

        (insert "Resources\n")
        (insert "---------\n\n")
        (insert "Resources are data sources that can be accessed by AI Auto Complete. They can be used to provide context to the LLM.\n\n")

        (insert "Prompts\n")
        (insert "-------\n\n")
        (insert "Prompts are templates that can be used to generate text. They can be used to provide instructions to the LLM.\n\n")

        ;; Add links
        (insert "Links\n")
        (insert "-----\n\n")
        (insert "- MCP Documentation: https://docs.anthropic.com/claude/docs/model-context-protocol\n")
        (insert "- MCP GitHub Repository: https://github.com/anthropics/anthropic-sdk-python\n")

        (goto-char (point-min))))

    (switch-to-buffer buffer-name)))

(provide 'mcp/mcp-ui)
;;; mcp-ui.el ends here
