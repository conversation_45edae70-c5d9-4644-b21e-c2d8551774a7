;;; mcp-persistence.el --- Persistence for MCP servers -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides functions to save and load MCP server configurations.

;;; Code:

(require 'mcp/mcp-core)
(require 'json)
(require 'cl-lib)

;; Define the path to the MCP servers configuration file
(defvar ai-auto-complete-mcp-servers-config-file
  (expand-file-name "mcp-servers-config.json" user-emacs-directory)
  "Path to the MCP servers configuration file.")

;; Save MCP servers to the configuration file
(defun ai-auto-complete-mcp-save-servers ()
  "Save all MCP server configurations to the configuration file."
  (interactive)
  (let ((servers-data (make-hash-table :test 'equal))
        (servers (ai-auto-complete-mcp-list-servers)))

    ;; Collect server data
    (dolist (server-name servers)
      (let* ((server (ai-auto-complete-mcp-get-server server-name))
             (server-data (make-hash-table :test 'equal)))

        ;; Add basic server properties
        (puthash "path" (plist-get server :path) server-data)
        (puthash "transport" (if (symbolp (plist-get server :transport))
                                   (symbol-name (plist-get server :transport))
                                 (plist-get server :transport))
                 server-data)
        (puthash "description" (plist-get server :description) server-data)
        (puthash "status" (if (symbolp (plist-get server :status))
                                 (symbol-name (plist-get server :status))
                               (or (plist-get server :status) "unknown"))
               server-data)
        (puthash "server-type" (if (plist-get server :server-type)
                                   (if (symbolp (plist-get server :server-type))
                                       (symbol-name (plist-get server :server-type))
                                     (plist-get server :server-type))
                                 "unknown")
                 server-data)
        (puthash "runner" (plist-get server :runner) server-data)
        (puthash "api-key" (plist-get server :api-key) server-data)
        (puthash "serverArgs" (plist-get server :serverArgs) server-data)

        ;; Add command, args, and env if present
        (when (plist-get server :command)
          (puthash "command" (plist-get server :command) server-data))
        (when (plist-get server :args)
          (puthash "args" (plist-get server :args) server-data))
        (when (plist-get server :env)
          (puthash "env" (plist-get server :env) server-data))

        ;; Add tools, resources, and prompts if present
        (when (plist-get server :tools)
          (let ((tools (plist-get server :tools)))
            ;; Convert tools to a format that preserves the original structure
            (puthash "tools" (ai-auto-complete-mcp-convert-tools-for-saving tools server-name) server-data)))

        (when (plist-get server :resources)
          (puthash "resources" (plist-get server :resources) server-data))
        (when (plist-get server :prompts)
          (puthash "prompts" (plist-get server :prompts) server-data))

        ;; Add the server data to the servers hash
        (puthash server-name server-data servers-data)))

    ;; Debug output for troubleshooting
    (when ai-auto-complete-mcp-debug-mode
      (maphash (lambda (server-name server-data)
                 (message "Saving server %s with type: %s, transport: %s, status: %s"
                          server-name
                          (gethash "server-type" server-data "unknown")
                          (gethash "transport" server-data "unknown")
                          (gethash "status" server-data "unknown")))
               servers-data))

    ;; Save to file
    (let ((json-object-type 'hash-table)
          (json-array-type 'list)
          (json-key-type 'string))
      (with-temp-file ai-auto-complete-mcp-servers-config-file
        (insert (json-encode servers-data))))

    (message "Saved %d MCP server configurations to %s"
             (hash-table-count servers-data)
             ai-auto-complete-mcp-servers-config-file)))

;; Load MCP servers from the configuration file
(defun ai-auto-complete-mcp-load-servers ()
  "Load MCP server configurations from the configuration file."
  (interactive)
  (when (file-exists-p ai-auto-complete-mcp-servers-config-file)
    (let ((loaded-count 0))
      (condition-case err
          (let ((json-object-type 'hash-table)
                (json-array-type 'list)
                (json-key-type 'string))
            (with-temp-buffer
              (insert-file-contents ai-auto-complete-mcp-servers-config-file)
              (let* ((servers-data (json-read))
                     (server-names (hash-table-keys servers-data)))

                ;; Register each server
                (dolist (server-name server-names)
                  (let* ((server-data (gethash server-name servers-data))
                         (path (gethash "path" server-data))
                         (transport-str (gethash "transport" server-data))
                         (transport (if transport-str
                                        (if (string-match-p "^[a-zA-Z0-9-]+$" transport-str)
                                            (intern transport-str)
                                          transport-str)
                                      'stdio))
                         (description (gethash "description" server-data))
                         (server-type (gethash "server-type" server-data))
                         (runner (gethash "runner" server-data))
                         (api-key (gethash "api-key" server-data))
                         (server-args (gethash "serverArgs" server-data))
                         (tools (gethash "tools" server-data))
                         (resources (gethash "resources" server-data))
                         (prompts (gethash "prompts" server-data))
                         (command (gethash "command" server-data))
                         (args (gethash "args" server-data))
                         (env (gethash "env" server-data)))

                    ;; Debug output for tools before conversion
                    (when ai-auto-complete-mcp-debug-mode
                      (message "Tools for server %s before conversion: %S" server-name tools))

                    ;; Convert tools to list format using our helper function
                    (let ((processed-tools (ai-auto-complete-mcp-convert-tools-to-list tools server-name)))
                      ;; Debug output for troubleshooting
                      (when ai-auto-complete-mcp-debug-mode
                        (message "Loading server %s with type: %s, transport: %s, runner: %s"
                                 server-name
                                 (or server-type "unknown")
                                 (if (symbolp transport) (symbol-name transport) transport)
                                 (or runner "unknown"))
                        (message "Processed tools for server %s: %S" server-name processed-tools)
                        (message "Number of tools for server %s: %d"
                                 server-name
                                 (if (listp processed-tools) (length processed-tools) 0)))

                      ;; Register the server with processed tools
                      (ai-auto-complete-mcp-register-server
                       server-name path transport description server-type runner processed-tools resources prompts api-key))

                    ;; Add additional properties if present
                    (when (or command args env)
                      (let ((server (ai-auto-complete-mcp-get-server server-name)))
                        (when server
                          (when command
                            (puthash server-name
                                     (plist-put server :command command)
                                     ai-auto-complete-mcp-servers))
                          (when args
                            (puthash server-name
                                     (plist-put server :args args)
                                     ai-auto-complete-mcp-servers))
                          (when env
                            (puthash server-name
                                     (plist-put server :env env)
                                     ai-auto-complete-mcp-servers)))))

                    (setq loaded-count (1+ loaded-count))))

                (message "Loaded %d MCP server configurations from %s"
                         loaded-count
                         ai-auto-complete-mcp-servers-config-file))))
        (error
         (message "Error loading MCP server configurations: %s" (error-message-string err))
         nil))
      loaded-count)))

;; Auto-save MCP servers when registering or modifying
(defun ai-auto-complete-mcp-auto-save-servers ()
  "Automatically save MCP server configurations after changes."
  (when (boundp 'ai-auto-complete-mcp-auto-save-enabled)
    (when ai-auto-complete-mcp-auto-save-enabled
      (ai-auto-complete-mcp-save-servers))))

;; Add advice to save servers after registration
(advice-add 'ai-auto-complete-mcp-register-server :after
            (lambda (&rest _) (ai-auto-complete-mcp-auto-save-servers)))

;; Add advice to save servers after removal
(advice-add 'ai-auto-complete-mcp-remove-server :after
            (lambda (&rest _) (ai-auto-complete-mcp-auto-save-servers)))

;; Helper function to convert a parameter list to a proper plist
(defun ai-auto-complete-mcp-convert-parameter-to-plist (param)
  "Convert PARAM to a proper plist format.
If PARAM is already a plist, return it as is.
If PARAM is a list of strings, convert it to a plist.
If PARAM is a hash-table, convert it to a plist."
  (cond
   ;; If param is nil, return nil
   ((null param)
    nil)

   ;; If param is already a plist (has keyword symbols), return it as is
   ((and (listp param)
         (> (length param) 0)
         (keywordp (car param)))
    param)

   ;; If param is a list but not a plist, convert it to a plist
   ((listp param)
    (let ((result nil)
          (current-key nil))
      (dolist (item param)
        (if current-key
            (progn
              ;; Convert boolean values
              (let ((value (cond
                            ((and (stringp item) (string= item "true")) t)
                            ((and (stringp item) (string= item "false")) nil)
                            (t item))))
                (setq result (plist-put result
                                       (intern (concat ":" current-key))
                                       value))
                (setq current-key nil)))
          (setq current-key item)))
      result))

   ;; If param is a hash-table, convert it to a plist
   ((hash-table-p param)
    (let ((result nil))
      (maphash (lambda (key value)
                 (setq result (plist-put result
                                        (intern (concat ":" key))
                                        value)))
               param)
      result))

   ;; For any other type, return nil
   (t
    nil)))

;; Convert tools from hash-table to list format
(defun ai-auto-complete-mcp-convert-tools-to-list (tools server-name)
  "Convert TOOLS to a list format for SERVER-NAME.
If TOOLS is already a list, return it as is.
If TOOLS is a hash-table, convert it to a list of plists.
For any other type, return nil and log a warning."
  (cond
   ;; If tools is nil, return nil
   ((null tools)
    nil)

   ;; If tools is a list of plists (each element starts with a keyword), return it as is
   ((and (listp tools)
         (> (length tools) 0)
         (listp (car tools))
         (> (length (car tools)) 0)
         (keywordp (car (car tools))))
    tools)

   ;; If tools is a list but not a list of plists, try to convert it
   ((listp tools)
    (let ((converted-tools nil))
      (if (and (> (length tools) 0) (stringp (car tools)))
          ;; This might be a flat list like ("name" "search" "description" "search the internet")
          ;; Try to convert it to a plist
          (let ((tool-plist (ai-auto-complete-mcp-convert-parameter-to-plist tools)))
            (if tool-plist
                (list tool-plist)
              (progn
                (message "Warning: Could not convert tool list to plist format: %S" tools)
                nil)))
        ;; Otherwise, try to convert each element
        (dolist (tool tools)
          (cond
           ;; If tool is a list and starts with a keyword, it's already a plist
           ((and (listp tool) (> (length tool) 0) (keywordp (car tool)))
            (push tool converted-tools))

           ;; If tool is a list, try to convert it to a plist
           ((listp tool)
            (let ((tool-plist (ai-auto-complete-mcp-convert-parameter-to-plist tool)))
              (if tool-plist
                  (push tool-plist converted-tools)
                (message "Warning: Could not convert tool to plist format: %S" tool))))

           ;; If tool is a hash-table, convert it to a plist
           ((hash-table-p tool)
            (when ai-auto-complete-mcp-debug-mode
              (message "Converting hash-table tool in list for server %s" server-name))
            (let* ((name (gethash "name" tool nil))
                   (description (gethash "description" tool "No description"))
                   (parameters (gethash "parameters" tool nil))
                   (input-schema (gethash "inputSchema" tool nil))
                   (tool-plist (list :name (or name "unknown")
                                    :description description)))

              ;; Process parameters if present
              (when parameters
                (cond
                 ;; If parameters is a hash-table, convert it
                 ((hash-table-p parameters)
                  (let ((params-list '()))
                    (maphash (lambda (param-name param-data)
                               (let ((param-plist (list :name param-name)))
                                 (when (hash-table-p param-data)
                                   (maphash (lambda (key value)
                                              (unless (string= key "name")
                                                (setq param-plist (plist-put param-plist
                                                                           (intern (concat ":" key))
                                                                           value))))
                                            param-data))
                                 (push param-plist params-list)))
                             parameters)
                    (setq tool-plist (plist-put tool-plist :parameters (nreverse params-list)))))

                 ;; If parameters is a list, process it
                 ((listp parameters)
                  (let ((processed-params '()))
                    (dolist (param parameters)
                      (if (hash-table-p param)
                          (let ((param-name (gethash "name" param nil))
                                (param-plist nil))
                            (when param-name
                              (setq param-plist (list :name param-name))
                              (maphash (lambda (key value)
                                         (unless (string= key "name")
                                           (setq param-plist (plist-put param-plist
                                                                      (intern (concat ":" key))
                                                                      value))))
                                       param)
                              (push param-plist processed-params)))
                        (message "Warning: Parameter is not a hash-table: %S" param)))
                    (when processed-params
                      (setq tool-plist (plist-put tool-plist :parameters (nreverse processed-params))))))))

              ;; Add inputSchema if present
              (when input-schema
                (setq tool-plist (plist-put tool-plist :inputSchema input-schema)))

              ;; Add any other properties
              (maphash (lambda (key value)
                         (unless (member key '("name" "description" "parameters" "inputSchema"))
                           (setq tool-plist (plist-put tool-plist
                                                     (intern (concat ":" key))
                                                     value))))
                       tool)

              (push tool-plist converted-tools)))

           ;; Otherwise, log a warning
           (t
            (message "Warning: Tool is not a list or hash-table: %S" tool))))
        (nreverse converted-tools))))

   ;; If tools is a hash table, convert it to a list
   ((hash-table-p tools)
    (let ((tools-list '()))
      ;; Debug output to help diagnose the issue
      (when ai-auto-complete-mcp-debug-mode
        (message "Converting hash-table tools for server %s with %d keys"
                 server-name (hash-table-count tools)))

      (maphash (lambda (name tool-data)
                 ;; Debug output for each tool
                 (when ai-auto-complete-mcp-debug-mode
                   (message "  - Processing tool %s (type: %s)" name (type-of tool-data)))

                 (cond
                  ;; Special case for the format in the JSON file: ["description", "desc text", "parameters", "name", "name"]
                  ((and (listp tool-data)
                        (> (length tool-data) 1)
                        (stringp (car tool-data))
                        (string= (car tool-data) "description"))
                   (let* ((description (if (> (length tool-data) 1) (nth 1 tool-data) "No description"))
                          (parameters-index (cl-position "parameters" tool-data :test #'string=))
                          (parameters (when parameters-index
                                        (let ((param-data (nthcdr (1+ parameters-index) tool-data))
                                              (param-list '()))
                                          (when (and param-data (> (length param-data) 0))
                                            ;; Create a parameter plist from the remaining items
                                            (let ((param-plist (list :name (car param-data))))
                                              (push param-plist param-list)))
                                          param-list)))
                          (tool-plist (list :name name
                                           :description description)))

                     ;; Add parameters if we found any
                     (when parameters
                       (setq tool-plist (plist-put tool-plist :parameters parameters)))

                     (push tool-plist tools-list)
                     (when ai-auto-complete-mcp-debug-mode
                       (message "    - Added tool %s from special format list" name))))

                  ;; If tool-data is a list, ensure it's a proper plist
                  ((listp tool-data)
                   (let ((tool-plist (ai-auto-complete-mcp-convert-parameter-to-plist tool-data)))
                     (if tool-plist
                         (let ((tool-with-name (if (plist-member tool-plist :name)
                                                  tool-plist
                                                (plist-put (copy-sequence tool-plist) :name name))))
                           (push tool-with-name tools-list)
                           (when ai-auto-complete-mcp-debug-mode
                             (message "    - Added tool %s from list data" name)))
                       ;; If conversion failed, create a basic tool plist
                       (push (list :name name
                                  :description "No description"
                                  :parameters nil)
                             tools-list)
                       (when ai-auto-complete-mcp-debug-mode
                         (message "    - Added basic tool %s after conversion failure" name)))))

                  ;; If tool-data is not a list, create a proper tool plist
                  (t
                   (when ai-auto-complete-mcp-debug-mode
                     (message "    - Converting tool %s for server %s (type: %s)"
                              name server-name (type-of tool-data)))
                   (let ((description "No description")
                         (tool-plist (list :name name
                                          :description description)))
                     (push tool-plist tools-list))))
               tools)

      ;; Debug output for the final result
      (when ai-auto-complete-mcp-debug-mode
        (message "Converted %d tools for server %s" (length tools-list) server-name))

      (nreverse tools-list)))

   ;; For any other type, log a warning and return nil
   (t
    (message "Warning: Tools data for server %s is in an unexpected format: %s"
             server-name (type-of tools))
    nil))))

;; Convert tools to a format suitable for saving to JSON
(defun ai-auto-complete-mcp-convert-tools-for-saving (tools server-name)
  "Convert TOOLS to a format suitable for saving to JSON for SERVER-NAME.
This function preserves the original structure of the tools as much as possible."
  ;; Debug output
  (when ai-auto-complete-mcp-debug-mode
    (message "Converting tools for saving for server %s: %S" server-name tools))
  (cond
   ;; If tools is nil, return nil
   ((null tools)
    nil)

   ;; If tools is a list of plists, convert it to a hash table
   ((and (listp tools)
         (> (length tools) 0)
         (listp (car tools))
         (> (length (car tools)) 0)
         (keywordp (car (car tools))))
    (let ((tools-hash (make-hash-table :test 'equal)))
      (dolist (tool tools)
        (let* ((name (plist-get tool :name))
               (description (plist-get tool :description))
               (parameters (plist-get tool :parameters))
               (tool-data (list "description" description)))

          ;; Debug output
          (when ai-auto-complete-mcp-debug-mode
            (message "  - Processing tool %s with %d parameters"
                     name (if (listp parameters) (length parameters) 0)))

          ;; Add parameters if present
          (when parameters
            (setq tool-data (append tool-data (list "parameters")))
            (dolist (param parameters)
              (let ((param-name (plist-get param :name)))
                (when param-name
                  (setq tool-data (append tool-data (list param-name)))
                  (when ai-auto-complete-mcp-debug-mode
                    (message "    - Added parameter %s" param-name))))))

          ;; Add the tool to the hash table
          (puthash name tool-data tools-hash)
          (when ai-auto-complete-mcp-debug-mode
            (message "  - Added tool %s to hash table" name))))

      ;; Debug output for the final result
      (when ai-auto-complete-mcp-debug-mode
        (message "Converted %d tools for server %s for saving" (hash-table-count tools-hash) server-name))

      tools-hash))

   ;; For any other type, just return it as is
   (t
    tools)))

;; Test function to verify tools conversion
(defun ai-auto-complete-mcp-test-tools-conversion ()
  "Test the conversion of tools from hash-table to list format."
  (interactive)
  (message "Testing MCP server tools conversion...")

  ;; Test 1: Hash table with nested hash tables
  (let ((test-tools (make-hash-table :test 'equal)))
    ;; Add a tool with parameters
    (puthash "search"
             (let ((tool-data (make-hash-table :test 'equal)))
               (puthash "description" "search the internet" tool-data)
               (puthash "parameters"
                        (let ((params (make-hash-table :test 'equal)))
                          (puthash "name"
                                   (list "query"
                                         "description" "search term to search"
                                         "required" t)
                                   params)
                          params)
                        tool-data)
               tool-data)
             test-tools)

    ;; Convert the tools to list format
    (let ((converted-tools (ai-auto-complete-mcp-convert-tools-to-list test-tools "test-server")))
      ;; Display the original and converted tools
      (message "Test 1 - Original tools (hash-table): %S" test-tools)
      (message "Test 1 - Converted tools (list): %S" converted-tools)

      ;; Verify the conversion
      (when (and (listp converted-tools)
                 (= (length converted-tools) 1)
                 (let ((tool (car converted-tools)))
                   (and (listp tool)
                        (string= (plist-get tool :name) "search")
                        (string= (plist-get tool :description) "search the internet")
                        (listp (plist-get tool :parameters)))))
        (message "Test 1 - Conversion successful!"))))

  ;; Test 2: List with strings (not a proper plist)
  (let ((test-tools (list "search" "description" "search the internet" "parameters"
                          (let ((params (make-hash-table :test 'equal)))
                            (puthash "name"
                                     (list "query"
                                           "description" "search term to search"
                                           "required" t)
                                     params)
                            params))))

    ;; Convert the tools to list format
    (let ((converted-tools (ai-auto-complete-mcp-convert-tools-to-list test-tools "test-server")))
      ;; Display the original and converted tools
      (message "Test 2 - Original tools (list with strings): %S" test-tools)
      (message "Test 2 - Converted tools (list): %S" converted-tools)

      ;; Verify the conversion
      (when (and (listp converted-tools)
                 (= (length converted-tools) 1)
                 (let ((tool (car converted-tools)))
                   (and (listp tool)
                        (plist-get tool :search)
                        (plist-get tool :description)
                        (plist-get tool :parameters))))
        (message "Test 2 - Conversion successful!"))))

  ;; Test 3: Parameter list conversion
  (let ((param-list (list "query" "description" "search term to search" "required" t)))

    ;; Convert the parameter to plist format
    (let ((converted-param (ai-auto-complete-mcp-convert-parameter-to-plist param-list)))
      ;; Display the original and converted parameter
      (message "Test 3 - Original parameter (list): %S" param-list)
      (message "Test 3 - Converted parameter (plist): %S" converted-param)

      ;; Verify the conversion
      (when (and (listp converted-param)
                 (plist-get converted-param :query)
                 (plist-get converted-param :description)
                 (plist-get converted-param :required))
        (message "Test 3 - Conversion successful!"))))

  ;; Test 4: Converting tools for saving
  (let* ((tool1 (list :name "search"
                     :description "search the internet"
                     :parameters (list (list :name "query"
                                            :description "search term to search"
                                            :required t))))
         (tool2 (list :name "text_to_sound_effects"
                     :description "Convert text description of a sound effect to sound effect with a given duration."
                     :parameters (list (list :name "name"))))
         (tools (list tool1 tool2)))

    ;; Convert the tools for saving
    (let ((saved-tools (ai-auto-complete-mcp-convert-tools-for-saving tools "test-server")))
      ;; Display the original and saved tools
      (message "Test 4 - Original tools (list of plists): %S" tools)
      (message "Test 4 - Saved tools (hash-table): %S" saved-tools)

      ;; Verify the conversion
      (when (and (hash-table-p saved-tools)
                 (= (hash-table-count saved-tools) 2)
                 (let ((search-tool (gethash "search" saved-tools nil))
                       (sound-tool (gethash "text_to_sound_effects" saved-tools nil)))
                   (and search-tool sound-tool
                        (listp search-tool)
                        (listp sound-tool)
                        (string= (nth 0 search-tool) "description")
                        (string= (nth 1 search-tool) "search the internet")
                        (string= (nth 2 search-tool) "parameters")
                        (string= (nth 3 search-tool) "query")
                        (string= (nth 0 sound-tool) "description")
                        (string= (nth 1 sound-tool) "Convert text description of a sound effect to sound effect with a given duration.")
                        (string= (nth 2 sound-tool) "parameters")
                        (string= (nth 3 sound-tool) "name"))))
        (message "Test 4 - Conversion successful!")))))

;; Initialize persistence
(defun ai-auto-complete-mcp-init-persistence ()
  "Initialize MCP server persistence."
  (interactive)
  ;; Load servers from configuration file
  (ai-auto-complete-mcp-load-servers))

(provide 'mcp/mcp-persistence)
;;; mcp-persistence.el ends here
