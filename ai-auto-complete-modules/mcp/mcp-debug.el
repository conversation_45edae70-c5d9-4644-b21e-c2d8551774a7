;;; mcp-debug.el --- Debugging utilities for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides debugging utilities for the MCP integration in AI Auto Complete.
;; It helps diagnose issues with MCP servers, transports, and protocol handling.

;;; Code:

;; Debug buffer name
(defvar ai-auto-complete-mcp-debug-buffer "*MCP Debug*"
  "Name of the MCP debug buffer.")

;; Debug mode flag
(defvar ai-auto-complete-mcp-debug-mode nil
  "Non-nil means MCP debugging is enabled.")

;; Toggle debug mode
(defun ai-auto-complete-mcp-toggle-debug-mode ()
  "Toggle MCP debug mode."
  (interactive)
  (setq ai-auto-complete-mcp-debug-mode (not ai-auto-complete-mcp-debug-mode))
  (message "MCP debug mode %s" (if ai-auto-complete-mcp-debug-mode "enabled" "disabled")))

;; Debug levels
(defvar ai-auto-complete-mcp-debug-level-info 0
  "Debug level for informational messages.")

(defvar ai-auto-complete-mcp-debug-level-warning 1
  "Debug level for warning messages.")

(defvar ai-auto-complete-mcp-debug-level-error 2
  "Debug level for error messages.")

;; Current debug level
(defvar ai-auto-complete-mcp-debug-level 0
  "Current debug level for MCP debugging.")

;; Minimal debug functions
(defun ai-auto-complete-mcp-debug-info (format-string &rest args)
  "Log an info debug message with FORMAT-STRING and ARGS."
  (when ai-auto-complete-mcp-debug-mode
    (apply #'message (concat "[MCP INFO] " format-string) args)))

(defun ai-auto-complete-mcp-debug-warning (format-string &rest args)
  "Log a warning debug message with FORMAT-STRING and ARGS."
  (when ai-auto-complete-mcp-debug-mode
    (apply #'message (concat "[MCP WARNING] " format-string) args)))

(defun ai-auto-complete-mcp-debug-error (format-string &rest args)
  "Log an error debug message with FORMAT-STRING and ARGS."
  (when ai-auto-complete-mcp-debug-mode
    (apply #'message (concat "[MCP ERROR] " format-string) args)))

;; Initialize debugging
(defun ai-auto-complete-mcp-debug-initialize ()
  "Initialize MCP debugging."
  (message "MCP debugging module loaded"))

;; Defer initialization to after Emacs is fully initialized
(add-hook 'after-init-hook 'ai-auto-complete-mcp-debug-initialize)

(provide 'mcp/mcp-debug)
;;; mcp-debug.el ends here
