;;; mcp-client.el --- MCP client interface for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the client interface for MCP integration in the AI Auto Complete package.
;; It handles communication with MCP servers for calling tools, reading resources, and getting prompts.

;;; Code:

(require 'mcp/mcp-core)

;; Forward declarations to avoid circular dependencies
(declare-function ai-auto-complete-mcp-stdio-call-tool "mcp/transports/mcp-stdio")
(declare-function ai-auto-complete-mcp-stdio-read-resource "mcp/transports/mcp-stdio")
(declare-function ai-auto-complete-mcp-stdio-get-prompt "mcp/transports/mcp-stdio")
(declare-function ai-auto-complete-mcp-stdio-list-tools "mcp/transports/mcp-stdio")
(declare-function ai-auto-complete-mcp-stdio-list-resources "mcp/transports/mcp-stdio")
(declare-function ai-auto-complete-mcp-stdio-list-prompts "mcp/transports/mcp-stdio")

;; Forward declarations for simplified stdio transport
(declare-function ai-auto-complete-mcp-stdio-simple-call-tool "mcp/transports/mcp-stdio-simple")
(declare-function ai-auto-complete-mcp-stdio-simple-read-resource "mcp/transports/mcp-stdio-simple")
(declare-function ai-auto-complete-mcp-stdio-simple-get-prompt "mcp/transports/mcp-stdio-simple")
(declare-function ai-auto-complete-mcp-stdio-simple-list-tools "mcp/transports/mcp-stdio-simple")
(declare-function ai-auto-complete-mcp-stdio-simple-list-resources "mcp/transports/mcp-stdio-simple")
(declare-function ai-auto-complete-mcp-stdio-simple-list-prompts "mcp/transports/mcp-stdio-simple")

;; Forward declarations for improved stdio transport
(declare-function ai-auto-complete-mcp-stdio-improved-call-tool "mcp/transports/mcp-stdio-improved")
(declare-function ai-auto-complete-mcp-stdio-improved-read-resource "mcp/transports/mcp-stdio-improved")
(declare-function ai-auto-complete-mcp-stdio-improved-get-prompt "mcp/transports/mcp-stdio-improved")
(declare-function ai-auto-complete-mcp-stdio-improved-list-tools "mcp/transports/mcp-stdio-improved")
(declare-function ai-auto-complete-mcp-stdio-improved-list-resources "mcp/transports/mcp-stdio-improved")
(declare-function ai-auto-complete-mcp-stdio-improved-list-prompts "mcp/transports/mcp-stdio-improved")
(declare-function ai-auto-complete-mcp-stdio-improved-direct-call-tool "mcp/transports/mcp-stdio-improved")

(declare-function ai-auto-complete-mcp-sse-call-tool "mcp/transports/mcp-sse")
(declare-function ai-auto-complete-mcp-sse-read-resource "mcp/transports/mcp-sse")
(declare-function ai-auto-complete-mcp-sse-get-prompt "mcp/transports/mcp-sse")
(declare-function ai-auto-complete-mcp-sse-list-tools "mcp/transports/mcp-sse")
(declare-function ai-auto-complete-mcp-sse-list-resources "mcp/transports/mcp-sse")
(declare-function ai-auto-complete-mcp-sse-list-prompts "mcp/transports/mcp-sse")

(declare-function ai-auto-complete-mcp-websocket-call-tool "mcp/transports/mcp-websocket")
(declare-function ai-auto-complete-mcp-websocket-read-resource "mcp/transports/mcp-websocket")
(declare-function ai-auto-complete-mcp-websocket-get-prompt "mcp/transports/mcp-websocket")
(declare-function ai-auto-complete-mcp-websocket-list-tools "mcp/transports/mcp-websocket")
(declare-function ai-auto-complete-mcp-websocket-list-resources "mcp/transports/mcp-websocket")
(declare-function ai-auto-complete-mcp-websocket-list-prompts "mcp/transports/mcp-websocket")

(declare-function ai-auto-complete-mcp-grpc-call-tool "mcp/transports/mcp-grpc")
(declare-function ai-auto-complete-mcp-grpc-read-resource "mcp/transports/mcp-grpc")
(declare-function ai-auto-complete-mcp-grpc-get-prompt "mcp/transports/mcp-grpc")
(declare-function ai-auto-complete-mcp-grpc-list-tools "mcp/transports/mcp-grpc")
(declare-function ai-auto-complete-mcp-grpc-list-resources "mcp/transports/mcp-grpc")
(declare-function ai-auto-complete-mcp-grpc-list-prompts "mcp/transports/mcp-grpc")

;; Generate a unique request ID
(defvar ai-auto-complete-mcp-request-id 0
  "Counter for generating unique request IDs.")

(defun ai-auto-complete-mcp-generate-request-id ()
  "Generate a unique request ID for MCP communication."
  (setq ai-auto-complete-mcp-request-id (1+ ai-auto-complete-mcp-request-id))
  (format "req-%d" ai-auto-complete-mcp-request-id))

;; Call an MCP tool
(defun ai-auto-complete-mcp-call-tool (server-name tool-name params callback)
  "Call TOOL-NAME on SERVER-NAME with PARAMS and process result with CALLBACK.
PARAMS should be an alist of parameter names and values."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-call-tool nil))

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((status (plist-get server :status))
            (transport (plist-get server :transport)))
        (if (not (eq status 'running))
            (progn
              (message "MCP server %s is not running" server-name)
              nil)
          (when ai-auto-complete-mcp-debug-mode
            (message "Calling tool %s on MCP server %s with params %s"
                     tool-name server-name params))

          ;; Check if we should use the improved stdio transport
          (if (and (boundp 'ai-auto-complete-mcp-use-improved-stdio)
                   ai-auto-complete-mcp-use-improved-stdio
                   (fboundp 'ai-auto-complete-mcp-stdio-improved-direct-call-tool))
              ;; Use the direct call approach for immediate results
              (progn
                (message "Using direct call approach for tool %s on server %s"
                         tool-name server-name)
                (let ((result (ai-auto-complete-mcp-stdio-improved-direct-call-tool server-name tool-name params)))
                  (funcall callback result)))

            ;; Check if we should use the improved stdio transport
            (if (and (boundp 'ai-auto-complete-mcp-use-improved-stdio)
                     ai-auto-complete-mcp-use-improved-stdio
                     (fboundp 'ai-auto-complete-mcp-stdio-improved-call-tool))
                ;; Use the improved stdio transport
                (progn
                  (message "Using improved stdio transport for tool %s on server %s"
                           tool-name server-name)
                  (ai-auto-complete-mcp-stdio-improved-call-tool server-name tool-name params callback))

              ;; Check if we should use the simplified stdio transport
              (if (and (boundp 'ai-auto-complete-mcp-use-simplified-stdio)
                       ai-auto-complete-mcp-use-simplified-stdio
                       (fboundp 'ai-auto-complete-mcp-stdio-simple-call-tool))
                  ;; Use the simplified stdio transport
                  (progn
                    (when ai-auto-complete-mcp-debug-mode
                      (message "Using simplified stdio transport for tool %s on server %s"
                               tool-name server-name))
                    (ai-auto-complete-mcp-stdio-simple-call-tool server-name tool-name params callback))

                ;; Use the standard transport based on the server's transport setting
                (cond
                 ((eq transport 'stdio)
                  (if (fboundp 'ai-auto-complete-mcp-stdio-call-tool)
                      (ai-auto-complete-mcp-stdio-call-tool server-name tool-name params callback)
                    (message "Stdio transport not available")
                    nil))

           ((eq transport 'sse)
            (if (fboundp 'ai-auto-complete-mcp-sse-call-tool)
                (ai-auto-complete-mcp-sse-call-tool server-name tool-name params callback)
              (message "SSE transport not available")
              nil))

           ((eq transport 'websocket)
            (if (fboundp 'ai-auto-complete-mcp-websocket-call-tool)
                (ai-auto-complete-mcp-websocket-call-tool server-name tool-name params callback)
              (message "WebSocket transport not available")
              nil))

           ((eq transport 'grpc)
            (if (fboundp 'ai-auto-complete-mcp-grpc-call-tool)
                (ai-auto-complete-mcp-grpc-call-tool server-name tool-name params callback)
              (message "gRPC transport not available")
              nil))

           (t
            (message "Unsupported transport: %s" transport)
            nil))))))))))

;; Read an MCP resource
(defun ai-auto-complete-mcp-read-resource (server-name resource-uri callback)
  "Read RESOURCE-URI from SERVER-NAME and process result with CALLBACK."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-read-resource nil))

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((status (plist-get server :status))
            (transport (plist-get server :transport)))
        (if (not (eq status 'running))
            (progn
              (message "MCP server %s is not running" server-name)
              nil)
          (when ai-auto-complete-mcp-debug-mode
            (message "Reading resource %s from MCP server %s"
                     resource-uri server-name))

          ;; Read the resource based on transport type
          (cond
           ((eq transport 'stdio)
            (if (fboundp 'ai-auto-complete-mcp-stdio-read-resource)
                (ai-auto-complete-mcp-stdio-read-resource server-name resource-uri callback)
              (message "Stdio transport not available")
              nil))

           ((eq transport 'sse)
            (if (fboundp 'ai-auto-complete-mcp-sse-read-resource)
                (ai-auto-complete-mcp-sse-read-resource server-name resource-uri callback)
              (message "SSE transport not available")
              nil))

           ((eq transport 'websocket)
            (if (fboundp 'ai-auto-complete-mcp-websocket-read-resource)
                (ai-auto-complete-mcp-websocket-read-resource server-name resource-uri callback)
              (message "WebSocket transport not available")
              nil))

           ((eq transport 'grpc)
            (if (fboundp 'ai-auto-complete-mcp-grpc-read-resource)
                (ai-auto-complete-mcp-grpc-read-resource server-name resource-uri callback)
              (message "gRPC transport not available")
              nil))

           (t
            (message "Unsupported transport: %s" transport)
            nil)))))))

;; Get an MCP prompt
(defun ai-auto-complete-mcp-get-prompt (server-name prompt-name params callback)
  "Get PROMPT-NAME from SERVER-NAME with PARAMS and process result with CALLBACK.
PARAMS should be an alist of parameter names and values."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-get-prompt nil))

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((status (plist-get server :status))
            (transport (plist-get server :transport)))
        (if (not (eq status 'running))
            (progn
              (message "MCP server %s is not running" server-name)
              nil)
          (when ai-auto-complete-mcp-debug-mode
            (message "Getting prompt %s from MCP server %s with params %s"
                     prompt-name server-name params))

          ;; Get the prompt based on transport type
          (cond
           ((eq transport 'stdio)
            (if (fboundp 'ai-auto-complete-mcp-stdio-get-prompt)
                (ai-auto-complete-mcp-stdio-get-prompt server-name prompt-name params callback)
              (message "Stdio transport not available")
              nil))

           ((eq transport 'sse)
            (if (fboundp 'ai-auto-complete-mcp-sse-get-prompt)
                (ai-auto-complete-mcp-sse-get-prompt server-name prompt-name params callback)
              (message "SSE transport not available")
              nil))

           ((eq transport 'websocket)
            (if (fboundp 'ai-auto-complete-mcp-websocket-get-prompt)
                (ai-auto-complete-mcp-websocket-get-prompt server-name prompt-name params callback)
              (message "WebSocket transport not available")
              nil))

           ((eq transport 'grpc)
            (if (fboundp 'ai-auto-complete-mcp-grpc-get-prompt)
                (ai-auto-complete-mcp-grpc-get-prompt server-name prompt-name params callback)
              (message "gRPC transport not available")
              nil))

           (t
            (message "Unsupported transport: %s" transport)
            nil)))))))

;; List tools from an MCP server
(defun ai-auto-complete-mcp-list-tools (server-name callback)
  "List tools from SERVER-NAME and process result with CALLBACK."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-list-tools nil))

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((status (plist-get server :status))
            (transport (plist-get server :transport)))
        (if (not (eq status 'running))
            (progn
              (message "MCP server %s is not running" server-name)
              nil)
          (when ai-auto-complete-mcp-debug-mode
            (message "Listing tools from MCP server %s" server-name))

          ;; Check if we should use the simplified stdio transport
          (if (and (boundp 'ai-auto-complete-mcp-use-simplified-stdio)
                   ai-auto-complete-mcp-use-simplified-stdio
                   (fboundp 'ai-auto-complete-mcp-stdio-simple-list-tools))
              ;; Use the simplified stdio transport
              (progn
                (when ai-auto-complete-mcp-debug-mode
                  (message "Using simplified stdio transport for listing tools from server %s"
                           server-name))
                (ai-auto-complete-mcp-stdio-simple-list-tools server-name callback))

            ;; Use the standard transport based on the server's transport setting
            (cond
             ((eq transport 'stdio)
              (if (fboundp 'ai-auto-complete-mcp-stdio-list-tools)
                  (ai-auto-complete-mcp-stdio-list-tools server-name callback)
                (message "Stdio transport not available")
                nil))

           ((eq transport 'sse)
            (if (fboundp 'ai-auto-complete-mcp-sse-list-tools)
                (ai-auto-complete-mcp-sse-list-tools server-name callback)
              (message "SSE transport not available")
              nil))

           ((eq transport 'websocket)
            (if (fboundp 'ai-auto-complete-mcp-websocket-list-tools)
                (ai-auto-complete-mcp-websocket-list-tools server-name callback)
              (message "WebSocket transport not available")
              nil))

           ((eq transport 'grpc)
            (if (fboundp 'ai-auto-complete-mcp-grpc-list-tools)
                (ai-auto-complete-mcp-grpc-list-tools server-name callback)
              (message "gRPC transport not available")
              nil))

           (t
            (message "Unsupported transport: %s" transport)
            nil))))))))

;; List resources from an MCP server
(defun ai-auto-complete-mcp-list-resources (server-name callback)
  "List resources from SERVER-NAME and process result with CALLBACK."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-list-resources nil))

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((status (plist-get server :status))
            (transport (plist-get server :transport)))
        (if (not (eq status 'running))
            (progn
              (message "MCP server %s is not running" server-name)
              nil)
          (when ai-auto-complete-mcp-debug-mode
            (message "Listing resources from MCP server %s" server-name))

          ;; List resources based on transport type
          (cond
           ((eq transport 'stdio)
            (if (fboundp 'ai-auto-complete-mcp-stdio-list-resources)
                (ai-auto-complete-mcp-stdio-list-resources server-name callback)
              (message "Stdio transport not available")
              nil))

           ((eq transport 'sse)
            (if (fboundp 'ai-auto-complete-mcp-sse-list-resources)
                (ai-auto-complete-mcp-sse-list-resources server-name callback)
              (message "SSE transport not available")
              nil))

           ((eq transport 'websocket)
            (if (fboundp 'ai-auto-complete-mcp-websocket-list-resources)
                (ai-auto-complete-mcp-websocket-list-resources server-name callback)
              (message "WebSocket transport not available")
              nil))

           ((eq transport 'grpc)
            (if (fboundp 'ai-auto-complete-mcp-grpc-list-resources)
                (ai-auto-complete-mcp-grpc-list-resources server-name callback)
              (message "gRPC transport not available")
              nil))

           (t
            (message "Unsupported transport: %s" transport)
            nil)))))))

;; List prompts from an MCP server
(defun ai-auto-complete-mcp-list-prompts (server-name callback)
  "List prompts from SERVER-NAME and process result with CALLBACK."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-list-prompts nil))

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((status (plist-get server :status))
            (transport (plist-get server :transport)))
        (if (not (eq status 'running))
            (progn
              (message "MCP server %s is not running" server-name)
              nil)
          (when ai-auto-complete-mcp-debug-mode
            (message "Listing prompts from MCP server %s" server-name))

          ;; List prompts based on transport type
          (cond
           ((eq transport 'stdio)
            (if (fboundp 'ai-auto-complete-mcp-stdio-list-prompts)
                (ai-auto-complete-mcp-stdio-list-prompts server-name callback)
              (message "Stdio transport not available")
              nil))

           ((eq transport 'sse)
            (if (fboundp 'ai-auto-complete-mcp-sse-list-prompts)
                (ai-auto-complete-mcp-sse-list-prompts server-name callback)
              (message "SSE transport not available")
              nil))

           ((eq transport 'websocket)
            (if (fboundp 'ai-auto-complete-mcp-websocket-list-prompts)
                (ai-auto-complete-mcp-websocket-list-prompts server-name callback)
              (message "WebSocket transport not available")
              nil))

           ((eq transport 'grpc)
            (if (fboundp 'ai-auto-complete-mcp-grpc-list-prompts)
                (ai-auto-complete-mcp-grpc-list-prompts server-name callback)
              (message "gRPC transport not available")
              nil))

           (t
            (message "Unsupported transport: %s" transport)
            nil)))))))

(provide 'mcp/mcp-client)
;;; mcp-client.el ends here
