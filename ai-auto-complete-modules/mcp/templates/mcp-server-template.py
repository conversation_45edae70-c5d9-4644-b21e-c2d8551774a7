#!/usr/bin/env python3
"""
MCP Server Template for AI Auto Complete

This is a template for creating MCP servers that can be used with AI Auto Complete.
"""

from mcp.server.fastmcp import FastMCP, Context

# Create an MCP server
mcp = FastMCP("Template Server")

# Example tool
@mcp.tool()
def hello(name: str) -> str:
    """Say hello to someone"""
    return f"Hello, {name}!"

# Example tool with multiple parameters
@mcp.tool()
def calculate(operation: str, a: float, b: float) -> float:
    """Perform a calculation
    
    Args:
        operation: The operation to perform (add, subtract, multiply, divide)
        a: The first number
        b: The second number
    
    Returns:
        The result of the calculation
    """
    if operation == "add":
        return a + b
    elif operation == "subtract":
        return a - b
    elif operation == "multiply":
        return a * b
    elif operation == "divide":
        if b == 0:
            return float('nan')
        return a / b
    else:
        return float('nan')

# Example resource
@mcp.resource("greeting://{name}")
def get_greeting(name: str) -> str:
    """Get a personalized greeting"""
    return f"Welcome, {name}!"

# Example resource with context
@mcp.resource("status://system")
def get_system_status(ctx: Context) -> dict:
    """Get the system status"""
    ctx.info("Getting system status")
    return {
        "status": "ok",
        "version": "1.0.0",
        "uptime": 123
    }

# Example prompt
@mcp.prompt()
def code_review(code: str) -> str:
    """Generate a prompt for code review"""
    return f"""Please review the following code:

```
{code}
```

Provide feedback on:
1. Correctness
2. Style
3. Performance
4. Security
"""

if __name__ == "__main__":
    mcp.run()
