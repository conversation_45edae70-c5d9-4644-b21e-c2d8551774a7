#!/usr/bin/env node
/**
 * Node.js MCP Server Template
 * 
 * This is a template for creating MCP servers using Node.js.
 * It implements the basic MCP protocol functionality and includes example tools.
 */

const readline = require('readline');

/**
 * MCP Server class
 */
class MCPServer {
    constructor(name) {
        this.name = name;
        this.tools = {};
        this.resources = {};
        this.prompts = {};
        this.callbacks = new Map();
        this.nextRequestId = 1;
    }

    /**
     * Register a tool
     */
    tool(options = {}) {
        return (fn) => {
            const name = options.name || fn.name;
            if (!name) {
                throw new Error('Tool function must have a name or be provided with a name option');
            }

            // Extract parameter information from function
            const fnStr = fn.toString();
            const paramMatch = fnStr.match(/\(([^)]*)\)/);
            const params = paramMatch ? paramMatch[1].split(',').map(p => p.trim()).filter(p => p) : [];
            
            // Extract JSDoc comments for description and parameters
            const fnBody = fnStr.substring(fnStr.indexOf('{') + 1, fnStr.lastIndexOf('}'));
            const jsDocMatch = fnBody.match(/\/\*\*([\s\S]*?)\*\//);
            let description = options.description || '';
            
            if (jsDocMatch) {
                const jsDoc = jsDocMatch[1];
                
                // Extract description
                const descMatch = jsDoc.match(/@description\s+(.*?)(?=@|\*\/|$)/s);
                if (descMatch) {
                    description = descMatch[1].trim();
                } else {
                    // Try to extract description from the first line
                    const firstLineMatch = jsDoc.match(/\s*\*\s+(.*?)(?=@|\*\/|$)/);
                    if (firstLineMatch) {
                        description = firstLineMatch[1].trim();
                    }
                }
                
                // Extract parameter descriptions
                const paramDescs = {};
                const paramMatches = jsDoc.matchAll(/@param\s+{[^}]*}\s+(\w+)\s+(.*?)(?=@|\*\/|$)/g);
                for (const match of paramMatches) {
                    paramDescs[match[1]] = match[2].trim();
                }
            }
            
            // Register the tool
            this.tools[name] = {
                fn,
                name,
                description,
                parameters: params.map(param => ({
                    name: param,
                    description: '',
                    type: 'string'
                }))
            };
            
            return fn;
        };
    }

    /**
     * Register a resource
     */
    resource(uri, content, mimeType = 'text/plain') {
        this.resources[uri] = {
            content,
            mimeType
        };
    }

    /**
     * Register a prompt
     */
    prompt(options = {}) {
        return (fn) => {
            const name = options.name || fn.name;
            if (!name) {
                throw new Error('Prompt function must have a name or be provided with a name option');
            }

            // Extract parameter information from function
            const fnStr = fn.toString();
            const paramMatch = fnStr.match(/\(([^)]*)\)/);
            const params = paramMatch ? paramMatch[1].split(',').map(p => p.trim()).filter(p => p) : [];
            
            // Extract JSDoc comments for description and parameters
            const fnBody = fnStr.substring(fnStr.indexOf('{') + 1, fnStr.lastIndexOf('}'));
            const jsDocMatch = fnBody.match(/\/\*\*([\s\S]*?)\*\//);
            let description = options.description || '';
            
            if (jsDocMatch) {
                const jsDoc = jsDocMatch[1];
                
                // Extract description
                const descMatch = jsDoc.match(/@description\s+(.*?)(?=@|\*\/|$)/s);
                if (descMatch) {
                    description = descMatch[1].trim();
                } else {
                    // Try to extract description from the first line
                    const firstLineMatch = jsDoc.match(/\s*\*\s+(.*?)(?=@|\*\/|$)/);
                    if (firstLineMatch) {
                        description = firstLineMatch[1].trim();
                    }
                }
                
                // Extract parameter descriptions
                const paramDescs = {};
                const paramMatches = jsDoc.matchAll(/@param\s+{[^}]*}\s+(\w+)\s+(.*?)(?=@|\*\/|$)/g);
                for (const match of paramMatches) {
                    paramDescs[match[1]] = match[2].trim();
                }
            }
            
            // Register the prompt
            this.prompts[name] = {
                fn,
                name,
                description,
                parameters: params.map(param => ({
                    name: param,
                    description: '',
                    type: 'string'
                }))
            };
            
            return fn;
        };
    }

    /**
     * Handle a JSON-RPC request
     */
    async handleRequest(request) {
        const { method, params, id } = request;
        
        try {
            let result;
            
            switch (method) {
                case 'initialize':
                    result = {
                        serverName: this.name,
                        serverVersion: '1.0.0',
                        capabilities: {
                            tools: { listChanged: true },
                            resources: { listChanged: true, subscribe: true },
                            prompts: { listChanged: true }
                        }
                    };
                    break;
                
                case 'listTools':
                    result = Object.values(this.tools).map(tool => ({
                        name: tool.name,
                        description: tool.description,
                        parameters: tool.parameters
                    }));
                    break;
                
                case 'listResources':
                    result = Object.keys(this.resources).map(uri => ({
                        uri,
                        mimeType: this.resources[uri].mimeType
                    }));
                    break;
                
                case 'listPrompts':
                    result = Object.values(this.prompts).map(prompt => ({
                        name: prompt.name,
                        description: prompt.description,
                        parameters: prompt.parameters
                    }));
                    break;
                
                case 'callTool':
                    const tool = this.tools[params.name];
                    if (!tool) {
                        throw new Error(`Tool not found: ${params.name}`);
                    }
                    
                    result = await tool.fn(params.arguments);
                    break;
                
                case 'readResource':
                    const resource = this.resources[params.uri];
                    if (!resource) {
                        throw new Error(`Resource not found: ${params.uri}`);
                    }
                    
                    result = {
                        content: resource.content,
                        mimeType: resource.mimeType
                    };
                    break;
                
                case 'getPrompt':
                    const prompt = this.prompts[params.name];
                    if (!prompt) {
                        throw new Error(`Prompt not found: ${params.name}`);
                    }
                    
                    result = await prompt.fn(params.arguments);
                    break;
                
                default:
                    throw new Error(`Unknown method: ${method}`);
            }
            
            return {
                jsonrpc: '2.0',
                id,
                result
            };
        } catch (error) {
            return {
                jsonrpc: '2.0',
                id,
                error: {
                    code: -32000,
                    message: error.message
                }
            };
        }
    }

    /**
     * Run the server
     */
    run() {
        // Set up readline interface
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            terminal: false
        });
        
        // Process input lines
        rl.on('line', async (line) => {
            if (line.trim()) {
                try {
                    const request = JSON.parse(line);
                    const response = await this.handleRequest(request);
                    console.log(JSON.stringify(response));
                } catch (error) {
                    console.error(`Error processing request: ${error.message}`);
                    console.log(JSON.stringify({
                        jsonrpc: '2.0',
                        id: null,
                        error: {
                            code: -32700,
                            message: `Parse error: ${error.message}`
                        }
                    }));
                }
            }
        });
        
        // Handle process exit
        process.on('SIGINT', () => {
            console.error('Received SIGINT, shutting down');
            process.exit(0);
        });
        
        process.on('SIGTERM', () => {
            console.error('Received SIGTERM, shutting down');
            process.exit(0);
        });
    }
}

// Create an MCP server
const mcp = new MCPServer('Node.js MCP Server Template');

// Example tool: hello
mcp.tool()((name) => {
    /**
     * Say hello to someone
     * @param {string} name - The name to greet
     * @returns {string} A greeting message
     */
    return `Hello, ${name}!`;
});

// Example tool: add
mcp.tool()((a, b) => {
    /**
     * Add two numbers
     * @param {number} a - First number
     * @param {number} b - Second number
     * @returns {number} The sum of a and b
     */
    return Number(a) + Number(b);
});

// Example resource
mcp.resource('greeting://world', 'Hello, World!', 'text/plain');

// Example prompt
mcp.prompt()((topic) => {
    /**
     * Generate a prompt about a topic
     * @param {string} topic - The topic to generate a prompt about
     * @returns {string} A prompt about the topic
     */
    return `Write a short paragraph about ${topic}.`;
});

// Run the server
if (require.main === module) {
    console.error('Node.js MCP server is running. Press Ctrl+C to exit.');
    mcp.run();
}

// Export the mcp object for testing
module.exports = mcp;
