;;; mcp-toggles.el --- Toggle functions for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides toggle functions for MCP settings in AI Auto Complete.

;;; Code:

;; Toggle MCP caching
(defun ai-auto-complete-mcp-toggle-caching ()
  "Toggle MCP caching."
  (interactive)
  (when (boundp 'ai-auto-complete-mcp-enable-caching)
    (setq ai-auto-complete-mcp-enable-caching (not ai-auto-complete-mcp-enable-caching))
    (message "MCP caching %s" (if ai-auto-complete-mcp-enable-caching "enabled" "disabled"))))

;; Toggle MCP auto-restart servers
(defun ai-auto-complete-mcp-toggle-auto-restart-servers ()
  "Toggle MCP auto-restart servers."
  (interactive)
  (when (boundp 'ai-auto-complete-mcp-auto-restart-servers)
    (setq ai-auto-complete-mcp-auto-restart-servers (not ai-auto-complete-mcp-auto-restart-servers))
    (message "MCP auto-restart servers %s" (if ai-auto-complete-mcp-auto-restart-servers "enabled" "disabled"))))

;; Toggle MCP context integration
(defun ai-auto-complete-mcp-toggle-context-integration ()
  "Toggle MCP context integration."
  (interactive)
  (when (boundp 'ai-auto-complete-mcp-enable-context-integration)
    (setq ai-auto-complete-mcp-enable-context-integration (not ai-auto-complete-mcp-enable-context-integration))
    (message "MCP context integration %s" (if ai-auto-complete-mcp-enable-context-integration "enabled" "disabled"))))

;; Toggle MCP session integration
(defun ai-auto-complete-mcp-toggle-session-integration ()
  "Toggle MCP session integration."
  (interactive)
  (when (boundp 'ai-auto-complete-mcp-enable-session-integration)
    (setq ai-auto-complete-mcp-enable-session-integration (not ai-auto-complete-mcp-enable-session-integration))
    (message "MCP session integration %s" (if ai-auto-complete-mcp-enable-session-integration "enabled" "disabled"))))

(provide 'mcp/mcp-toggles)
;;; mcp-toggles.el ends here
