;;; mcp.el --- Model Context Protocol integration for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides Model Context Protocol (MCP) integration for the AI Auto Complete package.
;; MCP is an open protocol developed by Anthropic that standardizes how applications provide context to LLMs.

;;; Code:

;; Load MCP modules
(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)

;; Load MCP settings module
(condition-case err
    (require 'mcp/mcp-settings)
  (error (message "Error loading MCP settings module: %s" (error-message-string err))))

;(require 'mcp/transports/mcp-stdio-mcp)
(require 'mcp/transports/mcp-stdio-bridge)
(require 'mcp/transports/mcp-typescript-bridge)
(require 'mcp/mcp-server-bridge)
;(require 'mcp/servers/setup-typescript-weather-server.el)
;(require 'mcp/bridge/test_bridge)
;; Load transports with error handling
;; (condition-case err
;;     (require 'mcp/transports/mcp-stdio-mcp)
;;   (error
;;    (message "Error loading improved mcp-stdio-mcp transport: %s" (error-message-string err))
;;    (condition-case err2
;;        (require 'mcp/transports/mcp-stdio-simple)
;;      (error
;;       (message "Error loading simplified stdio transport: %s" (error-message-string err2))
;;       (condition-case err3
;;           (require 'mcp/transports/mcp-stdio-fixed)
;;         (error
;;          (message "Error loading fixed stdio transport: %s" (error-message-string err3))
;;          (condition-case err4
;;              (require 'mcp/transports/mcp-stdio)
;;            (error
;;             (message "Error loading stdio transport: %s" (error-message-string err4))
;;             ;; Try to load our stdio fix as a last resort
;;             (condition-case err5
;;                 (require 'mcp/transports/mcp-stdio-fix)
;;               (error (message "Error loading stdio fix: %s" (error-message-string err5))))))))))))

;(condition-case err
 ;   (require 'mcp/transports/mcp-sse)
  ;(error (message "Error loading SSE transport: %s" (error-message-string err))))

;(condition-case err
;    (require 'mcp/transports/mcp-websocket)
 ; (error (message "Error loading WebSocket transport: %s" (error-message-string err))))

;(condition-case err
 ;   (require 'mcp/transports/mcp-grpc)
  ;(error (message "Error loading gRPC transport: %s" (error-message-string err))))
;(require 'mcp/mcp-server-tools)
(require 'mcp/mcp-tools-bridge)
(require 'mcp/mcp-tools-integration)
;(require 'mcp/mcp-simplified)
(require 'mcp/mcp-directory)
(require 'mcp/mcp-persistence)
(require 'mcp/mcp-tools-ui)
(require 'mcp/mcp-ui)
(require 'mcp/mcp-agents)
(require 'mcp/mcp-prompt)
(require 'mcp/mcp-resources)
(require 'mcp/mcp-sampling)
(require 'mcp/mcp-completion)
(require 'mcp/mcp-optimization)
(require 'mcp/mcp-error-handling)
(require 'mcp/mcp-feedback)
(require 'mcp/mcp-integration)

;; Load our fixed MCP tools bridge
(condition-case err
    (require 'mcp/mcp-tools-bridge)
  (error (message "Error loading MCP tools bridge: %s" (error-message-string err))))

;; Load our custom server initialization module
(condition-case err
    (require 'mcp/mcp-server-init)
  (error (message "Error loading server initialization module: %s" (error-message-string err))))

;; Load our status checking module
(condition-case err
    (require 'mcp/mcp-status)
  (error (message "Error loading status module: %s" (error-message-string err))))

;; Load our test command module
(condition-case err
    (require 'mcp/mcp-test-command)
  (error (message "Error loading test command module: %s" (error-message-string err))))

;; Load our menu module
(condition-case err
    (require 'mcp/mcp-menu)
  (error (message "Error loading menu module: %s" (error-message-string err))))

;; Load our Python utilities
(condition-case err
    (require 'mcp/mcp-python)
  (error (message "Error loading Python utilities: %s" (error-message-string err))))

;; Load debugging, testing, and utility modules last
;; These modules depend on the core modules being loaded first
;; Use condition-case to handle any errors during loading
(condition-case err
    (require 'mcp/mcp-debug)
  (error (message "Error loading mcp-debug: %s" (error-message-string err))))

(condition-case err
    (require 'mcp/mcp-test)
  (error (message "Error loading mcp-test: %s" (error-message-string err))))

(condition-case err
    (require 'mcp/mcp-toggles)
  (error (message "Error loading mcp-toggles: %s" (error-message-string err))))

(condition-case err
    (require 'mcp/mcp-debug-tools)
  (error (message "Error loading mcp-debug-tools: %s" (error-message-string err))))

;; Initialize MCP
(defun ai-auto-complete-mcp-initialize ()
  "Initialize MCP integration."
  ;; Log initialization start
  (message "Initializing MCP integration")

  ;; Use condition-case to handle errors during initialization
  (condition-case err
      (progn
        ;; Try to find a working Python executable
        (when (fboundp 'ai-auto-complete-mcp-find-python-executable)
          (ai-auto-complete-mcp-find-python-executable))

        ;; Ensure the MCP servers directory exists
        (when (fboundp 'ai-auto-complete-mcp-ensure-servers-directory)
          (ai-auto-complete-mcp-ensure-servers-directory))

        ;; Log servers directory
        (when (fboundp 'ai-auto-complete-mcp-debug-info)
          (ai-auto-complete-mcp-debug-info "MCP servers directory: %s" ai-auto-complete-mcp-servers-directory)
          (ai-auto-complete-mcp-debug-info "MCP servers directory exists: %s"
                                          (if (file-exists-p ai-auto-complete-mcp-servers-directory) "yes" "no")))

        ;; Import example MCP servers if the servers directory is empty
        ;; don't import from examples. leave it!
        ;(when (boundp 'ai-auto-complete-mcp-servers-directory)
         ; (let ((servers-dir ai-auto-complete-mcp-servers-directory))
          ;  (when (and (file-exists-p servers-dir)
           ;            (= 0 (length (directory-files servers-dir nil "\\.py$"))))
            ;  (when (fboundp 'ai-auto-complete-mcp-debug-info)
             ;   (ai-auto-complete-mcp-debug-info "MCP servers directory is empty, importing examples"))
              ;(when (fboundp 'ai-auto-complete-mcp-import-examples)
               ; (ai-auto-complete-mcp-import-examples)))))

        ;; Scan the servers directory if auto-import is enabled
        (when (boundp 'ai-auto-complete-mcp-auto-import)
          (if ai-auto-complete-mcp-auto-import
              (progn
                (when (fboundp 'ai-auto-complete-mcp-debug-info)
                  (ai-auto-complete-mcp-debug-info "Auto-import is enabled, scanning servers directory"))
                (when (fboundp 'ai-auto-complete-mcp-scan-directory)
                  (ai-auto-complete-mcp-scan-directory ai-auto-complete-mcp-servers-directory)))
            (when (fboundp 'ai-auto-complete-mcp-debug-info)
              (ai-auto-complete-mcp-debug-info "Auto-import is disabled, skipping directory scan"))))

        ;; Copy the test server to the servers directory if it doesn't exist
        ;; don't copy test server and all that shit.
        ;(when (boundp 'ai-auto-complete-mcp-servers-directory)
         ; (let* ((test-server-source (expand-file-name "examples/mcp-test-server.py"
          ;                                           (file-name-directory (locate-library "mcp/mcp"))))
           ;      (test-server-dest (expand-file-name "mcp-test-server.py" ai-auto-complete-mcp-servers-directory)))
            ;(when (and (file-exists-p test-server-source)
             ;          (not (file-exists-p test-server-dest)))
              ;(when (fboundp 'ai-auto-complete-mcp-debug-info)
               ; (ai-auto-complete-mcp-debug-info "Copying MCP test server to servers directory"))
              ;(copy-file test-server-source test-server-dest t)
              ;(set-file-modes test-server-dest (logior (file-modes test-server-dest) #o111)) ; Make executable

              ;; Register the test server
              ;(when (fboundp 'ai-auto-complete-mcp-register-server)
              ;  (ai-auto-complete-mcp-register-server "mcp-test-server" test-server-dest)))))

        ;; Initialize MCP tools if MCP is enabled
        ;; Don't initialize tools. Only register from directory and settings json, done above.
        (when (and (boundp 'ai-auto-complete-mcp-enabled) ai-auto-complete-mcp-enabled)
          ;; Load saved server configurations first
          (when (fboundp 'ai-auto-complete-mcp-init-persistence)
            (when (fboundp 'ai-auto-complete-mcp-debug-info)
              (ai-auto-complete-mcp-debug-info "Loading saved MCP server configurations"))
            (ai-auto-complete-mcp-init-persistence))

          ;; Initialize MCP tools using the loaded configurations
          (when (fboundp 'ai-auto-complete-mcp-initialize-tools)
            (when (fboundp 'ai-auto-complete-mcp-debug-info)
              (ai-auto-complete-mcp-debug-info "Initializing MCP tools"))
            (ai-auto-complete-mcp-initialize-tools)) ; Main fork to mcp-tools-bridge.el. it has to be this function that is calling the LLMs during initialization.

          ;; Display a message about the loaded servers
          (let ((server-count (length (ai-auto-complete-mcp-list-servers))))
            (when (> server-count 0)
              (message "MCP: %d servers available" server-count))))

        ;; Log initialization completion
        (message "MCP initialization complete"))

    ;; Handle any errors during initialization
    (error (message "Error during MCP initialization: %s" (error-message-string err)))))

;; Defer initialization to after Emacs startup
;(add-hook 'after-init-hook 'ai-auto-complete-mcp-initialize)

(provide 'mcp/mcp)
;;; mcp.el ends here
