;;; mcp-simplified.el --- Simplified MCP integration for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a simplified approach to MCP integration in the AI Auto Complete package.
;; It handles communication with MCP servers using a more direct approach.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)
(require 'mcp/transports/mcp-stdio-simple)

;; Call an MCP tool using the simplified approach
(defun ai-auto-complete-mcp-simplified-call-tool (server-name tool-name params callback)
  "Call TOOL-NAME on SERVER-NAME with PARAMS using the simplified approach.
CALLBACK will be called with the result."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-simplified-call-tool nil))

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          (funcall callback (format "Error: MCP server %s not found" server-name)))
      
      ;; Use the simplified stdio transport
      (ai-auto-complete-mcp-stdio-simple-call-tool server-name tool-name params callback))))

;; List tools from an MCP server using the simplified approach
(defun ai-auto-complete-mcp-simplified-list-tools (server-name callback)
  "List tools from SERVER-NAME using the simplified approach.
CALLBACK will be called with the result."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-simplified-list-tools nil))

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          (funcall callback (format "Error: MCP server %s not found" server-name)))
      
      ;; Use the simplified stdio transport
      (ai-auto-complete-mcp-stdio-simple-list-tools server-name callback))))

;; Start an MCP server using the simplified approach
(defun ai-auto-complete-mcp-simplified-start-server (server-name)
  "Start an MCP server with SERVER-NAME using the simplified approach."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-simplified-start-server nil))

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      
      ;; Use the simplified stdio transport
      (ai-auto-complete-mcp-stdio-simple-ensure-server server-name))))

;; Register a tool function that uses the simplified approach
(defun ai-auto-complete-mcp-simplified-create-tool-function (server-name tool-name)
  "Create a function that calls MCP tool TOOL-NAME on SERVER-NAME using the simplified approach."
  (lambda (params)
    (let ((result nil)
          (error-message nil)
          (done nil))

      (when ai-auto-complete-mcp-debug-mode
        (message "[MCP-DEBUG] Creating simplified tool function for %s on server %s" tool-name server-name)
        (message "[MCP-DEBUG] Parameters: %S" params))

      ;; Call the MCP tool using the simplified approach
      (ai-auto-complete-mcp-simplified-call-tool
       server-name
       tool-name
       params
       (lambda (response)
         (setq result response)
         (setq done t)
         (when ai-auto-complete-mcp-debug-mode
           (message "[MCP-DEBUG] Tool %s on server %s returned result: %s"
                    tool-name server-name
                    (if (stringp response)
                        (substring response 0 (min 100 (length response)))
                      response)))))

      ;; Wait for the result with timeout
      (let ((timeout 30) ;; Increased timeout to 30 seconds
            (start-time (current-time)))
        (while (and (not done)
                    (< (float-time (time-since start-time)) timeout))
          (sleep-for 0.1))

        (when ai-auto-complete-mcp-debug-mode
          (if done
              (message "[MCP-DEBUG] Tool %s on server %s completed successfully" tool-name server-name)
            (message "[MCP-DEBUG] Tool %s on server %s timed out" tool-name server-name)))

        ;; Return the result or error
        (if done
            result
          (let ((error-msg (format "Error: Timeout waiting for MCP tool %s" tool-name)))
            (message "Error executing MCP tool %s: %s" tool-name error-msg)
            error-msg))))))

;; Register tools directly using the simplified approach
(defun ai-auto-complete-mcp-simplified-register-tools-directly ()
  "Register MCP tools directly using the simplified approach."
  (interactive)
  (message "Directly registering MCP tools using simplified approach...")
  
  ;; Only proceed if MCP is enabled
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-simplified-register-tools-directly nil))
    
  ;; Use the server tools defined in mcp-server-tools.el
  (when (and (boundp 'ai-auto-complete-mcp-server-tools) ai-auto-complete-mcp-server-tools)
    (message "Using predefined server tools from mcp-server-tools.el")
    (let ((server-tools ai-auto-complete-mcp-server-tools)
          (total-tools 0))
      (dolist (server-entry server-tools)
        (let ((server-name (car server-entry))
              (tools (cdr server-entry)))
          
          ;; Register each tool
          (dolist (tool tools)
            (let ((tool-name (nth 0 tool))
                  (description (nth 1 tool))
                  (parameters (nth 2 tool))
                  (full-tool-name (format "mcp:%s:%s" server-name (nth 0 tool))))
              
              ;; Create a function that calls the MCP tool using the simplified approach
              (let ((tool-function (ai-auto-complete-mcp-simplified-create-tool-function server-name tool-name)))
                
                (message "Directly registering MCP tool %s using simplified approach" full-tool-name)
                
                ;; Register the tool
                (ai-auto-complete-register-tool
                 full-tool-name
                 (format "MCP tool from server %s: %s" server-name description)
                 tool-function
                 parameters)
                
                (setq total-tools (1+ total-tools)))))))
      
      (message "Directly registered %d MCP tools using simplified approach" total-tools)
      
      ;; Refresh tool definitions
      (when (fboundp 'ai-auto-complete-mcp-refresh-tool-definitions)
        (ai-auto-complete-mcp-refresh-tool-definitions))
      
      total-tools)
    
    ;; Return nil if no server tools are defined
    (message "No server tools defined in mcp-server-tools.el")
    nil))

(provide 'mcp/mcp-simplified)
;;; mcp-simplified.el ends here
