# Model Context Protocol (MCP) Integration

This module provides Model Context Protocol (MCP) integration for AI Auto Complete.

## Overview

The Model Context Protocol (MCP) is an open protocol developed by Anthropic that standardizes how applications provide context to LLMs. This module integrates MCP into AI Auto Complete, allowing you to:

- Register and manage MCP servers
- Call MCP tools from conversations
- Convert between tools and MCP servers
- Create agents from MCP servers
- Use MCP resources and prompts
- Subscribe to MCP resources
- Sample MCP tools and resources
- Integrate MCP with other systems

## Directory Structure

- `mcp-core.el`: Core MCP functionality
- `mcp-server.el`: Server management
- `mcp-client.el`: Client functionality
- `transports/`: Transport implementations
  - `mcp-stdio.el`: Stdio transport
  - `mcp-sse.el`: SSE transport
  - `mcp-websocket.el`: WebSocket transport
  - `mcp-grpc.el`: gRPC transport
- `mcp-tools-bridge.el`: Bridge between tools and MCP
- `mcp-tools-integration.el`: Integration with tools system
- `mcp-directory.el`: Directory management
- `mcp-ui.el`: User interface
- `mcp-agents.el`: Integration with agents
- `mcp-prompt.el`: System prompt integration
- `mcp-resources.el`: Resource subscription
- `mcp-sampling.el`: Sampling functionality
- `mcp-completion.el`: Completion integration
- `mcp-optimization.el`: Performance optimization
- `mcp-error-handling.el`: Error handling and recovery
- `mcp-feedback.el`: User feedback
- `mcp-integration.el`: Integration with other systems
- `examples/`: Example MCP servers
- `templates/`: Templates for creating MCP servers
- `scripts/`: Utility scripts
- `docs/`: Documentation
- `tests/`: Test suite

## Getting Started

1. Install the MCP Python SDK:
   ```bash
   cd ~/.emacs.d/ai-auto-complete-unified/ai-auto-complete-modules/mcp/scripts
   ./install-mcp-sdk.sh
   ```

2. Enable MCP integration:
   ```
   M-x ai-auto-complete-mcp-toggle
   ```

3. Manage MCP servers:
   ```
   M-x ai-auto-complete-mcp-list-servers-ui
   ```

## Key Features

### Server Management
- Register, start, stop, and restart MCP servers
- Import MCP servers from directories
- Convert tools to MCP servers
- Import MCP servers as tools

### Tool Integration
- Call MCP tools from conversations
- Bridge between MCP tools and AI Auto Complete tools
- Sample MCP tools to provide examples

### Resource Management
- Read MCP resources
- Subscribe to MCP resources for updates
- Sample MCP resources to provide examples

### Agent Integration
- Create agents from MCP servers
- Register MCP tools with agents
- Create a master agent for all MCP servers

### Performance Optimization
- Caching for MCP operations
- Batch processing for multiple servers and tools
- Optimized communication with MCP servers

### Error Handling
- Comprehensive error logging
- Automatic server restart on errors
- Detailed error reporting

### User Experience
- Progress reporting for long-running operations
- Status indicators for MCP servers
- Notifications for important events

### System Integration
- Integration with the context engine
- Integration with the session manager
- Integration with the completion system
- Integration with the help system

## Documentation

For more information, see the following documentation:

- [MCP Integration Documentation](docs/mcp-integration.md)
- [MCP Tutorial](docs/mcp-tutorial.md)
- [MCP Examples](docs/mcp-examples.md)
- [MCP Development Guide](docs/mcp-development.md)

## Examples

Example MCP servers are provided in the `examples/` directory:

- `file-tools-server.py`: Tools for working with files
- `system-info-server.py`: Tools for getting system information
- `code-analysis-server.py`: Tools for analyzing code
- `web-tools-server.py`: Tools for working with web resources
- `data-analysis-server.py`: Tools for data analysis
- `emacs-tools-server.py`: Tools for working with Emacs
- `websocket-server.py`: Example WebSocket MCP server
- `grpc-server.py`: Example gRPC MCP server

## Testing

To run the MCP tests:

```bash
cd ~/.emacs.d/ai-auto-complete-unified/ai-auto-complete-modules/mcp/tests
./run-tests.sh
```

Or from Emacs:

```
M-x ai-auto-complete-mcp-run-tests
```

## License

This module is part of AI Auto Complete and is subject to the same license.
