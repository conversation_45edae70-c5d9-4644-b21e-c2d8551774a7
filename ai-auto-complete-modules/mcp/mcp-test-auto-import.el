;;; mcp-test-auto-import.el --- Test auto-import functionality -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a test function to verify that the auto-import customization option is working correctly.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-directory)

;; Test function to verify auto-import customization
(defun ai-auto-complete-mcp-test-auto-import ()
  "Test function to verify that the auto-import customization option is working correctly."
  (interactive)
  
  ;; Display current value of auto-import
  (message "Current value of ai-auto-complete-mcp-auto-import: %s"
           (if (and (boundp 'ai-auto-complete-mcp-auto-import)
                    ai-auto-complete-mcp-auto-import)
               "enabled"
             "disabled"))
  
  ;; Test initialization with current setting
  (message "Testing initialization with current setting...")
  (ai-auto-complete-mcp-initialize)
  
  ;; Toggle auto-import and test again
  (let ((current-value (and (boundp 'ai-auto-complete-mcp-auto-import)
                           ai-auto-complete-mcp-auto-import)))
    (setq ai-auto-complete-mcp-auto-import (not current-value))
    (message "Temporarily changed ai-auto-complete-mcp-auto-import to: %s"
             (if ai-auto-complete-mcp-auto-import "enabled" "disabled"))
    
    ;; Test initialization with toggled setting
    (message "Testing initialization with toggled setting...")
    (ai-auto-complete-mcp-initialize)
    
    ;; Restore original value
    (setq ai-auto-complete-mcp-auto-import current-value)
    (message "Restored ai-auto-complete-mcp-auto-import to: %s"
             (if ai-auto-complete-mcp-auto-import "enabled" "disabled"))))

(provide 'mcp/mcp-test-auto-import)
;;; mcp-test-auto-import.el ends here
