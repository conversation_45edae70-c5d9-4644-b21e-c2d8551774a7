;;; mcp-core.el --- Core MCP functionality for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides core Model Context Protocol (MCP) functionality for the AI Auto Complete package.
;; MCP is an open protocol developed by Anthropic that standardizes how applications provide context to LLMs.

;;; Code:

(require 'cl-lib)
(require 'json)
(require 'url)

;; Load MCP customization
(require 'customization/mcp-customization)

;

;; MCP server registry
(defvar ai-auto-complete-mcp-servers (make-hash-table :test 'equal)
  "Hash table of registered MCP servers, mapping server names to their definitions.")

;; Tracking variables to prevent repeated polling
(defvar ai-auto-complete-mcp-tools-imported (make-hash-table :test 'equal)
  "Hash table tracking which servers have had their tools imported.")

(defvar ai-auto-complete-mcp-resources-listed (make-hash-table :test 'equal)
  "Hash table tracking which servers have had their resources listed.")

(defvar ai-auto-complete-mcp-prompts-listed (make-hash-table :test 'equal)
  "Hash table tracking which servers have had their prompts listed.")

;; Server types
;; lets ignore node for now
;; this function doesn't belong here. It belongs to mcp-server-type.el but for dependdencies issues I will
;; let it be here for now. I will move it later.
 (defvar ai-auto-complete-mcp-server-types
   '((:name python :extensions (".py") :command "python" :bridge-script "python-mcp-bridge/main.py" :transport stdio)
     ;(:name node :extensions (".js" ".mjs" ".cjs") :command "node" :bridge-script "mcp_node_bridge.js" :transport stdio)
    (:name javascript :extensions (".js" ".mjs" ".cjs" ".ts") :command "node" :bridge-script "typescript-mcp-bridge/dist/index.js" :transport typescript-bridge))
  "List of supported MCP server types.")

;; Determine server type based on file extension
;; (defun ai-auto-complete-mcp-determine-server-type (path)
;;   "Determine the server type based on the file extension of PATH."
;;   (let ((ext (file-name-extension path))
;;         (server-type nil))
;;     (dolist (type ai-auto-complete-mcp-server-types)
;;       (when (member (concat "." ext) (plist-get type :extensions))
;;         (setq server-type type)))
;;     server-type))

;; Register an MCP server
(defun ai-auto-complete-mcp-register-server (name path &optional transport description server-type runner tools resources prompts api-key)
  "Register an MCP server with NAME at PATH using TRANSPORT with DESCRIPTION, RUNNER, TOOLS, RESOURCES, ROMPTS and API-KEY.
TRANSPORT can be 'stdio or 'sse. If not provided, uses `ai-auto-complete-mcp-default-transport'.
RUNNER is the program used to execute the server (e.g., 'python', 'node', 'npx').
TOOLS is a list of tool definitions that can be used instead of contacting the server.
RESOURCES is a list of resource definitions that can be used instead of contacting the server.
PROMPTS is a list of prompt definitions that can be used instead of contacting the server.
API-KEY is the API key to use for authentication."
  (let* ((effective-transport (or transport ai-auto-complete-mcp-default-transport))
         (effective-description (or description (format "MCP server: %s" name))))
        ; (server-type (ai-auto-complete-mcp-determine-server-type name))
         ;; If runner is not provided, try to determine it from server type
         ;(effective-runner (or runner
          ;                    (when server-type
           ;                     (plist-get server-type :command))
            ;                  ;; Default fallbacks based on file extension
             ;                 (cond
              ;                 ((string-match-p "\\.py$" path) "python")
               ;                ((string-match-p "\\.js$" path) "node")
                ;               ((string-match-p "\\.ts$" path) "npx")
                 ;              (t nil)))))
    ;; Enhanced debug output for tools
    (when (and ai-auto-complete-mcp-debug-mode tools)
      (message "[MCP-DEBUG] Tools for server %s before registration:" name)
      (if (listp tools)
          (progn
            (message "  - Tools is a list with %d elements" (length tools))
            (dolist (tool tools)
              (message "  - Tool: %S" tool)))
        (message "  - Tools is not a list: %S" tools)))

    (puthash name (list :path path
                        :transport effective-transport
                        :description effective-description
                        :process nil
                        :connection nil
                        :capabilities nil
                        :status 'stopped
                        :server-type server-type ;(when server-type (plist-get server-type :name))
                        :runner runner
                        :tools tools
                        :resources resources
                        :prompts prompts
                        :api-key api-key)
             ai-auto-complete-mcp-servers)
    (when ai-auto-complete-mcp-debug-mode
      (message "Registered MCP server: %s at %s using %s transport (type: %s, runner: %s)"
               name path effective-transport server-type runner)
      (when tools
        (message "  - With %d predefined tools" (length tools))
        (when (listp tools)
          (dolist (tool tools)
            (message "    - Tool: %s" (if (listp tool)
                                         (plist-get tool :name)
                                       (format "%S" tool))))))
      (when resources
        (message "  - With %d predefined resources" (length resources)))
      (when prompts
        (message "  - With %d predefined prompts" (length prompts))))
    name))

;; Get MCP server definition
(defun ai-auto-complete-mcp-get-server (name)
  "Get the definition of MCP server with NAME.
Ensures that tools are always returned in the correct list format."
  (let ((server (gethash name ai-auto-complete-mcp-servers)))
    (when server
      ;; Check if tools need to be converted to list format
      (let ((tools (plist-get server :tools)))
        (when (and tools (not (listp tools)))
          ;; If we have the conversion function available, use it
          (if (fboundp 'ai-auto-complete-mcp-convert-tools-to-list)
              (let ((converted-tools (ai-auto-complete-mcp-convert-tools-to-list tools name)))
                ;; Update the server with the converted tools
                (setq server (plist-put server :tools converted-tools))
                (puthash name server ai-auto-complete-mcp-servers))
            ;; Otherwise, just log a warning
            (message "Warning: Tools for server %s are not in list format: %s"
                     name (type-of tools))))))
    server))

;; Check if MCP server exists
(defun ai-auto-complete-mcp-server-exists-p (name)
  "Check if MCP server with NAME exists."
  (not (null (ai-auto-complete-mcp-get-server name))))

;; Get MCP server status
(defun ai-auto-complete-mcp-get-server-status (name)
  "Get the status of MCP server with NAME."
  (let ((server (ai-auto-complete-mcp-get-server name)))
    (if server
        (plist-get server :status)
      nil)))

;; Update MCP server status
(defun ai-auto-complete-mcp-update-server-status (name status)
  "Update the status of MCP server with NAME to STATUS."
  (let ((server (ai-auto-complete-mcp-get-server name)))
    (when server
      (puthash name (plist-put server :status status) ai-auto-complete-mcp-servers)
      (when ai-auto-complete-mcp-debug-mode
        (message "Updated MCP server %s status to %s" name status))
      status)))

;; Update MCP server process
(defun ai-auto-complete-mcp-update-server-process (name process)
  "Update the process of MCP server with NAME to PROCESS."
  (let ((server (ai-auto-complete-mcp-get-server name)))
    (when server
      (puthash name (plist-put server :process process) ai-auto-complete-mcp-servers)
      process)))

;; Update MCP server connection
(defun ai-auto-complete-mcp-update-server-connection (name connection)
  "Update the connection of MCP server with NAME to CONNECTION."
  (let ((server (ai-auto-complete-mcp-get-server name)))
    (when server
      (puthash name (plist-put server :connection connection) ai-auto-complete-mcp-servers)
      connection)))

;; Update MCP server capabilities
(defun ai-auto-complete-mcp-update-server-capabilities (name capabilities)
  "Update the capabilities of MCP server with NAME to CAPABILITIES."
  (let ((server (ai-auto-complete-mcp-get-server name)))
    (when server
      (puthash name (plist-put server :capabilities capabilities) ai-auto-complete-mcp-servers)
      (when ai-auto-complete-mcp-debug-mode
        (message "Updated MCP server %s capabilities" name))
      capabilities)))

;; List all MCP servers
(defun ai-auto-complete-mcp-list-servers ()
  "List all registered MCP servers."
  (let ((servers '()))
    (maphash (lambda (name _)
               (push name servers))
             ai-auto-complete-mcp-servers)
    (sort servers 'string<)))

;; Remove an MCP server
(defun ai-auto-complete-mcp-remove-server (name)
  "Remove MCP server with NAME from the registry."
  (let ((server (ai-auto-complete-mcp-get-server name)))
    (when server
      ;; Stop the server if it's running
      (when (eq (plist-get server :status) 'running)
        (ai-auto-complete-mcp-stop-server name))
      ;; Remove from registry
      (remhash name ai-auto-complete-mcp-servers)
      (when ai-auto-complete-mcp-debug-mode
        (message "Removed MCP server: %s" name))
      t)))

;; Toggle MCP integration
(defun ai-auto-complete-mcp-toggle ()
  "Toggle MCP integration in AI Auto Complete."
  (interactive)
  (setq ai-auto-complete-mcp-enabled (not ai-auto-complete-mcp-enabled))
  (message "MCP integration %s" (if ai-auto-complete-mcp-enabled "enabled" "disabled"))

  (if ai-auto-complete-mcp-enabled
      (progn
        ;; Try to find a working Python executable
        (when (fboundp 'ai-auto-complete-mcp-find-python-executable)
          (ai-auto-complete-mcp-find-python-executable))

        ;; If enabled and auto-import is on, scan the servers directory
        (if (and (boundp 'ai-auto-complete-mcp-auto-import)
                 (not ai-auto-complete-mcp-auto-import))
            (message "MCP: Auto-import is disabled, skipping server scan")
          (when (fboundp 'ai-auto-complete-mcp-scan-directory)
            (message "MCP: Scanning for servers")
            (ai-auto-complete-mcp-scan-directory ai-auto-complete-mcp-servers-directory)))

        ;; Check if we've already imported tools
        (if (and (boundp 'ai-auto-complete-mcp-tools-imported)
                 (hash-table-p ai-auto-complete-mcp-tools-imported)
                 (> (hash-table-count ai-auto-complete-mcp-tools-imported) 0))
            (message "MCP tools already imported, skipping initialization")

          ;; Initialize MCP tools
          (when (fboundp 'ai-auto-complete-mcp-initialize-tools)
            (ai-auto-complete-mcp-initialize-tools))

          ;; Import all servers as tools synchronously
         ; (when (fboundp 'ai-auto-complete-mcp-import-all-servers-as-tools-sync)
          ;  (ai-auto-complete-mcp-import-all-servers-as-tools-sync))

          ;; Also register tools directly as a fallback
          ;(when (fboundp 'ai-auto-complete-mcp-register-tools-directly)
           ; (ai-auto-complete-mcp-register-tools-directly))

          ;; Use the simplified approach if enabled
          (when (and (boundp 'ai-auto-complete-mcp-use-simplified-stdio)
                     ai-auto-complete-mcp-use-simplified-stdio
                     (fboundp 'ai-auto-complete-mcp-simplified-register-tools-directly))
            (message "Using simplified approach for MCP tools")
            (ai-auto-complete-mcp-simplified-register-tools-directly))))

    ;; When disabled, we don't need to do anything special with the tools
    ;; They will remain registered but won't be used since MCP is disabled
    ))

;; Create JSON-RPC request
(defun ai-auto-complete-mcp-create-jsonrpc-request (method params &optional id)
  "Create a JSON-RPC request for METHOD with PARAMS and optional ID."
  (let ((request (list :jsonrpc "2.0"
                       :method method
                       :params params)))
    (when id
      (setq request (plist-put request :id id)))
    (json-encode request)))

;; Parse JSON-RPC response
(defun ai-auto-complete-mcp-parse-jsonrpc-response (response)
  "Parse JSON-RPC RESPONSE string into a plist."
  (let ((json-object-type 'plist)
        (json-array-type 'list)
        (json-key-type 'keyword))
    (condition-case err
        (progn
          (when ai-auto-complete-mcp-debug-mode
            (message "Parsing JSON-RPC response: %s"
                     (substring response 0 (min 100 (length response)))))
          (json-read-from-string response))
      (json-readtable-error
       (message "JSON readtable error: %s" (error-message-string err))
       (when ai-auto-complete-mcp-debug-mode
         (message "JSON response causing error: %s" response)
         ;; Try to identify the problematic character
         (let ((error-msg (error-message-string err)))
           (when (string-match "\\([0-9]+\\)" error-msg)
             (let ((char-code (string-to-number (match-string 1 error-msg))))
               (message "Problematic character: ASCII %d (%c)"
                        char-code (decode-char 'ascii char-code))))))
       nil)
      (json-error
       (message "JSON error: %s" (error-message-string err))
       (when ai-auto-complete-mcp-debug-mode
         (message "JSON response causing error: %s" response))
       nil)
      (error
       (message "Error parsing JSON-RPC response: %s" (error-message-string err))
       (when ai-auto-complete-mcp-debug-mode
         (message "JSON response causing error: %s" response))
       nil))))

;; Ensure MCP servers directory exists
(defun ai-auto-complete-mcp-ensure-servers-directory ()
  "Ensure the MCP servers directory exists."
  (unless (file-exists-p ai-auto-complete-mcp-servers-directory)
    (make-directory ai-auto-complete-mcp-servers-directory t)))

;; Transport registry
(defvar ai-auto-complete-mcp-transports (make-hash-table :test 'eq)
  "Hash table of registered MCP transports, mapping transport names to their functions.")

;; Register an MCP transport
(defun ai-auto-complete-mcp-register-transport (name start-fn stop-fn call-tool-fn list-tools-fn list-resources-fn list-prompts-fn read-resource-fn get-prompt-fn)
  "Register an MCP transport with NAME and associated functions.
START-FN is the function to start a server.
STOP-FN is the function to stop a server.
CALL-TOOL-FN is the function to call a tool.
LIST-TOOLS-FN is the function to list tools.
LIST-RESOURCES-FN is the function to list resources.
LIST-PROMPTS-FN is the function to list prompts.
READ-RESOURCE-FN is the function to read a resource.
GET-PROMPT-FN is the function to get a prompt."
  (puthash name (list :start-fn start-fn
                      :stop-fn stop-fn
                      :call-tool-fn call-tool-fn
                      :list-tools-fn list-tools-fn
                      :list-resources-fn list-resources-fn
                      :list-prompts-fn list-prompts-fn
                      :read-resource-fn read-resource-fn
                      :get-prompt-fn get-prompt-fn)
           ai-auto-complete-mcp-transports)
  (when ai-auto-complete-mcp-debug-mode
    (message "Registered MCP transport: %s" name))
  name)

;; Get MCP transport
(defun ai-auto-complete-mcp-get-transport (name)
  "Get the MCP transport with NAME."
  (gethash name ai-auto-complete-mcp-transports))

;; Initialize MCP
(defun ai-auto-complete-mcp-initialize ()
  "Initialize MCP integration."
  (ai-auto-complete-mcp-ensure-servers-directory)

  ;; Check if we should prefer loading from persistent data
  (if (and (boundp 'ai-auto-complete-mcp-prefer-persistent-data)
           ai-auto-complete-mcp-prefer-persistent-data
           (boundp 'ai-auto-complete-mcp-servers-config-file)
           (file-exists-p ai-auto-complete-mcp-servers-config-file))
      (progn
        (message "MCP: Loading servers from persistent data")
        (when (fboundp 'ai-auto-complete-mcp-load-servers)
          (ai-auto-complete-mcp-load-servers))

        ;; Check if we loaded any servers
        (let ((server-count (length (ai-auto-complete-mcp-list-servers))))
          (if (> server-count 0)
              (message "MCP: Loaded %d servers from persistent data" server-count)
            ;; If no servers were loaded, fall back to scanning
            (message "MCP: No servers found in persistent data, falling back to scanning")
            (ai-auto-complete-mcp-scan-directory-if-enabled))))

    ;; Otherwise, scan directories as usual
   ; (ai-auto-complete-mcp-scan-directory-if-enabled)
   (message "MCP: Skipped scanning for servers for now.")))

;; Helper function to scan directories if enabled
(defun ai-auto-complete-mcp-scan-directory-if-enabled ()
  "Scan for MCP servers in directories if auto-import is enabled."
  (if (and (boundp 'ai-auto-complete-mcp-auto-import)
           (not ai-auto-complete-mcp-auto-import))
      (message "MCP: Auto-import is disabled, skipping server scan")
    (when (fboundp 'ai-auto-complete-mcp-scan-directory)
      (message "MCP: Scanning for servers")
      (ai-auto-complete-mcp-scan-directory ai-auto-complete-mcp-servers-directory))))

(provide 'mcp/mcp-core)
;;; mcp-core.el ends here
