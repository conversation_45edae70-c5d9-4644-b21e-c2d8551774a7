;;; mcp-feedback.el --- User feedback for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides enhanced user feedback for MCP integration in the AI Auto Complete package.
;; It improves the user experience with better feedback and more intuitive interfaces.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)

;; Progress reporter for long-running operations
(defvar ai-auto-complete-mcp-progress-reporters (make-hash-table :test 'equal)
  "Hash table of progress reporters for MCP operations.")

;; Status indicators for MCP servers
(defvar ai-auto-complete-mcp-status-indicators (make-hash-table :test 'equal)
  "Hash table of status indicators for MCP servers.")

;; Enable/disable progress reporting
(defcustom ai-auto-complete-mcp-enable-progress-reporting t
  "Whether to enable progress reporting for MCP operations."
  :type 'boolean
  :group 'ai-auto-complete-mcp)

;; Enable/disable status indicators
(defcustom ai-auto-complete-mcp-enable-status-indicators nil
  "Whether to enable status indicators for MCP servers.
Set to nil to prevent continuous polling of MCP servers."
  :type 'boolean
  :group 'ai-auto-complete-mcp)

;; Status indicator refresh interval (in seconds)
(defcustom ai-auto-complete-mcp-status-indicator-refresh-interval 300
  "Refresh interval for MCP server status indicators (in seconds).
Set to a high value (300 seconds = 5 minutes) to prevent excessive polling."
  :type 'integer
  :group 'ai-auto-complete-mcp)

;; Status indicator timer
(defvar ai-auto-complete-mcp-status-indicator-timer nil
  "Timer for refreshing MCP server status indicators.")

;; Start a progress reporter
(defun ai-auto-complete-mcp-start-progress (operation-id message &optional min-value max-value current-value)
  "Start a progress reporter for OPERATION-ID with MESSAGE.
Optional MIN-VALUE, MAX-VALUE, and CURRENT-VALUE specify the progress range and initial value."
  (when ai-auto-complete-mcp-enable-progress-reporting
    (let ((reporter (make-progress-reporter message (or min-value 0) (or max-value 100) (or current-value 0))))
      (puthash operation-id reporter ai-auto-complete-mcp-progress-reporters)
      reporter)))

;; Update a progress reporter
(defun ai-auto-complete-mcp-update-progress (operation-id &optional value)
  "Update the progress reporter for OPERATION-ID with optional VALUE."
  (when ai-auto-complete-mcp-enable-progress-reporting
    (let ((reporter (gethash operation-id ai-auto-complete-mcp-progress-reporters)))
      (when reporter
        (progress-reporter-update reporter (or value nil))))))

;; Finish a progress reporter
(defun ai-auto-complete-mcp-finish-progress (operation-id)
  "Finish the progress reporter for OPERATION-ID."
  (when ai-auto-complete-mcp-enable-progress-reporting
    (let ((reporter (gethash operation-id ai-auto-complete-mcp-progress-reporters)))
      (when reporter
        (progress-reporter-done reporter)
        (remhash operation-id ai-auto-complete-mcp-progress-reporters)))))

;; Create a status indicator for a server
(defun ai-auto-complete-mcp-create-status-indicator (server-name)
  "Create a status indicator for SERVER-NAME."
  (when ai-auto-complete-mcp-enable-status-indicators
    (let ((indicator (list :server-name server-name
                          :status 'unknown
                          :last-update (current-time)
                          :tools-count 0
                          :resources-count 0
                          :prompts-count 0)))
      (puthash server-name indicator ai-auto-complete-mcp-status-indicators)
      indicator)))

;; Update a status indicator
(defun ai-auto-complete-mcp-update-status-indicator (server-name)
  "Update the status indicator for SERVER-NAME."
  (when ai-auto-complete-mcp-enable-status-indicators
    (let ((indicator (gethash server-name ai-auto-complete-mcp-status-indicators)))
      (unless indicator
        (setq indicator (ai-auto-complete-mcp-create-status-indicator server-name)))

      ;; Update the status
      (let ((server (ai-auto-complete-mcp-get-server server-name)))
        (when server
          (let ((status (plist-get server :status)))
            (plist-put indicator :status status)
            (plist-put indicator :last-update (current-time))

            ;; If the server is running, update the counts
            (when (eq status 'running)
              ;; Update tools count
              (ai-auto-complete-mcp-list-tools
               server-name
               (lambda (tools)
                 (when (and tools (not (stringp tools)))
                   (plist-put indicator :tools-count (length tools)))))

              ;; Update resources count
              (ai-auto-complete-mcp-list-resources
               server-name
               (lambda (resources)
                 (when (and resources (not (stringp resources)))
                   (plist-put indicator :resources-count (length resources)))))

              ;; Update prompts count
              (ai-auto-complete-mcp-list-prompts
               server-name
               (lambda (prompts)
                 (when (and prompts (not (stringp prompts)))
                   (plist-put indicator :prompts-count (length prompts))))))))

        ;; Update the hash table
        (puthash server-name indicator ai-auto-complete-mcp-status-indicators)

        indicator))))

;; Get a status indicator
(defun ai-auto-complete-mcp-get-status-indicator (server-name)
  "Get the status indicator for SERVER-NAME."
  (when ai-auto-complete-mcp-enable-status-indicators
    (or (gethash server-name ai-auto-complete-mcp-status-indicators)
        (ai-auto-complete-mcp-create-status-indicator server-name))))

;; Format a status indicator as a string
(defun ai-auto-complete-mcp-format-status-indicator (server-name)
  "Format the status indicator for SERVER-NAME as a string."
  (when ai-auto-complete-mcp-enable-status-indicators
    (let ((indicator (ai-auto-complete-mcp-get-status-indicator server-name)))
      (let ((status (plist-get indicator :status))
            (tools-count (plist-get indicator :tools-count))
            (resources-count (plist-get indicator :resources-count))
            (prompts-count (plist-get indicator :prompts-count))
            (last-update (plist-get indicator :last-update)))
        (format "%s: %s [Tools: %d, Resources: %d, Prompts: %d] (Updated: %s)"
                server-name
                (cond
                 ((eq status 'running) "Running")
                 ((eq status 'stopped) "Stopped")
                 (t "Unknown"))
                tools-count
                resources-count
                prompts-count
                (format-time-string "%H:%M:%S" last-update))))))

;; Display status indicators for all servers
(defun ai-auto-complete-mcp-display-status-indicators ()
  "Display status indicators for all MCP servers."
  (interactive)
  (when ai-auto-complete-mcp-enable-status-indicators
    (let ((buffer-name "*MCP Status*"))
      (with-current-buffer (get-buffer-create buffer-name)
        (let ((inhibit-read-only t))
          (erase-buffer)
          (special-mode) ;; Use special-mode for the buffer

          ;; Add header
          (insert "MCP Server Status\n")
          (insert "================\n\n")

          ;; Add status indicators
          (let ((servers (ai-auto-complete-mcp-list-servers)))
            (if (null servers)
                (insert "No MCP servers registered.\n")
              (dolist (server-name servers)
                (let ((indicator (ai-auto-complete-mcp-update-status-indicator server-name)))
                  (let ((status (plist-get indicator :status))
                        (tools-count (plist-get indicator :tools-count))
                        (resources-count (plist-get indicator :resources-count))
                        (prompts-count (plist-get indicator :prompts-count))
                        (last-update (plist-get indicator :last-update)))

                    (insert (format "Server: %s\n" server-name))
                    (insert (format "  Status: %s\n"
                                   (cond
                                    ((eq status 'running) "Running")
                                    ((eq status 'stopped) "Stopped")
                                    (t "Unknown"))))
                    (insert (format "  Tools: %d\n" tools-count))
                    (insert (format "  Resources: %d\n" resources-count))
                    (insert (format "  Prompts: %d\n" prompts-count))
                    (insert (format "  Last Update: %s\n" (format-time-string "%Y-%m-%d %H:%M:%S" last-update)))

                    ;; Add buttons for actions
                    (insert "  Actions: ")
                    (if (eq status 'running)
                        (insert-button "Stop"
                                      'action (lambda (_)
                                               (ai-auto-complete-mcp-stop-server server-name)
                                               (ai-auto-complete-mcp-display-status-indicators))
                                      'follow-link t)
                      (insert-button "Start"
                                    'action (lambda (_)
                                             (ai-auto-complete-mcp-start-server server-name)
                                             (ai-auto-complete-mcp-display-status-indicators))
                                    'follow-link t))

                    (insert " | ")
                    (insert-button "Restart"
                                  'action (lambda (_)
                                           (ai-auto-complete-mcp-restart-server server-name)
                                           (ai-auto-complete-mcp-display-status-indicators))
                                  'follow-link t)

                    (insert " | ")
                    (insert-button "Details"
                                  'action (lambda (_)
                                           (ai-auto-complete-mcp-server-details server-name))
                                  'follow-link t)

                    (insert "\n\n"))))))

          ;; Add buttons for global actions
          (insert "Global Actions:\n")
          (insert-button "Refresh"
                        'action (lambda (_)
                                 (ai-auto-complete-mcp-display-status-indicators))
                        'follow-link t)

          (insert " | ")
          (insert-button "Start All"
                        'action (lambda (_)
                                 (ai-auto-complete-mcp-start-all-servers)
                                 (ai-auto-complete-mcp-display-status-indicators))
                        'follow-link t)

          (insert " | ")
          (insert-button "Stop All"
                        'action (lambda (_)
                                 (ai-auto-complete-mcp-stop-all-servers)
                                 (ai-auto-complete-mcp-display-status-indicators))
                        'follow-link t)

          (goto-char (point-min))))

      (switch-to-buffer buffer-name))))

;; Start the status indicator timer
(defun ai-auto-complete-mcp-start-status-indicator-timer ()
  "Start the timer for refreshing MCP server status indicators."
  (when (and ai-auto-complete-mcp-enable-status-indicators
             (null ai-auto-complete-mcp-status-indicator-timer))
    (setq ai-auto-complete-mcp-status-indicator-timer
          (run-with-timer ai-auto-complete-mcp-status-indicator-refresh-interval
                          ai-auto-complete-mcp-status-indicator-refresh-interval
                          #'ai-auto-complete-mcp-refresh-status-indicators))))

;; Stop the status indicator timer
(defun ai-auto-complete-mcp-stop-status-indicator-timer ()
  "Stop the timer for refreshing MCP server status indicators."
  (when ai-auto-complete-mcp-status-indicator-timer
    (cancel-timer ai-auto-complete-mcp-status-indicator-timer)
    (setq ai-auto-complete-mcp-status-indicator-timer nil)))

;; Refresh status indicators
(defun ai-auto-complete-mcp-refresh-status-indicators ()
  "Refresh status indicators for all MCP servers."
  (when ai-auto-complete-mcp-enable-status-indicators
    (let ((servers (ai-auto-complete-mcp-list-servers)))
      (dolist (server-name servers)
        (ai-auto-complete-mcp-update-status-indicator server-name)))

    ;; Update the status buffer if it exists
    (when (get-buffer "*MCP Status*")
      (with-current-buffer "*MCP Status*"
        (let ((inhibit-read-only t))
          (ai-auto-complete-mcp-display-status-indicators))))))

;; Display a notification
(defun ai-auto-complete-mcp-display-notification (title message &optional timeout)
  "Display a notification with TITLE and MESSAGE.
Optional TIMEOUT specifies how long to display the notification (in seconds)."
  (let ((buffer-name "*MCP Notification*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add notification content
        (insert (format "%s\n" title))
        (insert (format "%s\n" (make-string (length title) ?=)))
        (insert "\n")
        (insert (format "%s\n" message))

        (goto-char (point-min))))

    ;; Display the buffer
    (display-buffer buffer-name)

    ;; Set up a timer to close the notification
    (when timeout
      (run-with-timer timeout nil
                      (lambda ()
                        (when (get-buffer buffer-name)
                          (kill-buffer buffer-name)))))))

;; Advice function to display notifications when servers are started
(defun ai-auto-complete-mcp-advice-start-server-notification (orig-fun server-name)
  "Advice function to display notifications when servers are started.
Calls ORIG-FUN with SERVER-NAME."
  (let ((result (funcall orig-fun server-name)))
    (when result
      (ai-auto-complete-mcp-display-notification
       "MCP Server Started"
       (format "MCP server %s has been started successfully." server-name)
       3))
    result))

;; Advice function to display notifications when servers are stopped
(defun ai-auto-complete-mcp-advice-stop-server-notification (orig-fun server-name)
  "Advice function to display notifications when servers are stopped.
Calls ORIG-FUN with SERVER-NAME."
  (let ((result (funcall orig-fun server-name)))
    (when result
      (ai-auto-complete-mcp-display-notification
       "MCP Server Stopped"
       (format "MCP server %s has been stopped." server-name)
       3))
    result))

;; Advice function to display progress for tool calls
(defun ai-auto-complete-mcp-advice-call-tool-progress (orig-fun server-name tool-name params callback)
  "Advice function to display progress for tool calls.
Calls ORIG-FUN with SERVER-NAME, TOOL-NAME, PARAMS, and CALLBACK."
  (let ((operation-id (format "tool-call-%s-%s-%s" server-name tool-name (ai-auto-complete-mcp-generate-request-id))))
    (ai-auto-complete-mcp-start-progress operation-id (format "Calling tool %s on server %s..." tool-name server-name))
    (funcall orig-fun server-name tool-name params
             (lambda (result)
               (ai-auto-complete-mcp-finish-progress operation-id)
               (funcall callback result)))))

;; Advice function to display progress for resource reads
(defun ai-auto-complete-mcp-advice-read-resource-progress (orig-fun server-name resource-uri callback)
  "Advice function to display progress for resource reads.
Calls ORIG-FUN with SERVER-NAME, RESOURCE-URI, and CALLBACK."
  (let ((operation-id (format "resource-read-%s-%s-%s" server-name resource-uri (ai-auto-complete-mcp-generate-request-id))))
    (ai-auto-complete-mcp-start-progress operation-id (format "Reading resource %s from server %s..." resource-uri server-name))
    (funcall orig-fun server-name resource-uri
             (lambda (result)
               (ai-auto-complete-mcp-finish-progress operation-id)
               (funcall callback result)))))

;; Advice function to display progress for prompt gets
(defun ai-auto-complete-mcp-advice-get-prompt-progress (orig-fun server-name prompt-name params callback)
  "Advice function to display progress for prompt gets.
Calls ORIG-FUN with SERVER-NAME, PROMPT-NAME, PARAMS, and CALLBACK."
  (let ((operation-id (format "prompt-get-%s-%s-%s" server-name prompt-name (ai-auto-complete-mcp-generate-request-id))))
    (ai-auto-complete-mcp-start-progress operation-id (format "Getting prompt %s from server %s..." prompt-name server-name))
    (funcall orig-fun server-name prompt-name params
             (lambda (result)
               (ai-auto-complete-mcp-finish-progress operation-id)
               (funcall callback result)))))

;; Setup function to add advice to MCP functions
(defun ai-auto-complete-mcp-feedback-setup ()
  "Set up MCP feedback."
  ;; Add advice to display notifications when servers are started
  (advice-add 'ai-auto-complete-mcp-start-server :around
              #'ai-auto-complete-mcp-advice-start-server-notification)

  ;; Add advice to display notifications when servers are stopped
  (advice-add 'ai-auto-complete-mcp-stop-server :around
              #'ai-auto-complete-mcp-advice-stop-server-notification)

  ;; Add advice to display progress for tool calls
  (advice-add 'ai-auto-complete-mcp-call-tool :around
              #'ai-auto-complete-mcp-advice-call-tool-progress)

  ;; Add advice to display progress for resource reads
  (advice-add 'ai-auto-complete-mcp-read-resource :around
              #'ai-auto-complete-mcp-advice-read-resource-progress)

  ;; Add advice to display progress for prompt gets
  (advice-add 'ai-auto-complete-mcp-get-prompt :around
              #'ai-auto-complete-mcp-advice-get-prompt-progress)

  ;; Only start the status indicator timer if explicitly enabled
  (when ai-auto-complete-mcp-enable-status-indicators
    (ai-auto-complete-mcp-start-status-indicator-timer)))

;; Teardown function to remove advice from MCP functions
(defun ai-auto-complete-mcp-feedback-teardown ()
  "Remove MCP feedback."
  ;; Remove advice from start server function
  (advice-remove 'ai-auto-complete-mcp-start-server
                 #'ai-auto-complete-mcp-advice-start-server-notification)

  ;; Remove advice from stop server function
  (advice-remove 'ai-auto-complete-mcp-stop-server
                 #'ai-auto-complete-mcp-advice-stop-server-notification)

  ;; Remove advice from tool call function
  (advice-remove 'ai-auto-complete-mcp-call-tool
                 #'ai-auto-complete-mcp-advice-call-tool-progress)

  ;; Remove advice from resource read function
  (advice-remove 'ai-auto-complete-mcp-read-resource
                 #'ai-auto-complete-mcp-advice-read-resource-progress)

  ;; Remove advice from prompt get function
  (advice-remove 'ai-auto-complete-mcp-get-prompt
                 #'ai-auto-complete-mcp-advice-get-prompt-progress)

  ;; Stop the status indicator timer
  (ai-auto-complete-mcp-stop-status-indicator-timer))

;; Set up MCP feedback when this module is loaded
(ai-auto-complete-mcp-feedback-setup)

(provide 'mcp/mcp-feedback)
;;; mcp-feedback.el ends here
