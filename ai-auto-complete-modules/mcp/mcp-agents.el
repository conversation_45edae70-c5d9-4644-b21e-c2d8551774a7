;;; mcp-agents.el --- Integration with agents for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides integration with the agent system for MCP in the AI Auto Complete package.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-client)
(require 'agents/agents-core)

;; Register an MCP server with an agent
(defun ai-auto-complete-mcp-register-server-with-agent (server-name agent-name)
  "Register MCP SERVER-NAME with AGENT-NAME."
  (interactive
   (list (completing-read "Select MCP server: " (ai-auto-complete-mcp-list-servers))
         (completing-read "Select agent: " (hash-table-keys ai-auto-complete-agents))))
  
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-register-server-with-agent nil))
  
  (let ((server (ai-auto-complete-mcp-get-server server-name))
        (agent (gethash agent-name ai-auto-complete-agents)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (if (not agent)
          (progn
            (message "Agent %s not found" agent-name)
            nil)
        ;; Start the server if it's not running
        (unless (eq (plist-get server :status) 'running)
          (ai-auto-complete-mcp-start-server server-name))
        
        ;; List tools from the server
        (ai-auto-complete-mcp-list-tools
         server-name
         (lambda (tools)
           (if (stringp tools)
               (message "Error listing tools from server %s: %s" server-name tools)
             ;; Register each tool with the agent
             (let ((tool-names '()))
               (dolist (tool tools)
                 (let ((tool-name (plist-get tool :name))
                       (full-tool-name (format "mcp:%s:%s" server-name (plist-get tool :name))))
                   
                   ;; Import the tool if it doesn't exist
                   (unless (gethash full-tool-name ai-auto-complete-tools)
                     (ai-auto-complete-mcp-import-server-as-tool server-name))
                   
                   ;; Add the tool name to the list
                   (push full-tool-name tool-names)))
               
               ;; Update the agent's tools
               (let* ((agent-tools (plist-get agent :tools))
                      (updated-tools (append agent-tools tool-names)))
                 
                 ;; Remove duplicates
                 (setq updated-tools (delete-dups updated-tools))
                 
                 ;; Update the agent
                 (puthash agent-name
                          (plist-put agent :tools updated-tools)
                          ai-auto-complete-agents)
                 
                 (message "Registered MCP server %s with agent %s (%d tools)" 
                          server-name agent-name (length tool-names)))))))))))

;; Create a new agent from an MCP server
(defun ai-auto-complete-mcp-create-agent-from-server (server-name)
  "Create a new agent based on MCP SERVER-NAME."
  (interactive
   (list (completing-read "Select MCP server: " (ai-auto-complete-mcp-list-servers))))
  
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-create-agent-from-server nil))
  
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      ;; Start the server if it's not running
      (unless (eq (plist-get server :status) 'running)
        (ai-auto-complete-mcp-start-server server-name))
      
      ;; List tools from the server
      (ai-auto-complete-mcp-list-tools
       server-name
       (lambda (tools)
         (if (stringp tools)
             (message "Error listing tools from server %s: %s" server-name tools)
           ;; Create a new agent
           (let ((agent-name (format "mcp-%s" server-name))
                 (description (format "Agent for MCP server: %s" server-name))
                 (prompt (format "You are an agent for the MCP server '%s'. You have access to the following tools from this server." server-name))
                 (tool-names '()))
             
             ;; Import the server's tools
             (ai-auto-complete-mcp-import-server-as-tool server-name)
             
             ;; Collect tool names
             (dolist (tool tools)
               (let ((tool-name (plist-get tool :name))
                     (full-tool-name (format "mcp:%s:%s" server-name (plist-get tool :name))))
                 (push full-tool-name tool-names)))
             
             ;; Register the agent
             (ai-auto-complete-register-agent
              agent-name
              description
              prompt
              (nreverse tool-names))
             
             (message "Created agent %s for MCP server %s" agent-name server-name)
             agent-name)))))))

;; Register all MCP servers as agents
(defun ai-auto-complete-mcp-register-all-servers-as-agents ()
  "Register all MCP servers as agents."
  (interactive)
  
  (let ((servers (ai-auto-complete-mcp-list-servers))
        (registered 0))
    (dolist (server-name servers)
      (when (ai-auto-complete-mcp-create-agent-from-server server-name)
        (setq registered (1+ registered))))
    (message "Registered %d of %d MCP servers as agents" registered (length servers))))

;; Create an MCP agent that can access all MCP servers
(defun ai-auto-complete-mcp-create-master-agent ()
  "Create a master agent that can access all MCP servers."
  (interactive)
  
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-create-master-agent nil))
  
  (let ((servers (ai-auto-complete-mcp-list-servers))
        (tool-names '()))
    
    ;; Import all servers as tools
    (ai-auto-complete-mcp-import-all-servers-as-tools)
    
    ;; Collect all tool names
    (dolist (server-name servers)
      (ai-auto-complete-mcp-list-tools
       server-name
       (lambda (tools)
         (unless (stringp tools)
           (dolist (tool tools)
             (let ((tool-name (plist-get tool :name))
                   (full-tool-name (format "mcp:%s:%s" server-name (plist-get tool :name))))
               (push full-tool-name tool-names)))))))
    
    ;; Wait for all tool names to be collected
    (let ((timeout 10)
          (start-time (current-time)))
      (while (and (< (length tool-names) 1)
                  (< (float-time (time-since start-time)) timeout))
        (sleep-for 0.1)))
    
    ;; Register the master agent
    (ai-auto-complete-register-agent
     "mcp-master"
     "Master agent for all MCP servers"
     "You are a master agent with access to all MCP servers. You can use any of the tools provided by these servers."
     (nreverse tool-names))
    
    (message "Created master agent for all MCP servers with %d tools" (length tool-names))
    "mcp-master"))

(provide 'mcp/mcp-agents)
;;; mcp-agents.el ends here
