;;; mcp-tools-ui.el --- UI for managing MCP server tools -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides UI functions for managing MCP server tools.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-tools-bridge)
(require 'mcp/mcp-persistence)
(require 'json)
(require 'widget)
(require 'wid-edit)

;; Display a UI for managing MCP server tools
(defun ai-auto-complete-mcp-manage-server-tools (server-name)
  "Display a UI for managing tools for MCP server with SERVER-NAME."
  (interactive
   (list (completing-read "Select MCP server: " (ai-auto-complete-mcp-list-servers))))

  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (buffer-name (format "*MCP Server Tools: %s*" server-name)))

    (if (not server)
        (message "MCP server %s not found" server-name)

      ;; Create and set up the buffer
      (with-current-buffer (get-buffer-create buffer-name)
        (let ((inhibit-read-only t))
          (erase-buffer)
          (special-mode) ;; Use special-mode for the buffer

          ;; Add header
          (insert (format "MCP Server Tools: %s\n" server-name))
          (insert "=======================\n\n")

          ;; Display server info
          (insert (format "Path: %s\n" (plist-get server :path)))
          (insert (format "Status: %s\n" (plist-get server :status)))
          (insert (format "Transport: %s\n" (plist-get server :transport)))
          (insert (format "Runner: %s\n" (or (plist-get server :runner) "default")))
          (insert "\n")

          ;; Get existing tools from server configuration
          (let ((tools (plist-get server :tools)))
            (insert "Predefined Tools (from server configuration):\n")
            (insert "-------------------------------------------\n")

            (if (not tools)
                (insert "No predefined tools. Add tools below.\n\n")

              ;; Check if tools is a hash table (unexpected format)
              (if (hash-table-p tools)
                  (progn
                    (insert "Tools data is in an unexpected format (hash table).\n")
                    (insert "Please use the 'Add New Tool' button below to add tools.\n\n"))

                ;; Check if tools is a list (expected format)
                (if (not (listp tools))
                    (progn
                      (insert (format "Tools data is in an unexpected format: %s\n" (type-of tools)))
                      (insert "Please use the 'Add New Tool' button below to add tools.\n\n"))

                  ;; Display existing tools (list format)
                  (dolist (tool tools)
                    (if (not (listp tool))
                        (insert (format "Warning: Invalid tool format: %s\n\n" tool))

                      (let ((tool-name (plist-get tool :name))
                            (description (plist-get tool :description))
                            (parameters (plist-get tool :parameters))
                            (input-schema (plist-get tool :inputSchema)))

                        (insert (format "• %s: %s\n"
                                        (or tool-name "Unnamed Tool")
                                        (or description "No description")))

                        ;; Display inputSchema if available
                        (when input-schema
                          (insert "  Input Schema:\n")
                          (let ((schema-type (plist-get input-schema :type))
                                (properties (plist-get input-schema :properties))
                                (required (plist-get input-schema :required)))

                            (when schema-type
                              (insert (format "    Type: %s\n" schema-type)))

                            (when properties
                              (insert "    Properties:\n")
                              (let ((prop-name nil)
                                    (prop-info nil))
                                (dolist (item properties)
                                  (if prop-name
                                      (progn
                                        ;; We have a property name and now its value
                                        (setq prop-info item)

                                        ;; Process this property
                                        (let* ((prop-type (plist-get prop-info :type))
                                               (prop-desc (plist-get prop-info :description))
                                               (is-required (and required (member (symbol-name prop-name) required))))

                                          (insert (format "      - %s: %s (Type: %s)%s\n"
                                                          (symbol-name prop-name)
                                                          (or prop-desc "No description")
                                                          (or prop-type "unknown")
                                                          (if is-required " (required)" ""))))

                                        ;; Reset for next pair
                                        (setq prop-name nil)
                                        (setq prop-info nil))
                                    ;; This is a property name
                                    (setq prop-name item))))))

                          ;; Add a blank line after inputSchema
                          (insert "\n"))

                        ;; Display parameters
                        (when parameters
                          (insert "  Parameters:\n")
                          (if (listp parameters)
                              (dolist (param parameters)
                                (if (listp param)
                                    (let ((param-name (plist-get param :name))
                                          (param-description (plist-get param :description))
                                          (param-required (plist-get param :required))
                                          (param-input-schema (plist-get param :inputSchema)))

                                      ;; Display parameter name and description
                                      (insert (format "    - %s: %s%s\n"
                                                      (or param-name "unnamed")
                                                      (or param-description "")
                                                      (if param-required " (required)" "")))

                                      ;; Display inputSchema if available
                                      (when param-input-schema
                                        (let ((schema-type (plist-get param-input-schema :type))
                                              (enum-values (plist-get param-input-schema :enum)))

                                          ;; Display type
                                          (when schema-type
                                            (insert (format "      Type: %s\n" schema-type)))

                                          ;; Display enum values if available
                                          (when enum-values
                                            (insert "      Allowed values: ")
                                            (let ((values-str (mapconcat (lambda (val)
                                                                          (format "\"%s\"" val))
                                                                        enum-values ", ")))
                                              (insert (format "%s\n" values-str)))))))
                                  (insert (format "    - Invalid parameter format: %s\n" param))))
                            (insert (format "    - Invalid parameters format: %s\n" parameters))))

                        ;; Add edit and delete buttons
                        (when tool-name
                          (insert "  ")
                          (insert-button "[Edit]"
                                        'action (lambda (_)
                                                 (ai-auto-complete-mcp-edit-tool server-name tool-name))
                                        'follow-link t)
                          (insert " ")
                          (insert-button "[Delete]"
                                        'action (lambda (_)
                                                 (when (yes-or-no-p (format "Delete tool %s? " tool-name))
                                                   (ai-auto-complete-mcp-delete-tool server-name tool-name)
                                                   (ai-auto-complete-mcp-manage-server-tools server-name)))
                                        'follow-link t))
                        (insert "\n\n")))))))

            ;; Add button to add a new tool
            (insert-button "Add New Tool"
                          'action (lambda (_)
                                   (ai-auto-complete-mcp-add-tool server-name))
                          'follow-link t)

            ;; Add button to import tools from server
            (insert " | ")
            (insert-button "Import Tools from Server"
                          'action (lambda (_)
                                   (ai-auto-complete-mcp-import-server-as-tool server-name)
                                   (ai-auto-complete-mcp-manage-server-tools server-name))
                          'follow-link t)

            ;; Add a prominent save button
            (insert "\n\n")
            (insert-button "SAVE ALL CHANGES"
                          'face '(:background "green" :foreground "black" :weight bold)
                          'action (lambda (_)
                                   (ai-auto-complete-mcp-save-servers)
                                   (message "Saved MCP server configurations"))
                          'follow-link t)

            ;; Add a section for already imported tools
            (insert "\n\n")
            (insert "Imported Tools (already registered in the system):\n")
            (insert "----------------------------------------------\n")

            ;; Check for imported tools in ai-auto-complete-tools
            (let ((imported-tools 0)
                  (imported-tool-list '()))

              ;; Find all tools for this server
              (maphash (lambda (name tool)
                         (when (string-match (format "^mcp:%s:" server-name) name)
                           (push (cons name tool) imported-tool-list)
                           (setq imported-tools (1+ imported-tools))))
                       ai-auto-complete-tools)

              ;; Display imported tools
              (if (= imported-tools 0)
                  (insert "No tools have been imported from this server yet.\n\n")

                ;; Sort tools by name for consistent display
                (setq imported-tool-list (sort imported-tool-list
                                              (lambda (a b) (string< (car a) (car b)))))

                ;; Display each imported tool
                (dolist (tool-entry imported-tool-list)
                  (let* ((full-name (car tool-entry))
                         (tool (cdr tool-entry))
                         (description (plist-get tool :description))
                         (parameters (plist-get tool :parameters))
                         (tool-name (replace-regexp-in-string (format "^mcp:%s:" server-name) "" full-name)))

                    (insert (format "• %s: %s\n" tool-name description))

                    ;; Display parameters
                    (when parameters
                      (insert "  Parameters:\n")
                      (dolist (param parameters)
                        (insert (format "    - %s: %s\n"
                                        (car param)
                                        (cdr param)))))

                    ;; Add button to save this tool as a predefined tool
                    (insert "  ")
                    (insert-button "[Save as Predefined Tool]"
                                  'action (lambda (_)
                                           (ai-auto-complete-mcp-save-imported-tool-as-predefined
                                            server-name tool-name description parameters)
                                           (ai-auto-complete-mcp-manage-server-tools server-name))
                                  'follow-link t)
                    (insert "\n\n")))

                ;; Add button to save all imported tools as predefined tools
                (when (> imported-tools 0)
                  (insert-button "Save All as Predefined Tools"
                                'action (lambda (_)
                                         (ai-auto-complete-mcp-save-all-imported-tools-as-predefined server-name)
                                         (ai-auto-complete-mcp-manage-server-tools server-name))
                                'follow-link t)
                  (insert "\n\n"))))

            ;; Add a section for server-provided tools, resources, and prompts
            (insert "\n\n")
            (insert "Server-Provided Data (direct from server):\n")
            (insert "---------------------------------------\n")
            (insert "Fetching data from server...\n")

            ;; Create a marker for where to insert server data
            (let ((server-data-marker (point-marker)))

              ;; Add refresh button
              (insert "\n")
              (insert-button "Refresh Server Data"
                            'action (lambda (_)
                                     (let ((inhibit-read-only t))
                                       (save-excursion
                                         (goto-char server-data-marker)
                                         (delete-region server-data-marker (point-max))
                                         (insert "Fetching data from server...\n\n")

                                         ;; Fetch server data
                                         (ai-auto-complete-mcp-fetch-server-data
                                          server-name
                                          (lambda (data)
                                            (let ((inhibit-read-only t))
                                              (save-excursion
                                                (goto-char server-data-marker)
                                                (delete-region server-data-marker (point-max))

                                                ;; Display tools
                                                (insert "Server Tools:\n")
                                                (let ((tools (plist-get data :tools)))
                                                  (if (or (not tools) (stringp tools))
                                                      (insert "  No tools available from server\n\n")
                                                    (dolist (tool tools)
                                                      (let ((tool-name (plist-get tool :name))
                                                            (description (plist-get tool :description))
                                                            (parameters (plist-get tool :parameters)))
                                                        (insert (format "• %s: %s\n"
                                                                        (or tool-name "Unnamed Tool")
                                                                        (or description "No description")))

                                                        ;; Display parameters
                                                        (when parameters
                                                          (insert "  Parameters:\n")
                                                          (if (listp parameters)
                                                              (dolist (param parameters)
                                                                (if (listp param)
                                                                    (let ((param-name (plist-get param :name))
                                                                          (param-description (plist-get param :description))
                                                                          (param-required (plist-get param :required)))
                                                                      (insert (format "    - %s: %s%s\n"
                                                                                      (or param-name "unnamed")
                                                                                      (or param-description "")
                                                                                      (if param-required " (required)" ""))))
                                                                  (insert (format "    - Invalid parameter format: %s\n" param))))
                                                            (insert (format "    - Invalid parameters format: %s\n" parameters))))
                                                        (insert "\n")))))

                                                ;; Display resources
                                                (insert "Server Resources:\n")
                                                (let ((resources (plist-get data :resources)))
                                                  (if (or (not resources) (stringp resources))
                                                      (insert "  No resources available from server\n\n")
                                                    (dolist (resource resources)
                                                      (let ((uri-template (plist-get resource :uriTemplate))
                                                            (description (plist-get resource :description)))
                                                        (insert (format "• %s: %s\n\n"
                                                                        (or uri-template "Unnamed Resource")
                                                                        (or description "No description")))))))

                                                ;; Display prompts
                                                (insert "Server Prompts:\n")
                                                (let ((prompts (plist-get data :prompts)))
                                                  (if (or (not prompts) (stringp prompts))
                                                      (insert "  No prompts available from server\n\n")
                                                    (dolist (prompt prompts)
                                                      (let ((name (plist-get prompt :name))
                                                            (description (plist-get prompt :description)))
                                                        (insert (format "• %s: %s\n\n"
                                                                        (or name "Unnamed Prompt")
                                                                        (or description "No description")))))))

                                                ;; Add refresh button
                                                (insert "\n")
                                                (insert-button "Refresh Server Data"
                                                              'action (lambda (_)
                                                                       (let ((inhibit-read-only t))
                                                                         (save-excursion
                                                                           (goto-char server-data-marker)
                                                                           (delete-region server-data-marker (point-max))
                                                                           (insert "Fetching data from server...\n\n")
                                                                           (ai-auto-complete-mcp-fetch-server-data server-name
                                                                                                                  (lambda (data)
                                                                                                                    (message "Server data refreshed")))))))
                                                              'follow-link t)

                                                ;; Add buttons for server actions
                                                (insert "\n\n")
                                                (insert-button "Save Changes"
                                                              'action (lambda (_)
                                                                       (ai-auto-complete-mcp-save-servers)
                                                                       (message "Saved MCP server configurations"))
                                                              'follow-link t)

                                                ;; Add button to close
                                                (insert " | ")
                                                (insert-button "Close"
                                                              'action (lambda (_)
                                                                       (kill-buffer buffer-name))
                                                              'follow-link t)

                                                (insert "\n\n")
                                                (insert "Note: Changes are automatically saved if auto-save is enabled.\n")
                                                (insert "Current auto-save status: ")
                                                (if (and (boundp 'ai-auto-complete-mcp-auto-save-enabled)
                                                         ai-auto-complete-mcp-auto-save-enabled)
                                                    (insert "Enabled\n")
                                                  (insert "Disabled\n"))))))))
                            'follow-link t)

              ;; Fetch server data
              (ai-auto-complete-mcp-fetch-server-data
               server-name
               (lambda (data)
                 (let ((inhibit-read-only t))
                   (save-excursion
                     (goto-char server-data-marker)
                     (delete-region server-data-marker (point-max))

                     ;; Display tools
                     (insert "Server Tools:\n")
                     (let ((tools (plist-get data :tools)))
                       (if (or (not tools) (stringp tools))
                           (insert "  No tools available from server\n\n")
                         (dolist (tool tools)
                           (let ((tool-name (plist-get tool :name))
                                 (description (plist-get tool :description))
                                 (parameters (plist-get tool :parameters)))
                             (insert (format "• %s: %s\n"
                                             (or tool-name "Unnamed Tool")
                                             (or description "No description")))

                             ;; Display parameters
                             (when parameters
                               (insert "  Parameters:\n")
                               (if (listp parameters)
                                   (dolist (param parameters)
                                     (if (listp param)
                                         (let ((param-name (plist-get param :name))
                                               (param-description (plist-get param :description))
                                               (param-required (plist-get param :required)))
                                           (insert (format "    - %s: %s%s\n"
                                                           (or param-name "unnamed")
                                                           (or param-description "")
                                                           (if param-required " (required)" ""))))
                                       (insert (format "    - Invalid parameter format: %s\n" param))))
                                 (insert (format "    - Invalid parameters format: %s\n" parameters))))
                             (insert "\n")))))

                     ;; Display resources
                     (insert "Server Resources:\n")
                     (let ((resources (plist-get data :resources)))
                       (if (or (not resources) (stringp resources))
                           (insert "  No resources available from server\n\n")
                         (dolist (resource resources)
                           (let ((uri-template (plist-get resource :uriTemplate))
                                 (description (plist-get resource :description)))
                             (insert (format "• %s: %s\n\n"
                                             (or uri-template "Unnamed Resource")
                                             (or description "No description")))))))

                     ;; Display prompts
                     (insert "Server Prompts:\n")
                     (let ((prompts (plist-get data :prompts)))
                       (if (or (not prompts) (stringp prompts))
                           (insert "  No prompts available from server\n\n")
                         (dolist (prompt prompts)
                           (let ((name (plist-get prompt :name))
                                 (description (plist-get prompt :description)))
                             (insert (format "• %s: %s\n\n"
                                             (or name "Unnamed Prompt")
                                             (or description "No description")))))))

                     ;; Add refresh button
                     (insert "\n")
                     (insert-button "Refresh Server Data"
                                   'action (lambda (_)
                                            (let ((inhibit-read-only t))
                                              (save-excursion
                                                (goto-char server-data-marker)
                                                (delete-region server-data-marker (point-max))
                                                (insert "Fetching data from server...\n\n")
                                                (ai-auto-complete-mcp-fetch-server-data server-name
                                                                                       (lambda (data)
                                                                                         (message "Server data refreshed")))))))
                                   'follow-link t)

                     ;; Add buttons for server actions
                     (insert "\n\n")
                     (insert-button "Save Changes"
                                   'action (lambda (_)
                                            (ai-auto-complete-mcp-save-servers)
                                            (message "Saved MCP server configurations"))
                                   'follow-link t)

                     ;; Add button to close
                     (insert " | ")
                     (insert-button "Close"
                                   'action (lambda (_)
                                            (kill-buffer buffer-name))
                                   'follow-link t)

                     (insert "\n\n")
                     (insert "Note: Changes are automatically saved if auto-save is enabled.\n")
                     (insert "Current auto-save status: ")
                     (if (and (boundp 'ai-auto-complete-mcp-auto-save-enabled)
                              ai-auto-complete-mcp-auto-save-enabled)
                         (insert "Enabled\n")
                       (insert "Disabled\n"))))))))

            (goto-char (point-min))))

        (switch-to-buffer buffer-name)))

;; Add a new tool to an MCP server
(defun ai-auto-complete-mcp-add-tool (server-name)
  "Add a new tool to MCP server with SERVER-NAME."
  (interactive
   (list (completing-read "Select MCP server: " (ai-auto-complete-mcp-list-servers))))

  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (buffer-name (format "*Add MCP Tool: %s*" server-name)))

    (if (not server)
        (message "MCP server %s not found" server-name)

      ;; Create and set up the buffer
      (with-current-buffer (get-buffer-create buffer-name)
        (let ((inhibit-read-only t))
          (erase-buffer)
          (kill-all-local-variables)
          (remove-overlays)

          ;; Use widget mode
          (widget-setup)

          ;; Add header
          (widget-insert (format "Add Tool to MCP Server: %s\n" server-name))
          (widget-insert "==========================\n\n")

          ;; Create form fields
          (widget-insert "Tool Name: ")
          (let ((name-field (widget-create 'editable-field
                                          :size 30
                                          :format "%v")))

            (widget-insert "\nDescription: ")
            (let ((desc-field (widget-create 'editable-field
                                            :size 50
                                            :format "%v")))

              (widget-insert "\n\nParameters (JSON format):\n")
              (widget-insert "Example: [{\"name\": \"url\", \"description\": \"URL to fetch\", \"required\": true, \"inputSchema\": {\"type\": \"string\", \"enum\": [\"http://example.com\", \"https://example.org\"]}}]\n")
              (let ((params-field (widget-create 'text
                                               :size 60
                                               :format "%v"
                                               "[{\"name\": \"parameter1\", \"description\": \"First parameter\", \"required\": true, \"inputSchema\": {\"type\": \"string\"}}]")))

                ;; Define the save function
                (defun ai-auto-complete-mcp-save-tool-action ()
                  "Save the tool and update the server configuration."
                  (interactive)
                  (let ((tool-name (widget-value name-field))
                        (description (widget-value desc-field))
                        (params-json (widget-value params-field)))

                    ;; Validate input
                    (cond
                     ((string-empty-p tool-name)
                      (message "Tool name cannot be empty"))
                     ((string-empty-p description)
                      (message "Description cannot be empty"))
                     (t
                      ;; Parse parameters
                      (condition-case err
                          (let* ((json-array-type 'list)
                                 (json-object-type 'plist)
                                 (json-key-type 'keyword)
                                 (parameters (json-read-from-string params-json)))

                            ;; Verify each parameter has name and description
                            (let ((valid-params t))
                              (dolist (param parameters)
                                (unless (and (plist-get param :name)
                                           (plist-get param :description))
                                  (setq valid-params nil)
                                  (message "Each parameter must have a name and description")))

                              (if valid-params
                                  ;; Create a tool plist with the new format
                                  (let ((tool-plist (list :name tool-name
                                                         :description description
                                                         :parameters parameters)))

                                    ;; Add the tool
                                    (ai-auto-complete-mcp-add-server-tool-with-schema
                                     server-name tool-plist)

                                    ;; Explicitly save the server configuration
                                    (ai-auto-complete-mcp-save-servers)
                                    (message "Added tool %s and saved server configuration" tool-name)

                                    ;; Close the buffer and refresh the tools UI
                                    (kill-buffer buffer-name)
                                    (ai-auto-complete-mcp-manage-server-tools server-name)))))
                        (error
                         (message "Error parsing JSON: %s" (error-message-string err))))))))

                ;; Add prominent save button
                (widget-insert "\n\n")
                (widget-create 'push-button
                              :notify (lambda (&rest _) (ai-auto-complete-mcp-save-tool-action))
                              :button-face '(:background "green" :foreground "black" :weight bold)
                              "SAVE TOOL")

                ;; Regular add button
                (widget-insert "\n\n")
                (widget-create 'push-button
                              :notify (lambda (&rest _) (ai-auto-complete-mcp-save-tool-action))
                              "Add and Save")

                ;; Cancel button
                (widget-insert " ")
                (widget-create 'push-button
                              :notify (lambda (&rest _)
                                        (kill-buffer buffer-name))
                              "Cancel")

                ;; Help text
                (widget-insert "\n\nParameter Format Help:\n")
                (widget-insert "Each parameter should have at least a 'name' field.\n")
                (widget-insert "Optional fields: 'description', 'required', 'schema'.\n")
                (widget-insert "Example for a single parameter:\n")
                (widget-insert "  {\"name\": \"url\", \"description\": \"URL to fetch\", \"required\": true}\n")
                (widget-insert "Example for multiple parameters:\n")
                (widget-insert "  [{\"name\": \"url\", \"description\": \"URL to fetch\", \"required\": true},\n")
                (widget-insert "   {\"name\": \"headers\", \"description\": \"Optional headers\", \"required\": false}]\n")))))

          ;; Set up keymap
          (use-local-map widget-keymap)
          (local-set-key (kbd "C-c C-c") (lambda () (interactive)
                                           (ai-auto-complete-mcp-save-tool-action)))

          ;; Finalize widget setup
          (widget-setup))

        ;; Switch to the buffer
        (switch-to-buffer buffer-name))))

;; Edit an existing tool
(defun ai-auto-complete-mcp-edit-tool (server-name tool-name)
  "Edit tool TOOL-NAME for MCP server with SERVER-NAME."
  (interactive
   (let* ((server-name (completing-read "Select MCP server: " (ai-auto-complete-mcp-list-servers)))
          (server (ai-auto-complete-mcp-get-server server-name))
          (tools (plist-get server :tools))
          (tool-names '()))

     ;; Extract tool names safely
     (cond
      ((not tools)
       (message "No tools found for server %s" server-name))
      ((hash-table-p tools)
       (message "Warning: Tools data is in an unexpected format (hash table)"))
      ((not (listp tools))
       (message "Warning: Tools data is in an unexpected format: %s" (type-of tools)))
      (t
       (dolist (tool tools)
         (when (and (listp tool) (plist-get tool :name))
           (push (plist-get tool :name) tool-names)))))

     (if (null tool-names)
         (progn
           (message "No valid tools found for server %s" server-name)
           (list server-name ""))
       (list server-name (completing-read "Select tool to edit: " tool-names)))))

  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (tools (plist-get server :tools))
         (tool nil)
         (buffer-name (format "*Edit MCP Tool: %s - %s*" server-name tool-name)))

    ;; Find the tool safely
    (cond
     ((not tools)
      (setq tool nil))
     ((hash-table-p tools)
      (message "Warning: Tools data is in an unexpected format (hash table)"))
     ((not (listp tools))
      (message "Warning: Tools data is in an unexpected format: %s" (type-of tools)))
     (t
      (setq tool (cl-find-if (lambda (t)
                               (and (listp t)
                                    (string= (plist-get t :name) tool-name)))
                             tools))))

    (if (not tool)
        (message "Tool %s not found for server %s" tool-name server-name)

      ;; Create and set up the buffer
      (with-current-buffer (get-buffer-create buffer-name)
        (let ((inhibit-read-only t))
          (erase-buffer)
          (kill-all-local-variables)
          (remove-overlays)

          ;; Use widget mode
          (widget-setup)

          ;; Add header
          (widget-insert (format "Edit Tool: %s - %s\n" server-name tool-name))
          (widget-insert "==========================\n\n")

          ;; Tool name field (read-only)
          (widget-insert (format "Tool Name: %s\n" tool-name))

          ;; Tool description field
          (widget-insert "Description: ")
          (let ((desc-field (widget-create 'editable-field
                                          :size 50
                                          :format "%v"
                                          (plist-get tool :description))))

            ;; Parameters section
            (widget-insert "\n\nParameters (JSON format):\n")
            (widget-insert "Example: [{\"name\": \"url\", \"description\": \"URL to fetch\", \"required\": true}]\n")

            ;; Convert parameters to JSON
            (let* ((parameters (plist-get tool :parameters))
                   (params-json (if parameters
                                    (json-encode parameters)
                                  "[]"))
                   (params-field (widget-create 'text
                                               :size 60
                                               :format "%v"
                                               params-json)))

              ;; Define the save function
              (defun ai-auto-complete-mcp-update-tool-action ()
                "Update the tool and save the server configuration."
                (interactive)
                (let ((description (widget-value desc-field))
                      (params-json (widget-value params-field)))

                  ;; Validate input
                  (cond
                   ((string-empty-p description)
                    (message "Description cannot be empty"))
                   (t
                    ;; Parse parameters
                    (condition-case err
                        (let* ((json-array-type 'list)
                               (json-object-type 'plist)
                               (json-key-type 'keyword)
                               (parameters (json-read-from-string params-json)))

                          ;; Update the tool
                          (ai-auto-complete-mcp-update-server-tool
                           server-name tool-name description parameters)

                          ;; Explicitly save the server configuration
                          (ai-auto-complete-mcp-save-servers)
                          (message "Updated tool %s and saved server configuration" tool-name)

                          ;; Close the buffer and refresh the tools UI
                          (kill-buffer buffer-name)
                          (ai-auto-complete-mcp-manage-server-tools server-name))
                      (error
                       (message "Error parsing parameters: %s" (error-message-string err))))))))

              ;; Add prominent save button
              (widget-insert "\n\n")
              (widget-create 'push-button
                            :notify (lambda (&rest _) (ai-auto-complete-mcp-update-tool-action))
                            :button-face '(:background "green" :foreground "black" :weight bold)
                            "SAVE TOOL")

              ;; Regular update button
              (widget-insert "\n\n")
              (widget-create 'push-button
                            :notify (lambda (&rest _) (ai-auto-complete-mcp-update-tool-action))
                            "Update and Save")

              ;; Cancel button
              (widget-insert " ")
              (widget-create 'push-button
                            :notify (lambda (&rest _)
                                      (kill-buffer buffer-name))
                            "Cancel")

              ;; Help text
              (widget-insert "\n\nParameter Format Help:\n")
              (widget-insert "Each parameter should have at least a 'name' field.\n")
              (widget-insert "Optional fields: 'description', 'required', 'schema'.\n")
              (widget-insert "Example for a single parameter:\n")
              (widget-insert "  {\"name\": \"url\", \"description\": \"URL to fetch\", \"required\": true}\n")
              (widget-insert "Example for multiple parameters:\n")
              (widget-insert "  [{\"name\": \"url\", \"description\": \"URL to fetch\", \"required\": true},\n")
              (widget-insert "   {\"name\": \"headers\", \"description\": \"Optional headers\", \"required\": false}]\n")))

          ;; Set up keymap
          (use-local-map widget-keymap)
          (local-set-key (kbd "C-c C-c") (lambda () (interactive)
                                           (ai-auto-complete-mcp-update-tool-action)))

          ;; Finalize widget setup
          (widget-setup))

        ;; Switch to the buffer
        (switch-to-buffer buffer-name)))))

;; Add a tool to an MCP server
(defun ai-auto-complete-mcp-add-server-tool (server-name tool-name description parameters)
  "Add a tool with TOOL-NAME, DESCRIPTION, and PARAMETERS to MCP server with SERVER-NAME."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (tools (plist-get server :tools))
         (new-tool (list :name tool-name
                         :description description
                         :parameters parameters))
         (updated-tools nil))

    ;; Handle different tools data structures
    (cond
     ;; No tools or empty list
     ((or (not tools) (and (listp tools) (null tools)))
      (setq updated-tools (list new-tool)))

     ;; Hash table (convert to list format)
     ((hash-table-p tools)
      (if (fboundp 'ai-auto-complete-mcp-convert-tools-to-list)
          (let ((tools-list (ai-auto-complete-mcp-convert-tools-to-list tools server-name)))
            ;; Check if a tool with this name already exists in the converted list
            (let ((exists nil))
              (dolist (tool tools-list)
                (when (and (listp tool)
                           (string= (plist-get tool :name) tool-name))
                  (setq exists t)))

              ;; If tool already exists, update it instead
              (if exists
                  (ai-auto-complete-mcp-update-server-tool server-name tool-name description parameters)
                ;; Otherwise add the new tool to the list
                (setq updated-tools (cons new-tool tools-list)))))
        ;; If conversion function not available, just use the new tool
        (message "Warning: Tools data is in hash table format and conversion function not available")
        (setq updated-tools (list new-tool))))

     ;; Not a list (unexpected format)
     ((not (listp tools))
      (message "Warning: Tools data is in an unexpected format: %s" (type-of tools))
      (setq updated-tools (list new-tool)))

     ;; List (expected format)
     (t
      ;; Check if a tool with this name already exists
      (let ((exists nil))
        (dolist (tool tools)
          (when (and (listp tool)
                     (string= (plist-get tool :name) tool-name))
            (setq exists t)))

        ;; If tool already exists, update it instead
        (if exists
            (ai-auto-complete-mcp-update-server-tool server-name tool-name description parameters)
          ;; Otherwise add the new tool to the list
          (setq updated-tools (cons new-tool tools))))))

    ;; Update the server if we have updated tools
    (when updated-tools
      (puthash server-name
               (plist-put server :tools updated-tools)
               ai-auto-complete-mcp-servers)

      ;; Auto-save if enabled
      (ai-auto-complete-mcp-auto-save-servers))

    (message "Added tool %s to server %s" tool-name server-name)))

;; Update a tool in an MCP server
(defun ai-auto-complete-mcp-update-server-tool (server-name tool-name description parameters)
  "Update tool TOOL-NAME with DESCRIPTION and PARAMETERS in MCP server with SERVER-NAME."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (tools (plist-get server :tools))
         (updated-tools '()))

    ;; Handle different tools data structures
    (cond
     ;; No tools
     ((not tools)
      (setq updated-tools (list (list :name tool-name
                                     :description description
                                     :parameters parameters))))

     ;; Hash table (convert to list format)
     ((hash-table-p tools)
      (if (fboundp 'ai-auto-complete-mcp-convert-tools-to-list)
          (let ((tools-list (ai-auto-complete-mcp-convert-tools-to-list tools server-name)))
            ;; Create updated tools list from the converted list
            (let ((found nil))
              (dolist (tool tools-list)
                (if (and (listp tool) (string= (plist-get tool :name) tool-name))
                    (progn
                      ;; Update this tool
                      (push (list :name tool-name
                                 :description description
                                 :parameters parameters)
                           updated-tools)
                      (setq found t))
                  ;; Keep this tool as is
                  (push tool updated-tools)))

              ;; If tool wasn't found, add it
              (unless found
                (push (list :name tool-name
                           :description description
                           :parameters parameters)
                     updated-tools))

              ;; Reverse the list to maintain original order
              (setq updated-tools (nreverse updated-tools))))
        ;; If conversion function not available, just use the new tool
        (message "Warning: Tools data is in hash table format and conversion function not available")
        (setq updated-tools (list (list :name tool-name
                                       :description description
                                       :parameters parameters)))))

     ;; Not a list (unexpected format)
     ((not (listp tools))
      (message "Warning: Tools data is in an unexpected format: %s" (type-of tools))
      (setq updated-tools (list (list :name tool-name
                                     :description description
                                     :parameters parameters))))

     ;; List (expected format)
     (t
      ;; Create updated tools list
      (let ((found nil))
        (dolist (tool tools)
          (if (and (listp tool) (string= (plist-get tool :name) tool-name))
              (progn
                ;; Update this tool
                (push (list :name tool-name
                           :description description
                           :parameters parameters)
                     updated-tools)
                (setq found t))
            ;; Keep this tool as is
            (push tool updated-tools)))

        ;; If tool wasn't found, add it
        (unless found
          (push (list :name tool-name
                     :description description
                     :parameters parameters)
               updated-tools)))

      ;; Reverse the list to maintain original order
      (setq updated-tools (nreverse updated-tools))))

    ;; Update the server
    (puthash server-name
             (plist-put server :tools updated-tools)
             ai-auto-complete-mcp-servers)

    ;; Auto-save if enabled
    (ai-auto-complete-mcp-auto-save-servers)

    (message "Updated tool %s in server %s" tool-name server-name)))

;; Delete a tool from an MCP server
(defun ai-auto-complete-mcp-delete-tool (server-name tool-name)
  "Delete tool TOOL-NAME from MCP server with SERVER-NAME."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (tools (plist-get server :tools))
         (updated-tools nil))

    ;; Handle different tools data structures
    (cond
     ;; No tools
     ((not tools)
      (message "No tools found for server %s" server-name))

     ;; Hash table (convert to list format)
     ((hash-table-p tools)
      (if (fboundp 'ai-auto-complete-mcp-convert-tools-to-list)
          (let ((tools-list (ai-auto-complete-mcp-convert-tools-to-list tools server-name)))
            ;; Filter out the tool to delete from the converted list
            (setq updated-tools
                  (cl-remove-if (lambda (tool)
                                  (and (listp tool)
                                       (string= (plist-get tool :name) tool-name)))
                                tools-list)))
        ;; If conversion function not available, just use an empty list
        (message "Warning: Tools data is in hash table format and conversion function not available")
        (setq updated-tools '())))

     ;; Not a list (unexpected format)
     ((not (listp tools))
      (message "Warning: Tools data is in an unexpected format: %s" (type-of tools))
      (setq updated-tools '()))

     ;; List (expected format)
     (t
      ;; Filter out the tool to delete
      (setq updated-tools
            (cl-remove-if (lambda (tool)
                            (and (listp tool)
                                 (string= (plist-get tool :name) tool-name)))
                          tools))))

    ;; Update the server if we have updated tools
    (when updated-tools
      (puthash server-name
               (plist-put server :tools updated-tools)
               ai-auto-complete-mcp-servers)

      ;; Auto-save if enabled
      (ai-auto-complete-mcp-auto-save-servers))

    (message "Deleted tool %s from server %s" tool-name server-name)))

;; Add button to MCP Servers UI to manage tools
(defun ai-auto-complete-mcp-add-manage-tools-button (server-name)
  "Add a button to manage tools for MCP server with SERVER-NAME."
  (insert-button "[Manage Tools]"
                'action (lambda (_)
                         (ai-auto-complete-mcp-manage-server-tools server-name))
                'follow-link t))

;; Save an imported tool as a predefined tool
(defun ai-auto-complete-mcp-save-imported-tool-as-predefined (server-name tool-name description parameters)
  "Save an imported tool as a predefined tool for SERVER-NAME.
TOOL-NAME is the name of the tool without the 'mcp:server:' prefix.
DESCRIPTION is the tool description.
PARAMETERS is the tool parameters alist."
  (interactive
   (let* ((server-name (completing-read "Select MCP server: " (ai-auto-complete-mcp-list-servers)))
          (imported-tools '())
          (tool-name nil)
          (description nil)
          (parameters nil))

     ;; Find all tools for this server
     (maphash (lambda (name tool)
                (when (string-match (format "^mcp:%s:" server-name) name)
                  (let ((short-name (replace-regexp-in-string (format "^mcp:%s:" server-name) "" name)))
                    (push short-name imported-tools))))
              ai-auto-complete-tools)

     ;; Select a tool
     (setq tool-name (completing-read "Select tool to save: " imported-tools))

     ;; Get tool details
     (let* ((full-name (format "mcp:%s:%s" server-name tool-name))
            (tool (gethash full-name ai-auto-complete-tools)))
       (setq description (plist-get tool :description))
       (setq parameters (plist-get tool :parameters)))

     (list server-name tool-name description parameters)))

  ;; Convert parameters from alist to new format with inputSchema
  (let ((param-list '()))

    ;; Process each parameter
    (dolist (param parameters)
      (let* ((param-name (car param))
             (param-description (cdr param))
             ;; Create inputSchema for this parameter
             (input-schema (list :type "string")))

        ;; Create parameter plist with inputSchema
        (push (list :name param-name
                   :description param-description
                   :required t
                   :inputSchema input-schema)
              param-list)))

    ;; Create tool plist with parameters that have inputSchema
    (let ((tool-plist (list :name tool-name
                           :description description
                           :parameters (nreverse param-list))))

      ;; Add the tool to the server's predefined tools
      (ai-auto-complete-mcp-add-server-tool-with-schema
       server-name tool-plist)

      ;; Save the server configuration
      (ai-auto-complete-mcp-save-servers)

      (message "Saved imported tool %s as predefined tool for server %s" tool-name server-name))))

;; Add a tool with schema to an MCP server
(defun ai-auto-complete-mcp-add-server-tool-with-schema (server-name tool-plist)
  "Add a tool with TOOL-PLIST to MCP server with SERVER-NAME.
TOOL-PLIST should contain :name, :description, and :parameters.
Each parameter in :parameters should have :name, :description, and optionally :inputSchema."
  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (tools (plist-get server :tools))
         (tool-name (plist-get tool-plist :name))
         (updated-tools nil))

    ;; Handle different tools data structures
    (cond
     ;; No tools or empty list
     ((or (not tools) (and (listp tools) (null tools)))
      (setq updated-tools (list tool-plist)))

     ;; List (expected format)
     (t
      ;; Check if a tool with this name already exists
      (let ((exists nil)
            (existing-tools '()))
        (dolist (tool tools)
          (if (and (listp tool)
                   (string= (plist-get tool :name) tool-name))
              (progn
                (setq exists t)
                (push tool-plist existing-tools))
            (push tool existing-tools)))

        ;; Set the updated tools list
        (setq updated-tools (nreverse existing-tools)))))

    ;; Update the server if we have updated tools
    (when updated-tools
      (puthash server-name
               (plist-put server :tools updated-tools)
               ai-auto-complete-mcp-servers)

      ;; Auto-save if enabled
      (ai-auto-complete-mcp-auto-save-servers))

    (message "Added tool %s to server %s" tool-name server-name)))

;; Save all imported tools as predefined tools
(defun ai-auto-complete-mcp-save-all-imported-tools-as-predefined (server-name)
  "Save all imported tools as predefined tools for SERVER-NAME."
  (interactive
   (list (completing-read "Select MCP server: " (ai-auto-complete-mcp-list-servers))))

  (let ((saved-count 0))
    ;; Find all tools for this server
    (maphash (lambda (name tool)
               (when (string-match (format "^mcp:%s:" server-name) name)
                 (let* ((tool-name (replace-regexp-in-string (format "^mcp:%s:" server-name) "" name))
                        (description (plist-get tool :description))
                        (parameters (plist-get tool :parameters))
                        (input-schema (plist-get tool :inputSchema)))

                   ;; Check if parameters already have inputSchema
                   (let ((has-param-schema nil))
                     (when parameters
                       (dolist (param parameters)
                         (when (and (listp param) (plist-get param :inputSchema))
                           (setq has-param-schema t))))

                     ;; If parameters already have inputSchema, use them directly
                     (if has-param-schema
                         (let ((tool-plist (list :name tool-name
                                                :description description
                                                :parameters parameters)))
                           ;; Add the tool to the server's predefined tools
                           (ai-auto-complete-mcp-add-server-tool-with-schema
                            server-name tool-plist))

                     ;; Otherwise, convert parameters to inputSchema
                     (ai-auto-complete-mcp-save-imported-tool-as-predefined
                      server-name tool-name description parameters))

                   (setq saved-count (1+ saved-count)))))
             ai-auto-complete-tools)

    ;; Save the server configuration
    (ai-auto-complete-mcp-save-servers)

    (message "Saved %d imported tools as predefined tools for server %s" saved-count server-name))))

(provide 'mcp/mcp-tools-ui)
;;; mcp-tools-ui.el ends here
