;;; mcp-prompt.el --- System prompt integration for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides system prompt integration for MCP in the AI Auto Complete package.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-client)
;(require 'prompts/prompts-core)

;; MCP system prompt template
(defcustom ai-auto-complete-mcp-system-prompt-template
  "You can use Model Context Protocol (MCP) tools by using the <mcp-tool> tag.

Example:
<mcp-tool server=\"server-name\" name=\"tool-name\">
<parameters>
{
  \"param1\": \"value1\",
  \"param2\": \"value2\"
}
</parameters>
</mcp-tool>

Available MCP servers:
%s

For more information about a specific server, use the format:
<mcp-tool server=\"server-name\" name=\"listTools\">
<parameters>
{}
</parameters>
</mcp-tool>"
  "Template for MCP system prompt.
%s will be replaced with the list of available MCP servers."
  :type 'string
  :group 'ai-auto-complete-mcp)

;; Generate MCP system prompt
(defun ai-auto-complete-mcp-generate-system-prompt ()
  "Generate the MCP system prompt."
  (if (not ai-auto-complete-mcp-enabled)
      ""
    (let ((servers (ai-auto-complete-mcp-list-servers))
          (server-descriptions '()))

      ;; Generate server descriptions
      (dolist (server-name servers)
        (let* ((server (ai-auto-complete-mcp-get-server server-name))
               (description (plist-get server :description))
               (status (plist-get server :status)))
          (push (format "- %s: %s [%s]"
                        server-name
                        description
                        (if (eq status 'running) "running" "stopped"))
                server-descriptions)))

      ;; Format the prompt
      (format ai-auto-complete-mcp-system-prompt-template
              (mapconcat 'identity (nreverse server-descriptions) "\n")))))

;; Advice function to add MCP system prompt
(defun ai-auto-complete-mcp-advice-get-system-prompt (orig-fun &rest args)
  "Advice function to add MCP system prompt to the system prompt.
Calls ORIG-FUN with ARGS and appends the MCP system prompt."
  (let ((original-prompt (apply orig-fun args)))
    (if (not ai-auto-complete-mcp-enabled)
        original-prompt
      (let ((mcp-prompt (ai-auto-complete-mcp-generate-system-prompt)))
        (if (string-empty-p mcp-prompt)
            original-prompt
          (concat original-prompt "\n\n" mcp-prompt))))))

;; Setup function to add advice to system prompt function
(defun ai-auto-complete-mcp-prompt-setup ()
  "Set up MCP prompt integration."
  (advice-add 'ai-auto-complete-get-system-prompt :around
              #'ai-auto-complete-mcp-advice-get-system-prompt))

;; Teardown function to remove advice from system prompt function
(defun ai-auto-complete-mcp-prompt-teardown ()
  "Remove MCP prompt integration."
  (advice-remove 'ai-auto-complete-get-system-prompt
                 #'ai-auto-complete-mcp-advice-get-system-prompt))

;; Set up MCP prompt integration when this module is loaded
(ai-auto-complete-mcp-prompt-setup)

(provide 'mcp/mcp-prompt)
;;; mcp-prompt.el ends here
