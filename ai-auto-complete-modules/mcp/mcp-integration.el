;;; mcp-integration.el --- Integration with other systems for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides integration with other systems for MCP in the AI Auto Complete package.
;; It integrates MCP with the context engine, session manager, and other components.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-client)
(require 'mcp/mcp-tools-bridge)
;(require 'context/context-core)
;(require 'sessions/sessions-core)

;; Integration settings are defined in customization/mcp-customization.el

;; MCP context provider
(defun ai-auto-complete-mcp-context-provider ()
  "Provide context from MCP servers for the context engine."
  (when (and ai-auto-complete-mcp-enabled
             ai-auto-complete-mcp-enable-context-integration)
    (let ((servers (ai-auto-complete-mcp-list-servers))
          (context ""))
      (dolist (server-name servers)
        (let ((server (ai-auto-complete-mcp-get-server server-name)))
          (when (eq (plist-get server :status) 'running)
            ;; Add server information
            (setq context (concat context (format "MCP Server: %s\n" server-name)))

            ;; Add tools information
            (let ((tools-promise (make-hash-table :test 'eq)))
              ;; Set up the promise
              (puthash 'status 'pending tools-promise)
              (puthash 'value nil tools-promise)

              ;; List tools
              (ai-auto-complete-mcp-list-tools
               server-name
               (lambda (tools)
                 (puthash 'status 'fulfilled tools-promise)
                 (puthash 'value tools tools-promise)))

              ;; Wait for the result (with timeout)
              (let ((timeout 2)
                    (start-time (current-time)))
                (while (and (eq (gethash 'status tools-promise) 'pending)
                            (< (float-time (time-since start-time)) timeout))
                  (sleep-for 0.1))

                ;; Add tools to context
                (let ((tools (gethash 'value tools-promise)))
                  (when (and tools (not (stringp tools)))
                    (setq context (concat context "  Tools:\n"))
                    (dolist (tool tools)
                      (let ((tool-name (plist-get tool :name))
                            (tool-description (plist-get tool :description)))
                        (setq context (concat context (format "    %s: %s\n" tool-name tool-description)))))))))

            ;; Add resources information
            (let ((resources-promise (make-hash-table :test 'eq)))
              ;; Set up the promise
              (puthash 'status 'pending resources-promise)
              (puthash 'value nil resources-promise)

              ;; List resources
              (ai-auto-complete-mcp-list-resources
               server-name
               (lambda (resources)
                 (puthash 'status 'fulfilled resources-promise)
                 (puthash 'value resources resources-promise)))

              ;; Wait for the result (with timeout)
              (let ((timeout 2)
                    (start-time (current-time)))
                (while (and (eq (gethash 'status resources-promise) 'pending)
                            (< (float-time (time-since start-time)) timeout))
                  (sleep-for 0.1))

                ;; Add resources to context
                (let ((resources (gethash 'value resources-promise)))
                  (when (and resources (not (stringp resources)))
                    (setq context (concat context "  Resources:\n"))
                    (dolist (resource resources)
                      (let ((resource-uri (plist-get resource :uri))
                            (resource-description (plist-get resource :description)))
                        (setq context (concat context (format "    %s: %s\n" resource-uri resource-description)))))))))

            ;; Add prompts information
            (let ((prompts-promise (make-hash-table :test 'eq)))
              ;; Set up the promise
              (puthash 'status 'pending prompts-promise)
              (puthash 'value nil prompts-promise)

              ;; List prompts
              (ai-auto-complete-mcp-list-prompts
               server-name
               (lambda (prompts)
                 (puthash 'status 'fulfilled prompts-promise)
                 (puthash 'value prompts prompts-promise)))

              ;; Wait for the result (with timeout)
              (let ((timeout 2)
                    (start-time (current-time)))
                (while (and (eq (gethash 'status prompts-promise) 'pending)
                            (< (float-time (time-since start-time)) timeout))
                  (sleep-for 0.1))

                ;; Add prompts to context
                (let ((prompts (gethash 'value prompts-promise)))
                  (when (and prompts (not (stringp prompts)))
                    (setq context (concat context "  Prompts:\n"))
                    (dolist (prompt prompts)
                      (let ((prompt-name (plist-get prompt :name))
                            (prompt-description (plist-get prompt :description)))
                        (setq context (concat context (format "    %s: %s\n" prompt-name prompt-description)))))))))

            (setq context (concat context "\n")))))

      ;; Return the context
      context)))

;; Register MCP context provider
(defun ai-auto-complete-mcp-register-context-provider ()
  "Register the MCP context provider with the context engine."
  (when (and ai-auto-complete-mcp-enabled
             ai-auto-complete-mcp-enable-context-integration
             (fboundp 'ai-auto-complete-register-context-provider))
    (ai-auto-complete-register-context-provider
     'mcp
     "MCP Servers"
     #'ai-auto-complete-mcp-context-provider)))

;; Unregister MCP context provider
(defun ai-auto-complete-mcp-unregister-context-provider ()
  "Unregister the MCP context provider from the context engine."
  (when (fboundp 'ai-auto-complete-unregister-context-provider)
    (ai-auto-complete-unregister-context-provider 'mcp)))

;; Save MCP server state to a session
(defun ai-auto-complete-mcp-save-session (session)
  "Save MCP server state to SESSION."
  (when (and ai-auto-complete-mcp-enabled
             ai-auto-complete-mcp-enable-session-integration)
    (let ((servers (ai-auto-complete-mcp-list-servers))
          (server-states '()))
      (dolist (server-name servers)
        (let* ((server (ai-auto-complete-mcp-get-server server-name))
               (status (plist-get server :status))
               (path (plist-get server :path))
               (transport (plist-get server :transport))
               (description (plist-get server :description)))
          (push (list :name server-name
                      :status status
                      :path path
                      :transport transport
                      :description description)
                server-states)))

      ;; Add server states to the session
      (puthash 'mcp-servers server-states session)

      ;; Return the session
      session)))

;; Restore MCP server state from a session
(defun ai-auto-complete-mcp-restore-session (session)
  "Restore MCP server state from SESSION."
  (when (and ai-auto-complete-mcp-enabled
             ai-auto-complete-mcp-enable-session-integration)
    (let ((server-states (gethash 'mcp-servers session)))
      (when server-states
        ;; Register servers
        (dolist (server-state server-states)
          (let ((name (plist-get server-state :name))
                (status (plist-get server-state :status))
                (path (plist-get server-state :path))
                (transport (plist-get server-state :transport))
                (description (plist-get server-state :description)))

            ;; Register the server
            (ai-auto-complete-mcp-register-server name path transport description)

            ;; Start the server if it was running
            (when (eq status 'running)
              (ai-auto-complete-mcp-start-server name))))))))

;; Register MCP session handlers
(defun ai-auto-complete-mcp-register-session-handlers ()
  "Register MCP session handlers with the session manager."
  (when (and ai-auto-complete-mcp-enabled
             ai-auto-complete-mcp-enable-session-integration)
    ;; Register save handler
    (when (fboundp 'ai-auto-complete-register-session-save-handler)
      (ai-auto-complete-register-session-save-handler
       'mcp
       #'ai-auto-complete-mcp-save-session))

    ;; Register restore handler
    (when (fboundp 'ai-auto-complete-register-session-restore-handler)
      (ai-auto-complete-register-session-restore-handler
       'mcp
       #'ai-auto-complete-mcp-restore-session))))

;; Unregister MCP session handlers
(defun ai-auto-complete-mcp-unregister-session-handlers ()
  "Unregister MCP session handlers from the session manager."
  ;; Unregister save handler
  (when (fboundp 'ai-auto-complete-unregister-session-save-handler)
    (ai-auto-complete-unregister-session-save-handler 'mcp))

  ;; Unregister restore handler
  (when (fboundp 'ai-auto-complete-unregister-session-restore-handler)
    (ai-auto-complete-unregister-session-restore-handler 'mcp)))

;; Integrate MCP with the completion system
(defun ai-auto-complete-mcp-integrate-with-completion ()
  "Integrate MCP with the completion system."
  (when ai-auto-complete-mcp-enabled
    ;; Add MCP tool completion
    (when (fboundp 'ai-auto-complete-register-completion-provider)
      (ai-auto-complete-register-completion-provider
       'mcp
       (lambda ()
         (let ((completions '()))
           ;; Add MCP tool completions
           (dolist (server-name (ai-auto-complete-mcp-list-servers))
             (let ((server (ai-auto-complete-mcp-get-server server-name)))
               (when (eq (plist-get server :status) 'running)
                 ;; Add server name completion
                 (push (format "<mcp-tool server=\"%s\" name=\"" server-name) completions)

                 ;; Add tool name completions
                 (let ((tools-promise (make-hash-table :test 'eq)))
                   ;; Set up the promise
                   (puthash 'status 'pending tools-promise)
                   (puthash 'value nil tools-promise)

                   ;; List tools
                   (ai-auto-complete-mcp-list-tools
                    server-name
                    (lambda (tools)
                      (puthash 'status 'fulfilled tools-promise)
                      (puthash 'value tools tools-promise)))

                   ;; Wait for the result (with timeout)
                   (let ((timeout 2)
                         (start-time (current-time)))
                     (while (and (eq (gethash 'status tools-promise) 'pending)
                                 (< (float-time (time-since start-time)) timeout))
                       (sleep-for 0.1))

                     ;; Add tool completions
                     (let ((tools (gethash 'value tools-promise)))
                       (when (and tools (not (stringp tools)))
                         (dolist (tool tools)
                           (let ((tool-name (plist-get tool :name)))
                             (push (format "<mcp-tool server=\"%s\" name=\"%s\">"
                                          server-name tool-name)
                                   completions)
                             (push (format "<mcp-tool server=\"%s\" name=\"%s\">\n<parameters>\n{\n  \n}\n</parameters>\n</mcp-tool>"
                                          server-name tool-name)
                                   completions))))))))))

           ;; Return the completions
           completions))))))

;; Integrate MCP with the help system
(defun ai-auto-complete-mcp-integrate-with-help ()
  "Integrate MCP with the help system."
  (when ai-auto-complete-mcp-enabled
    ;; Add MCP help command
    (defun ai-auto-complete-mcp-help ()
      "Display help for MCP."
      (interactive)
      (let ((buffer-name "*MCP Help*"))
        (with-current-buffer (get-buffer-create buffer-name)
          (let ((inhibit-read-only t))
            (erase-buffer)
            (special-mode) ;; Use special-mode for the buffer

            ;; Add help content
            (insert "MCP Help\n")
            (insert "========\n\n")

            (insert "Model Context Protocol (MCP) is an open protocol developed by Anthropic that standardizes how applications provide context to LLMs.\n\n")

            (insert "Commands:\n")
            (insert "  M-x ai-auto-complete-mcp-toggle - Enable/disable MCP integration\n")
            (insert "  M-x ai-auto-complete-mcp-list-servers-ui - List MCP servers\n")
            (insert "  M-x ai-auto-complete-mcp-add-server-ui - Add an MCP server\n")
            (insert "  M-x ai-auto-complete-mcp-import-server-ui - Import MCP servers from a directory\n")
            (insert "  M-x ai-auto-complete-mcp-tool-to-server-ui - Convert a tool to an MCP server\n")
            (insert "  M-x ai-auto-complete-mcp-server-to-tool-ui - Import an MCP server as a tool\n")
            (insert "  M-x ai-auto-complete-mcp-start-all-servers - Start all MCP servers\n")
            (insert "  M-x ai-auto-complete-mcp-stop-all-servers - Stop all MCP servers\n")
            (insert "  M-x ai-auto-complete-mcp-display-examples - Generate and display MCP examples\n")
            (insert "  M-x ai-auto-complete-mcp-display-status-indicators - Display MCP server status\n")
            (insert "  M-x ai-auto-complete-mcp-display-error-log - Display MCP error log\n")
            (insert "  M-x ai-auto-complete-mcp-clear-error-log - Clear MCP error log\n")
            (insert "  M-x ai-auto-complete-mcp-clear-caches - Clear MCP caches\n\n")

            (insert "Syntax for calling MCP tools in conversations:\n")
            (insert "  <mcp-tool server=\"server-name\" name=\"tool-name\">\n")
            (insert "  <parameters>\n")
            (insert "  {\n")
            (insert "    \"param1\": \"value1\",\n")
            (insert "    \"param2\": \"value2\"\n")
            (insert "  }\n")
            (insert "  </parameters>\n")
            (insert "  </mcp-tool>\n\n")

            (insert "For more information, see the MCP documentation in the ai-auto-complete-modules/mcp/docs directory.\n")

            (goto-char (point-min))))

        (switch-to-buffer buffer-name)))))

;; Setup function to integrate MCP with other systems
(defun ai-auto-complete-mcp-integration-setup ()
  "Set up MCP integration with other systems."
  ;; Register context provider
  (ai-auto-complete-mcp-register-context-provider)

  ;; Register session handlers
  (ai-auto-complete-mcp-register-session-handlers)

  ;; Integrate with completion system
  (ai-auto-complete-mcp-integrate-with-completion)

  ;; Integrate with help system
  (ai-auto-complete-mcp-integrate-with-help))

;; Teardown function to remove MCP integration with other systems
(defun ai-auto-complete-mcp-integration-teardown ()
  "Remove MCP integration with other systems."
  ;; Unregister context provider
  (ai-auto-complete-mcp-unregister-context-provider)

  ;; Unregister session handlers
  (ai-auto-complete-mcp-unregister-session-handlers))

;; Set up MCP integration when this module is loaded
(ai-auto-complete-mcp-integration-setup)

(provide 'mcp/mcp-integration)
;;; mcp-integration.el ends here
