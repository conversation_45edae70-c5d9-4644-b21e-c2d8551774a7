;;; mcp-python.el --- Python utilities for MCP -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides utilities for checking and configuring Python for MCP.

;;; Code:

(require 'mcp/mcp-core)

;; Check if Python is available
(defun ai-auto-complete-mcp-check-python ()
  "Check if Python is available for MCP servers.
Returns the path to Python executable or nil if not found."
  (interactive)
  (let ((python-cmd (or (executable-find ai-auto-complete-mcp-python-command)
                        (executable-find "python3")
                        (executable-find "python"))))
    (if python-cmd
        (progn
          (message "MCP: Found Python at %s" python-cmd)
          (when (called-interactively-p 'any)
            (let ((version (shell-command-to-string (format "%s --version" python-cmd))))
              (message "Python version: %s" (string-trim version))))
          python-cmd)
      (message "MCP: No Python executable found. Please install Python.")
      nil)))

;; Set Python path
(defun ai-auto-complete-mcp-set-python-path (path)
  "Set the Python path for MCP to PATH."
  (interactive "fPython executable path: ")
  (when (file-exists-p path)
    (setq ai-auto-complete-mcp-python-command path)
    (message "MCP: Python path set to %s" path)
    (ai-auto-complete-mcp-check-python)))

;; Check if MCP package is available
(defun ai-auto-complete-mcp-check-mcp-package ()
  "Check if the MCP package is available in Python.
Returns t if available, nil otherwise."
  (interactive)
  (let ((python-cmd (ai-auto-complete-mcp-check-python)))
    (if (not python-cmd)
        (progn
          (message "MCP: Python is not available")
          nil)
      (let ((output (shell-command-to-string 
                    (format "%s -c \"try: import mcp; print('MCP package available'); except ImportError as e: print('MCP package not available: ' + str(e))\"" 
                           python-cmd))))
        (if (string-match-p "MCP package available" output)
            (progn
              (message "MCP: MCP package is available")
              t)
          (message "MCP: MCP package is not available: %s" (string-trim output))
          nil)))))

;; Display Python information
(defun ai-auto-complete-mcp-python-info ()
  "Display information about Python and MCP package availability."
  (interactive)
  (let ((buffer-name "*MCP Python Info*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer
        
        ;; Add header
        (insert "MCP Python Information\n")
        (insert "=====================\n\n")
        
        ;; Python information
        (let ((python-cmd (ai-auto-complete-mcp-check-python)))
          (if python-cmd
              (progn
                (insert (format "Python executable: %s\n" python-cmd))
                (let ((version (string-trim (shell-command-to-string (format "%s --version" python-cmd)))))
                  (insert (format "Python version: %s\n" version)))
                
                ;; Check sys.path
                (insert "\nPython sys.path:\n")
                (let ((sys-path (shell-command-to-string 
                                (format "%s -c \"import sys; print('\\n'.join(sys.path))\"" python-cmd))))
                  (dolist (path (split-string sys-path "\n"))
                    (when (not (string-empty-p path))
                      (insert (format "  - %s\n" path)))))
                
                ;; Check MCP package
                (insert "\nMCP Package:\n")
                (let ((mcp-output (shell-command-to-string 
                                  (format "%s -c \"try: import mcp; print('Available: ' + mcp.__file__); except ImportError as e: print('Not available: ' + str(e))\"" 
                                         python-cmd))))
                  (insert (format "  %s\n" (string-trim mcp-output)))))
            
            (insert "Python is not available. Please install Python.\n")))
        
        ;; Add buttons for actions
        (insert "\nActions: ")
        (insert-button "Refresh"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-python-info))
                      'follow-link t)
        (insert " | ")
        (insert-button "Set Python Path"
                      'action (lambda (_)
                               (call-interactively 'ai-auto-complete-mcp-set-python-path)
                               (ai-auto-complete-mcp-python-info))
                      'follow-link t)))
    
    ;; Display the buffer
    (switch-to-buffer buffer-name)))

(provide 'mcp/mcp-python)
;;; mcp-python.el ends here
