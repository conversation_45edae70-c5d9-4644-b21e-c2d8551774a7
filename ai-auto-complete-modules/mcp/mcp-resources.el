;;; mcp-resources.el --- Resource subscription for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides resource subscription functionality for MCP integration in the AI Auto Complete package.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-client)

;; Resource subscription structure
(cl-defstruct (ai-auto-complete-mcp-resource-subscription
               (:constructor ai-auto-complete-mcp-resource-subscription-create)
               (:copier nil))
  "Structure for MCP resource subscription."
  server-name             ; Name of the server
  resource-uri            ; URI of the resource
  callback                ; Callback function for resource changes
  active-p                ; Whether the subscription is active
  )

;; Resource subscriptions
(defvar ai-auto-complete-mcp-resource-subscriptions (make-hash-table :test 'equal)
  "Hash table of MCP resource subscriptions, mapping subscription IDs to subscriptions.")

;; Generate a unique subscription ID
(defvar ai-auto-complete-mcp-subscription-id 0
  "Counter for generating unique subscription IDs.")

(defun ai-auto-complete-mcp-generate-subscription-id ()
  "Generate a unique subscription ID."
  (setq ai-auto-complete-mcp-subscription-id (1+ ai-auto-complete-mcp-subscription-id))
  (format "sub-%d" ai-auto-complete-mcp-subscription-id))

;; Subscribe to a resource
(defun ai-auto-complete-mcp-subscribe-to-resource (server-name resource-uri callback)
  "Subscribe to RESOURCE-URI on SERVER-NAME and call CALLBACK when it changes."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-subscribe-to-resource nil))
  
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((status (plist-get server :status))
            (capabilities (plist-get server :capabilities)))
        (if (not (eq status 'running))
            (progn
              (message "MCP server %s is not running" server-name)
              nil)
          ;; Check if the server supports resource subscription
          (if (not (and (plist-member capabilities :resources)
                        (plist-member (plist-get capabilities :resources) :subscribe)
                        (plist-get (plist-get capabilities :resources) :subscribe)))
              (progn
                (message "MCP server %s does not support resource subscription" server-name)
                nil)
            ;; Create the subscription
            (let ((subscription-id (ai-auto-complete-mcp-generate-subscription-id))
                  (subscription (ai-auto-complete-mcp-resource-subscription-create
                                :server-name server-name
                                :resource-uri resource-uri
                                :callback callback
                                :active-p t)))
              
              ;; Register the subscription
              (puthash subscription-id subscription ai-auto-complete-mcp-resource-subscriptions)
              
              ;; Send the subscription request
              (ai-auto-complete-mcp-send-subscription-request
               server-name resource-uri subscription-id)
              
              (when ai-auto-complete-mcp-debug-mode
                (message "MCP: Subscribed to resource %s on server %s with ID %s"
                         resource-uri server-name subscription-id))
              
              subscription-id)))))))

;; Unsubscribe from a resource
(defun ai-auto-complete-mcp-unsubscribe-from-resource (subscription-id)
  "Unsubscribe from a resource with SUBSCRIPTION-ID."
  (let ((subscription (gethash subscription-id ai-auto-complete-mcp-resource-subscriptions)))
    (if (not subscription)
        (progn
          (message "MCP subscription %s not found" subscription-id)
          nil)
      (let ((server-name (ai-auto-complete-mcp-resource-subscription-server-name subscription))
            (resource-uri (ai-auto-complete-mcp-resource-subscription-resource-uri subscription)))
        
        ;; Send the unsubscription request
        (ai-auto-complete-mcp-send-unsubscription-request
         server-name resource-uri subscription-id)
        
        ;; Update the subscription
        (setf (ai-auto-complete-mcp-resource-subscription-active-p subscription) nil)
        
        ;; Remove the subscription
        (remhash subscription-id ai-auto-complete-mcp-resource-subscriptions)
        
        (when ai-auto-complete-mcp-debug-mode
          (message "MCP: Unsubscribed from resource %s on server %s with ID %s"
                   resource-uri server-name subscription-id))
        
        t))))

;; Send a subscription request
(defun ai-auto-complete-mcp-send-subscription-request (server-name resource-uri subscription-id)
  "Send a subscription request for RESOURCE-URI on SERVER-NAME with SUBSCRIPTION-ID."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :uri resource-uri
                             :subscriptionId subscription-id)))
    
    ;; Create the subscription request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "subscribeResource" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-send-message-to-server
       server-name request
       (lambda (response)
         ;; Process the subscription response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (progn
                 (message "MCP: Error subscribing to resource %s on server %s: %s"
                          resource-uri server-name (plist-get error :message))
                 (remhash subscription-id ai-auto-complete-mcp-resource-subscriptions))
             (when ai-auto-complete-mcp-debug-mode
               (message "MCP: Successfully subscribed to resource %s on server %s"
                        resource-uri server-name)))))))))

;; Send an unsubscription request
(defun ai-auto-complete-mcp-send-unsubscription-request (server-name resource-uri subscription-id)
  "Send an unsubscription request for RESOURCE-URI on SERVER-NAME with SUBSCRIPTION-ID."
  (let ((request-id (ai-auto-complete-mcp-generate-request-id))
        (request-params (list :uri resource-uri
                             :subscriptionId subscription-id)))
    
    ;; Create the unsubscription request
    (let ((request (ai-auto-complete-mcp-create-jsonrpc-request 
                    "unsubscribeResource" request-params request-id)))
      
      ;; Send the request
      (ai-auto-complete-mcp-send-message-to-server
       server-name request
       (lambda (response)
         ;; Process the unsubscription response
         (let ((result (plist-get response :result))
               (error (plist-get response :error)))
           (if error
               (message "MCP: Error unsubscribing from resource %s on server %s: %s"
                        resource-uri server-name (plist-get error :message))
             (when ai-auto-complete-mcp-debug-mode
               (message "MCP: Successfully unsubscribed from resource %s on server %s"
                        resource-uri server-name)))))))))

;; Send a message to a server
(defun ai-auto-complete-mcp-send-message-to-server (server-name message callback)
  "Send MESSAGE to SERVER-NAME and call CALLBACK with the response."
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((transport (plist-get server :transport)))
        (cond
         ((eq transport 'stdio)
          (if (fboundp 'ai-auto-complete-mcp-stdio-send-message)
              (ai-auto-complete-mcp-stdio-send-message server-name message callback)
            (message "Stdio transport not available")
            nil))
         
         ((eq transport 'sse)
          (if (fboundp 'ai-auto-complete-mcp-sse-send-message)
              (ai-auto-complete-mcp-sse-send-message server-name message callback)
            (message "SSE transport not available")
            nil))
         
         (t
          (message "Unsupported transport: %s" transport)
          nil))))))

;; Process a resource change notification
(defun ai-auto-complete-mcp-process-resource-change (server-name resource-uri subscription-id value)
  "Process a resource change notification for RESOURCE-URI on SERVER-NAME.
SUBSCRIPTION-ID is the ID of the subscription, VALUE is the new value of the resource."
  (let ((subscription (gethash subscription-id ai-auto-complete-mcp-resource-subscriptions)))
    (when subscription
      (let ((callback (ai-auto-complete-mcp-resource-subscription-callback subscription)))
        (when callback
          (funcall callback value))))))

;; List all resource subscriptions
(defun ai-auto-complete-mcp-list-resource-subscriptions ()
  "List all resource subscriptions."
  (let ((subscriptions '()))
    (maphash (lambda (id subscription)
               (push (cons id subscription) subscriptions))
             ai-auto-complete-mcp-resource-subscriptions)
    subscriptions))

;; Unsubscribe from all resources
(defun ai-auto-complete-mcp-unsubscribe-from-all-resources ()
  "Unsubscribe from all resources."
  (let ((subscriptions (ai-auto-complete-mcp-list-resource-subscriptions)))
    (dolist (subscription subscriptions)
      (ai-auto-complete-mcp-unsubscribe-from-resource (car subscription)))))

;; Clean up subscriptions when a server is stopped
(defun ai-auto-complete-mcp-cleanup-server-subscriptions (server-name)
  "Clean up subscriptions for SERVER-NAME when it is stopped."
  (let ((subscriptions (ai-auto-complete-mcp-list-resource-subscriptions))
        (to-remove '()))
    
    ;; Find subscriptions for this server
    (dolist (subscription subscriptions)
      (let ((id (car subscription))
            (sub (cdr subscription)))
        (when (string= (ai-auto-complete-mcp-resource-subscription-server-name sub) server-name)
          (push id to-remove))))
    
    ;; Remove the subscriptions
    (dolist (id to-remove)
      (remhash id ai-auto-complete-mcp-resource-subscriptions))))

;; Advice function to clean up subscriptions when a server is stopped
(defun ai-auto-complete-mcp-advice-stop-server (orig-fun server-name)
  "Advice function to clean up subscriptions when a server is stopped.
Calls ORIG-FUN with SERVER-NAME."
  (let ((result (funcall orig-fun server-name)))
    (when result
      (ai-auto-complete-mcp-cleanup-server-subscriptions server-name))
    result))

;; Setup function to add advice to stop-server function
(defun ai-auto-complete-mcp-resources-setup ()
  "Set up MCP resources integration."
  (advice-add 'ai-auto-complete-mcp-stop-server :around
              #'ai-auto-complete-mcp-advice-stop-server))

;; Teardown function to remove advice from stop-server function
(defun ai-auto-complete-mcp-resources-teardown ()
  "Remove MCP resources integration."
  (advice-remove 'ai-auto-complete-mcp-stop-server
                 #'ai-auto-complete-mcp-advice-stop-server))

;; Set up MCP resources integration when this module is loaded
(ai-auto-complete-mcp-resources-setup)

(provide 'mcp/mcp-resources)
;;; mcp-resources.el ends here
