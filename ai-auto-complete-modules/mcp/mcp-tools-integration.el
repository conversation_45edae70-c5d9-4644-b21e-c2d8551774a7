;;; mcp-tools-integration.el --- Integration between MCP and tools system -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides deeper integration between the MCP system and the existing tools system
;; in the AI Auto Complete package.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-client)
(require 'mcp/mcp-tools-bridge)
(require 'tools/tools-core)

;; Advice function to add MCP tool calls to the tools system
(defun ai-auto-complete-mcp-tools-advice-process-response (orig-fun response callback &optional agent-name)
  "Advice function to process MCP tool calls in responses.
Calls ORIG-FUN with RESPONSE and CALLBACK, but intercepts MCP tool calls.
AGENT-NAME is the optional name of the agent making the request."
  (if (not ai-auto-complete-mcp-enabled)
      ;; If MCP is not enabled, just call the original function
      (funcall orig-fun response callback agent-name)

    ;; Check if the response contains MCP tool calls
    (if (string-match-p "<mcp-tool\\s-+\\(name\\|server\\)=" response)
        ;; Process MCP tool calls
        (ai-auto-complete-mcp-process-tool-calls response callback agent-name)

      ;; No MCP tool calls, call the original function
      (funcall orig-fun response callback agent-name))))

;; Process MCP tool calls in a response
(defun ai-auto-complete-mcp-process-tool-calls (response callback &optional agent-name)
  "Process MCP tool calls in RESPONSE and call CALLBACK with the result.
AGENT-NAME is the optional name of the agent making the request."
  (let ((tool-calls (ai-auto-complete-mcp-parse-tool-calls response))
        (result response))

    (if (null tool-calls)
        ;; No tool calls found, just return the response
        (funcall callback response)

      ;; Process tool calls and send results back to LLM
      (ai-auto-complete-mcp-execute-tool-calls tool-calls response callback agent-name))))

;; Parse MCP tool calls from a response
(defun ai-auto-complete-mcp-parse-tool-calls (response)
  "Parse MCP tool calls from RESPONSE."
  (let ((tool-calls '())
        (start 0))

    ;; Find all MCP tool calls in the response
    (while (string-match "<mcp-tool\\s-+\\(name\\|server\\)=\\(\"[^\"]*\"\\|[^[:space:]>]*)\\)\\(\\s-+\\(name\\|server\\)=\\(\"[^\"]*\"\\|[^[:space:]>]*)\\)\\)?\\s-*>" response start)
      (let ((tool-start (match-beginning 0))
            (tool-end (match-end 0))
            (server-name nil)
            (tool-name nil))

        ;; Extract server and tool names
        (let ((attr1-name (match-string 1 response))
              (attr1-value (match-string 2 response))
              (attr2-name (match-string 4 response))
              (attr2-value (match-string 5 response)))

          ;; Clean up attribute values (remove quotes)
          (when attr1-value
            (setq attr1-value (replace-regexp-in-string "\"" "" attr1-value)))
          (when attr2-value
            (setq attr2-value (replace-regexp-in-string "\"" "" attr2-value)))

          ;; Assign server and tool names based on attribute names
          (cond
           ((string= attr1-name "server")
            (setq server-name attr1-value)
            (when (string= attr2-name "name")
              (setq tool-name attr2-value)))
           ((string= attr1-name "name")
            (setq tool-name attr1-value)
            (when (string= attr2-name "server")
              (setq server-name attr2-value)))))

        ;; Find the parameters
        (when (string-match "<parameters>\\(.*?\\)</parameters>" response tool-end)
          (let ((params-start (match-beginning 1))
                (params-end (match-end 1))
                (params-text (match-string 1 response))
                (params nil))

            ;; Parse parameters as JSON
            (condition-case err
                (setq params (json-read-from-string params-text))
              (error
               (message "Error parsing MCP tool parameters: %s" (error-message-string err))))

            ;; Find the end of the tool call
            (when (string-match "</mcp-tool>" response params-end)
              (let ((tool-call-end (match-end 0)))

                ;; Add the tool call to the list
                (push (list :server server-name
                           :name tool-name
                           :params params
                           :start tool-start
                           :end tool-call-end)
                      tool-calls)

                ;; Update the start position for the next search
                (setq start tool-call-end)))))))

    ;; Return the tool calls in the order they appear in the response
    (nreverse tool-calls)))

;; Execute MCP tool calls and continue with LLM
(defun ai-auto-complete-mcp-execute-tool-calls (tool-calls response callback &optional agent-name)
  "Execute MCP TOOL-CALLS and continue the conversation with the LLM.
RESPONSE is the LLM's response containing the tool calls.
CALLBACK is the function to call with the final result.
AGENT-NAME is the optional name of the agent making the request."
  (when ai-auto-complete-mcp-debug-mode
    (message "[MCP-DEBUG] Executing %d MCP tool calls" (length tool-calls)))

  ;; Process each tool call and collect results
  (let ((results '())
        (remaining-calls (length tool-calls)))

    (dolist (tool-call tool-calls)
      (let ((server-name (plist-get tool-call :server))
            (tool-name (plist-get tool-call :name))
            (params (plist-get tool-call :params)))

        (when ai-auto-complete-mcp-debug-mode
          (message "[MCP-DEBUG] Calling MCP tool %s on server %s with params %s"
                   tool-name server-name params))

        ;; Call the MCP tool
        (ai-auto-complete-mcp-call-tool
         server-name tool-name params
         (lambda (result)
           ;; Store the result
           (push (cons tool-call result) results)
           (setq remaining-calls (1- remaining-calls))

           ;; If all tool calls have been processed, continue with the LLM
           (when (= remaining-calls 0)
             (ai-auto-complete-mcp-continue-with-llm tool-calls results response callback agent-name))))))

    ;; If there are no tool calls, just return the response
    (when (= (length tool-calls) 0)
      (funcall callback response))))

;; Continue with LLM after executing MCP tool calls
(defun ai-auto-complete-mcp-continue-with-llm (tool-calls results response callback &optional agent-name)
  "Continue with LLM after executing MCP TOOL-CALLS with RESULTS.
RESPONSE is the original LLM response, CALLBACK is the function to call with the final result.
AGENT-NAME is the optional name of the agent making the request."
  (when ai-auto-complete-mcp-debug-mode
    (message "[MCP-DEBUG] Continuing with LLM after executing MCP tool calls"))

  ;; Build the continuation prompt
  (let ((continuation-prompt (ai-auto-complete-mcp-build-continuation-prompt
                             tool-calls results response)))

    ;; Send the continuation prompt to the LLM
    (when ai-auto-complete-mcp-debug-mode
      (message "[MCP-DEBUG] Sending continuation prompt to LLM"))

    (ai-auto-complete-complete
     (ai-auto-complete-get-current-backend)
     continuation-prompt
     nil  ;; Use empty history to avoid confusion with previous messages
     (lambda (continuation-response)
       (when ai-auto-complete-mcp-debug-mode
         (message "[MCP-DEBUG] Received continuation response from LLM"))

       ;; Check if the continuation response contains more tool calls
       (if (string-match-p "<mcp-tool\\s-+\\(name\\|server\\)=" continuation-response)
           ;; Process the new tool calls
           (ai-auto-complete-mcp-process-tool-calls continuation-response callback agent-name)

         ;; No more tool calls, combine the original response and continuation
         (let ((final-response (ai-auto-complete-mcp-combine-responses
                               response continuation-response tool-calls results)))
           (funcall callback final-response))))
     agent-name)))

;; Build continuation prompt for LLM
(defun ai-auto-complete-mcp-build-continuation-prompt (tool-calls results response)
  "Build a continuation prompt for LLM after executing MCP TOOL-CALLS with RESULTS.
RESPONSE is the original LLM response."
  (let ((prompt "I'll continue the conversation based on the tool results.\n\n"))

    ;; Add the original response (excluding tool calls)
    (setq prompt (concat prompt "My previous response (excluding tool calls):\n"))
    (let ((modified-response response))
      ;; Replace each tool call with a placeholder
      (dolist (tool-call (reverse tool-calls))
        (let ((start (plist-get tool-call :start))
              (end (plist-get tool-call :end))
              (server-name (plist-get tool-call :server))
              (tool-name (plist-get tool-call :name)))
          (setq modified-response
                (concat
                 (substring modified-response 0 start)
                 (format "[MCP Tool Call: %s on server %s]" tool-name server-name)
                 (substring modified-response end)))))
      (setq prompt (concat prompt modified-response "\n\n")))

    ;; Add the tool results
    (setq prompt (concat prompt "Tool results:\n"))
    (dolist (result results)
      (let* ((tool-call (car result))
             (server-name (plist-get tool-call :server))
             (tool-name (plist-get tool-call :name))
             (tool-result (cdr result)))
        (setq prompt (concat prompt
                            (format "- Tool: %s on server %s\n" tool-name server-name)
                            (format "  Result: %s\n\n" tool-result)))))

    ;; Add instructions for continuation
    (setq prompt (concat prompt
                        "Please continue the conversation based on these tool results. "
                        "You can call additional tools if needed.\n"))

    prompt))

;; Combine original response and continuation
(defun ai-auto-complete-mcp-combine-responses (original continuation tool-calls results)
  "Combine ORIGINAL response and CONTINUATION after executing TOOL-CALLS with RESULTS."
  (let ((result original))

    ;; Replace each tool call with its result
    (dolist (tool-call (reverse tool-calls))
      (let ((start (plist-get tool-call :start))
            (end (plist-get tool-call :end))
            (server-name (plist-get tool-call :server))
            (tool-name (plist-get tool-call :name))
            (tool-result (cdr (assoc tool-call results))))

        (setq result
              (concat
               (substring result 0 start)
               (format "I called the MCP tool %s on server %s and got the result: %s\n\n"
                       tool-name server-name tool-result)
               (substring result end)))))

    ;; Append the continuation
    (setq result (concat result "\n\n" continuation))

    ;; Clean up any double newlines that might have been created
    (while (string-match "\n\n\n+" result)
      (setq result (replace-match "\n\n" nil nil result)))

    result))

;; Setup function to add advice to tools functions
(defun ai-auto-complete-mcp-tools-integration-setup ()
  "Set up MCP tools integration."
  (advice-add 'ai-auto-complete-tools-process-response :around
              #'ai-auto-complete-mcp-tools-advice-process-response))

;; Teardown function to remove advice from tools functions
(defun ai-auto-complete-mcp-tools-integration-teardown ()
  "Remove MCP tools integration."
  (advice-remove 'ai-auto-complete-tools-process-response
                 #'ai-auto-complete-mcp-tools-advice-process-response))

;; Set up MCP tools integration when this module is loaded
(ai-auto-complete-mcp-tools-integration-setup)

(provide 'mcp/mcp-tools-integration)
;;; mcp-tools-integration.el ends here
