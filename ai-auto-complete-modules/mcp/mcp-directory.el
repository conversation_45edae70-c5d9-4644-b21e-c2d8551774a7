;;; mcp-directory.el --- Directory management for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides directory management for MCP integration in the AI Auto Complete package.
;; It handles scanning directories for MCP servers and managing server files.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)

;; Import example MCP servers
(defun ai-auto-complete-mcp-import-examples ()
  "Import example MCP servers from the examples directory to the servers directory."
  (interactive)
  (let* ((examples-dir (expand-file-name "examples"
                                        (file-name-directory (locate-library "mcp/mcp"))))
         (servers-dir ai-auto-complete-mcp-servers-directory)
         (imported 0))

    ;; Ensure the servers directory exists
    (ai-auto-complete-mcp-ensure-servers-directory)

    ;; Find all Python files in the examples directory
    (when (file-exists-p examples-dir)
      (let ((python-files (directory-files examples-dir t "\\.py$")))
        (dolist (file python-files)
          (let* ((file-name (file-name-nondirectory file))
                 (dest-file (expand-file-name file-name servers-dir)))

            ;; Copy the file if it doesn't exist in the servers directory
            (unless (file-exists-p dest-file)
              (copy-file file dest-file t)
              (set-file-modes dest-file (logior (file-modes dest-file) #o111)) ; Make executable
              (setq imported (1+ imported))))))

      (when (> imported 0)
        (message "Imported %d example MCP servers to %s" imported servers-dir))

      ;; Scan the servers directory to register the imported servers
      (ai-auto-complete-mcp-scan-directory servers-dir)
      imported)))

;; Check if a directory contains a package.json file
(defun ai-auto-complete-mcp-directory-has-package-json (dir)
  "Check if DIR contains a package.json file."
  (file-exists-p (expand-file-name "package.json" dir)))

;; Extract Node.js file path from a script command
(defun ai-auto-complete-mcp-extract-node-file-from-script (script-cmd dir)
  "Extract a Node.js file path from SCRIPT-CMD in DIR.
Returns the full path to the file or nil if not found."
  (when (and script-cmd (stringp script-cmd))
    (when ai-auto-complete-mcp-debug-mode
      (message "MCP: Extracting file path from script command: %s" script-cmd))

    ;; Look for patterns like "node path/to/file.js" or "node ./path/to/file.js"
    (if (string-match "\\<node\\s-+\\(\\./\\)?\\([^ \t\n]+\\)" script-cmd)
        (let ((file-path (match-string 2 script-cmd)))
          (when ai-auto-complete-mcp-debug-mode
            (message "MCP: Extracted file path: %s" file-path))

          (when file-path
            (let ((full-path (expand-file-name file-path dir)))
              (when ai-auto-complete-mcp-debug-mode
                (message "MCP: Checking full path: %s (exists: %s)"
                         full-path (if (file-exists-p full-path) "yes" "no")))

              (if (file-exists-p full-path)
                  full-path
                ;; If the file doesn't exist directly, try to find it in common directories
                (let ((alt-paths (list
                                 (expand-file-name file-path (expand-file-name "dist" dir))
                                 (expand-file-name file-path (expand-file-name "build" dir))
                                 (expand-file-name file-path (expand-file-name "lib" dir))
                                 (expand-file-name file-path (expand-file-name "src" dir)))))
                  (when ai-auto-complete-mcp-debug-mode
                    (message "MCP: Checking alternative paths: %s"
                             (mapconcat (lambda (p) (format "%s (exists: %s)"
                                                           p (if (file-exists-p p) "yes" "no")))
                                        alt-paths ", ")))

                  (let ((found-path (cl-find-if #'file-exists-p alt-paths)))
                    (when (and found-path ai-auto-complete-mcp-debug-mode)
                      (message "MCP: Found file at alternative path: %s" found-path))
                    found-path))))))
      (when ai-auto-complete-mcp-debug-mode
        (message "MCP: No node command pattern found in script: %s" script-cmd))
      nil)))

;; Find the main entry point for a Node.js package
(defun ai-auto-complete-mcp-find-node-main-file (dir)
  "Find the main entry point for a Node.js package in DIR.
Returns the path to the main file or nil if not found."
  (let ((package-json-path (expand-file-name "package.json" dir)))
    (when (file-exists-p package-json-path)
      (condition-case nil
          (with-temp-buffer
            (insert-file-contents package-json-path)
            (let* ((json-object-type 'hash-table)
                   (json-array-type 'list)
                   (json-key-type 'string)
                   (pkg-data (json-read))
                   (scripts (gethash "scripts" pkg-data))
                   (main-file nil))

              ;; First, check for script entries in preferred order
              (when scripts
                (when ai-auto-complete-mcp-debug-mode
                  (message "MCP: Found scripts in package.json: %s"
                           (let ((script-list '()))
                             (maphash (lambda (k v) (push (format "%s: %s" k v) script-list)) scripts)
                             (mapconcat 'identity script-list ", "))))

                (let ((script-names '("start" "serve" "dev" "run" "main")))
                  ;; First check exact matches
                  (dolist (name script-names)
                    (when (and (not main-file) (gethash name scripts))
                      (let ((script-cmd (gethash name scripts)))
                        (when ai-auto-complete-mcp-debug-mode
                          (message "MCP: Checking script '%s': %s" name script-cmd))
                        (setq main-file (ai-auto-complete-mcp-extract-node-file-from-script script-cmd dir))
                        (when (and main-file ai-auto-complete-mcp-debug-mode)
                          (message "MCP: Found main file from script '%s': %s" name main-file)))))

                  ;; Then check for prefixed matches like "start:*"
                  (unless main-file
                    (maphash (lambda (key value)
                               (when (and (not main-file)
                                          (cl-some (lambda (prefix)
                                                     (string-prefix-p (concat prefix ":") key))
                                                   script-names))
                                 (when ai-auto-complete-mcp-debug-mode
                                   (message "MCP: Checking prefixed script '%s': %s" key value))
                                 (setq main-file (ai-auto-complete-mcp-extract-node-file-from-script value dir))
                                 (when (and main-file ai-auto-complete-mcp-debug-mode)
                                   (message "MCP: Found main file from prefixed script '%s': %s" key main-file))))
                             scripts))

                  ;; If still not found, try any script that uses node
                  (unless main-file
                    (maphash (lambda (key value)
                               (when (and (not main-file)
                                          (string-match-p "\\<node\\>" value))
                                 (when ai-auto-complete-mcp-debug-mode
                                   (message "MCP: Checking node script '%s': %s" key value))
                                 (setq main-file (ai-auto-complete-mcp-extract-node-file-from-script value dir))
                                 (when (and main-file ai-auto-complete-mcp-debug-mode)
                                   (message "MCP: Found main file from node script '%s': %s" key main-file))))
                             scripts))))

              ;; If no script found, fall back to main/bin fields
              (unless main-file
                (let ((main-entry (or (gethash "main" pkg-data)
                                      (gethash "bin" pkg-data)
                                      "index.js")))
                  (setq main-file (expand-file-name
                                   (if (stringp main-entry)
                                       main-entry
                                     (car (hash-table-values main-entry)))
                                   dir))))

              main-file))
        (error nil)))))

;; Scan a directory for MCP servers
(defun ai-auto-complete-mcp-scan-directory (directory)
  "Scan DIRECTORY for MCP servers and register them."
  (interactive
   (list (read-directory-name "Scan directory for MCP servers: "
                             ai-auto-complete-mcp-servers-directory)))

  (unless (file-exists-p directory)
    (message "Directory %s does not exist" directory)
    (cl-return-from ai-auto-complete-mcp-scan-directory nil))

  (let ((python-files (directory-files directory t "\\.py$"))
        (python-registered 0)
        (node-registered 0))

    ;; Register Python files
    (dolist (file python-files)
      (let* ((file-name (file-name-nondirectory file))
             (base-name (file-name-sans-extension file-name))
             (server-name base-name))

        ;; Skip if server already exists
        (unless (ai-auto-complete-mcp-server-exists-p server-name)
          ;; Register the server with Python as the runner
          (ai-auto-complete-mcp-register-server server-name file nil nil "python")
          (setq python-registered (1+ python-registered)))))

    ;; Register Node.js packages
    (dolist (item (directory-files directory t))
      (when (and (file-directory-p item)
                 (not (string-match "/\\.$" item))
                 (not (string-match "/\\.\\.$" item))
                 (ai-auto-complete-mcp-directory-has-package-json item))
        (let* ((dir-name (file-name-nondirectory item))
               (server-name dir-name)
               (main-file (ai-auto-complete-mcp-find-node-main-file item)))
          (when (and main-file (not (ai-auto-complete-mcp-server-exists-p server-name)))
            (message "Registering Node.js MCP server: %s from %s" server-name main-file)
            (ai-auto-complete-mcp-register-server server-name main-file 'stdio
                                                 (format "Node.js MCP server: %s" server-name)
                                                 "node")
            (setq node-registered (1+ node-registered))))))

    (message "Registered %d Python and %d Node.js MCP servers from %s"
             python-registered node-registered directory)
    (+ python-registered node-registered)))

;; Scan subdirectories for MCP servers
(defun ai-auto-complete-mcp-scan-subdirectories (directory)
  "Scan DIRECTORY and its subdirectories for MCP servers and register them."
  (interactive
   (list (read-directory-name "Scan directory and subdirectories for MCP servers: "
                             ai-auto-complete-mcp-servers-directory)))

  (unless (file-exists-p directory)
    (message "Directory %s does not exist" directory)
    (cl-return-from ai-auto-complete-mcp-scan-subdirectories nil))

  (let ((registered 0))
    ;; Scan the main directory
    (setq registered (+ registered (ai-auto-complete-mcp-scan-directory directory)))

    ;; Scan subdirectories
    (dolist (subdir (directory-files directory t))
      (when (and (file-directory-p subdir)
                 (not (string-match "/\\.$" subdir))
                 (not (string-match "/\\.\\.$" subdir)))
        (setq registered (+ registered (ai-auto-complete-mcp-scan-subdirectories subdir)))))

    registered))

;; Export a tool as an MCP server
(defun ai-auto-complete-mcp-export-tool-as-server (tool-name directory)
  "Export TOOL-NAME as an MCP server to DIRECTORY."
  (interactive
   (list (completing-read "Export tool as MCP server: "
                          (hash-table-keys ai-auto-complete-tools))
         (read-directory-name "Export to directory: "
                             ai-auto-complete-mcp-servers-directory)))

  (let ((tool (gethash tool-name ai-auto-complete-tools)))
    (if (not tool)
        (progn
          (message "Tool %s not found" tool-name)
          nil)
      (let ((description (plist-get tool :description))
            (parameters (plist-get tool :parameters))
            (server-name (format "tool-%s" tool-name))
            (server-path (expand-file-name
                          (format "%s.py" tool-name)
                          directory)))

        ;; Create the directory if it doesn't exist
        (unless (file-exists-p directory)
          (make-directory directory t))

        ;; Create the Python MCP server file
        (ai-auto-complete-mcp-create-python-server
         server-name server-path description
         (list (list :name tool-name
                     :description description
                     :parameters parameters)))

        (message "Exported tool %s as MCP server to %s" tool-name server-path)
        server-path))))

;; Export multiple tools as an MCP server
(defun ai-auto-complete-mcp-export-tools-as-server (tool-names server-name directory)
  "Export multiple tools with TOOL-NAMES as an MCP server with SERVER-NAME to DIRECTORY."
  (interactive
   (list (completing-read-multiple "Export tools as MCP server (comma-separated): "
                                  (hash-table-keys ai-auto-complete-tools))
         (read-string "Server name: ")
         (read-directory-name "Export to directory: "
                             ai-auto-complete-mcp-servers-directory)))

  (let ((tools '())
        (server-path (expand-file-name
                      (format "%s.py" server-name)
                      directory)))

    ;; Collect tool information
    (dolist (tool-name tool-names)
      (let ((tool (gethash tool-name ai-auto-complete-tools)))
        (when tool
          (push (list :name tool-name
                      :description (plist-get tool :description)
                      :parameters (plist-get tool :parameters))
                tools))))

    (if (null tools)
        (progn
          (message "No valid tools found")
          nil)
      ;; Create the directory if it doesn't exist
      (unless (file-exists-p directory)
        (make-directory directory t))

      ;; Create the Python MCP server file
      (ai-auto-complete-mcp-create-python-server
       server-name server-path
       (format "MCP server for tools: %s" (mapconcat 'identity tool-names ", "))
       (nreverse tools))

      (message "Exported tools %s as MCP server to %s"
               (mapconcat 'identity tool-names ", ") server-path)
      server-path)))

;; Create a new MCP server from scratch
(defun ai-auto-complete-mcp-create-new-server (server-name directory &optional server-type)
  "Create a new MCP server with SERVER-NAME in DIRECTORY.
Optional SERVER-TYPE can be 'python or 'node. Defaults to 'python."
  (interactive
   (list (read-string "Server name: ")
         (read-directory-name "Create in directory: "
                             ai-auto-complete-mcp-servers-directory)
         (intern (completing-read "Server type: " '("python" "node") nil t))))

  ;; Create the directory if it doesn't exist
  (unless (file-exists-p directory)
    (make-directory directory t))

  ;; Create the server based on type
  (let* ((effective-server-type (or server-type 'python))
         (server-path nil)
         (description (format "MCP server: %s" server-name)))

    (cond
     ;; Python server
     ((eq effective-server-type 'python)
      (setq server-path (expand-file-name (format "%s.py" server-name) directory))

      ;; Create the Python MCP server file
      (with-temp-file server-path
        (insert "from mcp.server.fastmcp import FastMCP\n\n")
        (insert (format "mcp = FastMCP(\"%s\")\n\n" server-name))

        ;; Add example tool
        (insert "@mcp.tool()\n")
        (insert "def hello(name: str) -> str:\n")
        (insert "    \"\"\"Say hello to someone\"\"\"\n")
        (insert "    return f\"Hello, {name}!\"\n\n")

        ;; Add example resource
        (insert "@mcp.resource(\"greeting://{name}\")\n")
        (insert "def get_greeting(name: str) -> str:\n")
        (insert "    \"\"\"Get a personalized greeting\"\"\"\n")
        (insert "    return f\"Welcome, {name}!\"\n\n")

        ;; Add main block
        (insert "if __name__ == \"__main__\":\n")
        (insert "    mcp.run()\n"))

      ;; Make the file executable
      (set-file-modes server-path (logior (file-modes server-path) #o111))

      ;; Register the server with Python as the runner
      (ai-auto-complete-mcp-register-server server-name server-path nil nil "python"))

     ;; Node.js server
     ((eq effective-server-type 'node)
      ;; Create a directory for the Node.js package
      (let ((node-dir (expand-file-name server-name directory)))
        (unless (file-exists-p node-dir)
          (make-directory node-dir t))

        ;; Create package.json
        (with-temp-file (expand-file-name "package.json" node-dir)
          (insert "{\n")
          (insert (format "  \"name\": \"%s\",\n" server-name))
          (insert "  \"version\": \"1.0.0\",\n")
          (insert (format "  \"description\": \"%s\",\n" description))
          (insert "  \"main\": \"index.js\",\n")
          (insert "  \"scripts\": {\n")
          (insert "    \"start\": \"node index.js\"\n")
          (insert "  },\n")
          (insert "  \"dependencies\": {\n")
          (insert "  }\n")
          (insert "}\n"))

        ;; Create index.js
        (setq server-path (expand-file-name "index.js" node-dir))
        (with-temp-file server-path
          (insert "#!/usr/bin/env node\n\n")
          (insert "/**\n")
          (insert (format " * %s - Node.js MCP Server\n" server-name))
          (insert " */\n\n")
          (insert "const readline = require('readline');\n\n")
          (insert "class MCPServer {\n")
          (insert "  constructor(name) {\n")
          (insert "    this.name = name;\n")
          (insert "    this.tools = {};\n")
          (insert "    this.resources = {};\n")
          (insert "    this.prompts = {};\n")
          (insert "  }\n\n")

          ;; Tool registration method
          (insert "  tool(options = {}) {\n")
          (insert "    return (fn) => {\n")
          (insert "      const name = options.name || fn.name;\n")
          (insert "      this.tools[name] = {\n")
          (insert "        fn,\n")
          (insert "        name,\n")
          (insert "        description: options.description || ''\n")
          (insert "      };\n")
          (insert "      return fn;\n")
          (insert "    };\n")
          (insert "  }\n\n")

          ;; Handle request method
          (insert "  async handleRequest(request) {\n")
          (insert "    const { method, params, id } = request;\n")
          (insert "    try {\n")
          (insert "      let result;\n\n")
          (insert "      switch (method) {\n")
          (insert "        case 'initialize':\n")
          (insert "          result = {\n")
          (insert "            serverName: this.name,\n")
          (insert "            serverVersion: '1.0.0',\n")
          (insert "            capabilities: {\n")
          (insert "              tools: { listChanged: true },\n")
          (insert "              resources: { listChanged: true },\n")
          (insert "              prompts: { listChanged: true }\n")
          (insert "            }\n")
          (insert "          };\n")
          (insert "          break;\n\n")
          (insert "        case 'listTools':\n")
          (insert "          result = Object.values(this.tools).map(tool => ({\n")
          (insert "            name: tool.name,\n")
          (insert "            description: tool.description,\n")
          (insert "            parameters: []\n")
          (insert "          }));\n")
          (insert "          break;\n\n")
          (insert "        case 'callTool':\n")
          (insert "          const tool = this.tools[params.name];\n")
          (insert "          if (!tool) {\n")
          (insert "            throw new Error(`Tool not found: ${params.name}`);\n")
          (insert "          }\n")
          (insert "          result = await tool.fn(params.arguments);\n")
          (insert "          break;\n\n")
          (insert "        default:\n")
          (insert "          throw new Error(`Unknown method: ${method}`);\n")
          (insert "      }\n\n")
          (insert "      return {\n")
          (insert "        jsonrpc: '2.0',\n")
          (insert "        id,\n")
          (insert "        result\n")
          (insert "      };\n")
          (insert "    } catch (error) {\n")
          (insert "      return {\n")
          (insert "        jsonrpc: '2.0',\n")
          (insert "        id,\n")
          (insert "        error: {\n")
          (insert "          code: -32000,\n")
          (insert "          message: error.message\n")
          (insert "        }\n")
          (insert "      };\n")
          (insert "    }\n")
          (insert "  }\n\n")

          ;; Run method
          (insert "  run() {\n")
          (insert "    const rl = readline.createInterface({\n")
          (insert "      input: process.stdin,\n")
          (insert "      output: process.stdout,\n")
          (insert "      terminal: false\n")
          (insert "    });\n\n")
          (insert "    rl.on('line', async (line) => {\n")
          (insert "      if (line.trim()) {\n")
          (insert "        try {\n")
          (insert "          const request = JSON.parse(line);\n")
          (insert "          const response = await this.handleRequest(request);\n")
          (insert "          console.log(JSON.stringify(response));\n")
          (insert "        } catch (error) {\n")
          (insert "          console.error(`Error processing request: ${error.message}`);\n")
          (insert "        }\n")
          (insert "      }\n")
          (insert "    });\n")
          (insert "  }\n")
          (insert "}\n\n")

          ;; Create server instance
          (insert (format "const mcp = new MCPServer('%s');\n\n" server-name))

          ;; Example tool
          (insert "// Example tool\n")
          (insert "mcp.tool({ description: 'Say hello to someone' })((name) => {\n")
          (insert "  return `Hello, ${name}!`;\n")
          (insert "});\n\n")

          ;; Run the server
          (insert "if (require.main === module) {\n")
          (insert "  console.error('Node.js MCP server is running. Press Ctrl+C to exit.');\n")
          (insert "  mcp.run();\n")
          (insert "}\n\n")
          (insert "module.exports = mcp;\n"))

        ;; Make the file executable
        (set-file-modes server-path (logior (file-modes server-path) #o111))

        ;; Register the server with Node as the runner
        (ai-auto-complete-mcp-register-server server-name server-path 'stdio description "node")))

     ;; Unsupported server type
     (t
      (error "Unsupported server type: %s" effective-server-type)))

    (message "Created new MCP server %s at %s" server-name server-path)
    server-path))

;; Set custom MCP servers directory
(defun ai-auto-complete-mcp-set-servers-directory (directory)
  "Set a custom directory for MCP servers and scan it.
DIRECTORY should be the full path to the directory containing MCP servers."
  (interactive
   (list (read-directory-name "Set MCP servers directory: "
                             (or ai-auto-complete-mcp-servers-directory
                                 (expand-file-name "mcp-servers" user-emacs-directory)))))

  ;; Validate the directory
  (unless (file-directory-p directory)
    (if (yes-or-no-p (format "Directory %s does not exist. Create it?" directory))
        (make-directory directory t)
      (message "Directory not set")
      (cl-return-from ai-auto-complete-mcp-set-servers-directory nil)))

  ;; Set the directory
  (setq ai-auto-complete-mcp-servers-directory directory)
  (customize-save-variable 'ai-auto-complete-mcp-servers-directory directory)

  ;; Scan the directory for MCP servers
  (ai-auto-complete-mcp-scan-directory directory)

  (message "MCP servers directory set to %s" directory)
  directory)

;; Delete an MCP server
(defun ai-auto-complete-mcp-delete-server (server-name delete-file)
  "Delete MCP server with SERVER-NAME.
If DELETE-FILE is non-nil, also delete the server file.
Ensures the server is completely removed from the system and won't be loaded again."
  (interactive
   (let ((name (completing-read "Delete MCP server: " (ai-auto-complete-mcp-list-servers))))
     (list name (y-or-n-p "Also delete the server file? "))))

  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((path (plist-get server :path)))
        ;; Stop the server if it's running
        (when (eq (plist-get server :status) 'running)
          (ai-auto-complete-mcp-stop-server server-name))

        ;; Remove from registry
        (remhash server-name ai-auto-complete-mcp-servers)

        ;; Delete the file if requested
        (when (and delete-file path (file-exists-p path))
          (delete-file path))

        ;; Explicitly save the server configurations to ensure this server won't be loaded again
        (when (fboundp 'ai-auto-complete-mcp-save-servers)
          (ai-auto-complete-mcp-save-servers))

        ;; Clean up any cached data for this server
        (when (boundp 'ai-auto-complete-mcp-capabilities-cache)
          (remhash server-name ai-auto-complete-mcp-capabilities-cache))
        (when (boundp 'ai-auto-complete-mcp-tools-cache)
          (remhash server-name ai-auto-complete-mcp-tools-cache))
        (when (boundp 'ai-auto-complete-mcp-resources-cache)
          (remhash server-name ai-auto-complete-mcp-resources-cache))
        (when (boundp 'ai-auto-complete-mcp-prompts-cache)
          (remhash server-name ai-auto-complete-mcp-prompts-cache))

        ;; Clean up any tool results cache entries for this server
        (when (boundp 'ai-auto-complete-mcp-tool-results-cache)
          (maphash (lambda (key _)
                     (when (string-prefix-p (concat server-name ":") key)
                       (remhash key ai-auto-complete-mcp-tool-results-cache)))
                   ai-auto-complete-mcp-tool-results-cache))

        ;; Clean up any resource results cache entries for this server
        (when (boundp 'ai-auto-complete-mcp-resource-results-cache)
          (maphash (lambda (key _)
                     (when (string-prefix-p (concat server-name ":") key)
                       (remhash key ai-auto-complete-mcp-resource-results-cache)))
                   ai-auto-complete-mcp-resource-results-cache))

        ;; Clean up any prompt results cache entries for this server
        (when (boundp 'ai-auto-complete-mcp-prompt-results-cache)
          (maphash (lambda (key _)
                     (when (string-prefix-p (concat server-name ":") key)
                       (remhash key ai-auto-complete-mcp-prompt-results-cache)))
                   ai-auto-complete-mcp-prompt-results-cache))

        ;; Clean up any subscriptions for this server
        (when (fboundp 'ai-auto-complete-mcp-cleanup-server-subscriptions)
          (ai-auto-complete-mcp-cleanup-server-subscriptions server-name))

        (message "Deleted MCP server %s%s"
                 server-name
                 (if delete-file (format " and file %s" path) ""))
        t))))

(provide 'mcp/mcp-directory)
;;; mcp-directory.el ends here
