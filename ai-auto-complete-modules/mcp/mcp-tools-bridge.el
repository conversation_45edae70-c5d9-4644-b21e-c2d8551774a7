;;; mcp-tools-bridge.el --- Bridge between MCP and tools in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a bridge between the MCP system and the existing tools system
;; in the AI Auto Complete package.
;;
;; It supports both Python and Node.js MCP servers, handling their different parameter
;; formats to ensure tools are properly registered with the LLM system.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)
(require 'tools/tools-core)


; Add a fallback for the import-status variable
(defun ai-auto-complete-mcp-get-import-status (server-name)
  "Get the import status for SERVER-NAME.
If the import status doesn't exist, create it."
  (let ((import-status (gethash server-name ai-auto-complete-mcp-import-status-table)))
    (unless import-status
      (setq import-status (make-hash-table :test 'eq))
      (puthash 'done nil import-status)
      (puthash 'result nil import-status)
      (puthash 'tools-imported 0 import-status)
      (puthash 'start-time (current-time) import-status)
      (puthash server-name import-status ai-auto-complete-mcp-import-status-table))
    import-status))


;; Register an existing tool as an MCP server
(defun ai-auto-complete-mcp-register-tool-as-server (tool-name)
  "Register an existing tool with TOOL-NAME as an MCP server."
  (interactive
   (list (completing-read "Register tool as MCP server: "
                          (hash-table-keys ai-auto-complete-tools))))

  (let ((tool (gethash tool-name ai-auto-complete-tools)))
    (if (not tool)
        (progn
          (message "Tool %s not found" tool-name)
          nil)
      (let ((description (plist-get tool :description))
            (parameters (plist-get tool :parameters))
            (server-name (format "tool-%s" tool-name))
            (server-path (expand-file-name
                          (format "%s.py" tool-name)
                          ai-auto-complete-mcp-servers-directory)))

        ;; Create the server directory if it doesn't exist
        (unless (file-exists-p ai-auto-complete-mcp-servers-directory)
          (make-directory ai-auto-complete-mcp-servers-directory t))

        ;; Create the Python MCP server file
        (ai-auto-complete-mcp-create-python-server
         server-name server-path description
         (list (list :name tool-name
                     :description description
                     :parameters parameters)))

        (message "Registered tool %s as MCP server %s" tool-name server-name)
        server-name))))

;; Register multiple tools as an MCP server
(defun ai-auto-complete-mcp-register-tools-as-server (tool-names server-name)
  "Register multiple tools with TOOL-NAMES as an MCP server with SERVER-NAME."
  (interactive
   (list (completing-read-multiple "Register tools as MCP server (comma-separated): "
                                  (hash-table-keys ai-auto-complete-tools))
         (read-string "Server name: ")))

  (let ((tools '())
        (server-path (expand-file-name
                      (format "%s.py" server-name)
                      ai-auto-complete-mcp-servers-directory)))

    ;; Collect tool information
    (dolist (tool-name tool-names)
      (let ((tool (gethash tool-name ai-auto-complete-tools)))
        (when tool
          (push (list :name tool-name
                      :description (plist-get tool :description)
                      :parameters (plist-get tool :parameters))
                tools))))

    (if (null tools)
        (progn
          (message "No valid tools found")
          nil)
      ;; Create the server directory if it doesn't exist
      (unless (file-exists-p ai-auto-complete-mcp-servers-directory)
        (make-directory ai-auto-complete-mcp-servers-directory t))

      ;; Create the Python MCP server file
      (ai-auto-complete-mcp-create-python-server
       server-name server-path
       (format "MCP server for tools: %s" (mapconcat 'identity tool-names ", "))
       (nreverse tools))

      (message "Registered tools %s as MCP server %s"
               (mapconcat 'identity tool-names ", ") server-name)
      server-name)))

;; Import an MCP server's tools into the existing tools system
(defun ai-auto-complete-mcp-import-server-as-tool (server-name &optional skip-server-contact)
  "Import an MCP server with SERVER-NAME as a tool in the existing tools system.
If SKIP-SERVER-CONTACT is non-nil, use predefined tools instead of contacting the server."
  (interactive
   (list (completing-read "Import MCP server as tool: "
                          (ai-auto-complete-mcp-list-servers))))

  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-import-server-as-tool nil))

  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (predefined-tools (plist-get server :tools))
         (use-predefined (or skip-server-contact
                            (and (boundp 'ai-auto-complete-mcp-use-predefined-tools)
                                 ai-auto-complete-mcp-use-predefined-tools)
                            (and (boundp 'ai-auto-complete-mcp-prefer-persistent-data)
                                 ai-auto-complete-mcp-prefer-persistent-data
                                 predefined-tools))))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      ;; Check if we should use predefined tools
      (if use-predefined
          (progn
            (message "Using predefined tools for server %s" server-name)
            (if (not predefined-tools)
                (progn
                  (message "No predefined tools available for server %s" server-name)
                  nil)

              ;; Create a status buffer to show progress
              (let ((status-buffer (get-buffer-create "*MCP Tool Import*")))
                (with-current-buffer status-buffer
                  (let ((inhibit-read-only t))
                    (erase-buffer)
                    (special-mode) ;; Use special-mode for the buffer
                    (insert (format "Importing predefined tools from MCP server %s...\n\n" server-name))
                    (insert (format "Found %d predefined tools:\n" (length predefined-tools)))

                    (let ((imported-count 0))
                      (dolist (tool predefined-tools)
                        (let ((tool-name (plist-get tool :name))
                              (description (plist-get tool :description))
                              (parameters (plist-get tool :parameters))
                              (full-tool-name (format "mcp:%s:%s" server-name (plist-get tool :name))))

                          ;; Convert parameters to the format expected by register-tool
                          (let ((param-alist '()))
                            (dolist (param parameters)
                              (let ((param-name (plist-get param :name))
                                    (param-description (plist-get param :description)))

                                ;; Add parameter to the alist
                                (when param-name
                                  (push (cons param-name (or param-description "")) param-alist))))

                            ;; Create a function that calls the MCP tool
                            (let ((tool-function (ai-auto-complete-mcp-create-tool-function server-name tool-name)))

                              ;; Register the tool
                              (ai-auto-complete-register-tool
                               full-tool-name
                               (format "MCP tool from server %s: %s" server-name description)
                               tool-function
                               param-alist)

                              (setq imported-count (1+ imported-count))
                              (insert (format "  - Imported %s as %s\n" tool-name full-tool-name))))))

                      (insert (format "\nSuccessfully imported %d predefined tools from MCP server %s\n" imported-count server-name))
                      (insert "\nYou can now use these tools in your AI Auto Complete sessions.\n")
                      (insert "They will appear in the list of available tools (Menu: AI Auto Complete -> Tools -> List Available Tools).\n\n")

                      ;; Add buttons for actions
                      (insert "Actions: ")
                      (insert-button "List Available Tools"
                                    'action (lambda (_)
                                             (ai-auto-complete-tools-list))
                                    'follow-link t)

                      (insert " | ")
                      (insert-button "Close"
                                    'action (lambda (_)
                                             (kill-buffer status-buffer))
                                    'follow-link t)

                      (message "Successfully imported %d predefined tools from MCP server %s" imported-count server-name)

                      ;; Refresh tool definitions to include the newly imported MCP tools
                      (when (fboundp 'ai-auto-complete-mcp-refresh-tool-definitions-after-import)
                        (ai-auto-complete-mcp-refresh-tool-definitions-after-import server-name))

                      (switch-to-buffer status-buffer)
                      t)))))) ;NB: added a closeing paren here

        ;; Use the standard approach: start the server and contact it for tools
        (progn
          ;; Start the server if it's not running
          (unless (eq (plist-get server :status) 'running)
            (message "Starting MCP server %s..." server-name)
            (ai-auto-complete-mcp-start-server server-name))

          ;; Check if the server is running now
          (if (not (eq (plist-get (ai-auto-complete-mcp-get-server server-name) :status) 'running))
              (progn
                (message "Failed to start MCP server %s" server-name)
                nil)

            ;; Create a status buffer to show progress
            (let ((status-buffer (get-buffer-create "*MCP Tool Import*")))
              (with-current-buffer status-buffer
                (let ((inhibit-read-only t))
                  (erase-buffer)
                  (special-mode) ;; Use special-mode for the buffer
                  (insert (format "Importing tools from MCP server %s...\n\n" server-name))))

              ;; List tools from the server
              (message "Listing tools from MCP server %s..." server-name)
              (ai-auto-complete-mcp-list-tools
               server-name
               (lambda (tools)
                 (with-current-buffer status-buffer
                   (let ((inhibit-read-only t))
                     (goto-char (point-max))
                     (if (stringp tools)
                         (progn
                           (insert (format "Error listing tools from server %s: %s\n" server-name tools))
                           (message "Error listing tools from server %s: %s" server-name tools))
                       ;; Register each tool
                       (if (null tools)
                           (progn
                             (insert "No tools found on the server.\n")
                             (message "No tools found on MCP server %s" server-name))
                         (insert (format "Found %d tools:\n" (length tools)))
                         (let ((imported-count 0))
                           (dolist (tool tools)
                             (let ((tool-name (plist-get tool :name))
                                   (description (plist-get tool :description))
                                   (parameters (plist-get tool :parameters))
                                   (full-tool-name (format "mcp:%s:%s" server-name (plist-get tool :name))))

                               ;; Convert parameters to the format expected by register-tool
                               (let ((param-alist '()))
                                 (dolist (param parameters)
                                   (let ((param-name (plist-get param :name))
                                         (param-description (plist-get param :description))
                                         (param-required (plist-get param :required))
                                         (param-schema (plist-get param :schema)))

                                     ;; Handle Node.js MCP server parameter format which includes schema
                                     (when (and param-schema (or (not param-description) (string-empty-p param-description)))
                                       (let ((schema-type (cdr (assoc 'type param-schema)))
                                             (schema-default (cdr (assoc 'default param-schema)))
                                             (schema-desc ""))

                                         (when ai-auto-complete-mcp-debug-mode
                                           (message "[MCP-DEBUG] Processing Node.js parameter format for %s" param-name)
                                           (message "[MCP-DEBUG] Schema: %S" param-schema))

                                         ;; Build a description from schema information
                                         (setq schema-desc (format "Type: %s" (or schema-type "unknown")))
                                         (when schema-default
                                           (setq schema-desc (format "%s, Default: %s" schema-desc schema-default)))
                                         (when param-required
                                           (setq schema-desc (format "%s, Required: %s" schema-desc
                                                                    (if param-required "yes" "no"))))

                                         ;; Use schema description if no description provided
                                         (setq param-description (format "%s (%s)"
                                                                        (or param-description "")
                                                                        schema-desc))

                                         (when ai-auto-complete-mcp-debug-mode
                                           (message "[MCP-DEBUG] Generated parameter description: %s" param-description))))

                                     ;; Add parameter to the alist
                                     (when param-name
                                       (push (cons param-name (or param-description "")) param-alist))))

                                 ;; Log the final parameter list for debugging
                                 (when ai-auto-complete-mcp-debug-mode
                                   (message "[MCP-DEBUG] Final parameter list for tool %s: %S" tool-name param-alist))))

                               ;; Create a function that calls the MCP tool
                               (let ((tool-function (ai-auto-complete-mcp-create-tool-function server-name tool-name)))

                                 ;; Register the tool
                                 (ai-auto-complete-register-tool
                                  full-tool-name
                                  (format "MCP tool from server %s: %s" server-name description)
                                  tool-function
                                  param-alist)

                                 (setq imported-count (1+ imported-count))
                                 (insert (format "  - Imported %s as %s\n" tool-name full-tool-name))))))

                           (insert (format "\nSuccessfully imported %d tools from MCP server %s\n" imported-count server-name))
                           (insert "\nYou can now use these tools in your AI Auto Complete sessions.\n")
                           (insert "They will appear in the list of available tools (Menu: AI Auto Complete -> Tools -> List Available Tools).\n\n")

                           ;; Add buttons for actions
                           (insert "Actions: ")
                           (insert-button "List Available Tools"
                                         'action (lambda (_)
                                                  (ai-auto-complete-tools-list))
                                         'follow-link t)

                           (insert " | ")
                           (insert-button "Close"
                                         'action (lambda (_)
                                                  (kill-buffer status-buffer))
                                         'follow-link t)

                           (message "Successfully imported %d tools from MCP server %s" imported-count server-name)

                           ;; Refresh tool definitions to include the newly imported MCP tools
                           (when (fboundp 'ai-auto-complete-mcp-refresh-tool-definitions-after-import)
                             (ai-auto-complete-mcp-refresh-tool-definitions-after-import server-name))

                           (switch-to-buffer status-buffer)
                           t)))))))
              t)))))

;; Create a function that wraps an MCP tool call
(defun ai-auto-complete-mcp-create-tool-function (server-name tool-name)
  "Create a function that calls MCP tool TOOL-NAME on SERVER-NAME."
  (lambda (params)
    (let ((result nil)
          (error-message nil)
          (done nil))

      (when ai-auto-complete-mcp-debug-mode
        (message "[MCP-DEBUG] Creating tool function for %s on server %s" tool-name server-name)
        (message "[MCP-DEBUG] Parameters: %S" params))

      ;; Check if we should use the simplified stdio transport
      (if (and (boundp 'ai-auto-complete-mcp-use-simplified-stdio)
               ai-auto-complete-mcp-use-simplified-stdio
               (fboundp 'ai-auto-complete-mcp-stdio-simple-call-tool))
          ;; Use the simplified stdio transport
          (progn
            (message "Using simplified stdio transport for tool %s on server %s" tool-name server-name)

            ;; Call the MCP tool using the simplified stdio transport
            (ai-auto-complete-mcp-stdio-simple-call-tool
             server-name
             tool-name
             params
             (lambda (response)
               (setq result response)
               (setq done t)
               (when ai-auto-complete-mcp-debug-mode
                 (message "[MCP-DEBUG] Tool %s on server %s returned result: %s"
                          tool-name server-name
                          (if (stringp response)
                              (substring response 0 (min 100 (length response)))
                            response))))))

        ;; Use the standard MCP tool call
        (ai-auto-complete-mcp-call-tool
         server-name
         tool-name
         params
         (lambda (response)
           (setq result response)
           (setq done t)
           (when ai-auto-complete-mcp-debug-mode
             (message "[MCP-DEBUG] Tool %s on server %s returned result: %s"
                      tool-name server-name
                      (if (stringp response)
                          (substring response 0 (min 100 (length response)))
                        response))))))

      ;; Wait for the result with timeout
      (let ((timeout 20) ;; Increased timeout to 30 seconds
            (start-time (current-time)))
        (while (and (not done)
                    (< (float-time (time-since start-time)) timeout))
          (sleep-for 0.1))

        (when ai-auto-complete-mcp-debug-mode
          (if done
              (message "[MCP-DEBUG] Tool %s on server %s completed successfully" tool-name server-name)
            (message "[MCP-DEBUG] Tool %s on server %s timed out" tool-name server-name)))

        ;; Return the result or error
        (if done
            result
          (let ((error-msg (format "Error: Timeout waiting for MCP tool %s" tool-name)))
            (message "Error executing MCP tool %s: %s" tool-name error-msg)
            error-msg))))))

;; Test MCP tool integration with LLM
(defun ai-auto-complete-mcp-test-tool-integration ()
  "Test MCP tool integration with LLM."
  (interactive)
  (let ((buffer-name "*MCP Tool Integration Test*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert "MCP Tool Integration Test\n")
        (insert "=======================\n\n")

        ;; Check if MCP is enabled
        (if (not ai-auto-complete-mcp-enabled)
            (progn
              (insert "MCP integration is not enabled.\n\n")
              (insert-button "Enable MCP"
                            'action (lambda (_)
                                     (ai-auto-complete-mcp-toggle)
                                     (ai-auto-complete-mcp-test-tool-integration))
                            'follow-link t))

          ;; MCP is enabled, check if tools are enabled
          (if (not ai-auto-complete-tools-enabled)
              (progn
                (insert "Tools are not enabled.\n\n")
                (insert-button "Enable Tools"
                              'action (lambda (_)
                                       (ai-auto-complete-tools-toggle)
                                       (ai-auto-complete-mcp-test-tool-integration))
                              'follow-link t))

            ;; Both MCP and tools are enabled, check for MCP tools
            (let ((mcp-tools 0)
                  (mcp-tool-names '()))
              (maphash (lambda (name tool)
                         (when (string-match "^mcp:" name)
                           (setq mcp-tools (1+ mcp-tools))
                           (push name mcp-tool-names)))
                       ai-auto-complete-tools)

              (if (= mcp-tools 0)
                  (progn
                    (insert "No MCP tools found.\n\n")
                    (insert-button "Import All Servers as Tools"
                                  'action (lambda (_)
                                           (ai-auto-complete-mcp-import-all-servers-as-tools)
                                           (ai-auto-complete-mcp-test-tool-integration))
                                  'follow-link t))

                ;; MCP tools found, test with LLM
                (insert (format "Found %d MCP tools registered:\n\n" mcp-tools))
                (dolist (name (sort mcp-tool-names 'string<))
                  (let ((tool (gethash name ai-auto-complete-tools)))
                    (insert (format "- %s: %s\n" name (plist-get tool :description)))))

                (insert "\nTesting MCP tool integration with LLM...\n\n")

                ;; Create a test prompt that will likely trigger tool usage
                (let ((test-prompt (format "I need you to help me with a task that requires using tools.
Please use one of the MCP tools that are available to you. Here are some of the MCP tools you can use:
%s

Please use one of these tools to demonstrate that MCP tools are working correctly."
                                          (mapconcat (lambda (name)
                                                       (format "- %s" name))
                                                     (sort mcp-tool-names 'string<)
                                                     "\n"))))

                  (insert (format "Test prompt:\n%s\n\n" test-prompt))

                  ;; Add buttons for actions
                  (insert "Actions: ")
                  (insert-button "Send to LLM"
                                'action (lambda (_)
                                         (ai-auto-complete-complete
                                          (ai-auto-complete-get-current-backend)
                                          test-prompt
                                          nil
                                          (lambda (response)
                                            (with-current-buffer (get-buffer buffer-name)
                                              (let ((inhibit-read-only t))
                                                (goto-char (point-max))
                                                (insert "\nLLM Response:\n\n")
                                                (insert response))))))
                                'follow-link t)

                  (insert " | ")
                  (insert-button "List Available Tools"
                                'action (lambda (_)
                                         (ai-auto-complete-tools-list))
                                'follow-link t)

                  (insert " | ")
                  (insert-button "Refresh"
                                'action (lambda (_)
                                         (ai-auto-complete-mcp-test-tool-integration))
                                'follow-link t))))))))

    (switch-to-buffer buffer-name)))

;; Test an MCP tool
(defun ai-auto-complete-mcp-test-tool (server-name tool-name &optional params)
  "Test MCP tool TOOL-NAME on SERVER-NAME with optional PARAMS."
  (interactive
   (let* ((server-name (completing-read "Select MCP server: " (ai-auto-complete-mcp-list-servers)))
          (tools-promise (make-hash-table :test 'eq))
          (tool-name nil))

     ;; Set up the promise
     (puthash 'status 'pending tools-promise)
     (puthash 'value nil tools-promise)

     ;; List tools
     (ai-auto-complete-mcp-list-tools
      server-name
      (lambda (tools)
        (puthash 'status 'fulfilled tools-promise)
        (puthash 'value tools tools-promise)))

     ;; Wait for the result (with timeout)
     (let ((timeout 5)
           (start-time (current-time)))
       (while (and (eq (gethash 'status tools-promise) 'pending)
                   (< (float-time (time-since start-time)) timeout))
         (sleep-for 0.1))

       ;; Select a tool
       (let ((tools (gethash 'value tools-promise)))
         (if (or (not tools) (stringp tools))
             (error "Error listing tools for server %s" server-name)
           (let ((tool-names (mapcar (lambda (tool) (plist-get tool :name)) tools)))
             (setq tool-name (completing-read "Select tool: " tool-names))
             (list server-name tool-name)))))))

  (let ((full-tool-name (format "mcp:%s:%s" server-name tool-name)))
    (if (not (gethash full-tool-name ai-auto-complete-tools))
        (message "Tool %s not found. Import it first with M-x ai-auto-complete-mcp-import-server-as-tool" full-tool-name)
      (let ((result (ai-auto-complete-execute-tool full-tool-name (or params '()))))
        (with-output-to-temp-buffer "*MCP Tool Test*"
          (princ (format "Testing MCP tool: %s\n\n" full-tool-name))
          (princ (format "Parameters: %S\n\n" params))
          (princ "Result:\n")
          (princ result))))))


;; Import an MCP server as a tool synchronously - fixed version 2
(defun ai-auto-complete-mcp-import-server-as-tool-sync (server-name &optional skip-server-contact)
  "Import an MCP server with SERVER-NAME as a tool synchronously with additional fixes.
If SKIP-SERVER-CONTACT is non-nil, use predefined tools instead of contacting the server."
  (interactive
   (list (completing-read "Import MCP server as tool (sync): "
                          (ai-auto-complete-mcp-list-servers))))

  (message "Synchronously importing MCP server %s as tool..." server-name)

  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-import-server-as-tool-sync-fixed2 nil))

  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (predefined-tools (plist-get server :tools))
         (use-predefined (or skip-server-contact
                            (and (boundp 'ai-auto-complete-mcp-use-predefined-tools)
                                 ai-auto-complete-mcp-use-predefined-tools)
                            (and (boundp 'ai-auto-complete-mcp-prefer-persistent-data)
                                 ai-auto-complete-mcp-prefer-persistent-data
                                 predefined-tools))))

    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)

      ;; Check if we should use predefined tools
      (if use-predefined
          (progn
            (message "Using predefined tools for server %s" server-name)
            (if (not predefined-tools)
                (progn
                  (message "No predefined tools available for server %s" server-name)
                  nil)

              ;; Process predefined tools
              (let ((imported-count 0))
                (dolist (tool predefined-tools)
                  (let* ((tool-name (plist-get tool :name))
                         (tool-description (plist-get tool :description))
                         ;; Safely truncate description
                         (description (ai-auto-complete-mcp-safely-truncate-description tool-description))
                         (parameters (plist-get tool :parameters))
                         (input-schema (plist-get tool :inputSchema))
                         (full-tool-name (format "mcp:%s:%s" server-name tool-name)))

                    ;; Convert parameters to the format expected by register-tool
                    (let ((param-alist '()))
                      (dolist (param parameters)
                        (let ((param-name (plist-get param :name))
                              (param-description (plist-get param :description)))

                          ;; Add parameter to the alist
                          (when param-name
                            (push (cons param-name (or param-description "")) param-alist))))

                      ;; Create a function that calls the MCP tool
                      (let ((tool-function (ai-auto-complete-mcp-create-tool-function server-name tool-name)))

                        ;; Register the tool
                        (ai-auto-complete-register-tool
                         full-tool-name
                         (format "MCP tool from server %s: %s" server-name description)
                         tool-function
                         param-alist)

                        (setq imported-count (1+ imported-count))))))

                (message "Successfully imported %d predefined tools from MCP server %s" imported-count server-name)

                ;; Refresh tool definitions to include the newly imported MCP tools
                (when (fboundp 'ai-auto-complete-mcp-refresh-tool-definitions-after-import)
                  (ai-auto-complete-mcp-refresh-tool-definitions-after-import server-name))

                ;; Return success
                t)))

        ;; Use the standard approach: start the server and contact it for tools
        (ai-auto-complete-mcp-import-server-as-tool-sync-fixed server-name skip-server-contact)))))




; Define a global variable to store import status for each server
(defvar ai-auto-complete-mcp-import-status-table (make-hash-table :test 'equal)
  "Hash table to store import status for each MCP server.")

;; Process tools from an MCP server - fixed version
(defun ai-auto-complete-mcp-process-tools (server-name tools-list)
  "Process TOOLS-LIST from SERVER-NAME and update import status.
This function extracts tool information, parameters, and registers each tool."
  (let ((import-status (gethash server-name ai-auto-complete-mcp-import-status-table)))
    (condition-case process-err
        (progn
          (if (null tools-list)
              (progn
                (message "No tools found on MCP server %s" server-name)
                (puthash 'done t import-status))

            (message "Found %d tools on MCP server %s" (length tools-list) server-name)

            ;; Add debug logging to help diagnose issues
            (when ai-auto-complete-mcp-debug-mode
              (message "[MCP-DEBUG] Tools list: %S" tools-list))

            ;; Process each tool with error handling for each individual tool
            (dolist (tool tools-list)
              (condition-case tool-err
                  (progn
                    ;; Extract tool information based on the format
                    (let* ((tool-name (plist-get tool :name))
                           (raw-description (plist-get tool :description))
                           ;; Truncate description to a reasonable length (250 chars)
                           (description (if (and (stringp raw-description)
                                                (> (length raw-description) 250))
                                           (condition-case err
                                               (concat (substring raw-description 0 (min 247 (1- (length raw-description)))) "...")
                                             (error
                                              (message "Error truncating description: %s" (error-message-string err))
                                              (if (stringp raw-description)
                                                  (substring raw-description 0 (min 100 (length raw-description)))
                                                "Description truncated")))
                                         raw-description))
                           (input-schema (plist-get tool :inputSchema))
                           (parameters (plist-get tool :parameters)))

                      ;; Handle missing tool name
                      (when (not tool-name)
                        (let ((random-name (format "unknown-tool-%d" (random 10000))))
                          (message "Warning: Tool has no name, using %s" random-name)
                          (setq tool-name random-name)))

                      ;; Define full-tool-name now that we have a valid tool-name
                      (let ((full-tool-name (format "mcp:%s:%s" server-name tool-name)))

                        (message "Processing tool %s from server %s" tool-name server-name)

                        ;; Extract parameters from inputSchema for Python MCP servers
                        (when (and input-schema (not parameters))
                          (let ((properties (plist-get input-schema :properties))
                                (required (plist-get input-schema :required)))

                            (when properties
                              ;; Convert plist to a list of pairs for processing
                              (let ((param-list '()))
                                ;; Process properties plist
                                (let ((prop-name nil)
                                      (prop-info nil))
                                  (dolist (item properties)
                                    (if prop-name
                                        (progn
                                          ;; We have a property name and now its value
                                          (setq prop-info item)

                                          ;; Process this property
                                          (let* ((prop-title (plist-get prop-info :title))
                                                 (prop-type (plist-get prop-info :type))
                                                 (is-required (and required (member (symbol-name prop-name) required))))

                                            ;; Add to parameter list
                                            (push (list :name (symbol-name prop-name)
                                                       :description (or prop-title (symbol-name prop-name))
                                                       :required is-required
                                                       :schema (list 'type prop-type))
                                                  param-list))

                                          ;; Reset for next pair
                                          (setq prop-name nil)
                                          (setq prop-info nil))
                                      ;; This is a property name
                                      (setq prop-name item))))

                                ;; Set the parameters
                                (setq parameters param-list)))))

                        ;; Convert parameters to the format expected by register-tool
                        (let ((param-alist '()))
                          (dolist (param parameters)
                            (let ((param-name (plist-get param :name))
                                  (param-description (plist-get param :description)))

                              ;; Add parameter to the alist
                              (when param-name
                                (push (cons param-name (or param-description "")) param-alist))))

                          ;; Create a function that calls the MCP tool
                          (let ((tool-function (ai-auto-complete-mcp-create-tool-function server-name tool-name)))

                            ;; Register the tool
                            (ai-auto-complete-register-tool
                             full-tool-name
                             (format "MCP tool from server %s: %s" server-name description)
                             tool-function
                             param-alist)

                            ;; Save the tool in the server configuration for future use
                            (let ((tool-plist (list :name tool-name
                                                   :description description
                                                   :parameters parameters)))

                              ;; Add the tool to the server's predefined tools
                              (when (fboundp 'ai-auto-complete-mcp-add-server-tool-with-schema)
                                (ai-auto-complete-mcp-add-server-tool-with-schema
                                 server-name tool-plist)))

                            ;; Update the count in the hash table
                            (puthash 'tools-imported
                                    (1+ (gethash 'tools-imported import-status 0))
                                    import-status))))))
                (error
                 (message "Error processing tool from server %s: %s"
                          server-name (error-message-string tool-err)))))

            ;; Mark as done after processing all tools
            (message "Imported %d tools from MCP server %s"
                     (gethash 'tools-imported import-status 0) server-name)
            (puthash 'result t import-status)
            (puthash 'done t import-status)))
      (error
       (message "Error processing tools list from server %s: %s"
                server-name (error-message-string process-err))
       (puthash 'done t import-status)
       nil)))

  ;; Return true to indicate success
  t)



;; ;; Fixed version of the tool processing function
; Import all MCP servers as tools
(defun ai-auto-complete-mcp-import-all-servers-as-tools (&optional skip-server-contact)
  "Import all MCP servers as tools in the existing tools system.
If SKIP-SERVER-CONTACT is non-nil, use predefined tools instead of contacting the server."
  (interactive)
  (let ((servers (ai-auto-complete-mcp-list-servers))
        (imported 0)
        (use-predefined (or skip-server-contact
                           (and (boundp 'ai-auto-complete-mcp-use-predefined-tools)
                                ai-auto-complete-mcp-use-predefined-tools))))
    (dolist (server-name servers)
      (when (ai-auto-complete-mcp-import-server-as-tool server-name use-predefined)
        (setq imported (1+ imported))))
    (message "Imported tools from %d of %d MCP servers (using predefined: %s)"
             imported (length servers) (if use-predefined "yes" "no"))))

;; Find a working Python executable
(defun ai-auto-complete-mcp-find-python-executable ()
  "Find a working Python executable.
Tries different common Python executable names and returns the first one that works."
  (interactive)
  (message "Searching for a working Python executable...")

  (let ((python-candidates '("python3" "python" "python3.10" "python3.9" "python3.8" "python3.7"))
        (working-python nil))

    ;; Try each candidate
    (dolist (candidate python-candidates)
      (unless working-python
        (message "Trying Python executable: %s" candidate)
        (when (executable-find candidate)
          (message "Found working Python executable: %s" candidate)
          (setq working-python candidate))))

    ;; If a working Python executable was found, update the customization
    (if working-python
        (progn
          (message "Setting Python command to: %s" working-python)
          (setq ai-auto-complete-mcp-python-command working-python)
          working-python)
      (message "No working Python executable found. Please install Python or set ai-auto-complete-mcp-python-command manually.")
      nil)))

;; Register MCP tools directly
(defun ai-auto-complete-mcp-register-tools-directly ()
  "Register MCP tools directly without relying on asynchronous callbacks."
  (interactive)
  (message "Directly registering MCP tools...")

  ;; Only proceed if MCP is enabled
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-register-tools-directly nil))

  ;; Use the server tools defined in mcp-server-tools.el
  (when (and (boundp 'ai-auto-complete-mcp-server-tools) ai-auto-complete-mcp-server-tools)
    (message "Using predefined server tools from mcp-server-tools.el")
    (let ((server-tools ai-auto-complete-mcp-server-tools)
          (total-tools 0))
      (dolist (server-entry server-tools)
        (let ((server-name (car server-entry))
              (tools (cdr server-entry)))

          ;; Register each tool
          (dolist (tool tools)
            (let ((tool-name (nth 0 tool))
                  (description (nth 1 tool))
                  (parameters (nth 2 tool))
                  (full-tool-name (format "mcp:%s:%s" server-name (nth 0 tool))))

              ;; Create a function that calls the MCP tool
              (let ((tool-function (ai-auto-complete-mcp-create-tool-function server-name tool-name)))

                (message "Directly registering MCP tool %s" full-tool-name)

                ;; Register the tool
                (ai-auto-complete-register-tool
                 full-tool-name
                 (format "MCP tool from server %s: %s" server-name description)
                 tool-function
                 parameters)

                (setq total-tools (1+ total-tools)))))))

      (message "Directly registered %d MCP tools" total-tools)

      ;; Refresh tool definitions
      (ai-auto-complete-mcp-refresh-tool-definitions)

      total-tools)

    ;; Return nil if no server tools are defined
    (message "No server tools defined in mcp-server-tools.el")
    nil))

;; Import an MCP server as a tool synchronously - fixed version
;; Import an MCP server as a tool synchronously - fixed version
(defun ai-auto-complete-mcp-import-server-as-tool-sync-fixed (server-name &optional skip-server-contact)
  "Import an MCP server with SERVER-NAME as a tool synchronously.
If SKIP-SERVER-CONTACT is non-nil, use predefined tools instead of contacting the server."
  (interactive
   (list (completing-read "Import MCP server as tool (sync): "
                          (ai-auto-complete-mcp-list-servers))))

  (message "Synchronously importing MCP server %s as tool..." server-name)

  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-import-server-as-tool-sync-fixed nil))

  (let* ((server (ai-auto-complete-mcp-get-server server-name))
         (predefined-tools (plist-get server :tools))
         (use-predefined (or skip-server-contact
                            (and (boundp 'ai-auto-complete-mcp-use-predefined-tools)
                                 ai-auto-complete-mcp-use-predefined-tools)
                            (and (boundp 'ai-auto-complete-mcp-prefer-persistent-data)
                                 ai-auto-complete-mcp-prefer-persistent-data
                                 predefined-tools))))

    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)

      ;; Check if we should use predefined tools
      (if use-predefined
          (progn
            (message "Using predefined tools for server %s" server-name)
            (if (not predefined-tools)
                (progn
                  (message "No predefined tools available for server %s" server-name)
                  nil)

              ;; Process predefined tools
              (let ((imported-count 0))
                (dolist (tool predefined-tools)
                  (let ((tool-name (plist-get tool :name))
                        (raw-description (plist-get tool :description))
                        ;; Truncate description to a reasonable length (250 chars)
                        (description (if (and (stringp raw-description)
                                             (> (length raw-description) 250))
                                        (condition-case err
                                            (concat (substring raw-description 0 (min 247 (1- (length raw-description)))) "...")
                                          (error
                                           (message "Error truncating description: %s" (error-message-string err))
                                           (if (stringp raw-description)
                                               (substring raw-description 0 (min 100 (length raw-description)))
                                             "Description truncated")))
                                      raw-description))
                        (parameters (plist-get tool :parameters))
                        (input-schema (plist-get tool :inputSchema))
                        (full-tool-name (format "mcp:%s:%s" server-name (plist-get tool :name))))

                    ;; Convert parameters to the format expected by register-tool
                    (let ((param-alist '()))
                      (dolist (param parameters)
                        (let ((param-name (plist-get param :name))
                              (param-description (plist-get param :description)))

                          ;; Add parameter to the alist
                          (when param-name
                            (push (cons param-name (or param-description "")) param-alist))))

                      ;; Create a function that calls the MCP tool
                      (let ((tool-function (ai-auto-complete-mcp-create-tool-function server-name tool-name)))

                        ;; Register the tool
                        (ai-auto-complete-register-tool
                         full-tool-name
                         (format "MCP tool from server %s: %s" server-name description)
                         tool-function
                         param-alist)

                        (setq imported-count (1+ imported-count))))))

                (message "Successfully imported %d predefined tools from MCP server %s" imported-count server-name)

                ;; Refresh tool definitions to include the newly imported MCP tools
                (when (fboundp 'ai-auto-complete-mcp-refresh-tool-definitions-after-import)
                  (ai-auto-complete-mcp-refresh-tool-definitions-after-import server-name))

                ;; Return success
                t)))

        ;; Use the standard approach: start the server and contact it for tools
        (progn
          ;; Start the server if it's not running
          (unless (eq (plist-get server :status) 'running)
            (let ((path (plist-get server :path))
                  (transport (plist-get server :transport)))
              (message "Starting MCP server %s at path %s (transport: %s)..." server-name path transport)
              ;; Add debug logging for settings-based servers
              (when (eq transport 'settings)
                (message "MCP DEBUG: Starting settings-based server %s with path %s" server-name path))
              (ai-auto-complete-mcp-start-server server-name))) ;; Use the standard start-server function

          ;; Check if the server is running now
          (if (not (eq (plist-get (ai-auto-complete-mcp-get-server server-name) :status) 'running))
              (progn
                (message "Failed to start MCP server %s" server-name)
                nil)

            ;; Set up timeout variables and create a global hash table entry for this server
            (let ((timeout 10) ;; Timeout in seconds
                  (import-status (make-hash-table :test 'eq))
                  (result nil))

              ;; Initialize the status hash table
              (puthash 'done nil import-status)
              (puthash 'result nil import-status)
              (puthash 'tools-imported 0 import-status)
              (puthash 'start-time (current-time) import-status)

              ;; Store the import status in the global table
              (puthash server-name import-status ai-auto-complete-mcp-import-status-table)

              (when ai-auto-complete-mcp-debug-mode
                (message "[MCP-DEBUG] Created import status hash table for server %s: %S" server-name import-status))

              ;; Start the import
              (message "Listing tools from MCP server %s..." server-name)
              (ai-auto-complete-mcp-stdio-bridge-list-tools ;; Second fork: After initilizing the mcp server, ask server to list tools
               server-name
               (lambda (tools)
                 ;; Get the import status from the global table
                 (let ((import-status (gethash server-name ai-auto-complete-mcp-import-status-table)))
                   (message "Received tools list from server %s" server-name)

                   ;; Add debug logging to help diagnose issues
                   (when ai-auto-complete-mcp-debug-mode
                     (message "[MCP-DEBUG] Raw tools response: %S" tools)
                     (message "[MCP-DEBUG] Using import status from global table: %S" import-status))

                   ;; Safely process the tools response
                   (condition-case process-err
                       (progn
                         (let ((processed-tools tools)
                               (tools-list nil))

                           ;; Add debug logging for the raw response
                           (when ai-auto-complete-mcp-debug-mode
                             (message "[MCP-DEBUG] Processing tools response type: %s" (type-of tools))
                             (message "[MCP-DEBUG] Is tools a string? %s" (if (stringp tools) "yes" "no"))
                             (message "[MCP-DEBUG] Is tools a list? %s" (if (listp tools) "yes" "no")))

                           ;; If tools is a string, try to parse it as a plist or list
                           (when (stringp tools)
                             (condition-case parse-err
                                 (progn
                                   (when ai-auto-complete-mcp-debug-mode
                                     (message "[MCP-DEBUG] Attempting to parse string as plist or list: %s" tools))

                                   ;; Check if it looks like a plist string (starts with "(" and contains ":")
                                   (if (and (string-match-p "^\\s*(\\s*:" tools)
                                            (string-match-p ":\\s*[^\\s]" tools))
                                       (progn
                                         (when ai-auto-complete-mcp-debug-mode
                                           (message "[MCP-DEBUG] String looks like a plist, attempting to read"))
                                         ;; Try to read it as a plist
                                         (setq processed-tools (read tools)))

                                     ;; Check if it looks like a list string (starts with "[" or "(")
                                     (when (string-match-p "^\\s*[\\[(]" tools)
                                       (when ai-auto-complete-mcp-debug-mode
                                         (message "[MCP-DEBUG] String looks like a list, attempting to read"))
                                       ;; Try to read it as a list
                                       (setq processed-tools (read tools)))))
                               (error
                                (message "[MCP-DEBUG] Error parsing tools string: %s"
                                         (error-message-string parse-err))
                                ;; Keep it as a string if parsing fails
                                (setq processed-tools tools))))

                           ;; Now process the tools based on its type
                           (if (and (stringp processed-tools)
                                    (not (string-match-p "^\\s*[\\[(]" processed-tools)))
                               (progn
                                 (message "Error listing tools from server %s: %s" server-name processed-tools)
                                 (puthash 'done t import-status))

                             ;; Handle different response formats
                             ;; Check if the response is in the format {tools: [...]} (Node.js MCP servers)
                             (if (and (plist-member processed-tools :tools)
                                      (listp (plist-get processed-tools :tools)))
                                 (setq tools-list (plist-get processed-tools :tools))

                               ;; Check if the response is in the format [...] (Python MCP servers)
                               (if (listp processed-tools)
                                   (setq tools-list processed-tools)

                                 ;; Unknown format
                                 (message "Unknown tools response format from server %s" server-name)
                                 (when ai-auto-complete-mcp-debug-mode
                                   (message "[MCP-DEBUG] Unknown tools response format: %S" processed-tools))
                                 (setq tools-list nil)))

                             ;; Process the tools using our fixed function
                             (when ai-auto-complete-mcp-debug-mode
                               (message "[MCP-DEBUG] Processing tools list: %S" tools-list))
                             (ai-auto-complete-mcp-process-tools-fixed server-name tools-list)))))
                     (error
                      (message "Error processing tools response from server %s: %s"
                               server-name (error-message-string process-err))
                      (puthash 'done t import-status))))) ; NB : removed ')' from here

              ;; Wait for the import to complete
              (when ai-auto-complete-mcp-debug-mode
                (message "[MCP-DEBUG] Waiting for import to complete (timeout: %s seconds)" timeout)
                (message "[MCP-DEBUG] Start time: %s" (format-time-string "%Y-%m-%d %H:%M:%S" (gethash 'start-time import-status))))

              ;; Wait for the import to complete with timeout
              (while (and (not (gethash 'done import-status))
                          (< (float-time (time-since (gethash 'start-time import-status))) timeout))
                (sleep-for 0.1))

              ;; Check if the import timed out
              (if (gethash 'done import-status)
                  (progn
                    (message "Successfully imported tools from server %s" server-name)
                    ;; Refresh tool definitions
                    (when (fboundp 'ai-auto-complete-mcp-refresh-tool-definitions-after-import)
                      (ai-auto-complete-mcp-refresh-tool-definitions-after-import server-name))
                    ;; Return the result
                    (setq result (gethash 'result import-status)))
                (progn
                  (message "Timed out waiting for tools to be imported from server %s" server-name)
                  (setq result nil)))

              ;; Clean up the global table entry
              (remhash server-name ai-auto-complete-mcp-import-status-table)

              ;; Return the result
              result)))))))


;; Import all MCP servers as tools synchronously
(defun ai-auto-complete-mcp-import-all-servers-as-tools-sync (&optional skip-server-contact)
  "Import all MCP servers as tools synchronously in the existing tools system.
If SKIP-SERVER-CONTACT is non-nil, use predefined tools instead of contacting the server."
  (interactive)
  ;; Check if we've already imported tools
  (if (and (boundp 'ai-auto-complete-mcp-tools-imported)
           (hash-table-p ai-auto-complete-mcp-tools-imported)
           (> (hash-table-count ai-auto-complete-mcp-tools-imported) 0))
      (message "MCP tools already imported, skipping import")

    (let ((servers (ai-auto-complete-mcp-list-servers))
          (imported 0))
      (dolist (server-name servers)
        (when (ai-auto-complete-mcp-import-server-as-tool-sync-fixed server-name skip-server-contact)
          (setq imported (1+ imported))))
      (message "Synchronously imported tools from %d of %d MCP servers" imported (length servers))

      ;; Final refresh of tool definitions
      (when (fboundp 'ai-auto-complete-mcp-refresh-tool-definitions)
        (ai-auto-complete-mcp-refresh-tool-definitions)))))

;; Helper function to safely truncate a description
(defun ai-auto-complete-mcp-safely-truncate-description (description)
  "Safely truncate DESCRIPTION to a reasonable length (250 chars)."
  (if (and (stringp description)
           (> (length description) 250))
      (condition-case err
          (concat (substring description 0 (min 247 (1- (length description)))) "...")
        (error
         (message "Error truncating description: %s" (error-message-string err))
         (if (stringp description)
             (substring description 0 (min 100 (length description)))
           "Description truncated")))
    description))

;; Initialize MCP tools during application startup - fixed version 2
(defun ai-auto-complete-mcp-initialize-tools ()
  "Initialize MCP tools during application startup with additional fixes."
  (message "Initializing MCP tools...")

  ;; Only proceed if MCP is enabled
  (when ai-auto-complete-mcp-enabled
    ;; Check if we've already imported tools
    (if (and (boundp 'ai-auto-complete-mcp-tools-imported)
             (hash-table-p ai-auto-complete-mcp-tools-imported)
             (> (hash-table-count ai-auto-complete-mcp-tools-imported) 0))
        (message "MCP tools already initialized, skipping import")

      ;; First try to import all MCP servers as tools synchronously
      (let ((servers (ai-auto-complete-mcp-list-servers))
            (imported 0)
            (use-predefined (and (boundp 'ai-auto-complete-mcp-use-predefined-tools)
                                ai-auto-complete-mcp-use-predefined-tools)))
        (dolist (server-name servers)
          (condition-case err
              (when (ai-auto-complete-mcp-import-server-as-tool-sync server-name use-predefined)
                (setq imported (1+ imported)))
            (error
             (message "Error during MCP initialization: %s" (error-message-string err)))))
        
        (message "Imported tools from %d of %d MCP servers (using predefined: %s)"
                 imported (length servers) (if use-predefined "yes" "no"))

        ;; If no servers were imported, use the direct registration approach
        (when (= imported 0)
          (message "No servers imported, using direct registration approach")
          (ai-auto-complete-mcp-register-tools-directly))

        ;; Final refresh of tool definitions
        (when (fboundp 'ai-auto-complete-mcp-refresh-tool-definitions)
          (ai-auto-complete-mcp-refresh-tool-definitions))))))






;; Refresh tool definitions to include MCP tools
(defun ai-auto-complete-mcp-refresh-tool-definitions ()
  "Refresh tool definitions to include MCP tools in the system prompt."
  (interactive)
  (message "Refreshing tool definitions to include MCP tools")

  ;; Count MCP tools
  (let ((mcp-tools 0))
    (maphash (lambda (name _)
               (when (string-match "^mcp:" name)
                 (setq mcp-tools (1+ mcp-tools))))
             ai-auto-complete-tools)

    (message "Found %d MCP tools registered" mcp-tools)

    ;; If tools are enabled, make sure the advice is added
    (when ai-auto-complete-tools-enabled
      (unless (and (fboundp 'advice-member-p)
                   (advice-member-p 'ai-auto-complete-tools-advice-request 'ai-auto-complete-complete))
        (message "Adding tools advice to ai-auto-complete-complete")
        (advice-add 'ai-auto-complete-complete :around #'ai-auto-complete-tools-advice-request)))))

;; Refresh tool definitions after importing tools
(defun ai-auto-complete-mcp-refresh-tool-definitions-after-import (server-name)
  "Refresh tool definitions after importing tools from SERVER-NAME."
  (message "Refreshing tool definitions after importing tools from %s" server-name)

  ;; Count MCP tools
  (let ((mcp-tools 0)
        (server-tools 0))
    (maphash (lambda (name _)
               (when (string-match "^mcp:" name)
                 (setq mcp-tools (1+ mcp-tools))
                 (when (string-match (format "^mcp:%s:" server-name) name)
                   (setq server-tools (1+ server-tools)))))
             ai-auto-complete-tools)

    (message "Found %d MCP tools registered after import (%d from server %s)"
             mcp-tools server-tools server-name)

    ;; If tools are enabled, make sure the advice is added
    (when ai-auto-complete-tools-enabled
      (unless (and (fboundp 'advice-member-p)
                   (advice-member-p 'ai-auto-complete-tools-advice-request 'ai-auto-complete-complete))
        (message "Adding tools advice to ai-auto-complete-complete")
        (advice-add 'ai-auto-complete-complete :around #'ai-auto-complete-tools-advice-request)))))

;; Verify MCP tools registration
(defun ai-auto-complete-mcp-verify-tools-registration ()
  "Verify that MCP tools are properly registered."
  (interactive)
  (let ((buffer-name "*MCP Tools Verification*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert "MCP Tools Verification\n")
        (insert "=====================\n\n")

        ;; Count MCP tools
        (let ((mcp-tools 0)
              (mcp-tool-names '()))
          (maphash (lambda (name tool)
                     (when (string-match "^mcp:" name)
                       (setq mcp-tools (1+ mcp-tools))
                       (push name mcp-tool-names)))
                   ai-auto-complete-tools)

          (insert (format "Found %d MCP tools registered:\n\n" mcp-tools))

          (if (= mcp-tools 0)
              (insert "No MCP tools found. Import them first with M-x ai-auto-complete-mcp-import-server-as-tool\n")
            (dolist (name (sort mcp-tool-names 'string<))
              (let ((tool (gethash name ai-auto-complete-tools)))
                (insert (format "- %s: %s\n" name (plist-get tool :description)))))))

        ;; Add buttons for actions
        (insert "\nActions: ")
        (insert-button "Import All Servers as Tools"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-import-all-servers-as-tools)
                               (ai-auto-complete-mcp-verify-tools-registration))
                      'follow-link t)

        (insert " | ")
        (insert-button "List Available Tools"
                      'action (lambda (_)
                               (ai-auto-complete-tools-list))
                      'follow-link t)

        (insert " | ")
        (insert-button "Refresh"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-verify-tools-registration))
                      'follow-link t)))

    (switch-to-buffer buffer-name)))

(provide 'mcp/mcp-tools-bridge)
;;; mcp-tools-bridge.el ends here
