;; Fixed version of the tool processing function

(defun ai-auto-complete-mcp-process-tools (server-name tools-list import-status)
  "Process TOOLS-LIST from SERVER-NAME and update IMPORT-STATUS.
This function extracts tool information, parameters, and registers each tool."
  (if (null tools-list)
      (progn
        (message "No tools found on MCP server %s" server-name)
        (puthash 'done t import-status))
    
    (message "Found %d tools on MCP server %s" (length tools-list) server-name)
    
    ;; Add debug logging to help diagnose issues
    (when ai-auto-complete-mcp-debug-mode
      (message "[MCP-DEBUG] Tools list: %S" tools-list))
    
    (dolist (tool tools-list)
      ;; Extract tool information based on the format
      (let* ((tool-name (or (plist-get tool :name)
                           (and (plist-member tool :inputSchema)
                                (plist-get (plist-get tool :inputSchema) :title)
                                (string-match "\\`\\(.*\\)Arguments\\'"
                                             (plist-get (plist-get tool :inputSchema) :title))
                                (match-string 1 (plist-get (plist-get tool :inputSchema) :title)))))
             (description (plist-get tool :description))
             (input-schema (plist-get tool :inputSchema))
             (parameters nil))
        
        ;; Handle missing tool name
        (when (not tool-name)
          (let ((random-name (format "unknown-tool-%d" (random 10000))))
            (message "Warning: Tool has no name, using %s" random-name)
            (setq tool-name random-name)))
        
        ;; Define full-tool-name now that we have a valid tool-name
        (let ((full-tool-name (format "mcp:%s:%s" server-name tool-name)))
          
          (message "Processing tool %s from server %s" tool-name server-name)
          
          ;; Extract parameters from inputSchema for Python MCP servers
          (when input-schema
            (let ((properties (plist-get input-schema :properties))
                  (required (plist-get input-schema :required)))
              
              (when ai-auto-complete-mcp-debug-mode
                (message "[MCP-DEBUG] Processing Python MCP server format")
                (message "[MCP-DEBUG] Input schema: %S" input-schema)
                (message "[MCP-DEBUG] Properties: %S" properties)
                (message "[MCP-DEBUG] Required: %S" required))
              
              (when properties
                ;; Convert plist to a list of pairs for processing
                (let ((param-list '()))
                  ;; Process properties plist
                  (let ((prop-name nil)
                        (prop-info nil))
                    (dolist (item properties)
                      (if prop-name
                          (progn
                            ;; We have a property name and now its value
                            (setq prop-info item)
                            
                            ;; Process this property
                            (let* ((prop-title (plist-get prop-info :title))
                                   (prop-type (plist-get prop-info :type))
                                   (is-required (and required (member (symbol-name prop-name) required))))
                              
                              ;; Create inputSchema for this parameter
                              (let ((param-input-schema (list :type (or prop-type "string"))))
                                ;; Add to parameter list with inputSchema
                                (push (list :name (symbol-name prop-name)
                                           :description (or prop-title (symbol-name prop-name))
                                           :required is-required
                                           :inputSchema param-input-schema)
                                      param-list)))
                            
                            ;; Reset for next pair
                            (setq prop-name nil)
                            (setq prop-info nil))
                        ;; This is a property name
                        (setq prop-name item))))
                  
                  ;; Set the parameters
                  (setq parameters param-list)
                  
                  ;; Add debug logging
                  (when ai-auto-complete-mcp-debug-mode
                    (message "[MCP-DEBUG] Extracted parameters from properties: %S" parameters))))))
          
          ;; Use the parameters field if it exists (for Node.js MCP servers)
          (unless parameters
            (setq parameters (plist-get tool :parameters)))
          
          ;; Convert parameters to the format expected by register-tool
          (let ((param-alist '()))
            (dolist (param parameters)
              (let ((param-name (plist-get param :name))
                    (param-description (plist-get param :description))
                    (param-required (plist-get param :required))
                    (param-schema (plist-get param :schema))
                    (param-input-schema (plist-get param :inputSchema)))
                
                ;; Handle schema information
                (when (and param-schema (or (not param-description) (string-empty-p param-description)))
                  (let ((schema-type (cdr (assoc 'type param-schema)))
                        (schema-default (cdr (assoc 'default param-schema)))
                        (schema-desc ""))
                    
                    (when ai-auto-complete-mcp-debug-mode
                      (message "[MCP-DEBUG] Processing parameter format for %s" param-name)
                      (message "[MCP-DEBUG] Schema: %S" param-schema))
                    
                    ;; Build a description from schema information
                    (setq schema-desc (format "Type: %s" (or schema-type "unknown")))
                    (when schema-default
                      (setq schema-desc (format "%s, Default: %s" schema-desc schema-default)))
                    (when param-required
                      (setq schema-desc (format "%s, Required: %s" schema-desc
                                               (if param-required "yes" "no"))))
                    
                    ;; Use schema description if no description provided
                    (setq param-description (format "%s (%s)"
                                                   (or param-description "")
                                                   schema-desc))
                    
                    (when ai-auto-complete-mcp-debug-mode
                      (message "[MCP-DEBUG] Generated parameter description: %s" param-description))))
                
                ;; Also handle inputSchema information
                (when (and param-input-schema (or (not param-description) (string-empty-p param-description)))
                  (let ((schema-type (plist-get param-input-schema :type))
                        (schema-desc ""))
                    
                    (when ai-auto-complete-mcp-debug-mode
                      (message "[MCP-DEBUG] Processing inputSchema for parameter %s" param-name)
                      (message "[MCP-DEBUG] InputSchema: %S" param-input-schema))
                    
                    ;; Build a description from inputSchema information
                    (setq schema-desc (format "Type: %s" (or schema-type "unknown")))
                    (when param-required
                      (setq schema-desc (format "%s, Required: %s" schema-desc
                                               (if param-required "yes" "no"))))
                    
                    ;; Use schema description if no description provided
                    (setq param-description (format "%s (%s)"
                                                   (or param-description "")
                                                   schema-desc))
                    
                    (when ai-auto-complete-mcp-debug-mode
                      (message "[MCP-DEBUG] Generated parameter description from inputSchema: %s" param-description))))
                
                ;; Add parameter to the alist
                (when param-name
                  (push (cons param-name (or param-description "")) param-alist))))
            
            ;; Log the final parameter list for debugging
            (when ai-auto-complete-mcp-debug-mode
              (message "[MCP-DEBUG] Final parameter list for tool %s: %S" tool-name param-alist))
            
            ;; Create a function that calls the MCP tool
            (let ((tool-function (ai-auto-complete-mcp-create-tool-function server-name tool-name)))
              
              (message "Registering MCP tool %s from server %s" full-tool-name server-name)
              
              ;; Register the tool
              (ai-auto-complete-register-tool
               full-tool-name
               (format "MCP tool from server %s: %s" server-name description)
               tool-function
               param-alist)
              
              ;; Save the tool in the server configuration for future use
              (let ((tool-plist (list :name tool-name
                                     :description description
                                     :parameters parameters)))
                
                ;; Add the tool to the server's predefined tools
                (when (fboundp 'ai-auto-complete-mcp-add-server-tool-with-schema)
                  (ai-auto-complete-mcp-add-server-tool-with-schema
                   server-name tool-plist)))
              
              ;; Update the count in the hash table
              (puthash 'tools-imported
                      (1+ (gethash 'tools-imported import-status 0))
                      import-status))))))
    
    ;; Mark as done after processing all tools
    (message "Imported %d tools from MCP server %s"
             (gethash 'tools-imported import-status 0) server-name)
    (puthash 'result t import-status)
    (puthash 'done t import-status)))
