;;; mcp-server-tools.el --- MCP Server Tools Definitions -*- lexical-binding: t; -*-

;; Copyright (C) 2023 AI Auto Complete

;; Author: AI Auto Complete Team
;; Keywords: tools, mcp, ai
;; Version: 0.1.0

;; This file is not part of GNU Emacs.

;;; Commentary:

;; This file contains definitions for MCP server tools.
;; These are used when the actual MCP servers cannot be started.

;;; Code:

(require 'cl-lib)

;; Define MCP server tools
(defvar ai-auto-complete-mcp-server-tools
  '(("code-analysis-server" . (("analyze_code" "Analyze code and provide insights" 
                               (("code" . "The code to analyze")
                                ("language" . "The programming language")))
                              ("find_bugs" "Find bugs in code" 
                               (("code" . "The code to analyze")))
                              ("suggest_refactoring" "Suggest code refactoring" 
                               (("code" . "The code to refactor")
                                ("language" . "The programming language")))
                              ("format_code" "Format code according to style guidelines" 
                               (("code" . "The code to format")
                                ("language" . "The programming language")))))
    ("data-analysis-server" . (("analyze_data" "Analyze data and provide insights" 
                               (("data" . "The data to analyze")
                                ("format" . "The data format")))
                              ("visualize_data" "Visualize data" 
                               (("data" . "The data to visualize")))
                              ("transform_data" "Transform data from one format to another" 
                               (("data" . "The data to transform")
                                ("source_format" . "The source format")
                                ("target_format" . "The target format")))
                              ("generate_report" "Generate a report from data" 
                               (("data" . "The data to analyze")
                                ("format" . "The report format")))))
    ("emacs-tools-server" . (("get_buffer_info" "Get information about an Emacs buffer" 
                             (("buffer_name" . "The name of the buffer")))
                            ("execute_command" "Execute an Emacs command" 
                             (("command" . "The command to execute")))
                            ("get_buffer_list" "Get a list of all Emacs buffers" 
                             ())
                            ("get_major_mode" "Get the major mode of a buffer" 
                             (("buffer_name" . "The name of the buffer")))))
    ("file-tools-server" . (("read_file" "Read a file" 
                            (("path" . "The path to the file")))
                           ("write_file" "Write to a file" 
                            (("path" . "The path to the file")
                             ("content" . "The content to write")))
                           ("append_to_file" "Append content to a file" 
                            (("path" . "The path to the file")
                             ("content" . "The content to append")))
                           ("delete_file" "Delete a file" 
                            (("path" . "The path to the file")))))
    ("system-info-server" . (("get_system_info" "Get system information" 
                             (("type" . "The type of information to get")))
                            ("monitor_resource" "Monitor a system resource" 
                             (("resource" . "The resource to monitor")))
                            ("get_process_list" "Get a list of running processes" 
                             ())
                            ("get_disk_usage" "Get disk usage information" 
                             (("path" . "The path to check")))))
    ("web-tools-server" . (("fetch_url" "Fetch content from a URL" 
                           (("url" . "The URL to fetch")))
                          ("search_web" "Search the web" 
                           (("query" . "The search query")))
                          ("download_file" "Download a file from a URL" 
                           (("url" . "The URL to download from")
                            ("path" . "The path to save the file")))
                          ("post_request" "Send a POST request to a URL" 
                           (("url" . "The URL to send the request to")
                            ("data" . "The data to send")))))
    ("mcp-test-server" . (("hello" "Say hello" 
                          (("name" . "Your name")))
                         ("add" "Add two numbers" 
                          (("a" . "First number")
                           ("b" . "Second number")))
                         ("multiply" "Multiply two numbers" 
                          (("a" . "First number")
                           ("b" . "Second number")))
                         ("echo" "Echo a message" 
                          (("message" . "The message to echo")))))
    ("grpc-server" . (("call_grpc" "Call a gRPC service" 
                      (("service" . "The gRPC service to call")
                       ("method" . "The method to call")
                       ("request" . "The request data")))
                     ("list_services" "List available gRPC services" 
                      ())
                     ("describe_service" "Describe a gRPC service" 
                      (("service" . "The gRPC service to describe")))))
    ("websocket-server" . (("connect" "Connect to a WebSocket server" 
                           (("url" . "The WebSocket URL to connect to")))
                          ("send_message" "Send a message to a WebSocket server" 
                           (("connection_id" . "The connection ID")
                            ("message" . "The message to send")))
                          ("close_connection" "Close a WebSocket connection" 
                           (("connection_id" . "The connection ID"))))))
  "Definitions of MCP server tools.
This is used when the actual MCP servers cannot be started.")

(provide 'mcp/mcp-server-tools)
;;; mcp-server-tools.el ends here
