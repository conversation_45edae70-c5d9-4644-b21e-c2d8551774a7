;;; mcp-server.el --- MCP server interface for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the server interface for MCP integration in the AI Auto Complete package.
;; It handles starting, stopping, and managing MCP servers.

;;; Code:

(require 'mcp/mcp-core)

;; Forward declarations to avoid circular dependencies
(declare-function ai-auto-complete-mcp-stdio-start-server "mcp/transports/mcp-stdio")
(declare-function ai-auto-complete-mcp-stdio-stop-server "mcp/transports/mcp-stdio")
(declare-function ai-auto-complete-mcp-server-bridge-start-server "mcp/mcp-server-bridge")
(declare-function ai-auto-complete-mcp-server-bridge-stop-server "mcp/mcp-server-bridge")
(declare-function ai-auto-complete-mcp-typescript-bridge-start-server "mcp/transports/mcp-typescript-bridge")
(declare-function ai-auto-complete-mcp-typescript-bridge-stop-server "mcp/transports/mcp-typescript-bridge")  
(declare-function ai-auto-complete-mcp-sse-start-server "mcp/transports/mcp-sse")
(declare-function ai-auto-complete-mcp-sse-stop-server "mcp/transports/mcp-sse")
(declare-function ai-auto-complete-mcp-websocket-start-server "mcp/transports/mcp-websocket")
(declare-function ai-auto-complete-mcp-websocket-stop-server "mcp/transports/mcp-websocket")
(declare-function ai-auto-complete-mcp-grpc-start-server "mcp/transports/mcp-grpc")
(declare-function ai-auto-complete-mcp-grpc-stop-server "mcp/transports/mcp-grpc")

;; Start an MCP server
(defun ai-auto-complete-mcp-start-server (name)
  "Start the MCP server with NAME."
  (interactive
   (list (completing-read "Start MCP server: " (ai-auto-complete-mcp-list-servers))))

  ;; Log start attempt
  (when (fboundp 'ai-auto-complete-mcp-debug-info)
    (ai-auto-complete-mcp-debug-info "Attempting to start MCP server %s" name))

  (unless ai-auto-complete-mcp-enabled
    (when (fboundp 'ai-auto-complete-mcp-debug-error)
      (ai-auto-complete-mcp-debug-error "MCP integration is not enabled"))
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-start-server nil))

  (let ((server (ai-auto-complete-mcp-get-server name)))
    (if (not server)
        (progn
          (when (fboundp 'ai-auto-complete-mcp-debug-error)
            (ai-auto-complete-mcp-debug-error "Server %s not found" name))
          (message "MCP server %s not found" name)
          nil)
      (let ((status (plist-get server :status))
            (transport (plist-get server :transport))
            (path (plist-get server :path)))

        ;; Log server details
        (when (fboundp 'ai-auto-complete-mcp-debug-info)
          (ai-auto-complete-mcp-debug-info "Server %s details: status=%s, transport=%s, path=%s"
                                          name status transport path))

        (if (eq status 'running)
            (progn
              (when (fboundp 'ai-auto-complete-mcp-debug-info)
                (ai-auto-complete-mcp-debug-info "Server %s is already running" name))
              (message "MCP server %s is already running" name)
              t)

          ;; Check if this is a server from settings.json (using server-bridge transport)
          (if (eq transport 'server-bridge)
              (progn
                ;; Skip file existence and executable checks for server-bridge servers
                (message "MCP DEBUG: Skipping file existence and executable checks for server-bridge server %s" name)
                t)

            ;; For other transports, check if the path exists
            (if (not (file-exists-p path))
                (progn
                  (when (fboundp 'ai-auto-complete-mcp-debug-error)
                    (ai-auto-complete-mcp-debug-error "Server path %s does not exist" path))
                  (message "MCP server path %s does not exist" path)
                  nil)

              ;; Check if the file is executable
              (if (not (file-executable-p path))
                  (progn
                    (when (fboundp 'ai-auto-complete-mcp-debug-error)
                      (ai-auto-complete-mcp-debug-error "Server file %s is not executable" path))
                    (message "MCP server file %s is not executable" path)

                    ;; Try to make the file executable
                    (when (y-or-n-p (format "Make %s executable? " path))
                      (set-file-modes path (logior (file-modes path) #o111))
                      (when (fboundp 'ai-auto-complete-mcp-debug-info)
                        (ai-auto-complete-mcp-debug-info "Made server file %s executable" path))
                      (ai-auto-complete-mcp-start-server name))
                    nil)
                t))))

              (when ai-auto-complete-mcp-debug-mode
                (message "Starting MCP server %s using %s transport" name transport)
                (message "MCP DEBUG: Server details:")
                (message "  - Path: %s" path)
                (message "  - Transport: %s" transport)
                (message "  - Runner: %s" (plist-get server :runner)))

              ;; Start the server based on transport type
              (let ((result nil))
                ;; First, check if we have a registered transport
                (if (and (fboundp 'ai-auto-complete-mcp-get-transport)
                         (ai-auto-complete-mcp-get-transport transport))
                    (let* ((transport-funcs (ai-auto-complete-mcp-get-transport transport))
                           (start-fn (plist-get transport-funcs :start-fn)))
                      (when (fboundp 'ai-auto-complete-mcp-debug-info)
                        (ai-auto-complete-mcp-debug-info "Using registered %s transport for server %s" transport name))
                      (if (functionp start-fn)
                          (setq result (funcall start-fn name path))
                        (progn
                          (when (fboundp 'ai-auto-complete-mcp-debug-error)
                            (ai-auto-complete-mcp-debug-error "Start function for transport %s is not callable" transport))
                          (message "Start function for transport %s is not callable" transport)
                          (setq result nil))))

                  ;; Fall back to the hardcoded transports
                  (cond
                   ((eq transport 'stdio)
                    (if (fboundp 'ai-auto-complete-mcp-stdio-start-server)
                        (progn
                          (when (fboundp 'ai-auto-complete-mcp-debug-info)
                            (ai-auto-complete-mcp-debug-info "Using stdio transport for server %s" name))
                          (setq result (ai-auto-complete-mcp-stdio-start-server name path)))
                      (when (fboundp 'ai-auto-complete-mcp-debug-error)
                        (ai-auto-complete-mcp-debug-error "stdio transport not available"))
                      (message "Stdio transport not available")
                      (setq result nil)))

                    ((eq transport 'server-bridge)
                    (if (fboundp 'ai-auto-complete-mcp-server-bridge-start-server)
                     (progn
                       (when (fboundp 'ai-auto-complete-mcp-debug-info)
                         (ai-auto-complete-mcp-debug-info "Using server-bridge transport for server %s" name))
                       (setq result (ai-auto-complete-mcp-server-bridge-start-server name path)))
                       (when (fboundp 'ai-auto-complete-mcp-debug-error)
                         (ai-auto-complete-mcp-debug-error "server-bridge transport not available"))
                       (message "server-bridge transport not available")
                       (setq result nil)))

                      ((eq transport 'typescript-bridge)
                      (if (fboundp 'ai-auto-complete-mcp-typescript-bridge-start-server)  
                     (progn
                       (when (fboundp 'ai-auto-complete-mcp-debug-info)
                         (ai-auto-complete-mcp-debug-info "Using typescript-bridge transport for server %s" name))
                       (setq result (ai-auto-complete-mcp-typescript-bridge-start-server name path)))
                       (when (fboundp 'ai-auto-complete-mcp-debug-error)
                         (ai-auto-complete-mcp-debug-error "typescript-bridge transport not available"))
                       (message "typescript-bridge transport not available")
                       (setq result nil)))

                   ((eq transport 'sse)
                    (if (fboundp 'ai-auto-complete-mcp-sse-start-server)
                        (progn
                          (when (fboundp 'ai-auto-complete-mcp-debug-info)
                            (ai-auto-complete-mcp-debug-info "Using SSE transport for server %s" name))
                          (setq result (ai-auto-complete-mcp-sse-start-server name path)))
                      (when (fboundp 'ai-auto-complete-mcp-debug-error)
                        (ai-auto-complete-mcp-debug-error "SSE transport not available"))
                      (message "SSE transport not available")
                      (setq result nil)))

                   ((eq transport 'websocket)
                    (if (fboundp 'ai-auto-complete-mcp-websocket-start-server)
                        (progn
                          (when (fboundp 'ai-auto-complete-mcp-debug-info)
                            (ai-auto-complete-mcp-debug-info "Using WebSocket transport for server %s" name))
                          (setq result (ai-auto-complete-mcp-websocket-start-server name path)))
                      (when (fboundp 'ai-auto-complete-mcp-debug-error)
                        (ai-auto-complete-mcp-debug-error "WebSocket transport not available"))
                      (message "WebSocket transport not available")
                      (setq result nil)))

                   ((eq transport 'grpc)
                    (if (fboundp 'ai-auto-complete-mcp-grpc-start-server)
                        (progn
                          (when (fboundp 'ai-auto-complete-mcp-debug-info)
                            (ai-auto-complete-mcp-debug-info "Using gRPC transport for server %s" name))
                          (setq result (ai-auto-complete-mcp-grpc-start-server name path)))
                      (when (fboundp 'ai-auto-complete-mcp-debug-error)
                        (ai-auto-complete-mcp-debug-error "gRPC transport not available"))
                      (message "gRPC transport not available")
                      (setq result nil)))

                   (t
                    (when (fboundp 'ai-auto-complete-mcp-debug-error)
                      (ai-auto-complete-mcp-debug-error "Unsupported transport: %s" transport))
                    (message "Unsupported transport: %s" transport)
                    (setq result nil))))

                ;; Log the result
                (when (fboundp 'ai-auto-complete-mcp-debug-info)
                  (ai-auto-complete-mcp-debug-info "Start server result: %s" result))

                result)))))

;; Stop an MCP server
(defun ai-auto-complete-mcp-stop-server (name)
  "Stop the MCP server with NAME."
  (interactive
   (list (completing-read "Stop MCP server: "
                          (cl-remove-if-not
                           (lambda (server-name)
                             (eq (ai-auto-complete-mcp-get-server-status server-name) 'running))
                           (ai-auto-complete-mcp-list-servers)))))

  (let ((server (ai-auto-complete-mcp-get-server name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" name)
          nil)
      (let ((status (plist-get server :status))
            (transport (plist-get server :transport)))
        (if (not (eq status 'running))
            (progn
              (message "MCP server %s is not running" name)
              t)
          (when ai-auto-complete-mcp-debug-mode
            (message "Stopping MCP server %s" name))

          ;; Stop the server based on transport type
          ;; First, check if we have a registered transport
          (if (and (fboundp 'ai-auto-complete-mcp-get-transport)
                   (ai-auto-complete-mcp-get-transport transport))
              (let* ((transport-funcs (ai-auto-complete-mcp-get-transport transport))
                     (stop-fn (plist-get transport-funcs :stop-fn)))
                (when (fboundp 'ai-auto-complete-mcp-debug-info)
                  (ai-auto-complete-mcp-debug-info "Using registered %s transport to stop server %s" transport name))
                (if (functionp stop-fn)
                    (funcall stop-fn name)
                  (progn
                    (when (fboundp 'ai-auto-complete-mcp-debug-error)
                      (ai-auto-complete-mcp-debug-error "Stop function for transport %s is not callable" transport))
                    (message "Stop function for transport %s is not callable" transport)
                    nil)))

            ;; Fall back to the hardcoded transports
            (cond
             ((eq transport 'stdio)
              (if (fboundp 'ai-auto-complete-mcp-stdio-stop-server)
                  (ai-auto-complete-mcp-stdio-stop-server name)
                (message "Stdio transport not available")
                nil))

             ((eq transport 'sse)
              (if (fboundp 'ai-auto-complete-mcp-sse-stop-server)
                  (ai-auto-complete-mcp-sse-stop-server name)
                (message "SSE transport not available")
                nil))

             ((eq transport 'websocket)
              (if (fboundp 'ai-auto-complete-mcp-websocket-stop-server)
                  (ai-auto-complete-mcp-websocket-stop-server name)
                (message "WebSocket transport not available")
                nil))

             ((eq transport 'grpc)
              (if (fboundp 'ai-auto-complete-mcp-grpc-stop-server)
                  (ai-auto-complete-mcp-grpc-stop-server name)
                (message "gRPC transport not available")
                nil))

             (t
              (message "Unsupported transport: %s" transport)
              nil))))))))

;; Restart an MCP server
(defun ai-auto-complete-mcp-restart-server (name)
  "Restart the MCP server with NAME."
  (interactive
   (list (completing-read "Restart MCP server: " (ai-auto-complete-mcp-list-servers))))

  (when (ai-auto-complete-mcp-stop-server name)
    (ai-auto-complete-mcp-start-server name)))

;; Check if an MCP server is running
(defun ai-auto-complete-mcp-server-running-p (name)
  "Check if MCP server with NAME is running."
  (eq (ai-auto-complete-mcp-get-server-status name) 'running))

;; Get MCP server capabilities
(defun ai-auto-complete-mcp-get-server-capabilities (name)
  "Get the capabilities of MCP server with NAME."
  (let ((server (ai-auto-complete-mcp-get-server name)))
    (when server
      (plist-get server :capabilities))))

;; Check if an MCP server has a specific capability
(defun ai-auto-complete-mcp-server-has-capability-p (name capability)
  "Check if MCP server with NAME has CAPABILITY."
  (let ((capabilities (ai-auto-complete-mcp-get-server-capabilities name)))
    (and capabilities
         (plist-member capabilities capability)
         (not (null (plist-get capabilities capability))))))

;; Start all registered MCP servers
(defun ai-auto-complete-mcp-start-all-servers ()
  "Start all registered MCP servers."
  (interactive)
  (let ((servers (ai-auto-complete-mcp-list-servers))
        (started 0))
    (dolist (name servers)
      (when (ai-auto-complete-mcp-start-server name)
        (setq started (1+ started))))
    (message "Started %d of %d MCP servers" started (length servers))))

;; Stop all running MCP servers
(defun ai-auto-complete-mcp-stop-all-servers ()
  "Stop all running MCP servers."
  (interactive)
  (let ((servers (cl-remove-if-not
                  (lambda (server-name)
                    (eq (ai-auto-complete-mcp-get-server-status server-name) 'running))
                  (ai-auto-complete-mcp-list-servers)))
        (stopped 0))
    (dolist (name servers)
      (when (ai-auto-complete-mcp-stop-server name)
        (setq stopped (1+ stopped))))
    (message "Stopped %d of %d MCP servers" stopped (length servers))))

;; Create a new MCP server
(defun ai-auto-complete-mcp-create-new-server (name directory &optional server-type)
  "Create a new MCP server with NAME in DIRECTORY.
Optional SERVER-TYPE can be 'python or 'node. Defaults to 'python."
  (interactive
   (list (read-string "Server name: ")
         (read-directory-name "Server directory: " ai-auto-complete-mcp-servers-directory)
         (intern (completing-read "Server type: " '("python" "node") nil t))))

  ;; Create the directory if it doesn't exist
  (unless (file-exists-p directory)
    (make-directory directory t))

  ;; Create the server file based on server type
  (let* ((effective-server-type (or server-type 'python))
         (path nil)
         (description (format "MCP server: %s" name)))
    (cond
     ((eq effective-server-type 'python)
      (setq path (expand-file-name (format "%s.py" name) directory))
      (ai-auto-complete-mcp-create-python-server name path description nil))

     ((eq effective-server-type 'node)
      (setq path (expand-file-name (format "%s.js" name) directory))
      (ai-auto-complete-mcp-create-node-server name path description nil))

     (t
      (error "Unsupported server type: %s" effective-server-type)))

    path))

;; Create a Python MCP server file
(defun ai-auto-complete-mcp-create-python-server (name path description tools)
  "Create a Python MCP server file at PATH with NAME, DESCRIPTION, and TOOLS.
TOOLS should be a list of plists with :name, :description, and :parameters keys."
  (with-temp-file path
    (insert "from mcp.server.fastmcp import FastMCP\n\n")
    (insert (format "mcp = FastMCP(\"%s\")\n\n" name))

    ;; Add tool definitions
    (dolist (tool tools)
      (let ((tool-name (plist-get tool :name))
            (tool-description (plist-get tool :description))
            (tool-parameters (plist-get tool :parameters)))

        (insert "@mcp.tool()\n")
        (insert (format "def %s(" (replace-regexp-in-string "[^a-zA-Z0-9_]" "_" tool-name)))

        ;; Add parameters
        (let ((param-strings '()))
          (dolist (param tool-parameters)
            (let ((param-name (car param))
                  (param-type "str"))  ; Default to string type
              (push (format "%s: %s"
                            (replace-regexp-in-string "[^a-zA-Z0-9_]" "_" param-name)
                            param-type)
                    param-strings)))
          (insert (mapconcat 'identity (nreverse param-strings) ", ")))

        (insert ") -> str:\n")
        (insert (format "    \"\"\"%s\"\"\"\n" tool-description))
        (insert "    # Implement the tool functionality here\n")
        (insert "    return \"Not implemented yet\"\n\n")))

    ;; Add main block
    (insert "if __name__ == \"__main__\":\n")
    (insert "    mcp.run()\n"))

  ;; Make the file executable
  (set-file-modes path (logior (file-modes path) #o111))

  ;; Register the server
  (ai-auto-complete-mcp-register-server name path 'stdio description))

;; Create a Node.js MCP server file
(defun ai-auto-complete-mcp-create-node-server (name path description tools)
  "Create a Node.js MCP server file at PATH with NAME, DESCRIPTION, and TOOLS.
TOOLS should be a list of plists with :name, :description, and :parameters keys."
  (with-temp-file path
    (insert "#!/usr/bin/env node\n")
    (insert "/**\n")
    (insert (format " * %s - Node.js MCP Server\n" name))
    (insert " *\n")
    (insert (format " * %s\n" description))
    (insert " */\n\n")
    (insert "const readline = require('readline');\n\n")
    (insert "/**\n")
    (insert " * MCP Server class\n")
    (insert " */\n")
    (insert "class MCPServer {\n")
    (insert "    constructor(name) {\n")
    (insert "        this.name = name;\n")
    (insert "        this.tools = {};\n")
    (insert "        this.resources = {};\n")
    (insert "        this.prompts = {};\n")
    (insert "    }\n\n")
    (insert "    /**\n")
    (insert "     * Register a tool\n")
    (insert "     */\n")
    (insert "    tool(options = {}) {\n")
    (insert "        return (fn) => {\n")
    (insert "            const name = options.name || fn.name;\n")
    (insert "            if (!name) {\n")
    (insert "                throw new Error('Tool function must have a name or be provided with a name option');\n")
    (insert "            }\n\n")
    (insert "            // Extract parameter information from function\n")
    (insert "            const fnStr = fn.toString();\n")
    (insert "            const paramMatch = fnStr.match(/\\(([^)]*)\\)/);\n")
    (insert "            const params = paramMatch ? paramMatch[1].split(',').map(p => p.trim()).filter(p => p) : [];\n\n")
    (insert "            // Register the tool\n")
    (insert "            this.tools[name] = {\n")
    (insert "                fn,\n")
    (insert "                name,\n")
    (insert "                description: options.description || '',\n")
    (insert "                parameters: params.map(param => ({\n")
    (insert "                    name: param,\n")
    (insert "                    description: '',\n")
    (insert "                    type: 'string'\n")
    (insert "                }))\n")
    (insert "            };\n\n")
    (insert "            return fn;\n")
    (insert "        };\n")
    (insert "    }\n\n")
    (insert "    /**\n")
    (insert "     * Handle a JSON-RPC request\n")
    (insert "     */\n")
    (insert "    async handleRequest(request) {\n")
    (insert "        const { method, params, id } = request;\n\n")
    (insert "        try {\n")
    (insert "            let result;\n\n")
    (insert "            switch (method) {\n")
    (insert "                case 'initialize':\n")
    (insert "                    result = {\n")
    (insert "                        serverName: this.name,\n")
    (insert "                        serverVersion: '1.0.0',\n")
    (insert "                        capabilities: {\n")
    (insert "                            tools: { listChanged: true },\n")
    (insert "                            resources: { listChanged: true, subscribe: true },\n")
    (insert "                            prompts: { listChanged: true }\n")
    (insert "                        }\n")
    (insert "                    };\n")
    (insert "                    break;\n\n")
    (insert "                case 'listTools':\n")
    (insert "                    result = Object.values(this.tools).map(tool => ({\n")
    (insert "                        name: tool.name,\n")
    (insert "                        description: tool.description,\n")
    (insert "                        parameters: tool.parameters\n")
    (insert "                    }));\n")
    (insert "                    break;\n\n")
    (insert "                case 'listResources':\n")
    (insert "                    result = Object.keys(this.resources).map(uri => ({\n")
    (insert "                        uri,\n")
    (insert "                        mimeType: this.resources[uri].mimeType\n")
    (insert "                    }));\n")
    (insert "                    break;\n\n")
    (insert "                case 'listPrompts':\n")
    (insert "                    result = Object.values(this.prompts).map(prompt => ({\n")
    (insert "                        name: prompt.name,\n")
    (insert "                        description: prompt.description,\n")
    (insert "                        parameters: prompt.parameters\n")
    (insert "                    }));\n")
    (insert "                    break;\n\n")
    (insert "                case 'callTool':\n")
    (insert "                    const tool = this.tools[params.name];\n")
    (insert "                    if (!tool) {\n")
    (insert "                        throw new Error(`Tool not found: ${params.name}`);\n")
    (insert "                    }\n\n")
    (insert "                    result = await tool.fn(...Object.values(params.arguments));\n")
    (insert "                    break;\n\n")
    (insert "                case 'readResource':\n")
    (insert "                    const resource = this.resources[params.uri];\n")
    (insert "                    if (!resource) {\n")
    (insert "                        throw new Error(`Resource not found: ${params.uri}`);\n")
    (insert "                    }\n\n")
    (insert "                    result = {\n")
    (insert "                        content: resource.content,\n")
    (insert "                        mimeType: resource.mimeType\n")
    (insert "                    };\n")
    (insert "                    break;\n\n")
    (insert "                default:\n")
    (insert "                    throw new Error(`Unknown method: ${method}`);\n")
    (insert "            }\n\n")
    (insert "            return {\n")
    (insert "                jsonrpc: '2.0',\n")
    (insert "                id,\n")
    (insert "                result\n")
    (insert "            };\n")
    (insert "        } catch (error) {\n")
    (insert "            return {\n")
    (insert "                jsonrpc: '2.0',\n")
    (insert "                id,\n")
    (insert "                error: {\n")
    (insert "                    code: -32000,\n")
    (insert "                    message: error.message\n")
    (insert "                }\n")
    (insert "            };\n")
    (insert "        }\n")
    (insert "    }\n\n")
    (insert "    /**\n")
    (insert "     * Run the server\n")
    (insert "     */\n")
    (insert "    run() {\n")
    (insert "        // Set up readline interface\n")
    (insert "        const rl = readline.createInterface({\n")
    (insert "            input: process.stdin,\n")
    (insert "            output: process.stdout,\n")
    (insert "            terminal: false\n")
    (insert "        });\n\n")
    (insert "        // Process input lines\n")
    (insert "        rl.on('line', async (line) => {\n")
    (insert "            if (line.trim()) {\n")
    (insert "                try {\n")
    (insert "                    const request = JSON.parse(line);\n")
    (insert "                    const response = await this.handleRequest(request);\n")
    (insert "                    console.log(JSON.stringify(response));\n")
    (insert "                } catch (error) {\n")
    (insert "                    console.error(`Error processing request: ${error.message}`);\n")
    (insert "                    console.log(JSON.stringify({\n")
    (insert "                        jsonrpc: '2.0',\n")
    (insert "                        id: null,\n")
    (insert "                        error: {\n")
    (insert "                            code: -32700,\n")
    (insert "                            message: `Parse error: ${error.message}`\n")
    (insert "                        }\n")
    (insert "                    }));\n")
    (insert "                }\n")
    (insert "            }\n")
    (insert "        });\n\n")
    (insert "        // Handle process exit\n")
    (insert "        process.on('SIGINT', () => {\n")
    (insert "            console.error('Received SIGINT, shutting down');\n")
    (insert "            process.exit(0);\n")
    (insert "        });\n\n")
    (insert "        process.on('SIGTERM', () => {\n")
    (insert "            console.error('Received SIGTERM, shutting down');\n")
    (insert "            process.exit(0);\n")
    (insert "        });\n")
    (insert "    }\n")
    (insert "}\n\n")
    (insert "// Create an MCP server\n")
    (insert (format "const mcp = new MCPServer('%s');\n\n" name))

    ;; Add tool definitions
    (dolist (tool tools)
      (let ((tool-name (plist-get tool :name))
            (tool-description (plist-get tool :description))
            (tool-parameters (plist-get tool :parameters)))

        (insert "// Example tool\n")
        (insert (format "mcp.tool()((") )

        ;; Add parameters
        (let ((param-strings '()))
          (dolist (param tool-parameters)
            (let ((param-name (car param)))
              (push (replace-regexp-in-string "[^a-zA-Z0-9_]" "_" param-name)
                    param-strings)))
          (insert (mapconcat 'identity (nreverse param-strings) ", ")))

        (insert ") => {\n")
        (insert "    /**\n")
        (insert (format "     * %s\n" tool-description))

        ;; Add parameter documentation
        (dolist (param tool-parameters)
          (let ((param-name (car param))
                (param-desc (cdr param)))
            (insert (format "     * @param {string} %s - %s\n"
                            (replace-regexp-in-string "[^a-zA-Z0-9_]" "_" param-name)
                            (or param-desc "Parameter description")))))

        (insert "     * @returns {string} Result\n")
        (insert "     */\n")
        (insert "    // Implement the tool functionality here\n")
        (insert "    return \"Not implemented yet\";\n")
        (insert "});\n\n")))

    ;; Add main block
    (insert "// Run the server\n")
    (insert "if (require.main === module) {\n")
    (insert "    console.error('Node.js MCP server is running. Press Ctrl+C to exit.');\n")
    (insert "    mcp.run();\n")
    (insert "}\n\n")
    (insert "// Export the mcp object for testing\n")
    (insert "module.exports = mcp;\n"))

  ;; Make the file executable
  (set-file-modes path (logior (file-modes path) #o111))

  ;; Register the server with the node server type
  (ai-auto-complete-mcp-register-server name path 'stdio description))

(provide 'mcp/mcp-server)
;;; mcp-server.el ends here
