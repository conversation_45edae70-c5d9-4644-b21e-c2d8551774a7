;;; mcp-test-persistence.el --- Test persistence for MCP servers -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides test functions for MCP server persistence.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-persistence)
(require 'mcp/mcp-tools-ui)

;; Test function to create a sample server with tools
(defun ai-auto-complete-mcp-test-create-sample-server ()
  "Create a sample MCP server with predefined tools for testing."
  (interactive)
  
  ;; Create a sample server
  (let ((server-name "test-server-with-tools")
        (server-path "/path/to/test/server.py")
        (description "Test server with predefined tools")
        (tools '((:name "fetch_html"
                  :description "Fetch a website and return the content as HTML"
                  :parameters ((:name "url" 
                               :description "URL of the website to fetch" 
                               :required t)
                              (:name "headers" 
                               :description "Optional headers to include in the request" 
                               :required nil)))
                 (:name "fetch_json"
                  :description "Fetch a JSON file from a URL"
                  :parameters ((:name "url" 
                               :description "URL of the JSON to fetch" 
                               :required t)
                              (:name "headers" 
                               :description "Optional headers to include in the request" 
                               :required nil))))))
    
    ;; Register the server with tools
    (ai-auto-complete-mcp-register-server
     server-name server-path 'stdio description "python" tools)
    
    (message "Created sample server %s with %d tools" 
             server-name (length tools))
    
    ;; Return the server name
    server-name))

;; Test function to save and load servers
(defun ai-auto-complete-mcp-test-save-load-servers ()
  "Test saving and loading MCP server configurations."
  (interactive)
  
  ;; Create a sample server if it doesn't exist
  (unless (ai-auto-complete-mcp-server-exists-p "test-server-with-tools")
    (ai-auto-complete-mcp-test-create-sample-server))
  
  ;; Save all servers
  (message "Saving all MCP server configurations...")
  (ai-auto-complete-mcp-save-servers)
  
  ;; Remove all servers
  (let ((servers (ai-auto-complete-mcp-list-servers)))
    (message "Removing %d servers..." (length servers))
    (dolist (server-name servers)
      (ai-auto-complete-mcp-remove-server server-name)))
  
  ;; Load servers
  (message "Loading saved MCP server configurations...")
  (let ((loaded-count (ai-auto-complete-mcp-load-servers)))
    (message "Loaded %d MCP server configurations" loaded-count)
    
    ;; Display the servers UI
    (ai-auto-complete-mcp-list-servers-ui)))

;; Test function to manage tools for a server
(defun ai-auto-complete-mcp-test-manage-tools ()
  "Test managing tools for an MCP server."
  (interactive)
  
  ;; Create a sample server if it doesn't exist
  (unless (ai-auto-complete-mcp-server-exists-p "test-server-with-tools")
    (ai-auto-complete-mcp-test-create-sample-server))
  
  ;; Open the tools management UI
  (ai-auto-complete-mcp-manage-server-tools "test-server-with-tools"))

(provide 'mcp/mcp-test-persistence)
;;; mcp-test-persistence.el ends here
