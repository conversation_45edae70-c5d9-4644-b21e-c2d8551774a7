{"name": "npx", "description": "execute npm package binaries", "main": "index.js", "bin": "index.js", "man": "./npx.1", "files": ["*.js", "npx.1"], "keywords": ["npm", "npm exec", "shell", "scripts", "npm bin", "cli"], "repository": "https://github.com/npm/npx", "author": {"name": "<PERSON>", "email": "<EMAIL>", "twitter": "maybekatz"}, "license": "ISC", "dependencies": {"npm": "5.1.0", "libnpx": "10.2.2"}, "bundleDependencies": ["npm", "libnpx"], "version": "10.2.2"}