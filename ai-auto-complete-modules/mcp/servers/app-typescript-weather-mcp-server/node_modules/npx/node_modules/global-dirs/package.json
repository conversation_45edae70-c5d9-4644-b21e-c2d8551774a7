{"_from": "global-dirs@^0.1.0", "_id": "global-dirs@0.1.1", "_inBundle": false, "_integrity": "sha1-sxnA3UYH81PzvpzKTHL8FIxJ9EU=", "_location": "/global-dirs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "global-dirs@^0.1.0", "name": "global-dirs", "escapedName": "global-dirs", "rawSpec": "^0.1.0", "saveSpec": null, "fetchSpec": "^0.1.0"}, "_requiredBy": ["/is-installed-globally"], "_resolved": "https://registry.npmjs.org/global-dirs/-/global-dirs-0.1.1.tgz", "_shasum": "b319c0dd4607f353f3be9cca4c72fc148c49f445", "_spec": "global-dirs@^0.1.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/is-installed-globally", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/global-dirs/issues"}, "bundleDependencies": false, "dependencies": {"ini": "^1.3.4"}, "deprecated": false, "description": "Get the directory of globally installed packages and binaries", "devDependencies": {"ava": "*", "execa": "^0.7.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/global-dirs#readme", "keywords": ["global", "prefix", "path", "paths", "npm", "yarn", "node", "modules", "node-modules", "package", "packages", "binary", "binaries", "bin", "directory", "directories", "npmrc", "rc", "config", "root", "resolve"], "license": "MIT", "name": "global-dirs", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/global-dirs.git"}, "scripts": {"test": "xo && ava"}, "version": "0.1.1"}