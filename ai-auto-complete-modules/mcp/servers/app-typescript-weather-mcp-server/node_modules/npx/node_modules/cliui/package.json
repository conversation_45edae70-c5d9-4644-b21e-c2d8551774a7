{"_from": "cliui@^4.0.0", "_id": "cliui@4.1.0", "_inBundle": false, "_integrity": "sha512-4FG+RSG9DL7uEwRUZXZn3SS34DiDPfzP0VOiEwtUWlE+AR2EIg+hSyvrIgUUfhdgR/UkAeW2QHgeP+hWrXs7jQ==", "_location": "/cliui", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cliui@^4.0.0", "name": "cliui", "escapedName": "cliui", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/cliui/-/cliui-4.1.0.tgz", "_shasum": "348422dbe82d800b3022eef4f6ac10bf2e4d1b49", "_spec": "cliui@^4.0.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": ["index.js"], "data-cover-never": ["node_modules", "test"], "output-reporter": "spec"}}, "dependencies": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}, "deprecated": false, "description": "easily create complex multi-column command-line-interfaces", "devDependencies": {"chai": "^3.5.0", "chalk": "^1.1.2", "coveralls": "^2.11.8", "mocha": "^3.0.0", "nyc": "^10.0.0", "standard": "^8.0.0", "standard-version": "^3.0.0"}, "engine": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/yargs/cliui#readme", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "license": "ISC", "main": "index.js", "name": "cliui", "repository": {"type": "git", "url": "git+ssh://**************/yargs/cliui.git"}, "scripts": {"coverage": "nyc --reporter=text-lcov mocha | coveralls", "pretest": "standard", "release": "standard-version", "test": "nyc mocha"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "version": "4.1.0"}