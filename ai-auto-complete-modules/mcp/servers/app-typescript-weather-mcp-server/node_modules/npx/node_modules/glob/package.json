{"_from": "glob@^7.1.3", "_id": "glob@7.1.6", "_inBundle": false, "_integrity": "sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==", "_location": "/glob", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "glob@^7.1.3", "name": "glob", "escapedName": "glob", "rawSpec": "^7.1.3", "saveSpec": null, "fetchSpec": "^7.1.3"}, "_requiredBy": ["/rimraf"], "_resolved": "https://registry.npmjs.org/glob/-/glob-7.1.6.tgz", "_shasum": "141f33b81a7c2492e125594307480c46679278a6", "_spec": "glob@^7.1.3", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/rimraf", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/node-glob/issues"}, "bundleDependencies": false, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "deprecated": false, "description": "a little globber", "devDependencies": {"mkdirp": "0", "rimraf": "^2.2.8", "tap": "^12.0.1", "tick": "0.0.6"}, "engines": {"node": "*"}, "files": ["glob.js", "sync.js", "common.js"], "funding": {"url": "https://github.com/sponsors/isaacs"}, "homepage": "https://github.com/isaacs/node-glob#readme", "license": "ISC", "main": "glob.js", "name": "glob", "repository": {"type": "git", "url": "git://github.com/isaacs/node-glob.git"}, "scripts": {"bench": "bash benchmark.sh", "benchclean": "node benchclean.js", "prepublish": "npm run benchclean", "prof": "bash prof.sh && cat profile.txt", "profclean": "rm -f v8.log profile.txt", "test": "tap test/*.js --cov", "test-regen": "npm run profclean && TEST_REGEN=1 node test/00-setup.js"}, "version": "7.1.6"}