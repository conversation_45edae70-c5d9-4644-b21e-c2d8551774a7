{"_from": "escape-string-regexp@^1.0.5", "_id": "escape-string-regexp@1.0.5", "_inBundle": false, "_integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "_location": "/escape-string-regexp", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "escape-string-regexp@^1.0.5", "name": "escape-string-regexp", "escapedName": "escape-string-regexp", "rawSpec": "^1.0.5", "saveSpec": null, "fetchSpec": "^1.0.5"}, "_requiredBy": ["/chalk"], "_resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "_shasum": "1b61c0562190a8dff6ae3bb2cf0200ca130b86d4", "_spec": "escape-string-regexp@^1.0.5", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/chalk", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Escape RegExp special characters", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.8.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/escape-string-regexp#readme", "keywords": ["escape", "regex", "regexp", "re", "regular", "expression", "string", "str", "special", "characters"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbna.nl"}], "name": "escape-string-regexp", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/escape-string-regexp.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.5"}