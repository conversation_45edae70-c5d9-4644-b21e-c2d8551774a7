{"_from": "deep-extend@^0.6.0", "_id": "deep-extend@0.6.0", "_inBundle": false, "_integrity": "sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==", "_location": "/deep-extend", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "deep-extend@^0.6.0", "name": "deep-extend", "escapedName": "deep-extend", "rawSpec": "^0.6.0", "saveSpec": null, "fetchSpec": "^0.6.0"}, "_requiredBy": ["/rc"], "_resolved": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz", "_shasum": "c4fa7c95404a17a9c3e8ca7e1537312b736330ac", "_spec": "deep-extend@^0.6.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/rc", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}, {"name": "<PERSON>", "url": "https://github.com/maxdeviant"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwakerman"}], "deprecated": false, "description": "Recursive object extending", "devDependencies": {"mocha": "5.2.0", "should": "13.2.1"}, "engines": {"node": ">=4.0.0"}, "files": ["index.js", "lib/"], "homepage": "https://github.com/unclechu/node-deep-extend", "keywords": ["deep-extend", "extend", "deep", "recursive", "xtend", "clone", "merge", "json"], "license": "MIT", "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/unclechu/node-deep-extend/master/LICENSE"}], "main": "lib/deep-extend.js", "name": "deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "scripts": {"test": "mocha"}, "version": "0.6.0"}