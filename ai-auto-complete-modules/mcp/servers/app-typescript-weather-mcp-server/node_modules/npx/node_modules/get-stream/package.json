{"_from": "get-stream@^3.0.0", "_id": "get-stream@3.0.0", "_inBundle": false, "_integrity": "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=", "_location": "/get-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "get-stream@^3.0.0", "name": "get-stream", "escapedName": "get-stream", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/execa", "/got"], "_resolved": "https://registry.npmjs.org/get-stream/-/get-stream-3.0.0.tgz", "_shasum": "8e943d1358dc37555054ecbe2edb05aa174ede14", "_spec": "get-stream@^3.0.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/execa", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Get a stream as a string, buffer, or array", "devDependencies": {"ava": "*", "into-stream": "^3.0.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js", "buffer-stream.js"], "homepage": "https://github.com/sindresorhus/get-stream#readme", "keywords": ["get", "stream", "promise", "concat", "string", "str", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object", "obj"], "license": "MIT", "name": "get-stream", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.0", "xo": {"esnext": true}}