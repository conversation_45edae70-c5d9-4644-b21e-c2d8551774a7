{"_from": "ansi-regex@^3.0.0", "_id": "ansi-regex@3.0.0", "_inBundle": false, "_integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "_location": "/ansi-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ansi-regex@^3.0.0", "name": "ansi-regex", "escapedName": "ansi-regex", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/strip-ansi"], "_resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz", "_shasum": "ed0317c322064f79466c02966bddb605ab37d998", "_spec": "ansi-regex@^3.0.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/strip-ansi", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Regular expression for matching ANSI escape codes", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/chalk/ansi-regex#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "license": "MIT", "name": "ansi-regex", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "scripts": {"test": "xo && ava", "view-supported": "node fixtures/view-codes.js"}, "version": "3.0.0"}