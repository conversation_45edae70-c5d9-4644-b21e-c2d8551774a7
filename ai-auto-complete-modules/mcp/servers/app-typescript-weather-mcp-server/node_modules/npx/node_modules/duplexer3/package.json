{"_from": "duplexer3@^0.1.4", "_id": "duplexer3@0.1.4", "_inBundle": false, "_integrity": "sha1-7gHdHKwO08vH/b6jfcCo8c4ALOI=", "_location": "/duplexer3", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "duplexer3@^0.1.4", "name": "duplexer3", "escapedName": "duplexer3", "rawSpec": "^0.1.4", "saveSpec": null, "fetchSpec": "^0.1.4"}, "_requiredBy": ["/got"], "_resolved": "https://registry.npmjs.org/duplexer3/-/duplexer3-0.1.4.tgz", "_shasum": "ee01dd1cac0ed3cbc7fdbea37dc0a8f1ce002ce2", "_spec": "duplexer3@^0.1.4", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/got", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.fknsrs.biz/"}, "bugs": {"url": "https://github.com/floatdrop/duplexer3/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Like duplexer but using streams3", "devDependencies": {"mocha": "^2.2.5"}, "engine": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/floatdrop/duplexer3#readme", "keywords": ["duplex", "duplexer", "stream", "stream3", "join", "combine"], "license": "BSD-3-<PERSON><PERSON>", "name": "duplexer3", "repository": {"type": "git", "url": "git+https://github.com/floatdrop/duplexer3.git"}, "scripts": {"test": "mocha -R tap"}, "version": "0.1.4"}