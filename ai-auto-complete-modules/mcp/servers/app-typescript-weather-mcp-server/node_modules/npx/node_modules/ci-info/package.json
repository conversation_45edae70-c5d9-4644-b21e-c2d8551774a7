{"_from": "ci-info@^1.5.0", "_id": "ci-info@1.6.0", "_inBundle": false, "_integrity": "sha512-vsGdkwSCDpWmP80ncATX7iea5DWQemg1UgCW5J8tqjU3lYw4FBYuj89J0CTVomA7BEfvSZd84GmHko+MxFQU2A==", "_location": "/ci-info", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ci-info@^1.5.0", "name": "ci-info", "escapedName": "ci-info", "rawSpec": "^1.5.0", "saveSpec": null, "fetchSpec": "^1.5.0"}, "_requiredBy": ["/is-ci"], "_resolved": "https://registry.npmjs.org/ci-info/-/ci-info-1.6.0.tgz", "_shasum": "2ca20dbb9ceb32d4524a683303313f0304b1e497", "_spec": "ci-info@^1.5.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/is-ci", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son"}, "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "bundleDependencies": false, "coordinates": [55.778271, 12.593091], "dependencies": {}, "deprecated": false, "description": "Get details about the current Continuous Integration environment", "devDependencies": {"clear-require": "^1.0.1", "standard": "^12.0.1", "tape": "^4.9.1"}, "homepage": "https://github.com/watson/ci-info", "keywords": ["ci", "continuous", "integration", "test", "detect"], "license": "MIT", "main": "index.js", "name": "ci-info", "repository": {"type": "git", "url": "git+https://github.com/watson/ci-info.git"}, "scripts": {"test": "standard && node test.js"}, "version": "1.6.0"}