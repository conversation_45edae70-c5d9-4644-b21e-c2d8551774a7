{"_from": "dot-prop@^4.1.0", "_id": "dot-prop@4.2.0", "_inBundle": false, "_integrity": "sha512-tUMXrxlExSW6U2EXiiKGSBVdYgtV8qlHL+C10TsW4PURY/ic+eaysnSkwB4kA/mBlCyy/IKDJ+Lc3wbWeaXtuQ==", "_location": "/dot-prop", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dot-prop@^4.1.0", "name": "dot-prop", "escapedName": "dot-prop", "rawSpec": "^4.1.0", "saveSpec": null, "fetchSpec": "^4.1.0"}, "_requiredBy": ["/configstore"], "_resolved": "https://registry.npmjs.org/dot-prop/-/dot-prop-4.2.0.tgz", "_shasum": "1f19e0c2e1aa0e32797c49799f2837ac6af69c57", "_spec": "dot-prop@^4.1.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/configstore", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "bundleDependencies": false, "dependencies": {"is-obj": "^1.0.0"}, "deprecated": false, "description": "Get, set, or delete a property from a nested object using a dot path", "devDependencies": {"ava": "*", "matcha": "^0.7.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/dot-prop#readme", "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "set", "delete", "del", "access", "notation", "dotty"], "license": "MIT", "name": "dot-prop", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "scripts": {"bench": "matcha bench.js", "test": "xo && ava"}, "version": "4.2.0", "xo": {"esnext": true}}