{"_from": "execa@^0.7.0", "_id": "execa@0.7.0", "_inBundle": false, "_integrity": "sha1-lEvs00zEHuMqY6n68nrVpl/Fl3c=", "_location": "/execa", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "execa@^0.7.0", "name": "execa", "escapedName": "execa", "rawSpec": "^0.7.0", "saveSpec": null, "fetchSpec": "^0.7.0"}, "_requiredBy": ["/term-size"], "_resolved": "https://registry.npmjs.org/execa/-/execa-0.7.0.tgz", "_shasum": "944becd34cc41ee32a63a9faf27ad5a65fc59777", "_spec": "execa@^0.7.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/term-size", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "bundleDependencies": false, "dependencies": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "deprecated": false, "description": "A better `child_process`", "devDependencies": {"ava": "*", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "delay": "^2.0.0", "is-running": "^2.0.0", "nyc": "^11.0.2", "tempfile": "^2.0.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js", "lib"], "homepage": "https://github.com/sindresorhus/execa#readme", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "license": "MIT", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}], "name": "execa", "nyc": {"reporter": ["text", "lcov"], "exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/execa.git"}, "scripts": {"test": "xo && nyc ava"}, "version": "0.7.0"}