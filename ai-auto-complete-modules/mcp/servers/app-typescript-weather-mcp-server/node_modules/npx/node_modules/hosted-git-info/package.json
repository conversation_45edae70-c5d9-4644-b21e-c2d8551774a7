{"_from": "hosted-git-info@^2.7.1", "_id": "hosted-git-info@2.8.5", "_inBundle": false, "_integrity": "sha512-kssjab8CvdXfcXMXVcvsXum4Hwdq9XGtRD3TteMEvEbq0LXyiNQr6AprqKqfeaDXze7SxWvRxdpwE6ku7ikLkg==", "_location": "/hosted-git-info", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "hosted-git-info@^2.7.1", "name": "hosted-git-info", "escapedName": "hosted-git-info", "rawSpec": "^2.7.1", "saveSpec": null, "fetchSpec": "^2.7.1"}, "_requiredBy": ["/npm-package-arg"], "_resolved": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.5.tgz", "_shasum": "759cfcf2c4d156ade59b0b2dfabddc42a6b9c70c", "_spec": "hosted-git-info@^2.7.1", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/npm-package-arg", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "bugs": {"url": "https://github.com/npm/hosted-git-info/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Provides metadata and conversions from repository urls for Github, Bitbucket and Gitlab", "devDependencies": {"standard": "^11.0.1", "standard-version": "^4.4.0", "tap": "^12.7.0"}, "files": ["index.js", "git-host.js", "git-host-info.js"], "homepage": "https://github.com/npm/hosted-git-info", "keywords": ["git", "github", "bitbucket", "gitlab"], "license": "ISC", "main": "index.js", "name": "hosted-git-info", "repository": {"type": "git", "url": "git+https://github.com/npm/hosted-git-info.git"}, "scripts": {"postrelease": "npm publish --tag=ancient-legacy-fixes && git push --follow-tags", "prerelease": "npm t", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --100 --no-esm test/*.js", "test:coverage": "tap --coverage-report=html -J --100 --no-esm test/*.js"}, "version": "2.8.5"}