{"_from": "import-lazy@^2.1.0", "_id": "import-lazy@2.1.0", "_inBundle": false, "_integrity": "sha1-BWmOPUXIjo1+nZLLBYTnfwlvPkM=", "_location": "/import-lazy", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "import-lazy@^2.1.0", "name": "import-lazy", "escapedName": "import-lazy", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/update-notifier"], "_resolved": "https://registry.npmjs.org/import-lazy/-/import-lazy-2.1.0.tgz", "_shasum": "05698e3d45c88e8d7e9d92cb0584e77f096f3e43", "_spec": "import-lazy@^2.1.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/update-notifier", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/import-lazy/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Import modules lazily", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/import-lazy#readme", "keywords": ["import", "require", "load", "module", "modules", "lazy", "lazily", "defer", "deferred", "proxy", "proxies"], "license": "MIT", "name": "import-lazy", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/import-lazy.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.0"}