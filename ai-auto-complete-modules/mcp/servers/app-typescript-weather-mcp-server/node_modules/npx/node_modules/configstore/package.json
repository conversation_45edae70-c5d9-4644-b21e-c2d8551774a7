{"_from": "configstore@^3.0.0", "_id": "configstore@3.1.2", "_inBundle": false, "_integrity": "sha512-vtv5HtGjcYUgFrXc6Kx747B83MRRVS5R1VTEQoXvuP+kMI+if6uywV0nDGoiydJRy4yk7h9od5Og0kxx4zUXmw==", "_location": "/configstore", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "configstore@^3.0.0", "name": "configstore", "escapedName": "configstore", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/update-notifier"], "_resolved": "https://registry.npmjs.org/configstore/-/configstore-3.1.2.tgz", "_shasum": "c6f25defaeef26df12dd33414b001fe81a543f8f", "_spec": "configstore@^3.0.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/update-notifier", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/yeoman/configstore/issues"}, "bundleDependencies": false, "dependencies": {"dot-prop": "^4.1.0", "graceful-fs": "^4.1.2", "make-dir": "^1.0.0", "unique-string": "^1.0.0", "write-file-atomic": "^2.0.0", "xdg-basedir": "^3.0.0"}, "deprecated": false, "description": "Easily load and save config without having to think about where and how", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/yeoman/configstore#readme", "keywords": ["config", "store", "storage", "conf", "configuration", "settings", "preferences", "json", "data", "persist", "persistent", "save"], "license": "BSD-2-<PERSON><PERSON>", "name": "configstore", "repository": {"type": "git", "url": "git+https://github.com/yeoman/configstore.git"}, "scripts": {"test": "xo && ava"}, "version": "3.1.2"}