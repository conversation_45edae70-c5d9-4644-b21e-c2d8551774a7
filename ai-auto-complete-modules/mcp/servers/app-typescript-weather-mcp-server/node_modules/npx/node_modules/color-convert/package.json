{"_from": "color-convert@^1.9.0", "_id": "color-convert@1.9.3", "_inBundle": false, "_integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "_location": "/color-convert", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "color-convert@^1.9.0", "name": "color-convert", "escapedName": "color-convert", "rawSpec": "^1.9.0", "saveSpec": null, "fetchSpec": "^1.9.0"}, "_requiredBy": ["/ansi-styles"], "_resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "_shasum": "bb71850690e1f136567de629d2d5471deda4c1e8", "_spec": "color-convert@^1.9.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/ansi-styles", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Qix-/color-convert/issues"}, "bundleDependencies": false, "dependencies": {"color-name": "1.1.3"}, "deprecated": false, "description": "Plain color conversion functions", "devDependencies": {"chalk": "1.1.1", "xo": "0.11.2"}, "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "homepage": "https://github.com/Qix-/color-convert#readme", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "license": "MIT", "name": "color-convert", "repository": {"type": "git", "url": "git+https://github.com/Qix-/color-convert.git"}, "scripts": {"pretest": "xo", "test": "node test/basic.js"}, "version": "1.9.3", "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}}