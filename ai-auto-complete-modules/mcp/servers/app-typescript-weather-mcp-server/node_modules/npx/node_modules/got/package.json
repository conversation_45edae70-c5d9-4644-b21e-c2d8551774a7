{"_from": "got@^6.7.1", "_id": "got@6.7.1", "_inBundle": false, "_integrity": "sha1-JAzQV4WpoY5WHcG0S0HHY+8ejbA=", "_location": "/got", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "got@^6.7.1", "name": "got", "escapedName": "got", "rawSpec": "^6.7.1", "saveSpec": null, "fetchSpec": "^6.7.1"}, "_requiredBy": ["/package-json"], "_resolved": "https://registry.npmjs.org/got/-/got-6.7.1.tgz", "_shasum": "240cd05785a9a18e561dc1b44b41c763ef1e8db0", "_spec": "got@^6.7.1", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/package-json", "ava": {"concurrency": 4}, "browser": {"unzip-response": false}, "bugs": {"url": "https://github.com/sindresorhus/got/issues"}, "bundleDependencies": false, "dependencies": {"create-error-class": "^3.0.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "is-redirect": "^1.0.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "lowercase-keys": "^1.0.0", "safe-buffer": "^5.0.1", "timed-out": "^4.0.0", "unzip-response": "^2.0.1", "url-parse-lax": "^1.0.0"}, "deprecated": false, "description": "Simplified HTTP requests", "devDependencies": {"ava": "^0.17.0", "coveralls": "^2.11.4", "form-data": "^2.1.1", "get-port": "^2.0.0", "into-stream": "^3.0.0", "nyc": "^10.0.0", "pem": "^1.4.4", "pify": "^2.3.0", "tempfile": "^1.1.1", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/got#readme", "keywords": ["http", "https", "get", "got", "url", "uri", "request", "util", "utility", "simple", "curl", "wget", "fetch"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}], "name": "got", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/got.git"}, "scripts": {"coveralls": "nyc report --reporter=text-lcov | coveralls", "test": "xo && nyc ava"}, "version": "6.7.1", "xo": {"esnext": true}}