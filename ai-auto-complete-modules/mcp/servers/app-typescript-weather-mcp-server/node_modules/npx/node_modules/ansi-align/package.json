{"_from": "ansi-align@^2.0.0", "_id": "ansi-align@2.0.0", "_inBundle": false, "_integrity": "sha1-w2rsy6VjuJzrVW82kPCx2eNUf38=", "_location": "/ansi-align", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ansi-align@^2.0.0", "name": "ansi-align", "escapedName": "ansi-align", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/boxen"], "_resolved": "https://registry.npmjs.org/ansi-align/-/ansi-align-2.0.0.tgz", "_shasum": "c36aeccba563b89ceb556f3690f0b1d9e3547f7f", "_spec": "ansi-align@^2.0.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/boxen", "author": {"name": "nexdrew"}, "bugs": {"url": "https://github.com/nexdrew/ansi-align/issues"}, "bundleDependencies": false, "dependencies": {"string-width": "^2.0.0"}, "deprecated": false, "description": "align-text with ANSI support for CLIs", "devDependencies": {"ava": "^0.19.1", "chalk": "^1.1.3", "coveralls": "^2.13.1", "nyc": "^10.3.0", "standard": "^10.0.2", "standard-version": "^4.0.0"}, "files": ["index.js"], "homepage": "https://github.com/nexdrew/ansi-align#readme", "keywords": ["ansi", "align", "cli", "center", "pad"], "license": "ISC", "main": "index.js", "name": "ansi-align", "repository": {"type": "git", "url": "git+https://github.com/nexdrew/ansi-align.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "release": "standard-version", "test": "nyc ava"}, "version": "2.0.0"}