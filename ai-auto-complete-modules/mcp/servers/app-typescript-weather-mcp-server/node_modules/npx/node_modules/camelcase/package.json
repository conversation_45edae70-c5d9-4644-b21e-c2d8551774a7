{"_from": "camelcase@^4.0.0", "_id": "camelcase@4.1.0", "_inBundle": false, "_integrity": "sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=", "_location": "/camelcase", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "camelcase@^4.0.0", "name": "camelcase", "escapedName": "camelcase", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/boxen", "/yargs-parser"], "_resolved": "https://registry.npmjs.org/camelcase/-/camelcase-4.1.0.tgz", "_shasum": "d545635be1e33c542649c69173e5de6acfae34dd", "_spec": "camelcase@^4.0.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/boxen", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/camelcase#readme", "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "license": "MIT", "name": "camelcase", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "scripts": {"test": "xo && ava"}, "version": "4.1.0", "xo": {"esnext": true}}