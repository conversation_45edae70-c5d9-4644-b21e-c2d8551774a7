{"_from": "code-point-at@^1.0.0", "_id": "code-point-at@1.1.0", "_inBundle": false, "_integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=", "_location": "/code-point-at", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "code-point-at@^1.0.0", "name": "code-point-at", "escapedName": "code-point-at", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/wrap-ansi/string-width"], "_resolved": "https://registry.npmjs.org/code-point-at/-/code-point-at-1.1.0.tgz", "_shasum": "0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77", "_spec": "code-point-at@^1.0.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/wrap-ansi/node_modules/string-width", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/code-point-at/issues"}, "bundleDependencies": false, "deprecated": false, "description": "ES2015 `String#codePointAt()` ponyfill", "devDependencies": {"ava": "*", "xo": "^0.16.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/code-point-at#readme", "keywords": ["es2015", "ponyfill", "polyfill", "shim", "string", "str", "code", "point", "at", "codepoint", "unicode"], "license": "MIT", "name": "code-point-at", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/code-point-at.git"}, "scripts": {"test": "xo && ava"}, "version": "1.1.0"}