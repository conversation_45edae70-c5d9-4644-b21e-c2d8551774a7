{"_from": "find-up@^2.1.0", "_id": "find-up@2.1.0", "_inBundle": false, "_integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "_location": "/find-up", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "find-up@^2.1.0", "name": "find-up", "escapedName": "find-up", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/find-up/-/find-up-2.1.0.tgz", "_shasum": "45d1b7e506c717ddd482775a2b77920a3c0c57a7", "_spec": "find-up@^2.1.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/yargs", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "bundleDependencies": false, "dependencies": {"locate-path": "^2.0.0"}, "deprecated": false, "description": "Find a file by walking up parent directories", "devDependencies": {"ava": "*", "tempfile": "^1.1.1", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/find-up#readme", "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "license": "MIT", "name": "find-up", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.0", "xo": {"esnext": true}}