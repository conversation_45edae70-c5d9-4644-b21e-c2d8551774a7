{"_from": "get-caller-file@^1.0.1", "_id": "get-caller-file@1.0.3", "_inBundle": false, "_integrity": "sha512-3t6rVToeoZfYSGd8YoLFR2DJkiQrIiUrGcjvFX2mDw3bn6k2OtwHN0TNCLbBO+w8qTvimhDkv+LSscbJY1vE6w==", "_location": "/get-caller-file", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "get-caller-file@^1.0.1", "name": "get-caller-file", "escapedName": "get-caller-file", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-1.0.3.tgz", "_shasum": "f978fa4c90d1dfe7ff2d6beda2a515e713bdcf4a", "_spec": "get-caller-file@^1.0.1", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/yargs", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "bundleDependencies": false, "deprecated": false, "description": "[![Build Status](https://travis-ci.org/stefanpenner/get-caller-file.svg?branch=master)](https://travis-ci.org/stefanpenner/get-caller-file) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.appveyor.com/project/embercli/get-caller-file/branch/master)", "devDependencies": {"chai": "^4.1.2", "ensure-posix-path": "^1.0.1", "mocha": "^5.2.0"}, "directories": {"test": "tests"}, "files": ["index.js"], "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "license": "ISC", "main": "index.js", "name": "get-caller-file", "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "scripts": {"test": "mocha test", "test:debug": "mocha test"}, "version": "1.0.3"}