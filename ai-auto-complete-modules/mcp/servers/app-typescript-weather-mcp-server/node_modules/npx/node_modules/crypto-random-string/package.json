{"_from": "crypto-random-string@^1.0.0", "_id": "crypto-random-string@1.0.0", "_inBundle": false, "_integrity": "sha1-ojD2T1aDEOFJgAmUB5DsmVRbyn4=", "_location": "/crypto-random-string", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "crypto-random-string@^1.0.0", "name": "crypto-random-string", "escapedName": "crypto-random-string", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/unique-string"], "_resolved": "https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-1.0.0.tgz", "_shasum": "a230f64f568310e1498009940790ec99545bca7e", "_spec": "crypto-random-string@^1.0.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/unique-string", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/crypto-random-string/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Generate a cryptographically strong random string", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/crypto-random-string#readme", "keywords": ["random", "string", "str", "rand", "text", "id", "identifier", "slug", "salt", "crypto", "strong", "secure", "hex"], "license": "MIT", "name": "crypto-random-string", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/crypto-random-string.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.0", "xo": {"esnext": true}}