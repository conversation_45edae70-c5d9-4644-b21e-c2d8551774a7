{"_from": "graceful-fs@^4.1.2", "_id": "graceful-fs@4.2.3", "_inBundle": false, "_integrity": "sha512-a30VEBm4PEdx1dRB7MFK7BejejvCvBronbLjht+sHuGYj8PHs7M/5Z+rt5lw551vZ7yfTCj4Vuyy3mSJytDWRQ==", "_location": "/graceful-fs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "graceful-fs@^4.1.2", "name": "graceful-fs", "escapedName": "graceful-fs", "rawSpec": "^4.1.2", "saveSpec": null, "fetchSpec": "^4.1.2"}, "_requiredBy": ["/configstore", "/write-file-atomic"], "_resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.3.tgz", "_shasum": "4a12ff1b60376ef09862c2093edd908328be8423", "_spec": "graceful-fs@^4.1.2", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/configstore", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "A drop-in replacement for fs, making various improvements.", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "directories": {"test": "test"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js", "clone.js"], "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "main": "graceful-fs.js", "name": "graceful-fs", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "scripts": {"postpublish": "git push origin --follow-tags", "postversion": "npm publish", "preversion": "npm test", "test": "node test.js | tap -"}, "version": "4.2.3"}