{"_from": "create-error-class@^3.0.0", "_id": "create-error-class@3.0.2", "_inBundle": false, "_integrity": "sha1-Br56vvlHo/FKMP1hBnHUAbyot7Y=", "_location": "/create-error-class", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "create-error-class@^3.0.0", "name": "create-error-class", "escapedName": "create-error-class", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/got"], "_resolved": "https://registry.npmjs.org/create-error-class/-/create-error-class-3.0.2.tgz", "_shasum": "06be7abef947a3f14a30fd610671d401bca8b7b6", "_spec": "create-error-class@^3.0.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/got", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "bugs": {"url": "https://github.com/floatdrop/create-error-class/issues"}, "bundleDependencies": false, "dependencies": {"capture-stack-trace": "^1.0.0"}, "deprecated": false, "description": "Create Error classes", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/floatdrop/create-error-class#readme", "keywords": [], "license": "MIT", "name": "create-error-class", "repository": {"type": "git", "url": "git+https://github.com/floatdrop/create-error-class.git"}, "scripts": {"test": "mocha"}, "version": "3.0.2"}