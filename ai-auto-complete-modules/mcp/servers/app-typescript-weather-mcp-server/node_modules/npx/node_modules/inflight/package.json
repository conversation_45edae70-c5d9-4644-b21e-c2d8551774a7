{"_from": "inflight@^1.0.4", "_id": "inflight@1.0.6", "_inBundle": false, "_integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "_location": "/inflight", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "inflight@^1.0.4", "name": "inflight", "escapedName": "inflight", "rawSpec": "^1.0.4", "saveSpec": null, "fetchSpec": "^1.0.4"}, "_requiredBy": ["/glob"], "_resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "_shasum": "49bd6331d7d02d0c09bc910a1075ba8165b56df9", "_spec": "inflight@^1.0.4", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/glob", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "bundleDependencies": false, "dependencies": {"once": "^1.3.0", "wrappy": "1"}, "deprecated": false, "description": "Add callbacks to requests in flight to avoid async duplication", "devDependencies": {"tap": "^7.1.2"}, "files": ["inflight.js"], "homepage": "https://github.com/isaacs/inflight", "license": "ISC", "main": "inflight.js", "name": "inflight", "repository": {"type": "git", "url": "git+https://github.com/npm/inflight.git"}, "scripts": {"test": "tap test.js --100"}, "version": "1.0.6"}