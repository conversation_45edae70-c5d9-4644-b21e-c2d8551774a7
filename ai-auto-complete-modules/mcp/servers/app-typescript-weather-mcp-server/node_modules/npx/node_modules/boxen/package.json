{"_from": "boxen@^1.2.1", "_id": "boxen@1.3.0", "_inBundle": false, "_integrity": "sha512-TNPjfTr432qx7yOjQyaXm3dSR0MH9vXp7eT1BFSl/C51g+EFnOR9hTg1IreahGBmDNCehscshe45f+C1TBZbLw==", "_location": "/boxen", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "boxen@^1.2.1", "name": "boxen", "escapedName": "boxen", "rawSpec": "^1.2.1", "saveSpec": null, "fetchSpec": "^1.2.1"}, "_requiredBy": ["/update-notifier"], "_resolved": "https://registry.npmjs.org/boxen/-/boxen-1.3.0.tgz", "_shasum": "55c6c39a8ba58d9c61ad22cd877532deb665a20b", "_spec": "boxen@^1.2.1", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/update-notifier", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/boxen/issues"}, "bundleDependencies": false, "dependencies": {"ansi-align": "^2.0.0", "camelcase": "^4.0.0", "chalk": "^2.0.1", "cli-boxes": "^1.0.0", "string-width": "^2.0.0", "term-size": "^1.2.0", "widest-line": "^2.0.0"}, "deprecated": false, "description": "Create boxes in the terminal", "devDependencies": {"ava": "*", "nyc": "^11.0.3", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/boxen#readme", "keywords": ["cli", "box", "boxes", "terminal", "term", "console", "ascii", "unicode", "border", "text"], "license": "MIT", "name": "boxen", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/boxen.git"}, "scripts": {"test": "xo && nyc ava"}, "version": "1.3.0"}