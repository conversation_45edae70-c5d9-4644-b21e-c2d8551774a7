{"_from": "end-of-stream@^1.1.0", "_id": "end-of-stream@1.4.4", "_inBundle": false, "_integrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==", "_location": "/end-of-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "end-of-stream@^1.1.0", "name": "end-of-stream", "escapedName": "end-of-stream", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/pump"], "_resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz", "_shasum": "5ae64a5f45057baf3626ec14da0ca5e4b2431eb0", "_spec": "end-of-stream@^1.1.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/pump", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mafintosh/end-of-stream/issues"}, "bundleDependencies": false, "dependencies": {"once": "^1.4.0"}, "deprecated": false, "description": "Call a callback when a readable/writable/duplex stream has completed or failed.", "devDependencies": {"tape": "^4.11.0"}, "files": ["index.js"], "homepage": "https://github.com/mafintosh/end-of-stream", "keywords": ["stream", "streams", "callback", "finish", "close", "end", "wait"], "license": "MIT", "main": "index.js", "name": "end-of-stream", "repository": {"type": "git", "url": "git://github.com/mafintosh/end-of-stream.git"}, "scripts": {"test": "node test.js"}, "version": "1.4.4"}