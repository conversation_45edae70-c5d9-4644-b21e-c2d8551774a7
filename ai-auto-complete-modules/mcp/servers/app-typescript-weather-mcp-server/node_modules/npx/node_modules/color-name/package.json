{"_from": "color-name@1.1.3", "_id": "color-name@1.1.3", "_inBundle": false, "_integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "_location": "/color-name", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "color-name@1.1.3", "name": "color-name", "escapedName": "color-name", "rawSpec": "1.1.3", "saveSpec": null, "fetchSpec": "1.1.3"}, "_requiredBy": ["/color-convert"], "_resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "_shasum": "a7d0558bd89c42f795dd42328f740831ca53bc25", "_spec": "color-name@1.1.3", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/color-convert", "author": {"name": "DY", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/dfcreative/color-name/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A list of color names and its values", "homepage": "https://github.com/dfcreative/color-name", "keywords": ["color-name", "color", "color-keyword", "keyword"], "license": "MIT", "main": "index.js", "name": "color-name", "repository": {"type": "git", "url": "git+ssh://**************/dfcreative/color-name.git"}, "scripts": {"test": "node test.js"}, "version": "1.1.3"}