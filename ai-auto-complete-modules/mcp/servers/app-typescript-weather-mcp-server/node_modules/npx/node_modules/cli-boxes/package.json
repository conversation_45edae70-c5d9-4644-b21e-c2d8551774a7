{"_from": "cli-boxes@^1.0.0", "_id": "cli-boxes@1.0.0", "_inBundle": false, "_integrity": "sha1-T6kXw+WclKAEzWH47lCdplFocUM=", "_location": "/cli-boxes", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cli-boxes@^1.0.0", "name": "cli-boxes", "escapedName": "cli-boxes", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/boxen"], "_resolved": "https://registry.npmjs.org/cli-boxes/-/cli-boxes-1.0.0.tgz", "_shasum": "4fa917c3e59c94a004cd61f8ee509da651687143", "_spec": "cli-boxes@^1.0.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/boxen", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/cli-boxes/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Boxes for use in the terminal", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "boxes.json"], "homepage": "https://github.com/sindresorhus/cli-boxes#readme", "keywords": ["cli", "box", "boxes", "terminal", "term", "console", "ascii", "unicode", "border", "text", "json"], "license": "MIT", "name": "cli-boxes", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/cli-boxes.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.0"}