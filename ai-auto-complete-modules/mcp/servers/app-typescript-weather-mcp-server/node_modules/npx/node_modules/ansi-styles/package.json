{"_from": "ansi-styles@^3.2.1", "_id": "ansi-styles@3.2.1", "_inBundle": false, "_integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "_location": "/ansi-styles", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ansi-styles@^3.2.1", "name": "ansi-styles", "escapedName": "ansi-styles", "rawSpec": "^3.2.1", "saveSpec": null, "fetchSpec": "^3.2.1"}, "_requiredBy": ["/chalk"], "_resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "_shasum": "41fbb20243e50b12be0f04b8dedbf07520ce841d", "_spec": "ansi-styles@^3.2.1", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/chalk", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "ava": {"require": "babel-polyfill"}, "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "bundleDependencies": false, "dependencies": {"color-convert": "^1.9.0"}, "deprecated": false, "description": "ANSI escape codes for styling strings in the terminal", "devDependencies": {"ava": "*", "babel-polyfill": "^6.23.0", "svg-term-cli": "^2.1.1", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/chalk/ansi-styles#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "name": "ansi-styles", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "scripts": {"screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor", "test": "xo && ava"}, "version": "3.2.1"}