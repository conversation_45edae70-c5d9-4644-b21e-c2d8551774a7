{"_from": "cross-spawn@^5.0.1", "_id": "cross-spawn@5.1.0", "_inBundle": false, "_integrity": "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=", "_location": "/cross-spawn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cross-spawn@^5.0.1", "name": "cross-spawn", "escapedName": "cross-spawn", "rawSpec": "^5.0.1", "saveSpec": null, "fetchSpec": "^5.0.1"}, "_requiredBy": ["/execa"], "_resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-5.1.0.tgz", "_shasum": "e8bd0efee58fcff6f8f94510a0a554bbfa235449", "_spec": "cross-spawn@^5.0.1", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/execa", "author": {"name": "IndigoUnited", "email": "<EMAIL>", "url": "http://indigounited.com"}, "bugs": {"url": "https://github.com/IndigoUnited/node-cross-spawn/issues/"}, "bundleDependencies": false, "dependencies": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "deprecated": false, "description": "Cross platform child_process#spawn and child_process#spawnSync", "devDependencies": {"@satazor/eslint-config": "^3.0.0", "eslint": "^3.0.0", "expect.js": "^0.3.0", "glob": "^7.0.0", "mkdirp": "^0.5.1", "mocha": "^3.0.2", "once": "^1.4.0", "rimraf": "^2.5.0"}, "files": ["index.js", "lib"], "homepage": "https://github.com/IndigoUnited/node-cross-spawn#readme", "keywords": ["spawn", "spawnSync", "windows", "cross", "platform", "path", "ext", "path-ext", "path_ext", "shebang", "hashbang", "cmd", "execute"], "license": "MIT", "main": "index.js", "name": "cross-spawn", "repository": {"type": "git", "url": "git://github.com/IndigoUnited/node-cross-spawn.git"}, "scripts": {"lint": "eslint '{*.js,lib/**/*.js,test/**/*.js}'", "test": "node test/prepare && mocha --bail test/test"}, "version": "5.1.0"}