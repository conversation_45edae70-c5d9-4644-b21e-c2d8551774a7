{"_from": "capture-stack-trace@^1.0.0", "_id": "capture-stack-trace@1.0.1", "_inBundle": false, "_integrity": "sha512-mYQLZnx5Qt1JgB1WEiMCf2647plpGeQ2NMR/5L0HNZzGQo4fuSPnK+wjfPnKZV0aiJDgzmWqqkV/g7JD+DW0qw==", "_location": "/capture-stack-trace", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "capture-stack-trace@^1.0.0", "name": "capture-stack-trace", "escapedName": "capture-stack-trace", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/create-error-class"], "_resolved": "https://registry.npmjs.org/capture-stack-trace/-/capture-stack-trace-1.0.1.tgz", "_shasum": "a6c0bbe1f38f3aa0b92238ecb6ff42c344d4135d", "_spec": "capture-stack-trace@^1.0.0", "_where": "/Users/<USER>/dev/npm/npx/bin/node_modules/create-error-class", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "bugs": {"url": "https://github.com/floatdrop/capture-stack-trace/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Error.captureStackTrace ponyfill", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/floatdrop/capture-stack-trace#readme", "keywords": ["Error", "captureStackTrace"], "license": "MIT", "name": "capture-stack-trace", "repository": {"type": "git", "url": "git+https://github.com/floatdrop/capture-stack-trace.git"}, "scripts": {"test": "mocha"}, "version": "1.0.1"}