#!/usr/bin/env node
/**
 * Test client for the TypeScript weather MCP server
 */
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import path from 'path';
import { fileURLToPath } from 'url';
// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
// Path to the weather server
const serverPath = path.resolve(__dirname, '../../dist/typescript-weather-server.js');
async function main() {
    console.log(`Testing weather server at: ${serverPath}`);
    try {
        // Create a client
        const client = new Client({
            name: "weather-test-client",
            version: "1.0.0"
        });
        // Create a stdio transport to the server
        const transport = new StdioClientTransport({
            command: 'node',
            args: [serverPath]
        });
        console.log('Connecting to server...');
        // Connect to the server
        await client.connect(transport);
        console.log('Connected to server successfully!');
        // The client.connect() method already initializes the server
        console.log('Server already initialized during connect()');
        // List tools
        console.log('Listing tools...');
        const tools = await client.listTools();
        console.log('Available tools:', JSON.stringify(tools, null, 2));
        // List resources
        console.log('Listing resources...');
        const resources = await client.listResources();
        console.log('Available resources:', JSON.stringify(resources, null, 2));
        // Call the get_forecast tool (using snake_case)
        console.log('Calling get_forecast tool...');
        const forecastResult = await client.callTool({
            name: 'get_forecast',
            arguments: {
                latitude: 37.7749,
                longitude: -122.4194
            }
        });
        console.log('Weather forecast result:', JSON.stringify(forecastResult, null, 2));
        // Call the get_alerts tool (using snake_case)
        console.log('Calling get_alerts tool...');
        const alertsResult = await client.callTool({
            name: 'get_alerts',
            arguments: {
                state: 'CA'
            }
        });
        console.log('Weather alerts result:', JSON.stringify(alertsResult, null, 2));
        // Read the disclaimer resource
        console.log('Reading disclaimer resource...');
        const disclaimerResult = await client.readResource({ uri: 'weather://disclaimer' });
        console.log('Disclaimer resource:', JSON.stringify(disclaimerResult, null, 2));
        console.log('All tests completed successfully!');
        // Close the connection
        await client.close();
    }
    catch (error) {
        console.error('Error testing weather server:', error);
        process.exit(1);
    }
}
main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map