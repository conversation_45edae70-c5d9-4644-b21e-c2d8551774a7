# MCP Servers

This directory contains MCP (Model Context Protocol) servers implemented using the official TypeScript SDK.

## TypeScript Weather Server

The TypeScript Weather Server provides weather-related tools using the official MCP TypeScript SDK.

### Installation

```bash
# Install dependencies
npm install

# Build the TypeScript code
npm run build
```

### Usage

```bash
# Start the weather server
npm run start:weather
```

Or directly:

```bash
node dist/typescript-weather-server.js
```

### Available Tools

1. **getWeatherForecast** - Get weather forecast for a location
   - Parameters:
     - `latitude` (number): Latitude of the location
     - `longitude` (number): Longitude of the location

2. **getWeatherAlerts** - Get weather alerts for a US state
   - Parameters:
     - `state` (string): Two-letter US state code (e.g. CA, NY)

### Resources

- `weather://disclaimer`: Weather data disclaimer

## Using with Emacs

To use this server with Emacs, you need to register it in your Emacs configuration:

```elisp
(require 'mcp/transports/mcp-typescript-bridge)

(ai-auto-complete-mcp-register-server 
  "typescript-weather" 
  "/path/to/dist/typescript-weather-server.js" 
  'typescript-bridge 
  "TypeScript Weather MCP Server")
```

Then you can use it in your Emacs session:

```elisp
(ai-auto-complete-mcp-call-tool 
  "typescript-weather" 
  "getWeatherForecast" 
  '((latitude . 37.7749) (longitude . -122.4194))
  (lambda (result) (message "Weather: %s" result)))
```
