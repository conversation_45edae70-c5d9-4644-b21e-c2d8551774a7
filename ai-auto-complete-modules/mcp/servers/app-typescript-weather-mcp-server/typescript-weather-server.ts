#!/usr/bin/env node
/**
 * TypeScript Weather MCP Server
 *
 * This MCP server provides weather-related tools using the official MCP TypeScript SDK.
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  InitializeRequestSchema,
  ListResourcesRequestSchema,
  ReadResourceRequestSchema
} from "@modelcontextprotocol/sdk/types.js";
import { z } from "zod";
import https from 'https';

// Helper function to make HTTP requests
function httpGet(url: string): Promise<any> {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(error);
        }
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
}

// Create an MCP server
const server = new Server({
  name: "TypeScript Weather Server",
  version: "1.0.0"
}, {
  capabilities: {
    resources: { listChanged: true },
    tools: { listChanged: true },
    prompts: { listChanged: true }
  }
});

// Define tool schemas using zod
const getWeatherForecastSchema = z.object({
  latitude: z.number().describe("Latitude of the location"),
  longitude: z.number().describe("Longitude of the location")
});

const getWeatherAlertsSchema = z.object({
  state: z.string().length(2).describe("Two-letter US state code (e.g. CA, NY)")
});

// Handle initialize request
server.setRequestHandler(InitializeRequestSchema, async (request) => {
  return {
    serverInfo: {
      name: "TypeScript Weather Server",
      version: "1.0.0"
    },
    protocolVersion: request.params.protocolVersion || "0.6.0",
    capabilities: {
      tools: { listChanged: true },
      resources: { listChanged: true },
      prompts: { listChanged: true }
    }
  };
});

// Handle list tools request
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: "getWeatherForecast",
        description: "Get weather forecast for a location",
        inputSchema: {
          type: "object",
          properties: {
            latitude: {
              type: "number",
              description: "Latitude of the location"
            },
            longitude: {
              type: "number",
              description: "Longitude of the location"
            }
          },
          required: ["latitude", "longitude"]
        }
      },
      {
        name: "getWeatherAlerts",
        description: "Get weather alerts for a US state",
        inputSchema: {
          type: "object",
          properties: {
            state: {
              type: "string",
              description: "Two-letter US state code (e.g. CA, NY)"
            }
          },
          required: ["state"]
        }
      }
    ]
  };
});

// Handle list resources request
server.setRequestHandler(ListResourcesRequestSchema, async () => {
  return {
    resources: [
      {
        uri: "weather://disclaimer",
        name: "Weather Data Disclaimer",
        mimeType: "text/plain"
      }
    ]
  };
});

// Handle read resource request
server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
  if (request.params.uri === "weather://disclaimer") {
    return {
      contents: [
        {
          uri: request.params.uri,
          text: "Weather data is provided by Open-Meteo.com and is for informational purposes only.",
          mimeType: "text/plain"
        }
      ]
    };
  }
  throw new Error(`Resource not found: ${request.params.uri}`);
});

// Handle call tool request
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  // Log the incoming request to help with debugging
  console.error('Received callTool request:', JSON.stringify(request, null, 2));

  if (request.params.name === "get_forecast") { // Changed to snake_case to match tool list
    try {
      // Parse and validate arguments using zod
      const args = getWeatherForecastSchema.parse(request.params.arguments);

      // Use Open-Meteo API to get weather forecast
      const url = `https://api.open-meteo.com/v1/forecast?latitude=${args.latitude}&longitude=${args.longitude}&current=temperature_2m,weather_code,wind_speed_10m&hourly=temperature_2m,weather_code,wind_speed_10m&forecast_days=1`;
      const data = await httpGet(url);

      // Format the response
      const current = data.current;
      const weatherCode = current.weather_code;

      // Map weather code to description
      const weatherDescriptions: Record<number, string> = {
        0: 'Clear sky',
        1: 'Mainly clear',
        2: 'Partly cloudy',
        3: 'Overcast',
        45: 'Fog',
        48: 'Depositing rime fog',
        51: 'Light drizzle',
        53: 'Moderate drizzle',
        55: 'Dense drizzle',
        56: 'Light freezing drizzle',
        57: 'Dense freezing drizzle',
        61: 'Slight rain',
        63: 'Moderate rain',
        65: 'Heavy rain',
        66: 'Light freezing rain',
        67: 'Heavy freezing rain',
        71: 'Slight snow fall',
        73: 'Moderate snow fall',
        75: 'Heavy snow fall',
        77: 'Snow grains',
        80: 'Slight rain showers',
        81: 'Moderate rain showers',
        82: 'Violent rain showers',
        85: 'Slight snow showers',
        86: 'Heavy snow showers',
        95: 'Thunderstorm',
        96: 'Thunderstorm with slight hail',
        99: 'Thunderstorm with heavy hail'
      };

      const weatherDescription = weatherDescriptions[weatherCode] || 'Unknown';

      const response = {
        location: `${data.latitude}, ${data.longitude}`,
        current: {
          temperature: `${current.temperature_2m} ${data.current_units.temperature_2m}`,
          weather: weatherDescription,
          windSpeed: `${current.wind_speed_10m} ${data.current_units.wind_speed_10m}`
        },
        forecast: 'Hourly forecast available for the next 24 hours'
      };

      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(response, null, 2)
          }
        ]
      };
    } catch (error: any) {
      return {
        content: [
          {
            type: "text",
            text: `Error getting weather forecast: ${error.message}`
          }
        ],
        isError: true
      };
    }
  } else if (request.params.name === "get_alerts") { // Changed to snake_case to match tool list
    try {
      // Parse and validate arguments using zod
      const args = getWeatherAlertsSchema.parse(request.params.arguments);

      // This is a mock implementation since we don't have a real API key
      // In a real implementation, you would use a weather API
      const alerts: Record<string, Array<{ type: string, area: string, expires: string }>> = {
        'CA': [
          { type: 'Heat Advisory', area: 'Southern California', expires: '2023-07-15T18:00:00Z' },
          { type: 'Fire Weather Watch', area: 'Northern California', expires: '2023-07-16T00:00:00Z' }
        ],
        'NY': [
          { type: 'Flood Warning', area: 'New York City', expires: '2023-07-14T12:00:00Z' }
        ],
        'FL': [
          { type: 'Hurricane Watch', area: 'Southern Florida', expires: '2023-07-17T00:00:00Z' }
        ],
        'TX': [
          { type: 'Tornado Watch', area: 'Central Texas', expires: '2023-07-14T22:00:00Z' },
          { type: 'Severe Thunderstorm Warning', area: 'Eastern Texas', expires: '2023-07-14T20:00:00Z' }
        ]
      };

      const stateAlerts = alerts[args.state.toUpperCase()] || [];

      if (stateAlerts.length === 0) {
        return {
          content: [
            {
              type: "text",
              text: `No weather alerts found for ${args.state.toUpperCase()}`
            }
          ]
        };
      } else {
        let response = `Weather alerts for ${args.state.toUpperCase()}:\n\n`;

        stateAlerts.forEach((alert, index) => {
          response += `${index + 1}. ${alert.type}\n`;
          response += `   Area: ${alert.area}\n`;
          response += `   Expires: ${new Date(alert.expires).toLocaleString()}\n\n`;
        });

        return {
          content: [
            {
              type: "text",
              text: response
            }
          ]
        };
      }
    } catch (error: any) {
      return {
        content: [
          {
            type: "text",
            text: `Error getting weather alerts: ${error.message}`
          }
        ],
        isError: true
      };
    }
  }

  // Handle unknown tool
  return {
    isError: true,
    content: [
      {
        type: "text",
        text: `Unknown tool: ${request.params.name}`
      }
    ]
  };
});

// Start the server with stdio transport
// In ES modules, we can check if this is the main module by comparing import.meta.url
// against the resolved path of the current file
const isMainModule = import.meta.url.startsWith('file:');

if (isMainModule) {
  // Don't print any messages to stderr as they can interfere with the MCP protocol
  // The MCP bridge expects only JSON-RPC messages on stdout
  async function main() {
    try {
      const transport = new StdioServerTransport();
      await server.connect(transport);
      // No console.error messages here - they can interfere with the protocol
    } catch (error) {
      // Only log errors
      console.error(`Error starting server: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    }
  }

  main();
}

// Export the server for testing
export default server;
