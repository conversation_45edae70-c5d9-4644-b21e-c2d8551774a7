;;; mcp-debug-tools.el --- Debug tools for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides debug tools for MCP integration in the AI Auto Complete package.
;; It helps diagnose issues with MCP servers, transports, and protocol handling.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)

;; Declare buffer-name function to avoid "Symbol's value as variable is void" error
(declare-function buffer-name "subr.el" (&optional buffer))

;; Debug MCP server startup
(defun ai-auto-complete-mcp-debug-server-startup (server-name)
  "Debug the startup of MCP server with SERVER-NAME."
  (interactive
   (list (completing-read "Debug MCP server startup: " (ai-auto-complete-mcp-list-servers))))

  ;; Create a debug buffer
  (let ((buffer-name "*MCP Debug Server*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert "MCP Server Startup Debug\n")
        (insert "=======================\n\n")

        ;; Get server information
        (let ((server (ai-auto-complete-mcp-get-server server-name)))
          (if (not server)
              (insert (format "Server %s not found.\n" server-name))

            ;; Display server information
            (insert (format "Server: %s\n" server-name))
            (insert (format "Path: %s\n" (plist-get server :path)))
            (insert (format "Transport: %s\n" (plist-get server :transport)))
            (insert (format "Status: %s\n\n" (plist-get server :status)))

            ;; Check if the path exists
            (let ((path (plist-get server :path)))
              (if (not (file-exists-p path))
                  (insert (format "ERROR: Server path %s does not exist.\n" path))

                ;; Check if the file is executable
                (if (not (file-executable-p path))
                    (insert (format "ERROR: Server file %s is not executable.\n" path))

                  ;; Try to run the server with --help
                  (insert "Running server with --help...\n\n")

                  (let ((temp-buffer (generate-new-buffer " *temp*")))
                    (call-process path nil temp-buffer nil "--help")
                    (insert (format "Help output:\n%s\n\n"
                                   (with-current-buffer temp-buffer
                                     (buffer-string))))
                    (kill-buffer temp-buffer))

                  ;; Try to run the server with --version
                  (insert "Running server with --version...\n\n")

                  (let ((temp-buffer (generate-new-buffer " *temp*")))
                    (call-process path nil temp-buffer nil "--version")
                    (insert (format "Version output:\n%s\n\n"
                                   (with-current-buffer temp-buffer
                                     (buffer-string))))
                    (kill-buffer temp-buffer))

                  ;; Check Python environment
                  (insert "Checking Python environment...\n\n")

                  (let ((temp-buffer (generate-new-buffer " *temp*")))
                    (call-process "python" nil temp-buffer nil "-c"
                                 "import sys; print(sys.executable); print(sys.version); print(sys.path)")
                    (insert (format "Python environment:\n%s\n\n"
                                   (with-current-buffer temp-buffer
                                     (buffer-string))))
                    (kill-buffer temp-buffer))

                  ;; Check if MCP module is available
                  (insert "Checking MCP module...\n\n")

                  (let ((temp-buffer (generate-new-buffer " *temp*")))
                    (call-process "python" nil temp-buffer nil "-c"
                                 "try: import mcp.server.fastmcp; print('MCP module found'); except ImportError as e: print(f'MCP module not found: {e}')")
                    (insert (format "MCP module check:\n%s\n\n"
                                   (with-current-buffer temp-buffer
                                     (buffer-string))))
                    (kill-buffer temp-buffer))

                  ;; Try to start the server with verbose output
                  (insert "Starting server with verbose output...\n\n")

                  (let ((process-environment (append process-environment '("MCP_DEBUG=1")))
                        (temp-buffer (generate-new-buffer " *temp*")))
                    (let ((process (start-process "mcp-debug" temp-buffer "python" path)))
                      (insert "Server started. Waiting for output...\n\n")
                      (sit-for 2) ;; Wait for 2 seconds
                      (insert (format "Initial output:\n%s\n\n"
                                     (with-current-buffer temp-buffer
                                       (buffer-string))))
                      (delete-process process))
                    (kill-buffer temp-buffer))

                  ;; Add buttons for actions
                  (insert "Actions: ")
                  (insert-button "Make Executable"
                                'action (lambda (_)
                                         (set-file-modes path (logior (file-modes path) #o111))
                                         (ai-auto-complete-mcp-debug-server-startup server-name))
                                'follow-link t)

                  (insert " | ")
                  (insert-button "Start Server"
                                'action (lambda (_)
                                         (ai-auto-complete-mcp-start-server server-name)
                                         (ai-auto-complete-mcp-debug-server-startup server-name))
                                'follow-link t)

                  (insert " | ")
                  (insert-button "Stop Server"
                                'action (lambda (_)
                                         (ai-auto-complete-mcp-stop-server server-name)
                                         (ai-auto-complete-mcp-debug-server-startup server-name))
                                'follow-link t)

                  (insert " | ")
                  (insert-button "Refresh"
                                'action (lambda (_)
                                         (ai-auto-complete-mcp-debug-server-startup server-name))
                                'follow-link t))))))))

    ;; Display the buffer
    (switch-to-buffer buffer-name)))

;; Debug MCP JSON-RPC communication
(defun ai-auto-complete-mcp-debug-jsonrpc (server-name)
  "Debug JSON-RPC communication with SERVER-NAME."
  (interactive
   (list (completing-read "Debug MCP JSON-RPC: " (ai-auto-complete-mcp-list-servers))))

  ;; Create a debug buffer
  (let ((buffer-name "*MCP Debug JSON-RPC*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert "MCP JSON-RPC Debug\n")
        (insert "=================\n\n")

        ;; Get server information
        (let ((server (ai-auto-complete-mcp-get-server server-name)))
          (if (not server)
              (insert (format "Server %s not found.\n" server-name))

            ;; Display server information
            (insert (format "Server: %s\n" server-name))
            (insert (format "Path: %s\n" (plist-get server :path)))
            (insert (format "Transport: %s\n" (plist-get server :transport)))
            (insert (format "Status: %s\n\n" (plist-get server :status)))

            ;; Check if the server is running
            (if (not (eq (plist-get server :status) 'running))
                (progn
                  (insert (format "Server %s is not running.\n" server-name))
                  (insert-button "Start Server"
                                'action (lambda (_)
                                         (ai-auto-complete-mcp-start-server server-name)
                                         (ai-auto-complete-mcp-debug-jsonrpc server-name))
                                'follow-link t))

              ;; Server is running, test JSON-RPC
              (insert "Testing JSON-RPC communication...\n\n")

              ;; Create a test request
              (let ((request-id (ai-auto-complete-mcp-generate-request-id))
                    (request (ai-auto-complete-mcp-create-jsonrpc-request
                             "initialize"
                             (list :serverName server-name
                                   :serverVersion "1.0.0"
                                   :capabilities (list :tools (list :listChanged t)
                                                      :resources (list :listChanged t :subscribe t)
                                                      :prompts (list :listChanged t)))
                             "test-initialize")))

                (insert (format "Test request:\n%s\n\n" request))

                ;; Send the request based on transport
                (let ((transport (plist-get server :transport)))
                  (cond
                   ((eq transport 'stdio)
                    (if (fboundp 'ai-auto-complete-mcp-stdio-send-message)
                        (progn
                          (insert "Sending request using stdio transport...\n\n")
                          (ai-auto-complete-mcp-stdio-send-message
                           server-name request
                           (lambda (response)
                             (with-current-buffer (get-buffer buffer-name)
                               (let ((inhibit-read-only t))
                                 (goto-char (point-max))
                                 (insert (format "Response:\n%s\n\n" (json-encode response))))))))
                      (insert "stdio transport not available.\n")))

                   ((eq transport 'sse)
                    (if (fboundp 'ai-auto-complete-mcp-sse-send-message)
                        (progn
                          (insert "Sending request using SSE transport...\n\n")
                          (ai-auto-complete-mcp-sse-send-message
                           server-name request
                           (lambda (response)
                             (with-current-buffer (get-buffer buffer-name)
                               (let ((inhibit-read-only t))
                                 (goto-char (point-max))
                                 (insert (format "Response:\n%s\n\n" (json-encode response))))))))
                      (insert "SSE transport not available.\n")))

                   ((eq transport 'websocket)
                    (if (fboundp 'ai-auto-complete-mcp-websocket-send-message)
                        (progn
                          (insert "Sending request using WebSocket transport...\n\n")
                          (ai-auto-complete-mcp-websocket-send-message
                           server-name request
                           (lambda (response)
                             (with-current-buffer (get-buffer buffer-name)
                               (let ((inhibit-read-only t))
                                 (goto-char (point-max))
                                 (insert (format "Response:\n%s\n\n" (json-encode response))))))))
                      (insert "WebSocket transport not available.\n")))

                   ((eq transport 'grpc)
                    (if (fboundp 'ai-auto-complete-mcp-grpc-send-message)
                        (progn
                          (insert "Sending request using gRPC transport...\n\n")
                          (ai-auto-complete-mcp-grpc-send-message
                           server-name request
                           (lambda (response)
                             (with-current-buffer (get-buffer buffer-name)
                               (let ((inhibit-read-only t))
                                 (goto-char (point-max))
                                 (insert (format "Response:\n%s\n\n" (json-encode response))))))))
                      (insert "gRPC transport not available.\n")))

                   (t
                    (insert (format "Unsupported transport: %s\n" transport))))))

              ;; Add buttons for actions
              (insert "Actions: ")
              (insert-button "Stop Server"
                            'action (lambda (_)
                                     (ai-auto-complete-mcp-stop-server server-name)
                                     (ai-auto-complete-mcp-debug-jsonrpc server-name))
                            'follow-link t)

              (insert " | ")
              (insert-button "Refresh"
                            'action (lambda (_)
                                     (ai-auto-complete-mcp-debug-jsonrpc server-name))
                            'follow-link t)))))))

    ;; Display the buffer
    (switch-to-buffer buffer-name))

;; Debug MCP tool import
(defun ai-auto-complete-mcp-debug-tool-import (server-name)
  "Debug the tool import process for SERVER-NAME."
  (interactive
   (list (completing-read "Debug MCP tool import: " (ai-auto-complete-mcp-list-servers))))

  (let ((buffer-name "*MCP Debug Tool Import*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert "MCP Tool Import Debug\n")
        (insert "===================\n\n")

        ;; Get server information
        (let ((server (ai-auto-complete-mcp-get-server server-name)))
          (if (not server)
              (insert (format "Server %s not found.\n" server-name))

            ;; Display server information
            (insert (format "Server: %s\n" server-name))
            (insert (format "Path: %s\n" (plist-get server :path)))
            (insert (format "Transport: %s\n" (plist-get server :transport)))
            (insert (format "Status: %s\n\n" (plist-get server :status)))

            ;; Check if the server is running
            (if (not (eq (plist-get server :status) 'running))
                (progn
                  (insert (format "Server %s is not running.\n" server-name))
                  (insert-button "Start Server"
                                'action (lambda (_)
                                         (ai-auto-complete-mcp-start-server server-name)
                                         (ai-auto-complete-mcp-debug-tool-import server-name))
                                'follow-link t))

              ;; Server is running, test tool import
              (insert "Testing tool import...\n\n")

              ;; List tools
              (insert "Listing tools...\n\n")
              (ai-auto-complete-mcp-list-tools
               server-name
               (lambda (tools)
                 (with-current-buffer (get-buffer buffer-name)
                   (let ((inhibit-read-only t))
                     (goto-char (point-max))
                     (if (stringp tools)
                         (insert (format "Error listing tools: %s\n\n" tools))
                       (insert (format "Found %d tools:\n" (length tools)))
                       (dolist (tool tools)
                         (insert (format "  %s: %s\n"
                                        (plist-get tool :name)
                                        (plist-get tool :description))))
                       (insert "\n")

                       ;; Check if tools are already imported
                       (insert "Checking if tools are already imported...\n\n")
                       (let ((imported-count 0))
                         (dolist (tool tools)
                           (let ((tool-name (plist-get tool :name))
                                 (full-tool-name (format "mcp:%s:%s" server-name tool-name)))
                             (if (gethash full-tool-name ai-auto-complete-tools)
                                 (progn
                                   (insert (format "  %s: Already imported\n" full-tool-name))
                                   (setq imported-count (1+ imported-count)))
                               (insert (format "  %s: Not imported\n" full-tool-name)))))

                         (insert (format "\n%d of %d tools are already imported.\n\n"
                                        imported-count (length tools)))

                         ;; Add buttons for actions
                         (insert "Actions: ")
                         (insert-button "Import Tools"
                                       'action (lambda (_)
                                                (ai-auto-complete-mcp-import-server-as-tool server-name)
                                                (ai-auto-complete-mcp-debug-tool-import server-name))
                                       'follow-link t)

                         (insert " | ")
                         (insert-button "List Available Tools"
                                       'action (lambda (_)
                                                (ai-auto-complete-tools-list))
                                       'follow-link t)

                         (insert " | ")
                         (insert-button "Refresh"
                                       'action (lambda (_)
                                                (ai-auto-complete-mcp-debug-tool-import server-name))
                                       'follow-link t))))))))))))))

    (switch-to-buffer buffer-name)))

;; Debug MCP tool integration
(defun ai-auto-complete-mcp-debug-tool-integration (server-name)
  "Debug tool integration for SERVER-NAME."
  (interactive
   (list (completing-read "Debug MCP tool integration: " (ai-auto-complete-mcp-list-servers))))

  ;; Create a debug buffer
  (let ((buffer-name "*MCP Debug Tool Integration*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert "MCP Tool Integration Debug\n")
        (insert "========================\n\n")

        ;; Get server information
        (let ((server (ai-auto-complete-mcp-get-server server-name)))
          (if (not server)
              (insert (format "Server %s not found.\n" server-name))

            ;; Display server information
            (insert (format "Server: %s\n" server-name))
            (insert (format "Path: %s\n" (plist-get server :path)))
            (insert (format "Transport: %s\n" (plist-get server :transport)))
            (insert (format "Status: %s\n\n" (plist-get server :status)))

            ;; Check if the server is running
            (if (not (eq (plist-get server :status) 'running))
                (progn
                  (insert (format "Server %s is not running.\n" server-name))
                  (insert-button "Start Server"
                                'action (lambda (_)
                                         (ai-auto-complete-mcp-start-server server-name)
                                         (ai-auto-complete-mcp-debug-tool-integration server-name))
                                'follow-link t))

              ;; Server is running, test tool integration
              (insert "Testing tool integration...\n\n")

              ;; List tools
              (insert "Listing tools...\n\n")
              (ai-auto-complete-mcp-list-tools
               server-name
               (lambda (tools)
                 (with-current-buffer (get-buffer buffer-name)
                   (let ((inhibit-read-only t))
                     (goto-char (point-max))
                     (if (stringp tools)
                         (insert (format "Error listing tools: %s\n\n" tools))
                       (insert (format "Found %d tools:\n" (length tools)))
                       (dolist (tool tools)
                         (insert (format "  %s: %s\n"
                                        (plist-get tool :name)
                                        (plist-get tool :description))))
                       (insert "\n")

                       ;; Import tools
                       (insert "Importing tools...\n\n")
                       (if (fboundp 'ai-auto-complete-mcp-import-server-as-tool)
                           (progn
                             (ai-auto-complete-mcp-import-server-as-tool server-name)
                             (insert "Tools imported.\n\n")

                             ;; List imported tools
                             (insert "Listing imported tools:\n")
                             (maphash (lambda (name tool)
                                        (when (string-prefix-p (format "mcp:%s:" server-name) name)
                                          (insert (format "  %s: %s\n" name (plist-get tool :description)))))
                                      ai-auto-complete-tools))
                         (insert "Tool import function not available.\n\n")))))))

              ;; Add buttons for actions
              (insert "\nActions: ")
              (insert-button "Stop Server"
                            'action (lambda (_)
                                     (ai-auto-complete-mcp-stop-server server-name)
                                     (ai-auto-complete-mcp-debug-tool-integration server-name))
                            'follow-link t)

              (insert " | ")
              (insert-button "Import Tools"
                            'action (lambda (_)
                                     (when (fboundp 'ai-auto-complete-mcp-import-server-as-tool)
                                       (ai-auto-complete-mcp-import-server-as-tool server-name))
                                     (ai-auto-complete-mcp-debug-tool-integration server-name))
                            'follow-link t)

              (insert " | ")
              (insert-button "Refresh"
                            'action (lambda (_)
                                     (ai-auto-complete-mcp-debug-tool-integration server-name))
                            'follow-link t)))))))

    ;; Display the buffer
    (switch-to-buffer buffer-name)))

;; Debug MCP tool execution
(defun ai-auto-complete-mcp-debug-tool-execution (server-name tool-name params)
  "Debug the execution of MCP tool TOOL-NAME on SERVER-NAME with PARAMS."
  (interactive
   (let* ((server-name (completing-read "Select MCP server: " (ai-auto-complete-mcp-list-servers)))
          (tools-promise (make-hash-table :test 'eq))
          (tool-name nil)
          (params-json nil))

     ;; Set up the promise
     (puthash 'status 'pending tools-promise)
     (puthash 'value nil tools-promise)

     ;; List tools
     (ai-auto-complete-mcp-list-tools
      server-name
      (lambda (tools)
        (puthash 'status 'fulfilled tools-promise)
        (puthash 'value tools tools-promise)))

     ;; Wait for the result (with timeout)
     (let ((timeout 5)
           (start-time (current-time)))
       (while (and (eq (gethash 'status tools-promise) 'pending)
                   (< (float-time (time-since start-time)) timeout))
         (sleep-for 0.1))

       ;; Select a tool
       (let ((tools (gethash 'value tools-promise)))
         (if (or (not tools) (stringp tools))
             (error "Error listing tools for server %s" server-name)
           (let ((tool-names (mapcar (lambda (tool) (plist-get tool :name)) tools)))
             (setq tool-name (completing-read "Select tool: " tool-names))
             (setq params-json (read-string "Parameters (JSON object): " "{}"))
             (list server-name tool-name (json-read-from-string params-json))))))))

  (let ((buffer-name "*MCP Tool Debug*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer

        ;; Add header
        (insert "MCP Tool Execution Debug\n")
        (insert "=======================\n\n")

        ;; Display tool information
        (insert (format "Server: %s\n" server-name))
        (insert (format "Tool: %s\n" tool-name))
        (insert (format "Parameters: %S\n\n" params))

        ;; Execute the tool directly
        (insert "Executing tool directly...\n\n")
        (let ((done nil)
              (result nil))
          (ai-auto-complete-mcp-call-tool
           server-name
           tool-name
           params
           (lambda (response)
             (setq result response)
             (setq done t)))

          ;; Wait for the result with timeout
          (let ((timeout 10)
                (start-time (current-time)))
            (while (and (not done)
                        (< (float-time (time-since start-time)) timeout))
              (sleep-for 0.1))

            (if done
                (insert (format "Direct execution result:\n%s\n\n" result))
              (insert "Error: Timeout waiting for direct execution\n\n"))))

        ;; Execute the tool through the standard tool system
        (insert "Executing tool through standard tool system...\n\n")
        (let ((full-tool-name (format "mcp:%s:%s" server-name tool-name)))
          (if (not (gethash full-tool-name ai-auto-complete-tools))
              (insert (format "Tool %s not found in standard tool system\n" full-tool-name))
            (let ((result (ai-auto-complete-execute-tool full-tool-name params)))
              (insert (format "Standard tool execution result:\n%s\n\n" result)))))

        ;; Add buttons for actions
        (insert "Actions: ")
        (insert-button "Import Tool"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-import-server-as-tool server-name)
                               (ai-auto-complete-mcp-debug-tool-execution server-name tool-name params))
                      'follow-link t)

        (insert " | ")
        (insert-button "List Available Tools"
                      'action (lambda (_)
                               (ai-auto-complete-tools-list))
                      'follow-link t)

        (insert " | ")
        (insert-button "Refresh"
                      'action (lambda (_)
                               (ai-auto-complete-mcp-debug-tool-execution server-name tool-name params))
                      'follow-link t)))

    (switch-to-buffer buffer-name)))

;; Add menu items for debugging
(defun ai-auto-complete-mcp-add-debug-menu-items ()
  "Add MCP debug menu items to the MCP menu."
  (when (and (boundp 'ai-auto-complete-menu) ai-auto-complete-menu)
    ;; Add directly to the main menu under MCP
    (easy-menu-add-item ai-auto-complete-menu '("MCP" "Debugging & Troubleshooting")
                       ["Debug Server Startup" ai-auto-complete-mcp-debug-server-startup t])
    (easy-menu-add-item ai-auto-complete-menu '("MCP" "Debugging & Troubleshooting")
                       ["Debug JSON-RPC" ai-auto-complete-mcp-debug-jsonrpc t])
    (easy-menu-add-item ai-auto-complete-menu '("MCP" "Debugging & Troubleshooting")
                       ["Debug Tool Import" ai-auto-complete-mcp-debug-tool-import t])
    (easy-menu-add-item ai-auto-complete-menu '("MCP" "Debugging & Troubleshooting")
                       ["Debug Tool Execution" ai-auto-complete-mcp-debug-tool-execution t])
    (easy-menu-add-item ai-auto-complete-menu '("MCP" "Debugging & Troubleshooting")
                       ["Debug Tool Integration" ai-auto-complete-mcp-debug-tool-integration t])))

;; Initialize debug tools
(defun ai-auto-complete-mcp-debug-tools-initialize ()
  "Initialize MCP debug tools."
  (message "Initializing MCP debug tools")

  ;; Only add menu items if the function exists
  (when (fboundp 'ai-auto-complete-mcp-add-debug-menu-items)
    (ai-auto-complete-mcp-add-debug-menu-items)))

;; Defer initialization to after Emacs startup
(add-hook 'after-init-hook 'ai-auto-complete-mcp-debug-tools-initialize)

(provide 'mcp/mcp-debug-tools)
;;; mcp-debug-tools.el ends here
