{"mcpServers": {"github.com/modelcontextprotocol/servers/tree/main/src/brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAYZHT8BTTufnAI2AZW46b2BNAmaUr"}, "disabled": false, "autoApprove": []}, "ElevenLabs": {"command": "uvx", "args": ["elevenlabs-mcp"], "env": {"ELEVENLABS_API_KEY": "***************************************************"}, "disabled": false, "autoApprove": []}}}