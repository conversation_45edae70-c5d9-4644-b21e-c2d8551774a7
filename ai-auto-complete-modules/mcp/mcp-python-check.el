;;; mcp-python-check.el --- Python availability check for MCP -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides functions to check Python availability for MCP servers.

;;; Code:

(require 'mcp/mcp-core)

(defun ai-auto-complete-mcp-check-python ()
  "Check if Python is available and properly configured for MCP.
Returns a plist with status information."
  (interactive)
  (let ((result (list :status nil :python-cmd nil :error nil)))
    ;; Try to find a working Python executable
    (let ((python-cmd (or (executable-find ai-auto-complete-mcp-python-command)
                         (executable-find "python3")
                         (executable-find "python"))))
      (if (not python-cmd)
          (progn
            (setq result (plist-put result :status 'error))
            (setq result (plist-put result :error "No Python executable found"))
            (message "MCP: No Python executable found"))
        
        ;; Check if Python can be executed
        (condition-case err
            (let ((output (shell-command-to-string (format "%s --version" python-cmd))))
              (setq result (plist-put result :status 'success))
              (setq result (plist-put result :python-cmd python-cmd))
              (setq result (plist-put result :version output))
              (message "MCP: Found Python: %s - %s" python-cmd output))
          (error
           (setq result (plist-put result :status 'error))
           (setq result (plist-put result :error (format "Error executing Python: %s" (error-message-string err))))
           (message "MCP: Error executing Python: %s" (error-message-string err)))))
      
      ;; Return the result
      result)))

(defun ai-auto-complete-mcp-check-python-mcp ()
  "Check if Python MCP package is available.
Returns a plist with status information."
  (interactive)
  (let ((result (list :status nil :mcp-available nil :error nil)))
    ;; First check if Python is available
    (let ((python-check (ai-auto-complete-mcp-check-python)))
      (if (not (eq (plist-get python-check :status) 'success))
          (progn
            (setq result (plist-put result :status 'error))
            (setq result (plist-put result :error (plist-get python-check :error))))
        
        ;; Check if MCP package is available
        (let ((python-cmd (plist-get python-check :python-cmd)))
          (condition-case err
              (let ((output (shell-command-to-string 
                            (format "%s -c \"try: import mcp; print('MCP package available'); except ImportError as e: print('MCP package not available: ' + str(e))\"" 
                                   python-cmd))))
                (if (string-match-p "MCP package available" output)
                    (progn
                      (setq result (plist-put result :status 'success))
                      (setq result (plist-put result :mcp-available t))
                      (message "MCP: MCP package is available"))
                  (setq result (plist-put result :status 'warning))
                  (setq result (plist-put result :mcp-available nil))
                  (setq result (plist-put result :error output))
                  (message "MCP: MCP package is not available: %s" output)))
            (error
             (setq result (plist-put result :status 'error))
             (setq result (plist-put result :error (format "Error checking MCP package: %s" (error-message-string err))))
             (message "MCP: Error checking MCP package: %s" (error-message-string err))))))
      
      ;; Return the result
      result)))

(defun ai-auto-complete-mcp-display-python-info ()
  "Display information about Python and MCP package availability."
  (interactive)
  (let ((python-check (ai-auto-complete-mcp-check-python))
        (mcp-check (ai-auto-complete-mcp-check-python-mcp)))
    
    ;; Create a buffer to display the information
    (let ((buffer-name "*MCP Python Info*"))
      (with-current-buffer (get-buffer-create buffer-name)
        (let ((inhibit-read-only t))
          (erase-buffer)
          (special-mode) ;; Use special-mode for the buffer
          
          ;; Add header
          (insert "MCP Python Information\n")
          (insert "=====================\n\n")
          
          ;; Python information
          (insert "Python Status: ")
          (insert (format "%s\n" (plist-get python-check :status)))
          
          (if (eq (plist-get python-check :status) 'success)
              (progn
                (insert "Python Command: ")
                (insert (format "%s\n" (plist-get python-check :python-cmd)))
                (insert "Python Version: ")
                (insert (format "%s\n" (plist-get python-check :version))))
            (insert "Python Error: ")
            (insert (format "%s\n" (plist-get python-check :error))))
          
          (insert "\n")
          
          ;; MCP package information
          (insert "MCP Package Status: ")
          (insert (format "%s\n" (plist-get mcp-check :status)))
          
          (if (eq (plist-get mcp-check :status) 'success)
              (insert "MCP Package Available: Yes\n")
            (insert "MCP Package Available: No\n")
            (insert "MCP Package Error: ")
            (insert (format "%s\n" (plist-get mcp-check :error))))
          
          ;; Add buttons for actions
          (insert "\nActions: ")
          (insert-button "Refresh"
                        'action (lambda (_)
                                 (ai-auto-complete-mcp-display-python-info))
                        'follow-link t)
          (insert " | ")
          (insert-button "Install MCP Package"
                        'action (lambda (_)
                                 (ai-auto-complete-mcp-install-mcp-package))
                        'follow-link t)
          (insert " | ")
          (insert-button "Set Python Path"
                        'action (lambda (_)
                                 (call-interactively 'ai-auto-complete-mcp-set-python-path))
                        'follow-link t)))
      
      ;; Display the buffer
      (switch-to-buffer buffer-name))))

(defun ai-auto-complete-mcp-set-python-path (path)
  "Set the Python path for MCP to PATH."
  (interactive "fPython executable path: ")
  (when (file-exists-p path)
    (setq ai-auto-complete-mcp-python-command path)
    (message "MCP: Python path set to %s" path)
    (ai-auto-complete-mcp-display-python-info)))

(defun ai-auto-complete-mcp-install-mcp-package ()
  "Try to install the MCP package using pip."
  (interactive)
  (let ((python-check (ai-auto-complete-mcp-check-python)))
    (if (not (eq (plist-get python-check :status) 'success))
        (message "MCP: Python is not available. Cannot install MCP package.")
      
      (let ((python-cmd (plist-get python-check :python-cmd)))
        (message "MCP: Attempting to install MCP package using %s..." python-cmd)
        (let ((buffer-name "*MCP Package Install*"))
          (with-current-buffer (get-buffer-create buffer-name)
            (let ((inhibit-read-only t))
              (erase-buffer)
              (insert "Installing MCP package...\n\n")
              
              ;; Try to install the package
              (let ((exit-code (call-process python-cmd nil buffer-name t "-m" "pip" "install" "mcp")))
                (if (= exit-code 0)
                    (insert "\nMCP package installed successfully.\n")
                  (insert "\nFailed to install MCP package.\n"))
                
                ;; Check if the package is now available
                (let ((mcp-check (ai-auto-complete-mcp-check-python-mcp)))
                  (insert (format "\nMCP Package Status: %s\n" (plist-get mcp-check :status)))
                  (if (eq (plist-get mcp-check :status) 'success)
                      (insert "MCP Package Available: Yes\n")
                    (insert "MCP Package Available: No\n")
                    (insert "MCP Package Error: ")
                    (insert (format "%s\n" (plist-get mcp-check :error))))))))
          
          ;; Display the buffer
          (switch-to-buffer buffer-name))))))

(provide 'mcp/mcp-python-check)
;;; mcp-python-check.el ends here
