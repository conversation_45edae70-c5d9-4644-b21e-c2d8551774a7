;;; mcp-test-command.el --- Test commands for MCP servers -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides test commands for MCP servers.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)

;; Test MCP server echo function
(defun ai-auto-complete-mcp-test-echo (server-name message)
  "Test the echo function of MCP server with SERVER-NAME using MESSAGE."
  (interactive
   (list
    (completing-read "Select MCP server: " (ai-auto-complete-mcp-list-servers))
    (read-string "Message to echo: ")))
  
  ;; Create a buffer for the result
  (let ((buffer-name "*MCP Echo Test*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer
        
        ;; Add header
        (insert "MCP Echo Test\n")
        (insert "=============\n\n")
        
        ;; Show test parameters
        (insert (format "Server: %s\n" server-name))
        (insert (format "Message: %s\n\n" message))
        
        ;; Check if the server is running
        (if (not (eq (ai-auto-complete-mcp-get-server-status server-name) 'running))
            (progn
              (insert "Server is not running. Attempting to start...\n")
              (if (ai-auto-complete-mcp-start-server server-name)
                  (insert "Server started successfully.\n\n")
                (insert "Failed to start server.\n\n")
                (insert-button "Try Again"
                              'action (lambda (_)
                                       (ai-auto-complete-mcp-test-echo server-name message))
                              'follow-link t)
                (cl-return-from ai-auto-complete-mcp-test-echo nil)))
          (insert "Server is running.\n\n"))
        
        ;; Call the echo tool
        (insert "Calling echo tool...\n")
        (ai-auto-complete-mcp-call-tool
         server-name "echo"
         `(("message" . ,message))
         (lambda (result)
           (with-current-buffer buffer-name
             (let ((inhibit-read-only t))
               (goto-char (point-max))
               (insert (format "\nResult: %s\n\n" result))
               
               ;; Add buttons for actions
               (insert "Actions: ")
               (insert-button "Try Again"
                             'action (lambda (_)
                                      (ai-auto-complete-mcp-test-echo server-name message))
                             'follow-link t)
               (insert " | ")
               (insert-button "Try Different Message"
                             'action (lambda (_)
                                      (let ((new-message (read-string "New message to echo: ")))
                                        (ai-auto-complete-mcp-test-echo server-name new-message)))
                             'follow-link t)
               (insert " | ")
               (insert-button "Try Different Server"
                             'action (lambda (_)
                                      (let ((new-server (completing-read "Select MCP server: " 
                                                                        (ai-auto-complete-mcp-list-servers))))
                                        (ai-auto-complete-mcp-test-echo new-server message)))
                             'follow-link t)))))))
    
    ;; Display the buffer
    (switch-to-buffer buffer-name)))

;; Test MCP server direct execution
(defun ai-auto-complete-mcp-test-direct-execution (server-name tool-name)
  "Test direct execution of TOOL-NAME on MCP server with SERVER-NAME."
  (interactive
   (let* ((server-name (completing-read "Select MCP server: " (ai-auto-complete-mcp-list-servers)))
          (server (ai-auto-complete-mcp-get-server server-name))
          (path (plist-get server :path))
          (tools (when (and path (file-exists-p path))
                   (with-temp-buffer
                     (insert-file-contents path)
                     (let ((tools '()))
                       (goto-char (point-min))
                       (while (re-search-forward "@mcp\\.tool\\(\\)" nil t)
                         (when (re-search-forward "def \\([a-zA-Z0-9_]+\\)" nil t)
                           (push (match-string 1) tools)))
                       tools)))))
     (list server-name
           (completing-read "Select tool: " (or tools '("echo" "hello" "add" "multiply"))))))
  
  ;; Create a buffer for the result
  (let ((buffer-name "*MCP Direct Execution Test*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode) ;; Use special-mode for the buffer
        
        ;; Add header
        (insert "MCP Direct Execution Test\n")
        (insert "=========================\n\n")
        
        ;; Show test parameters
        (insert (format "Server: %s\n" server-name))
        (insert (format "Tool: %s\n\n" tool-name))
        
        ;; Get the server path
        (let* ((server (ai-auto-complete-mcp-get-server server-name))
               (path (plist-get server :path)))
          
          (if (not path)
              (insert "Server not found or path not set.\n")
            
            (if (not (file-exists-p path))
                (insert (format "Server path %s does not exist.\n" path))
              
              ;; Try to execute the tool directly
              (insert "Attempting direct execution...\n")
              (let ((python-cmd (or (executable-find ai-auto-complete-mcp-python-command)
                                   (executable-find "python3")
                                   (executable-find "python"))))
                (if (not python-cmd)
                    (insert "No Python executable found.\n")
                  
                  ;; Create a temporary script to call the tool directly
                  (let* ((temp-dir (temporary-file-directory))
                         (temp-script (expand-file-name (format "mcp-direct-call-%s.py" (random 10000)) temp-dir))
                         (params (cond
                                  ((string= tool-name "echo") '(("message" . "Hello from direct execution")))
                                  ((string= tool-name "hello") '(("name" . "Direct Execution")))
                                  ((string= tool-name "add") '(("a" . 5) ("b" . 7)))
                                  ((string= tool-name "multiply") '(("a" . 6) ("b" . 8)))
                                  (t '())))
                         (json-params (json-encode params))
                         (script-content (format "
import sys
import json
import importlib.util
import os

# Add the server directory to the Python path
server_dir = os.path.dirname('%s')
if server_dir not in sys.path:
    sys.path.insert(0, server_dir)

try:
    # Try to import the server module
    spec = importlib.util.spec_from_file_location('server_module', '%s')
    server_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(server_module)
    
    # Get the tool function
    tool_func = None
    for attr_name in dir(server_module):
        attr = getattr(server_module, attr_name)
        if callable(attr) and hasattr(attr, '__name__') and attr.__name__ == '%s':
            tool_func = attr
            break
    
    if tool_func is None:
        # Try to find the tool in the mcp object if it exists
        if hasattr(server_module, 'mcp'):
            mcp = server_module.mcp
            if hasattr(mcp, 'tools') and '%s' in mcp.tools:
                tool_func = mcp.tools['%s']
    
    if tool_func is None:
        print(json.dumps({'error': 'Tool %s not found'}))
        sys.exit(1)
    
    # Call the tool function with the parameters
    params = json.loads('%s')
    result = tool_func(**params)
    
    # Return the result
    print(json.dumps({'result': result}))

except Exception as e:
    print(json.dumps({'error': str(e)}))
    sys.exit(1)
" path path tool-name tool-name tool-name tool-name json-params)))
                    
                    ;; Write the script to a temporary file
                    (with-temp-file temp-script
                      (insert script-content))
                    
                    ;; Make the script executable
                    (set-file-modes temp-script (logior (file-modes temp-script) #o111))
                    
                    ;; Execute the script
                    (insert (format "Executing: %s %s\n\n" python-cmd temp-script))
                    (let ((output (shell-command-to-string (format "%s %s" python-cmd temp-script))))
                      ;; Delete the temporary script
                      (delete-file temp-script)
                      
                      ;; Display the output
                      (insert "Raw output:\n")
                      (insert output)
                      (insert "\n\n")
                      
                      ;; Parse the output
                      (condition-case err
                          (let* ((json-object-type 'plist)
                                 (json-array-type 'list)
                                 (json-key-type 'keyword)
                                 (result (json-read-from-string output)))
                            (if (plist-member result :error)
                                (insert (format "Error: %s\n" (plist-get result :error)))
                              (insert (format "Result: %s\n" (plist-get result :result)))))
                        (error
                         (insert (format "Error parsing result: %s\n" (error-message-string err))))))))))))))
    
    ;; Display the buffer
    (switch-to-buffer buffer-name)))

(provide 'mcp/mcp-test-command)
;;; mcp-test-command.el ends here
