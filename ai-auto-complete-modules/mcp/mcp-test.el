;;; mcp-test.el --- Test utilities for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides test utilities for the MCP integration in AI Auto Complete.
;; It helps test MCP servers, transports, and protocol handling.

;;; Code:

;; Provide a minimal implementation to avoid loading errors
(defvar ai-auto-complete-mcp-test-buffer "*MCP Test*"
  "Name of the MCP test buffer.")

(defun ai-auto-complete-mcp-test-initialize ()
  "Initialize MCP testing."
  (message "MCP testing module loaded"))

;; Defer actual implementation to after Emacs is fully initialized
(add-hook 'after-init-hook 'ai-auto-complete-mcp-test-initialize)

(provide 'mcp/mcp-test)
;;; mcp-test.el ends here
