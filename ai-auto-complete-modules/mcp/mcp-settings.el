;;; mcp-settings.el --- MCP settings management -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides functions to read and manage MCP settings from a JSON file.

;;; Code:

(require 'json)
(require 'mcp/mcp-core)
(require 'mcp/mcp-server)

;; Define the path to the settings file
(defvar ai-auto-complete-mcp-settings-file
  (expand-file-name "ai-auto-complete-mcp-settings.json"
                   (file-name-directory (locate-library "mcp/mcp-core")))
  "Path to the MCP settings file.")

;; Read the MCP settings from the JSON file
(defun ai-auto-complete-mcp-read-settings ()
  "Read MCP settings from the settings file.
Returns a plist with the parsed settings or nil if the file cannot be read."
  (when (file-exists-p ai-auto-complete-mcp-settings-file)
    (condition-case err
        (let ((json-object-type 'hash-table)
              (json-array-type 'list)
              (json-key-type 'string))
          (with-temp-buffer
            (insert-file-contents ai-auto-complete-mcp-settings-file)
            (let ((json-data (json-read)))
              (when ai-auto-complete-mcp-debug-mode
                (message "MCP: Read settings from %s" ai-auto-complete-mcp-settings-file))
              json-data)))
      (error
       (message "MCP: Error reading settings file: %s" (error-message-string err))
       nil))))

;; Register MCP servers from settings
(defun ai-auto-complete-mcp-register-servers-from-settings ()
  "Register MCP servers from the settings file."
  (require 'mcp/mcp-server-type)
  (let ((settings (ai-auto-complete-mcp-read-settings))
        (registered-count 0))
    ;; Debug: Show the entire settings content
    (message "MCP DEBUG: Settings file content: %S" settings)

    (when settings
      (let ((servers (gethash "mcpServers" settings)))
        ;; Debug: Show the mcpServers content
        (message "MCP DEBUG: mcpServers content: %S" servers)

        (when servers
          ;; Get all server names without using maphash
          (let ((server-names '())
                (server-keys (hash-table-keys servers)))
            (setq server-names server-keys)
            (message "MCP DEBUG: Server names in settings: %S" server-names)

            ;; Process each server
            (dolist (server-name server-names)
              (let ((server-config (gethash server-name servers)))
                ;; Debug: Show each server's config
                (message "MCP DEBUG: Processing server %s with config: %S" server-name server-config)

                (let ((disabled (gethash "disabled" server-config)))
                  ;; Debug: Show disabled status
                  (message "MCP DEBUG: Server %s disabled status: %S" server-name disabled)

                  ;; Check if the server is disabled
                  ;; In JSON, false is represented as :json-false in Emacs Lisp
                  (when (or (not disabled) (eq disabled :json-false))
                    (let* ((command (gethash "command" server-config))
                           (args (gethash "args" server-config))
                           (env (gethash "env" server-config))
                           ;; Extract server ID from the full path or URL
                           (server-id (cond
                                      ;; For github.com URLs with brave-search, use "brave-search"
                                      ((string-match "github\\.com/.*brave-search" server-name)
                                       (message "MCP DEBUG: Matched brave-search pattern for %s" server-name)
                                       "brave-search")
                                      ;; For github.com URLs, use the last path component
                                      ((string-match "github\\.com/[^/]+/[^/]+/tree/[^/]+/[^/]+/\\([^/]+\\)" server-name)
                                       (message "MCP DEBUG: Matched github URL pattern for %s" server-name)
                                       (match-string 1 server-name))
                                      ;; For other URLs, use the last path component
                                      ((string-match ".*/\\([^/]+\\)$" server-name)
                                       (message "MCP DEBUG: Matched generic URL pattern for %s" server-name)
                                       (match-string 1 server-name))
                                      ;; Otherwise use the name as is
                                      (t
                                       (message "MCP DEBUG: Using server name as is: %s" server-name)
                                       server-name)))
                           ;; Create the full command string for display
                           (full-command (concat command " " (mapconcat 'identity args " ")))
                           ;; Create the server path by concatenating args
                           (server-path (cond
                                        ;; If args is nil, use an empty string
                                        ((null args)
                                         (message "MCP DEBUG: No args for server %s, using empty string" server-id)
                                         "")
                                        ;; If there's only one arg, use it directly
                                        ((= (length args) 1)
                                         (message "MCP DEBUG: Single arg for server %s: %s" server-id (car args))
                                         (car args))
                                        ;; Otherwise, concatenate all args with spaces
                                        (t
                                         (let ((path (mapconcat 'identity args " ")))
                                           (message "MCP DEBUG: Multiple args for server %s, concatenated: %s" server-id path)
                                           path))))
                           ;; Determine server type based on command
                           (server-type (ai-auto-complete-mcp-determine-server-type-from-command command))
                           ;; Get appropriate transport for server type
                           (transport (ai-auto-complete-mcp-get-transport-for-server-type server-type)))

                      ;; Debug: Show extracted values
                      (message "MCP DEBUG: Server %s details:" server-name)
                      (message "  - ID: %s" server-id)
                      (message "  - Command: %s" command)
                      (message "  - Args: %S" args)
                      (message "  - Path: %s" server-path)
                      (message "  - Server Type: %s" server-type)
                      (message "  - Transport: %s" transport)

                      ;; Register the server with the server-bridge transport
                      (message "MCP DEBUG: Registering server %s with server-bridge transport" server-id)

                      ;; Use the standard register-server function
                      (let ((result (ai-auto-complete-mcp-register-server
                                    server-id server-path 'server-bridge
                                    (format "MCP server: %s (from settings)" server-id)
                                    command)))

                        ;; Add additional server properties
                        (when result
                          (let ((server (ai-auto-complete-mcp-get-server server-id)))
                            (when server
                              ;; Add args, env, and server-type to the server definition
                              (puthash server-id
                                      (plist-put
                                       (plist-put
                                        (plist-put server :args args)
                                        :env env)
                                       :server-type server-type)
                                      ai-auto-complete-mcp-servers)

                              (message "MCP DEBUG: Successfully registered server %s" server-id)
                              (setq registered-count (1+ registered-count)))))

                        (unless result
                          (message "MCP DEBUG: Failed to register server %s" server-id))))))))))

        (message "MCP: Registered %d servers from settings" registered-count)
        registered-count))))

;; Register a settings-based MCP server
(defun ai-auto-complete-mcp-register-settings-server (name command args env &optional server-path runner)
  "Register an MCP server with NAME using COMMAND, ARGS, ENV, SERVER-PATH and RUNNER from settings."
  (message "MCP DEBUG: Registering settings-based server %s" name)
  (message "MCP DEBUG:   - Command: %s" command)
  (message "MCP DEBUG:   - Args: %S" args)
  (message "MCP DEBUG:   - Server path: %s" server-path)
  (message "MCP DEBUG:   - Runner: %s" runner)

  (if (or (null name) (null command))
      (progn
        (message "MCP DEBUG: Cannot register server with nil name or command")
        nil)
    (let ((description (format "MCP server: %s (from settings)" name)))
      ;; Use the server path directly without adding a prefix
      (let ((effective-path (or server-path
                               ;; Fall back to command if no server path
                               command)))
        ;; Register the server with the settings transport
        (puthash name (list :path effective-path
                            :transport 'settings
                            :description description
                            :process nil
                            :connection nil
                            :capabilities nil
                            :status 'stopped
                            :server-type 'settings
                            :command command
                            :args args
                            :env env
                            :server-path server-path
                            :runner (or runner command)) ; Use command as runner if not specified
                 ai-auto-complete-mcp-servers)

        (message "MCP DEBUG: Registered settings-based MCP server: %s" name)
        (message "MCP DEBUG:   - Transport: settings")
        (message "MCP DEBUG:   - Path: %s" effective-path)
        (message "MCP DEBUG:   - Command: %s" command)
        (message "MCP DEBUG:   - Args: %S" args)
        (when server-path
          (message "MCP DEBUG:   - Server path: %s" server-path))
        (when runner
          (message "MCP DEBUG:   - Runner: %s" runner))

        name))))

;; Start a settings-based MCP server
(defun ai-auto-complete-mcp-settings-start-server (name &optional path)
  "Start the settings-based MCP server with NAME.
Optional PATH is ignored for settings-based servers."
  ;; Debug: Show that we're trying to start a settings-based server
  (message "MCP DEBUG: Attempting to start settings-based server: %s" name)

  (let ((server (ai-auto-complete-mcp-get-server name)))
    ;; Debug: Show the server details
    (message "MCP DEBUG: Server details: %S" server)

    (if (not server)
        (progn
          (message "MCP: Settings-based server %s not found" name)
          nil)
      (let ((command (plist-get server :command))
            (args (plist-get server :args))
            (env (plist-get server :env))
            (path (plist-get server :path))
            (runner (plist-get server :runner))
            (process-environment process-environment))

        ;; Debug: Show the extracted values
        (message "MCP DEBUG: Server %s start parameters:" name)
        (message "  - Command: %s" command)
        (message "  - Runner: %s" runner)
        (message "  - Path: %s" path)
        (message "  - Args: %S" args)
        (message "  - Env: %S" env)

        ;; Set environment variables
        (when env
          (message "MCP DEBUG: Setting environment variables for %s" name)
          (if (hash-table-p env)
              (let ((env-keys (hash-table-keys env)))
                (dolist (key env-keys)
                  (let* ((value (gethash key env))
                         (env-var (format "%s=%s" key value)))
                    (message "MCP DEBUG:   - %s" env-var)
                    (push env-var process-environment))))
            (message "MCP DEBUG: Environment is not a hash table: %S" env)))

        ;; Create the process
        (let* ((process-name (format "mcp-%s" name))
               (buffer-name (format "*mcp-%s*" name))
               (effective-runner (or runner command))
               ;; Make sure args is a proper list
               (args-list (cond
                          ;; If it's nil, use an empty list
                          ((null args) '())
                          ;; If it's already a list, use it directly
                          ((listp args) args)
                          ;; If it's a vector, convert to list
                          ((vectorp args) (append args nil))
                          ;; If it's anything else, wrap in a list
                          (t (list args))))
               ;; Debug: Show the process creation details
               (_ (message "MCP DEBUG: Creating process for %s:" name))
               (_ (message "  - Process name: %s" process-name))
               (_ (message "  - Buffer name: %s" buffer-name))
               (_ (message "  - Runner: %s" effective-runner))
               (_ (message "  - Args: %S" args-list))
               ;; Create the process
               (process-cmd (cons effective-runner args-list))
               (_ (message "MCP DEBUG: Full command: %S" process-cmd))
               (process (condition-case err
                            (apply 'start-process
                                   process-name
                                   buffer-name
                                   effective-runner
                                   args-list)
                          (error
                           (message "MCP DEBUG: Error starting process: %s" (error-message-string err))
                           nil))))

          ;; Debug: Show the process result
          (if process
              (message "MCP DEBUG: Process started successfully with PID: %s" (process-id process))
            (message "MCP DEBUG: Failed to start process"))

          ;; More debug info
          (message "MCP DEBUG: Starting settings-based server %s with runner: %s"
                   name effective-runner)
          (message "MCP DEBUG: Args: %S" args-list)

          ;; Set up process sentinel
          (set-process-sentinel
           process
           (lambda (proc event)
             (let ((server-name (substring (process-name proc) 4)))
               (cond
                ((string-prefix-p "finished" event)
                 (message "MCP: Server %s process finished" server-name)
                 (ai-auto-complete-mcp-update-server-status server-name 'stopped))
                ((string-prefix-p "exited" event)
                 (message "MCP: Server %s process exited" server-name)
                 (ai-auto-complete-mcp-update-server-status server-name 'stopped))
                ((string-prefix-p "killed" event)
                 (message "MCP: Server %s process killed" server-name)
                 (ai-auto-complete-mcp-update-server-status server-name 'stopped))))))

          ;; Set up process filter for JSON-RPC communication
          (set-process-filter
           process
           (lambda (proc output)
             (when ai-auto-complete-mcp-debug-mode
               (message "MCP: Output from %s: %s" (process-name proc) output))

             ;; Process JSON-RPC messages
             (with-current-buffer (process-buffer proc)
               (goto-char (point-max))
               (insert output)

               ;; Process complete lines
               (goto-char (point-min))
               (while (re-search-forward "^\\(.+\\)\n" nil t)
                 (let ((line (match-string 1)))
                   (delete-region (match-beginning 0) (match-end 0))

                   ;; Parse JSON-RPC message
                   (condition-case nil
                       (let* ((json-object-type 'plist)
                              (json-array-type 'list)
                              (json-key-type 'keyword)
                              (msg (json-read-from-string line)))

                         ;; Handle initialization response
                         (when (and (plist-get msg :id)
                                    (eq (plist-get msg :id) 1)
                                    (plist-get msg :result))
                           (let* ((server-name (substring (process-name proc) 4))
                                  (capabilities (plist-get (plist-get msg :result) :capabilities)))
                             (ai-auto-complete-mcp-update-server-capabilities server-name capabilities))))
                     (error nil)))))))

          ;; Send initialization request
          (process-send-string
           process
           (concat
            (ai-auto-complete-mcp-create-jsonrpc-request
             "initialize"
             (list :clientName "ai-auto-complete"
                   :clientVersion "1.0.0")
             1)
            "\n"))

          ;; Update server status and process
          (ai-auto-complete-mcp-update-server-process name process)
          (ai-auto-complete-mcp-update-server-status name 'running)

          (message "MCP: Started settings-based server %s" name)
          t)))))

;; Stop a settings-based MCP server
(defun ai-auto-complete-mcp-settings-stop-server (name &optional path)
  "Stop the settings-based MCP server with NAME.
Optional PATH is ignored for settings-based servers."
  (let ((server (ai-auto-complete-mcp-get-server name)))
    (if (not server)
        (progn
          (message "MCP: Settings-based server %s not found" name)
          nil)
      (let ((process (plist-get server :process)))
        (when (and process (process-live-p process))
          (delete-process process)
          (message "MCP: Stopped settings-based server %s" name))

        (ai-auto-complete-mcp-update-server-process name nil)
        (ai-auto-complete-mcp-update-server-status name 'stopped)
        t))))

;; Register the settings transport
(defun ai-auto-complete-mcp-register-settings-transport ()
  "Register the settings transport for MCP servers."
  (ai-auto-complete-mcp-register-transport
   'settings
   'ai-auto-complete-mcp-settings-start-server
   'ai-auto-complete-mcp-settings-stop-server
   'ai-auto-complete-mcp-stdio-call-tool
   'ai-auto-complete-mcp-stdio-list-tools
   'ai-auto-complete-mcp-stdio-list-resources
   'ai-auto-complete-mcp-stdio-list-prompts
   'ai-auto-complete-mcp-stdio-read-resource
   'ai-auto-complete-mcp-stdio-get-prompt))

;; Initialize settings-based MCP servers
(defun ai-auto-complete-mcp-init-settings-servers ()
  "Initialize settings-based MCP servers."
  (interactive)
  (require 'mcp/mcp-server-bridge)

  (message "MCP DEBUG: Initializing settings-based MCP servers")

  ;; Register the server bridge transport
  (message "MCP DEBUG: Registering server bridge transport")
  (ai-auto-complete-mcp-register-server-bridge-transport)

  ;; Register servers from settings
  (message "MCP DEBUG: Registering servers from settings")
  (condition-case err
      (let ((count (ai-auto-complete-mcp-register-servers-from-settings)))
        (message "MCP DEBUG: Registered %d servers from settings" count)

        ;; List registered servers
        (let ((servers (ai-auto-complete-mcp-list-servers)))
          (message "MCP DEBUG: All registered servers: %S" servers)))
    (error
     (message "Error loading server initialization module: %s" (error-message-string err)))))

(provide 'mcp/mcp-settings)
;;; mcp-settings.el ends here
