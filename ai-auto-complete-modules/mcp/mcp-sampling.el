;;; mcp-sampling.el --- Sampling functionality for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides sampling functionality for MCP integration in the AI Auto Complete package.
;; It allows sampling MCP resources and tools to provide examples to LLMs.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-client)
(require 'mcp/mcp-resources)

;; Sample a resource
(defun ai-auto-complete-mcp-sample-resource (server-name resource-uri &optional params)
  "Sample RESOURCE-URI on SERVER-NAME with optional PARAMS.
Returns a plist with :uri, :value, and :description."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-sample-resource nil))
  
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((status (plist-get server :status)))
        (if (not (eq status 'running))
            (progn
              (message "MCP server %s is not running" server-name)
              nil)
          ;; Create a promise to get the result
          (let ((result-promise (make-hash-table :test 'eq))
                (result-value nil))
            ;; Set up the promise
            (puthash 'status 'pending result-promise)
            (puthash 'value nil result-promise)
            
            ;; Read the resource
            (ai-auto-complete-mcp-read-resource
             server-name resource-uri
             (lambda (result)
               (puthash 'status 'fulfilled result-promise)
               (puthash 'value result result-promise)))
            
            ;; Wait for the result (with timeout)
            (let ((timeout 10)
                  (start-time (current-time)))
              (while (and (eq (gethash 'status result-promise) 'pending)
                          (< (float-time (time-since start-time)) timeout))
                (sleep-for 0.1))
              
              ;; Return the result
              (if (eq (gethash 'status result-promise) 'fulfilled)
                  (list :uri resource-uri
                        :value (gethash 'value result-promise)
                        :description (format "Sample of resource %s from server %s"
                                            resource-uri server-name))
                (progn
                  (message "Timeout waiting for resource %s from server %s"
                           resource-uri server-name)
                  nil)))))))))

;; Sample a tool
(defun ai-auto-complete-mcp-sample-tool (server-name tool-name &optional params)
  "Sample TOOL-NAME on SERVER-NAME with optional PARAMS.
Returns a plist with :name, :result, and :description."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-sample-tool nil))
  
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((status (plist-get server :status)))
        (if (not (eq status 'running))
            (progn
              (message "MCP server %s is not running" server-name)
              nil)
          ;; Create a promise to get the result
          (let ((result-promise (make-hash-table :test 'eq))
                (result-value nil))
            ;; Set up the promise
            (puthash 'status 'pending result-promise)
            (puthash 'value nil result-promise)
            
            ;; Call the tool
            (ai-auto-complete-mcp-call-tool
             server-name tool-name (or params '())
             (lambda (result)
               (puthash 'status 'fulfilled result-promise)
               (puthash 'value result result-promise)))
            
            ;; Wait for the result (with timeout)
            (let ((timeout 10)
                  (start-time (current-time)))
              (while (and (eq (gethash 'status result-promise) 'pending)
                          (< (float-time (time-since start-time)) timeout))
                (sleep-for 0.1))
              
              ;; Return the result
              (if (eq (gethash 'status result-promise) 'fulfilled)
                  (list :name tool-name
                        :result (gethash 'value result-promise)
                        :description (format "Sample of tool %s from server %s"
                                            tool-name server-name))
                (progn
                  (message "Timeout waiting for tool %s from server %s"
                           tool-name server-name)
                  nil)))))))))

;; Sample a prompt
(defun ai-auto-complete-mcp-sample-prompt (server-name prompt-name &optional params)
  "Sample PROMPT-NAME on SERVER-NAME with optional PARAMS.
Returns a plist with :name, :result, and :description."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-sample-prompt nil))
  
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((status (plist-get server :status)))
        (if (not (eq status 'running))
            (progn
              (message "MCP server %s is not running" server-name)
              nil)
          ;; Create a promise to get the result
          (let ((result-promise (make-hash-table :test 'eq))
                (result-value nil))
            ;; Set up the promise
            (puthash 'status 'pending result-promise)
            (puthash 'value nil result-promise)
            
            ;; Get the prompt
            (ai-auto-complete-mcp-get-prompt
             server-name prompt-name (or params '())
             (lambda (result)
               (puthash 'status 'fulfilled result-promise)
               (puthash 'value result result-promise)))
            
            ;; Wait for the result (with timeout)
            (let ((timeout 10)
                  (start-time (current-time)))
              (while (and (eq (gethash 'status result-promise) 'pending)
                          (< (float-time (time-since start-time)) timeout))
                (sleep-for 0.1))
              
              ;; Return the result
              (if (eq (gethash 'status result-promise) 'fulfilled)
                  (list :name prompt-name
                        :result (gethash 'value result-promise)
                        :description (format "Sample of prompt %s from server %s"
                                            prompt-name server-name))
                (progn
                  (message "Timeout waiting for prompt %s from server %s"
                           prompt-name server-name)
                  nil)))))))))

;; Generate examples for a server
(defun ai-auto-complete-mcp-generate-examples (server-name)
  "Generate examples for SERVER-NAME.
Returns a plist with :resources, :tools, and :prompts."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-generate-examples nil))
  
  (let ((server (ai-auto-complete-mcp-get-server server-name)))
    (if (not server)
        (progn
          (message "MCP server %s not found" server-name)
          nil)
      (let ((status (plist-get server :status)))
        (if (not (eq status 'running))
            (progn
              (message "MCP server %s is not running" server-name)
              nil)
          ;; Create promises to get the results
          (let ((tools-promise (make-hash-table :test 'eq))
                (resources-promise (make-hash-table :test 'eq))
                (prompts-promise (make-hash-table :test 'eq)))
            ;; Set up the promises
            (puthash 'status 'pending tools-promise)
            (puthash 'value nil tools-promise)
            (puthash 'status 'pending resources-promise)
            (puthash 'value nil resources-promise)
            (puthash 'status 'pending prompts-promise)
            (puthash 'value nil prompts-promise)
            
            ;; List tools
            (ai-auto-complete-mcp-list-tools
             server-name
             (lambda (tools)
               (puthash 'status 'fulfilled tools-promise)
               (puthash 'value tools tools-promise)))
            
            ;; List resources
            (ai-auto-complete-mcp-list-resources
             server-name
             (lambda (resources)
               (puthash 'status 'fulfilled resources-promise)
               (puthash 'value resources resources-promise)))
            
            ;; List prompts
            (ai-auto-complete-mcp-list-prompts
             server-name
             (lambda (prompts)
               (puthash 'status 'fulfilled prompts-promise)
               (puthash 'value prompts prompts-promise)))
            
            ;; Wait for the results (with timeout)
            (let ((timeout 10)
                  (start-time (current-time)))
              (while (and (or (eq (gethash 'status tools-promise) 'pending)
                              (eq (gethash 'status resources-promise) 'pending)
                              (eq (gethash 'status prompts-promise) 'pending))
                          (< (float-time (time-since start-time)) timeout))
                (sleep-for 0.1))
              
              ;; Process the results
              (let ((tools (gethash 'value tools-promise))
                    (resources (gethash 'value resources-promise))
                    (prompts (gethash 'value prompts-promise))
                    (tool-examples '())
                    (resource-examples '())
                    (prompt-examples '()))
                
                ;; Sample tools
                (when (and tools (not (stringp tools)))
                  (let ((sample-count (min 3 (length tools))))
                    (dotimes (i sample-count)
                      (let* ((tool (nth i tools))
                             (tool-name (plist-get tool :name))
                             (example (ai-auto-complete-mcp-sample-tool server-name tool-name)))
                        (when example
                          (push example tool-examples))))))
                
                ;; Sample resources
                (when (and resources (not (stringp resources)))
                  (let ((sample-count (min 3 (length resources))))
                    (dotimes (i sample-count)
                      (let* ((resource (nth i resources))
                             (resource-uri (plist-get resource :uri))
                             (example (ai-auto-complete-mcp-sample-resource server-name resource-uri)))
                        (when example
                          (push example resource-examples))))))
                
                ;; Sample prompts
                (when (and prompts (not (stringp prompts)))
                  (let ((sample-count (min 3 (length prompts))))
                    (dotimes (i sample-count)
                      (let* ((prompt (nth i prompts))
                             (prompt-name (plist-get prompt :name))
                             (example (ai-auto-complete-mcp-sample-prompt server-name prompt-name)))
                        (when example
                          (push example prompt-examples))))))
                
                ;; Return the examples
                (list :tools (nreverse tool-examples)
                      :resources (nreverse resource-examples)
                      :prompts (nreverse prompt-examples))))))))))

;; Generate examples for all servers
(defun ai-auto-complete-mcp-generate-all-examples ()
  "Generate examples for all servers.
Returns a list of plists with :server-name and :examples."
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-generate-all-examples nil))
  
  (let ((servers (ai-auto-complete-mcp-list-servers))
        (examples '()))
    (dolist (server-name servers)
      (let ((server-examples (ai-auto-complete-mcp-generate-examples server-name)))
        (when server-examples
          (push (list :server-name server-name
                      :examples server-examples)
                examples))))
    (nreverse examples)))

;; Format examples as markdown
(defun ai-auto-complete-mcp-format-examples-as-markdown (examples)
  "Format EXAMPLES as markdown.
EXAMPLES should be a list of plists with :server-name and :examples."
  (let ((markdown "# MCP Server Examples\n\n"))
    (dolist (server-examples examples)
      (let ((server-name (plist-get server-examples :server-name))
            (server-data (plist-get server-examples :examples)))
        
        ;; Add server header
        (setq markdown (concat markdown (format "## Server: %s\n\n" server-name)))
        
        ;; Add tool examples
        (let ((tool-examples (plist-get server-data :tools)))
          (when tool-examples
            (setq markdown (concat markdown "### Tools\n\n"))
            (dolist (tool tool-examples)
              (let ((tool-name (plist-get tool :name))
                    (tool-result (plist-get tool :result))
                    (tool-description (plist-get tool :description)))
                (setq markdown (concat markdown (format "#### %s\n\n" tool-name)))
                (setq markdown (concat markdown (format "%s\n\n" tool-description)))
                (setq markdown (concat markdown "```\n"))
                (setq markdown (concat markdown (format "%s\n" tool-result)))
                (setq markdown (concat markdown "```\n\n"))))))
        
        ;; Add resource examples
        (let ((resource-examples (plist-get server-data :resources)))
          (when resource-examples
            (setq markdown (concat markdown "### Resources\n\n"))
            (dolist (resource resource-examples)
              (let ((resource-uri (plist-get resource :uri))
                    (resource-value (plist-get resource :value))
                    (resource-description (plist-get resource :description)))
                (setq markdown (concat markdown (format "#### %s\n\n" resource-uri)))
                (setq markdown (concat markdown (format "%s\n\n" resource-description)))
                (setq markdown (concat markdown "```\n"))
                (setq markdown (concat markdown (format "%s\n" resource-value)))
                (setq markdown (concat markdown "```\n\n"))))))
        
        ;; Add prompt examples
        (let ((prompt-examples (plist-get server-data :prompts)))
          (when prompt-examples
            (setq markdown (concat markdown "### Prompts\n\n"))
            (dolist (prompt prompt-examples)
              (let ((prompt-name (plist-get prompt :name))
                    (prompt-result (plist-get prompt :result))
                    (prompt-description (plist-get prompt :description)))
                (setq markdown (concat markdown (format "#### %s\n\n" prompt-name)))
                (setq markdown (concat markdown (format "%s\n\n" prompt-description)))
                (setq markdown (concat markdown "```\n"))
                (setq markdown (concat markdown (format "%s\n" prompt-result)))
                (setq markdown (concat markdown "```\n\n"))))))))
    
    markdown))

;; Generate examples and display them
(defun ai-auto-complete-mcp-display-examples ()
  "Generate examples for all servers and display them."
  (interactive)
  
  (unless ai-auto-complete-mcp-enabled
    (message "MCP integration is not enabled. Enable it with M-x ai-auto-complete-mcp-toggle")
    (cl-return-from ai-auto-complete-mcp-display-examples nil))
  
  (message "Generating MCP examples...")
  
  (let ((examples (ai-auto-complete-mcp-generate-all-examples)))
    (if (null examples)
        (message "No MCP examples generated")
      (let ((markdown (ai-auto-complete-mcp-format-examples-as-markdown examples))
            (buffer-name "*MCP Examples*"))
        
        ;; Display the examples
        (with-current-buffer (get-buffer-create buffer-name)
          (let ((inhibit-read-only t))
            (erase-buffer)
            (insert markdown)
            (markdown-mode)
            (goto-char (point-min))))
        
        (switch-to-buffer buffer-name)
        (message "MCP examples generated")))))

(provide 'mcp/mcp-sampling)
;;; mcp-sampling.el ends here
