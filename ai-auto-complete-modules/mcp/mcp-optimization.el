;;; mcp-optimization.el --- Performance optimization for MCP in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides performance optimization for MCP integration in the AI Auto Complete package.
;; It optimizes the performance for large numbers of servers and tools.

;;; Code:

(require 'mcp/mcp-core)
(require 'mcp/mcp-server)
(require 'mcp/mcp-client)

;; Cache for MCP server capabilities
(defvar ai-auto-complete-mcp-capabilities-cache (make-hash-table :test 'equal)
  "Cache for MCP server capabilities.")

;; Cache for MCP server tools
(defvar ai-auto-complete-mcp-tools-cache (make-hash-table :test 'equal)
  "Cache for MCP server tools.")

;; Cache for MCP server resources
(defvar ai-auto-complete-mcp-resources-cache (make-hash-table :test 'equal)
  "Cache for MCP server resources.")

;; Cache for MCP server prompts
(defvar ai-auto-complete-mcp-prompts-cache (make-hash-table :test 'equal)
  "Cache for MCP server prompts.")

;; Cache for MCP tool results
(defvar ai-auto-complete-mcp-tool-results-cache (make-hash-table :test 'equal)
  "Cache for MCP tool results.")

;; Cache for MCP resource results
(defvar ai-auto-complete-mcp-resource-results-cache (make-hash-table :test 'equal)
  "Cache for MCP resource results.")

;; Cache for MCP prompt results
(defvar ai-auto-complete-mcp-prompt-results-cache (make-hash-table :test 'equal)
  "Cache for MCP prompt results.")

;; Cache timeout and enable/disable caching are defined in customization/mcp-customization.el

;; Clear all caches
(defun ai-auto-complete-mcp-clear-caches ()
  "Clear all MCP caches."
  (interactive)
  (clrhash ai-auto-complete-mcp-capabilities-cache)
  (clrhash ai-auto-complete-mcp-tools-cache)
  (clrhash ai-auto-complete-mcp-resources-cache)
  (clrhash ai-auto-complete-mcp-prompts-cache)
  (clrhash ai-auto-complete-mcp-tool-results-cache)
  (clrhash ai-auto-complete-mcp-resource-results-cache)
  (clrhash ai-auto-complete-mcp-prompt-results-cache)
  (message "Cleared all MCP caches"))

;; Generate a cache key
(defun ai-auto-complete-mcp-generate-cache-key (server-name &rest args)
  "Generate a cache key for SERVER-NAME and ARGS."
  (concat server-name ":" (mapconcat (lambda (arg)
                                      (if (stringp arg)
                                          arg
                                        (format "%S" arg)))
                                    args
                                    ":")))

;; Get a value from a cache with timeout
(defun ai-auto-complete-mcp-get-from-cache (cache key)
  "Get a value from CACHE with KEY, respecting timeout."
  (when ai-auto-complete-mcp-enable-caching
    (let ((entry (gethash key cache)))
      (when entry
        (let ((timestamp (car entry))
              (value (cdr entry)))
          (if (< (float-time (time-since timestamp)) ai-auto-complete-mcp-cache-timeout)
              value
            (remhash key cache)
            nil))))))

;; Put a value in a cache with timestamp
(defun ai-auto-complete-mcp-put-in-cache (cache key value)
  "Put VALUE in CACHE with KEY and current timestamp."
  (when ai-auto-complete-mcp-enable-caching
    (puthash key (cons (current-time) value) cache)
    value))

;; Advice function to cache server capabilities
(defun ai-auto-complete-mcp-advice-update-server-capabilities (orig-fun name capabilities)
  "Advice function to cache server capabilities.
Calls ORIG-FUN with NAME and CAPABILITIES."
  (let ((result (funcall orig-fun name capabilities)))
    (ai-auto-complete-mcp-put-in-cache ai-auto-complete-mcp-capabilities-cache name capabilities)
    result))

;; Advice function to use cached server capabilities
(defun ai-auto-complete-mcp-advice-get-server-capabilities (orig-fun name)
  "Advice function to use cached server capabilities.
Calls ORIG-FUN with NAME."
  (or (ai-auto-complete-mcp-get-from-cache ai-auto-complete-mcp-capabilities-cache name)
      (funcall orig-fun name)))

;; Advice function to cache tool calls
(defun ai-auto-complete-mcp-advice-call-tool (orig-fun server-name tool-name params callback)
  "Advice function to cache tool calls.
Calls ORIG-FUN with SERVER-NAME, TOOL-NAME, PARAMS, and CALLBACK."
  (let ((cache-key (ai-auto-complete-mcp-generate-cache-key server-name tool-name params)))
    (let ((cached-result (ai-auto-complete-mcp-get-from-cache ai-auto-complete-mcp-tool-results-cache cache-key)))
      (if cached-result
          (progn
            (when ai-auto-complete-mcp-debug-mode
              (message "Using cached result for tool %s on server %s" tool-name server-name))
            (funcall callback cached-result))
        (funcall orig-fun server-name tool-name params
                 (lambda (result)
                   (ai-auto-complete-mcp-put-in-cache ai-auto-complete-mcp-tool-results-cache cache-key result)
                   (funcall callback result)))))))

;; Advice function to cache resource reads
(defun ai-auto-complete-mcp-advice-read-resource (orig-fun server-name resource-uri callback)
  "Advice function to cache resource reads.
Calls ORIG-FUN with SERVER-NAME, RESOURCE-URI, and CALLBACK."
  (let ((cache-key (ai-auto-complete-mcp-generate-cache-key server-name resource-uri)))
    (let ((cached-result (ai-auto-complete-mcp-get-from-cache ai-auto-complete-mcp-resource-results-cache cache-key)))
      (if cached-result
          (progn
            (when ai-auto-complete-mcp-debug-mode
              (message "Using cached result for resource %s on server %s" resource-uri server-name))
            (funcall callback cached-result))
        (funcall orig-fun server-name resource-uri
                 (lambda (result)
                   (ai-auto-complete-mcp-put-in-cache ai-auto-complete-mcp-resource-results-cache cache-key result)
                   (funcall callback result)))))))

;; Advice function to cache prompt gets
(defun ai-auto-complete-mcp-advice-get-prompt (orig-fun server-name prompt-name params callback)
  "Advice function to cache prompt gets.
Calls ORIG-FUN with SERVER-NAME, PROMPT-NAME, PARAMS, and CALLBACK."
  (let ((cache-key (ai-auto-complete-mcp-generate-cache-key server-name prompt-name params)))
    (let ((cached-result (ai-auto-complete-mcp-get-from-cache ai-auto-complete-mcp-prompt-results-cache cache-key)))
      (if cached-result
          (progn
            (when ai-auto-complete-mcp-debug-mode
              (message "Using cached result for prompt %s on server %s" prompt-name server-name))
            (funcall callback cached-result))
        (funcall orig-fun server-name prompt-name params
                 (lambda (result)
                   (ai-auto-complete-mcp-put-in-cache ai-auto-complete-mcp-prompt-results-cache cache-key result)
                   (funcall callback result)))))))

;; Advice function to cache tools list
(defun ai-auto-complete-mcp-advice-list-tools (orig-fun server-name callback)
  "Advice function to cache tools list.
Calls ORIG-FUN with SERVER-NAME and CALLBACK."
  (let ((cached-result (ai-auto-complete-mcp-get-from-cache ai-auto-complete-mcp-tools-cache server-name)))
    (if cached-result
        (progn
          (when ai-auto-complete-mcp-debug-mode
            (message "Using cached tools list for server %s" server-name))
          (funcall callback cached-result))
      (funcall orig-fun server-name
               (lambda (result)
                 (unless (stringp result)
                   (ai-auto-complete-mcp-put-in-cache ai-auto-complete-mcp-tools-cache server-name result))
                 (funcall callback result))))))

;; Advice function to cache resources list
(defun ai-auto-complete-mcp-advice-list-resources (orig-fun server-name callback)
  "Advice function to cache resources list.
Calls ORIG-FUN with SERVER-NAME and CALLBACK."
  (let ((cached-result (ai-auto-complete-mcp-get-from-cache ai-auto-complete-mcp-resources-cache server-name)))
    (if cached-result
        (progn
          (when ai-auto-complete-mcp-debug-mode
            (message "Using cached resources list for server %s" server-name))
          (funcall callback cached-result))
      (funcall orig-fun server-name
               (lambda (result)
                 (unless (stringp result)
                   (ai-auto-complete-mcp-put-in-cache ai-auto-complete-mcp-resources-cache server-name result))
                 (funcall callback result))))))

;; Advice function to cache prompts list
(defun ai-auto-complete-mcp-advice-list-prompts (orig-fun server-name callback)
  "Advice function to cache prompts list.
Calls ORIG-FUN with SERVER-NAME and CALLBACK."
  (let ((cached-result (ai-auto-complete-mcp-get-from-cache ai-auto-complete-mcp-prompts-cache server-name)))
    (if cached-result
        (progn
          (when ai-auto-complete-mcp-debug-mode
            (message "Using cached prompts list for server %s" server-name))
          (funcall callback cached-result))
      (funcall orig-fun server-name
               (lambda (result)
                 (unless (stringp result)
                   (ai-auto-complete-mcp-put-in-cache ai-auto-complete-mcp-prompts-cache server-name result))
                 (funcall callback result))))))

;; Advice function to clear caches when a server is stopped
(defun ai-auto-complete-mcp-advice-stop-server-clear-caches (orig-fun server-name)
  "Advice function to clear caches when a server is stopped.
Calls ORIG-FUN with SERVER-NAME."
  (let ((result (funcall orig-fun server-name)))
    (when result
      ;; Clear caches for this server
      (maphash (lambda (key _)
                 (when (string-prefix-p (concat server-name ":") key)
                   (remhash key ai-auto-complete-mcp-tool-results-cache)
                   (remhash key ai-auto-complete-mcp-resource-results-cache)
                   (remhash key ai-auto-complete-mcp-prompt-results-cache)))
               ai-auto-complete-mcp-tool-results-cache)
      (remhash server-name ai-auto-complete-mcp-capabilities-cache)
      (remhash server-name ai-auto-complete-mcp-tools-cache)
      (remhash server-name ai-auto-complete-mcp-resources-cache)
      (remhash server-name ai-auto-complete-mcp-prompts-cache))
    result))

;; Batch processing for multiple servers
(defun ai-auto-complete-mcp-batch-start-servers (server-names)
  "Start multiple MCP servers with SERVER-NAMES."
  (let ((started 0)
        (total (length server-names)))
    (dolist (name server-names)
      (when (ai-auto-complete-mcp-start-server name)
        (setq started (1+ started))))
    (message "Started %d of %d MCP servers" started total)
    started))

;; Batch processing for multiple tools
(defun ai-auto-complete-mcp-batch-call-tools (server-name tool-names params-list callback)
  "Call multiple tools with TOOL-NAMES on SERVER-NAME with PARAMS-LIST.
CALLBACK will be called with a list of results."
  (let ((results (make-vector (length tool-names) nil))
        (completed 0)
        (total (length tool-names)))
    (cl-loop for tool-name in tool-names
             for params in params-list
             for i from 0
             do (ai-auto-complete-mcp-call-tool
                 server-name tool-name params
                 (lambda (result)
                   (aset results i result)
                   (setq completed (1+ completed))
                   (when (= completed total)
                     (funcall callback (append results nil))))))
    nil))

;; Batch processing for multiple resources
(defun ai-auto-complete-mcp-batch-read-resources (server-name resource-uris callback)
  "Read multiple resources with RESOURCE-URIS from SERVER-NAME.
CALLBACK will be called with a list of results."
  (let ((results (make-vector (length resource-uris) nil))
        (completed 0)
        (total (length resource-uris)))
    (cl-loop for resource-uri in resource-uris
             for i from 0
             do (ai-auto-complete-mcp-read-resource
                 server-name resource-uri
                 (lambda (result)
                   (aset results i result)
                   (setq completed (1+ completed))
                   (when (= completed total)
                     (funcall callback (append results nil))))))
    nil))

;; Batch processing for multiple prompts
(defun ai-auto-complete-mcp-batch-get-prompts (server-name prompt-names params-list callback)
  "Get multiple prompts with PROMPT-NAMES from SERVER-NAME with PARAMS-LIST.
CALLBACK will be called with a list of results."
  (let ((results (make-vector (length prompt-names) nil))
        (completed 0)
        (total (length prompt-names)))
    (cl-loop for prompt-name in prompt-names
             for params in params-list
             for i from 0
             do (ai-auto-complete-mcp-get-prompt
                 server-name prompt-name params
                 (lambda (result)
                   (aset results i result)
                   (setq completed (1+ completed))
                   (when (= completed total)
                     (funcall callback (append results nil))))))
    nil))

;; Setup function to add advice to MCP functions
(defun ai-auto-complete-mcp-optimization-setup ()
  "Set up MCP optimization."
  ;; Add advice to cache server capabilities
  (advice-add 'ai-auto-complete-mcp-update-server-capabilities :around
              #'ai-auto-complete-mcp-advice-update-server-capabilities)
  (advice-add 'ai-auto-complete-mcp-get-server-capabilities :around
              #'ai-auto-complete-mcp-advice-get-server-capabilities)

  ;; Add advice to cache tool calls
  (advice-add 'ai-auto-complete-mcp-call-tool :around
              #'ai-auto-complete-mcp-advice-call-tool)

  ;; Add advice to cache resource reads
  (advice-add 'ai-auto-complete-mcp-read-resource :around
              #'ai-auto-complete-mcp-advice-read-resource)

  ;; Add advice to cache prompt gets
  (advice-add 'ai-auto-complete-mcp-get-prompt :around
              #'ai-auto-complete-mcp-advice-get-prompt)

  ;; Add advice to cache tools list
  (advice-add 'ai-auto-complete-mcp-list-tools :around
              #'ai-auto-complete-mcp-advice-list-tools)

  ;; Add advice to cache resources list
  (advice-add 'ai-auto-complete-mcp-list-resources :around
              #'ai-auto-complete-mcp-advice-list-resources)

  ;; Add advice to cache prompts list
  (advice-add 'ai-auto-complete-mcp-list-prompts :around
              #'ai-auto-complete-mcp-advice-list-prompts)

  ;; Add advice to clear caches when a server is stopped
  (advice-add 'ai-auto-complete-mcp-stop-server :around
              #'ai-auto-complete-mcp-advice-stop-server-clear-caches))

;; Teardown function to remove advice from MCP functions
(defun ai-auto-complete-mcp-optimization-teardown ()
  "Remove MCP optimization."
  ;; Remove advice from server capabilities functions
  (advice-remove 'ai-auto-complete-mcp-update-server-capabilities
                 #'ai-auto-complete-mcp-advice-update-server-capabilities)
  (advice-remove 'ai-auto-complete-mcp-get-server-capabilities
                 #'ai-auto-complete-mcp-advice-get-server-capabilities)

  ;; Remove advice from tool call function
  (advice-remove 'ai-auto-complete-mcp-call-tool
                 #'ai-auto-complete-mcp-advice-call-tool)

  ;; Remove advice from resource read function
  (advice-remove 'ai-auto-complete-mcp-read-resource
                 #'ai-auto-complete-mcp-advice-read-resource)

  ;; Remove advice from prompt get function
  (advice-remove 'ai-auto-complete-mcp-get-prompt
                 #'ai-auto-complete-mcp-advice-get-prompt)

  ;; Remove advice from tools list function
  (advice-remove 'ai-auto-complete-mcp-list-tools
                 #'ai-auto-complete-mcp-advice-list-tools)

  ;; Remove advice from resources list function
  (advice-remove 'ai-auto-complete-mcp-list-resources
                 #'ai-auto-complete-mcp-advice-list-resources)

  ;; Remove advice from prompts list function
  (advice-remove 'ai-auto-complete-mcp-list-prompts
                 #'ai-auto-complete-mcp-advice-list-prompts)

  ;; Remove advice from stop server function
  (advice-remove 'ai-auto-complete-mcp-stop-server
                 #'ai-auto-complete-mcp-advice-stop-server-clear-caches))

;; Set up MCP optimization when this module is loaded
(ai-auto-complete-mcp-optimization-setup)

(provide 'mcp/mcp-optimization)
;;; mcp-optimization.el ends here
