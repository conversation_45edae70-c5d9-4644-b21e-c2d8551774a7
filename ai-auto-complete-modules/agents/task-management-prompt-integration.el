;;; task-management-prompt-integration.el --- Task management prompt integration -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides integration for task management prompts in the AI Auto Complete package.
;; It uses advice to ensure task management prompts are included in LLM requests,
;; similar to how tools-integration.el works for tool definitions.

;;; Code:

(require 'agents/task-management-prompts)
(require 'customization/prompts)

;; Enable/disable task management prompt integration
(defcustom ai-auto-complete-task-management-prompt-integration-enabled t
  "Whether to enable task management prompt integration."
  :type 'boolean
  :group 'ai-auto-complete-task-management)

;; Function to generate task management system prompt
(defun ai-auto-complete-task-management-generate-system-prompt ()
  "Generate the task management system prompt if task management is enabled."
  (if (and ai-auto-complete-task-management-prompt-integration-enabled
           (boundp 'ai-auto-complete-task-management-enabled)
           ai-auto-complete-task-management-enabled
           (boundp 'ai-auto-complete-task-management-instructions)
           ai-auto-complete-task-management-instructions)
      ai-auto-complete-task-management-instructions
    ""))

;; Advice function to add task management system prompt
(defun ai-auto-complete-task-management-advice-get-system-prompt (orig-fun &rest args)
  "Advice function to add task management system prompt to the system prompt.
Calls ORIG-FUN with ARGS and appends the task management system prompt."
  (let ((original-prompt (apply orig-fun args)))
    (if (not ai-auto-complete-task-management-prompt-integration-enabled)
        original-prompt
      (let ((task-mgmt-prompt (ai-auto-complete-task-management-generate-system-prompt)))
        (if (string-empty-p task-mgmt-prompt)
            original-prompt
          (concat original-prompt "\n\n" task-mgmt-prompt))))))

;; Setup function to add advice to system prompt function
(defun ai-auto-complete-task-management-prompt-setup ()
  "Set up task management prompt integration."
  (advice-add 'ai-auto-complete-get-system-prompt :around
              #'ai-auto-complete-task-management-advice-get-system-prompt)
  (message "Task management prompt integration enabled"))

;; Teardown function to remove advice from system prompt function
(defun ai-auto-complete-task-management-prompt-teardown ()
  "Remove task management prompt integration."
  (advice-remove 'ai-auto-complete-get-system-prompt
                 #'ai-auto-complete-task-management-advice-get-system-prompt)
  (message "Task management prompt integration disabled"))

;; Function to enable task management prompt integration
(defun ai-auto-complete-enable-task-management-prompt-integration ()
  "Enable task management prompt integration."
  (interactive)
  (setq ai-auto-complete-task-management-prompt-integration-enabled t)
  (ai-auto-complete-task-management-prompt-setup))

;; Function to disable task management prompt integration
(defun ai-auto-complete-disable-task-management-prompt-integration ()
  "Disable task management prompt integration."
  (interactive)
  (setq ai-auto-complete-task-management-prompt-integration-enabled nil)
  (ai-auto-complete-task-management-prompt-teardown))

;; Function to toggle task management prompt integration
(defun ai-auto-complete-toggle-task-management-prompt-integration ()
  "Toggle task management prompt integration."
  (interactive)
  (if ai-auto-complete-task-management-prompt-integration-enabled
      (ai-auto-complete-disable-task-management-prompt-integration)
    (ai-auto-complete-enable-task-management-prompt-integration)))

;; Set up task management prompt integration when this module is loaded
(when ai-auto-complete-task-management-prompt-integration-enabled
  (ai-auto-complete-task-management-prompt-setup))

(provide 'agents/task-management-prompt-integration)
;;; task-management-prompt-integration.el ends here
