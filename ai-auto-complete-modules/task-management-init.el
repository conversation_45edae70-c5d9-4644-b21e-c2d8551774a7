;;; task-management-init.el --- Initialize task management system -*- lexical-binding: t -*-

;;; Commentary:
;; This file initializes the task management system for AI Auto Complete.
;; It loads the necessary modules and sets up the task management tools.

;;; Code:

(require 'cl-lib)

;; Try to load task management modules
(condition-case err
    (progn
      (require 'tools/task-management)
      (message "Loaded task management tools"))
  (error
   (message "Warning: Failed to load task management tools: %s" (error-message-string err))
   (condition-case nil
       (load (expand-file-name "tools/task-management.el"
                              (file-name-directory (or load-file-name buffer-file-name))))
     (error
      (message "Error: Could not load task management tools")))))

;; Try to load task management prompts
(condition-case err
    (progn
      (require 'agents/task-management-prompts)
      (message "Loaded task management prompts"))
  (error
   (message "Warning: Failed to load task management prompts: %s" (error-message-string err))
   (condition-case nil
       (load (expand-file-name "agents/task-management-prompts.el"
                              (file-name-directory (or load-file-name buffer-file-name))))
     (error
      (message "Error: Could not load task management prompts")))))

;; Try to load task management prompt integration
(condition-case err
    (progn
      (require 'agents/task-management-prompt-integration)
      (message "Loaded task management prompt integration"))
  (error
   (message "Warning: Failed to load task management prompt integration: %s" (error-message-string err))
   (condition-case nil
       (load (expand-file-name "agents/task-management-prompt-integration.el"
                              (file-name-directory (or load-file-name buffer-file-name))))
     (error
      (message "Error: Could not load task management prompt integration")))))

;; Define customization group for task management
(defgroup ai-auto-complete-task-management nil
  "Customization group for AI Auto Complete task management."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-task-management-")

;; Enable/disable task management
(defcustom ai-auto-complete-task-management-enabled t
  "Whether to enable the task management system in AI Auto Complete."
  :type 'boolean
  :group 'ai-auto-complete-task-management)

;; Persistence for task plans
(defcustom ai-auto-complete-task-plans-file
  (expand-file-name "ai-auto-complete-task-plans.el" user-emacs-directory)
  "File to save task plans between sessions."
  :type 'file
  :group 'ai-auto-complete-task-management)

;; Function to save task plans
(defun ai-auto-complete-save-task-plans ()
  "Save active task plans to a file."
  (interactive)
  (when (and ai-auto-complete-task-management-enabled
             (boundp 'ai-auto-complete-active-task-plans)
             (hash-table-p ai-auto-complete-active-task-plans)
             (> (hash-table-count ai-auto-complete-active-task-plans) 0))

    (with-temp-file ai-auto-complete-task-plans-file
      (let ((print-level nil)
            (print-length nil)
            (task-plans nil))

        ;; Convert hash table to alist for saving
        (maphash (lambda (id plan)
                   (push (cons id plan) task-plans))
                 ai-auto-complete-active-task-plans)

        ;; Print the file header
        (insert ";;; ai-auto-complete-task-plans.el --- Saved task plans -*- lexical-binding: t -*-\n\n")
        (insert ";;; Commentary:\n")
        (insert ";; This file contains saved task plans for AI Auto Complete.\n")
        (insert ";; Do not edit this file manually.\n\n")
        (insert ";;; Code:\n\n")

        ;; Print the task plans
        (insert "(setq ai-auto-complete-saved-task-plans\n")
        (insert "      '")
        (prin1 task-plans (current-buffer))
        (insert ")\n\n")

        ;; Print the footer
        (insert "(provide 'ai-auto-complete-task-plans)\n")
        (insert ";;; ai-auto-complete-task-plans.el ends here\n")))

    (message "Saved %d task plans to %s"
             (hash-table-count ai-auto-complete-active-task-plans)
             ai-auto-complete-task-plans-file)))

;; Function to load task plans
(defun ai-auto-complete-load-task-plans ()
  "Load saved task plans from a file."
  (interactive)
  (when (and ai-auto-complete-task-management-enabled
             (file-exists-p ai-auto-complete-task-plans-file))

    ;; Load the file
    (condition-case err
        (load ai-auto-complete-task-plans-file)
      (error
       (message "Error loading task plans: %s" (error-message-string err))
       nil))

    ;; Check if the variable is defined
    (when (boundp 'ai-auto-complete-saved-task-plans)
      ;; Clear the current hash table
      (clrhash ai-auto-complete-active-task-plans)

      ;; Populate the hash table with saved plans
      (dolist (pair ai-auto-complete-saved-task-plans)
        (puthash (car pair) (cdr pair) ai-auto-complete-active-task-plans))

      (message "Loaded %d task plans from %s"
               (hash-table-count ai-auto-complete-active-task-plans)
               ai-auto-complete-task-plans-file))))

;; Function to initialize task management
(defun ai-auto-complete-initialize-task-management ()
  "Initialize the task management system."
  (interactive)

  ;; Enable task management
  (setq ai-auto-complete-task-management-enabled t)

  ;; Load saved task plans
  (ai-auto-complete-load-task-plans)

  ;; Enable task management prompt integration (this ensures prompts reach LLMs)
  (when (fboundp 'ai-auto-complete-enable-task-management-prompt-integration)
    (ai-auto-complete-enable-task-management-prompt-integration))

  ;; Also update agent prompts with task management instructions (for backward compatibility)
  (when (and (boundp 'ai-auto-complete-agents-enabled)
             ai-auto-complete-agents-enabled
             (fboundp 'ai-auto-complete-update-agents-with-task-prompts))
    (ai-auto-complete-update-agents-with-task-prompts))

  (message "Task management system initialized"))

;; Add hook to save task plans when Emacs exits
(add-hook 'kill-emacs-hook #'ai-auto-complete-save-task-plans)

;; Initialize task management when this module is loaded
(ai-auto-complete-initialize-task-management)

(provide 'task-management-init)
;;; task-management-init.el ends here
