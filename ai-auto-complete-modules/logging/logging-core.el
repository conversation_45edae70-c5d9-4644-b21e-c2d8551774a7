;;; logging-core.el --- Logging system for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a comprehensive logging system for the AI Auto Complete package.
;; It logs all exchanges between agents and LLMs, including requests, responses,
;; tool calls, and other relevant information.

;;; Code:

(require 'cl-lib)
(require 'json)
(require 'subr-x)
(require 'view) ; For view-buffer

;; Customization group for logging
(defgroup ai-auto-complete-logging nil
  "Customization group for AI Auto Complete logging."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-logging-")

;; Enable/disable logging
(defcustom ai-auto-complete-logging-enabled t
  "Whether to enable the logging system in AI Auto Complete."
  :type 'boolean
  :group 'ai-auto-complete-logging)

;; Enable/disable detailed raw logging
(defcustom ai-auto-complete-logging-detailed-enabled t
  "Whether to enable detailed raw logging of exact LLM requests and responses.
This logs the exact JSON payloads sent to and received from LLM APIs.
Note: This can generate large log files and may contain sensitive data."
  :type 'boolean
  :group 'ai-auto-complete-logging)

;; Log directory
(defcustom ai-auto-complete-logging-directory (expand-file-name "logs" user-emacs-directory)
  "Directory where log files are stored."
  :type 'directory
  :group 'ai-auto-complete-logging)

;; Log level
(defcustom ai-auto-complete-logging-level 'info
  "Logging level for AI Auto Complete.
Possible values are:
- 'debug: Log everything, including debug information
- 'info: Log normal operations (default)
- 'warning: Log only warnings and errors
- 'error: Log only errors"
  :type '(choice (const :tag "Debug" debug)
                (const :tag "Info" info)
                (const :tag "Warning" warning)
                (const :tag "Error" error))
  :group 'ai-auto-complete-logging)

;; Log retention period (in days)
(defcustom ai-auto-complete-logging-retention-days 14
  "Number of days to keep log files before automatic deletion."
  :type 'integer
  :group 'ai-auto-complete-logging)

;; Current session ID
(defvar ai-auto-complete-logging-current-session-id nil
  "ID of the current logging session.")

;; Session log files
(defvar ai-auto-complete-logging-session-files (make-hash-table :test 'equal)
  "Hash table mapping session IDs to their log file paths.")

;; Log levels as integers for comparison
(defvar ai-auto-complete-logging-level-values
  '((debug . 0)
    (info . 1)
    (warning . 2)
    (error . 3))
  "Numeric values for log levels, for comparison.")

;; Initialize logging system
(defun ai-auto-complete-logging-initialize ()
  "Initialize the logging system."
  (interactive)
  ;; Create log directory if it doesn't exist
  (unless (file-exists-p ai-auto-complete-logging-directory)
    (make-directory ai-auto-complete-logging-directory t))

  ;; Clean up old log files
  (ai-auto-complete-logging-cleanup-old-logs)

  (message "AI Auto Complete logging system initialized"))

;; Generate a new session ID
(defun ai-auto-complete-logging-generate-session-id ()
  "Generate a new unique session ID."
  (format "%s-%s"
          (format-time-string "%Y%m%d-%H%M%S")
          (if (fboundp 'md5)
              ;; Use md5 if available
              (substring (md5 (format "%s%s%s"
                                     (current-time-string)
                                     (random)
                                     (emacs-pid)))
                        0 8)
            ;; Fallback to a simple random string
            (format "%08x" (random (expt 16 8))))))

;; Start a new logging session
(defun ai-auto-complete-logging-start-session ()
  "Start a new logging session and return the session ID."
  (when ai-auto-complete-logging-enabled
    (let ((session-id (ai-auto-complete-logging-generate-session-id))
          (log-file (expand-file-name
                    (format "session-%s.log"
                            (format-time-string "%Y%m%d-%H%M%S"))
                    ai-auto-complete-logging-directory)))

      ;; Store the session ID and log file
      (setq ai-auto-complete-logging-current-session-id session-id)
      (puthash session-id log-file ai-auto-complete-logging-session-files)

      ;; Create the log file with initial metadata
      (with-temp-file log-file
        (insert (format "# AI Auto Complete Session Log\n"))
        (insert (format "# Session ID: %s\n" session-id))
        (insert (format "# Started: %s\n" (format-time-string "%Y-%m-%d %H:%M:%S")))
        (insert (format "# Emacs Version: %s\n" emacs-version))
        (insert (format "# System: %s\n" system-type))
        (insert "\n"))

      ;; Log session start
      (ai-auto-complete-logging-log 'info nil
                                   "Session started"
                                   (list (cons 'session_id session-id)))

      session-id)))

;; End the current logging session
(defun ai-auto-complete-logging-end-session ()
  "End the current logging session."
  (when (and ai-auto-complete-logging-enabled
             ai-auto-complete-logging-current-session-id)
    (ai-auto-complete-logging-log 'info nil
                                 "Session ended"
                                 (list (cons 'session_id ai-auto-complete-logging-current-session-id)))
    (setq ai-auto-complete-logging-current-session-id nil)))

;; Get the log file for a session
(defun ai-auto-complete-logging-get-session-file (session-id)
  "Get the log file path for SESSION-ID."
  (gethash session-id ai-auto-complete-logging-session-files))

;; Main logging function
(defun ai-auto-complete-logging-log (level agent-name message &optional data)
  "Log a message with LEVEL, AGENT-NAME, MESSAGE, and optional DATA.
LEVEL should be one of 'debug, 'info, 'warning, or 'error.
AGENT-NAME is the name of the agent involved, or nil if not applicable.
MESSAGE is the log message.
DATA is an alist of additional data to include in the log entry."
  (when (and ai-auto-complete-logging-enabled
             ai-auto-complete-logging-current-session-id
             (>= (cdr (assq level ai-auto-complete-logging-level-values))
                 (cdr (assq ai-auto-complete-logging-level ai-auto-complete-logging-level-values))))

    (let* ((log-file (ai-auto-complete-logging-get-session-file
                     ai-auto-complete-logging-current-session-id))
           (time-str (format-time-string "%Y-%m-%d %H:%M:%S"))
           (entry-alist (list (cons 'timestamp time-str)
                             (cons 'level (symbol-name level))
                             (cons 'message message)))
           log-entry)

      ;; Add agent name if provided
      (when agent-name
        (setq entry-alist (append entry-alist (list (cons 'agent agent-name)))))

      ;; Add data if provided
      (when data
        (setq entry-alist (append entry-alist (list (cons 'data data)))))

      ;; Encode the entry as JSON
      (setq log-entry (json-encode entry-alist))

      (when log-file
        (with-temp-buffer
          (when (file-exists-p log-file)
            (insert-file-contents log-file))
          (goto-char (point-max))
          (insert log-entry "\n")
          (write-region (point-min) (point-max) log-file nil 'silent))))))

;; Log LLM request
(defun ai-auto-complete-logging-log-llm-request (backend model prompt agent-name &optional history)
  "Log an LLM request to BACKEND using MODEL with PROMPT.
AGENT-NAME is the name of the agent making the request, or nil if not applicable.
HISTORY is the conversation history, if available."
  (ai-auto-complete-logging-log
   'info
   agent-name
   "LLM request"
   (list (cons 'backend (symbol-name backend))
         (cons 'model model)
         (cons 'prompt prompt)
         (cons 'history (if history (format "%S" history) "none")))))

;; Log LLM response
(defun ai-auto-complete-logging-log-llm-response (backend model response agent-name &optional contains-tool-calls)
  "Log an LLM response from BACKEND using MODEL with RESPONSE.
AGENT-NAME is the name of the agent that made the request, or nil if not applicable.
CONTAINS-TOOL-CALLS is a boolean indicating if the response contains tool calls."
  (ai-auto-complete-logging-log
   'info
   agent-name
   "LLM response"
   (list (cons 'backend (symbol-name backend))
         (cons 'model model)
         (cons 'response response)
         (cons 'contains_tool_calls (if contains-tool-calls "true" "false")))))

;; Log raw LLM request (exact JSON payload)
(defun ai-auto-complete-logging-log-llm-request-raw (backend model url headers request-data agent-name)
  "Log the exact raw LLM request data sent to BACKEND.
BACKEND is the provider symbol, MODEL is the model name, URL is the API endpoint,
HEADERS are the HTTP headers, REQUEST-DATA is the exact JSON payload,
and AGENT-NAME is the optional agent name."
  (when ai-auto-complete-logging-detailed-enabled
    (ai-auto-complete-logging-log
     'debug
     agent-name
     "LLM request (raw)"
     (list (cons 'backend (symbol-name backend))
           (cons 'model model)
           (cons 'url url)
           (cons 'headers (format "%S" headers))
           (cons 'request_data request-data)))))

;; Log raw LLM response (exact JSON response)
(defun ai-auto-complete-logging-log-llm-response-raw (backend model url response-data agent-name)
  "Log the exact raw LLM response data received from BACKEND.
BACKEND is the provider symbol, MODEL is the model name, URL is the API endpoint,
RESPONSE-DATA is the exact JSON response, and AGENT-NAME is the optional agent name."
  (when ai-auto-complete-logging-detailed-enabled
    (ai-auto-complete-logging-log
     'debug
     agent-name
     "LLM response (raw)"
     (list (cons 'backend (symbol-name backend))
           (cons 'model model)
           (cons 'url url)
           (cons 'response_data (format "%S" response-data))))))

;; Log tool call
(defun ai-auto-complete-logging-log-tool-call (tool-name params agent-name)
  "Log a tool call to TOOL-NAME with PARAMS.
AGENT-NAME is the name of the agent making the tool call, or nil if not applicable."
  (ai-auto-complete-logging-log
   'info
   agent-name
   "Tool call"
   (list (cons 'tool tool-name)
         (cons 'params (format "%S" params)))))

;; Log tool result
(defun ai-auto-complete-logging-log-tool-result (tool-name result agent-name &optional success)
  "Log a tool result from TOOL-NAME with RESULT.
AGENT-NAME is the name of the agent that made the tool call, or nil if not applicable.
SUCCESS is a boolean indicating if the tool call was successful."
  (ai-auto-complete-logging-log
   'info
   agent-name
   "Tool result"
   (list (cons 'tool tool-name)
         (cons 'result (format "%S" result))
         (cons 'success (if success "true" "false")))))

;; Clean up old log files
(defun ai-auto-complete-logging-cleanup-old-logs ()
  "Delete log files older than `ai-auto-complete-logging-retention-days`."
  (when (file-exists-p ai-auto-complete-logging-directory)
    (let* ((current-time (float-time))
           (max-age (* ai-auto-complete-logging-retention-days 24 60 60)) ; days to seconds
           (cutoff-time (- current-time max-age)))

      (dolist (file (directory-files ai-auto-complete-logging-directory t "\\.log$"))
        (when (and (file-regular-p file)
                   (< (float-time (file-attribute-modification-time
                                  (file-attributes file)))
                      cutoff-time))
          (delete-file file))))))

;; View logs for the current session
(defun ai-auto-complete-logging-view-current-session ()
  "View logs for the current session."
  (interactive)
  (if (and ai-auto-complete-logging-enabled
           ai-auto-complete-logging-current-session-id)
      (let ((log-file (ai-auto-complete-logging-get-session-file
                      ai-auto-complete-logging-current-session-id)))
        (if (and log-file (file-exists-p log-file))
            (with-current-buffer (find-file-noselect log-file)
              (view-buffer (current-buffer)))
          (message "No log file found for current session")))
    (message "No active logging session")))

;; List all log sessions
(defun ai-auto-complete-logging-list-sessions ()
  "List all available log sessions."
  (interactive)
  (if (file-exists-p ai-auto-complete-logging-directory)
      (let ((log-files (directory-files ai-auto-complete-logging-directory t "\\.log$"))
            (buffer-name "*AI Auto Complete Logs*"))
        (with-current-buffer (get-buffer-create buffer-name)
          (let ((inhibit-read-only t))
            (erase-buffer)
            (special-mode) ;; Use special-mode for the buffer

            ;; Add header
            (insert "AI Auto Complete Log Sessions\n")
            (insert "============================\n\n")

            ;; Add log entries
            (if (null log-files)
                (insert "No log sessions found.\n")
              (dolist (file log-files)
                (let ((file-name (file-name-nondirectory file))
                      (mod-time (format-time-string "%Y-%m-%d %H:%M:%S"
                                                   (file-attribute-modification-time
                                                    (file-attributes file)))))

                  (insert (format "Session: %s\n" (file-name-sans-extension file-name)))
                  (insert (format "Last modified: %s\n" mod-time))
                  (insert-button "View"
                                'action (lambda (_) (find-file file))
                                'follow-link t)
                  (insert "\n\n"))))

            (goto-char (point-min))))
        (switch-to-buffer buffer-name))
    (message "Log directory does not exist")))

;; Toggle detailed logging
(defun ai-auto-complete-logging-toggle-detailed ()
  "Toggle detailed raw logging on/off."
  (interactive)
  (setq ai-auto-complete-logging-detailed-enabled
        (not ai-auto-complete-logging-detailed-enabled))
  (message "AI Auto Complete detailed logging %s"
           (if ai-auto-complete-logging-detailed-enabled "enabled" "disabled"))

  ;; Reinstall advice to apply the change
  (when (and ai-auto-complete-logging-enabled
             (fboundp 'ai-auto-complete-logging-remove-advice)
             (fboundp 'ai-auto-complete-logging-install-advice))
    (ai-auto-complete-logging-remove-advice)
    (ai-auto-complete-logging-install-advice)))

;; Show current logging status
(defun ai-auto-complete-logging-status ()
  "Show the current status of the logging system."
  (interactive)
  (message "AI Auto Complete Logging Status:
  General logging: %s
  Detailed logging: %s
  Log level: %s
  Current session: %s
  Log directory: %s"
           (if ai-auto-complete-logging-enabled "enabled" "disabled")
           (if ai-auto-complete-logging-detailed-enabled "enabled" "disabled")
           ai-auto-complete-logging-level
           (or ai-auto-complete-logging-current-session-id "none")
           ai-auto-complete-logging-directory))

;; Set log level to debug to see detailed logging
(defun ai-auto-complete-logging-enable-debug ()
  "Enable debug logging level to see detailed raw logging."
  (interactive)
  (setq ai-auto-complete-logging-level 'debug)
  (message "Logging level set to debug - you will now see detailed raw logging"))

;; Set log level back to info
(defun ai-auto-complete-logging-disable-debug ()
  "Disable debug logging level (set back to info)."
  (interactive)
  (setq ai-auto-complete-logging-level 'info)
  (message "Logging level set to info - detailed raw logging disabled"))

(provide 'logging/logging-core)
;;; logging-core.el ends here
