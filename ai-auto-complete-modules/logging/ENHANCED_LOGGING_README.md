# Enhanced Logging System for AI Auto Complete

## Overview

The enhanced logging system has been updated to capture **exact messages exchanged between LLMs and the client**, providing complete visibility into API interactions rather than just high-level function parameters.

## What Changed

### Previous System
- Used advice on `ai-auto-complete-complete` function
- Captured only high-level parameters:
  - `context` (user input)
  - `history` (conversation history)
  - `response` (processed output)
  - `backend` and `model` names

### New System
- Uses advice on **provider functions** (`ai-auto-complete-*-provider`)
- Captures **exact JSON payloads** sent to and received from LLM APIs
- Intercepts HTTP requests using `cl-letf` to wrap the `request` function
- Provides complete audit trail of LLM interactions

## New Features

### 1. Raw Request Logging
Captures the exact JSON request sent to LLM APIs:
```json
{
  "timestamp": "2024-01-15 14:30:25",
  "level": "debug",
  "message": "LLM request (raw)",
  "agent": "coding-assistant",
  "data": {
    "backend": "openai",
    "model": "gpt-4",
    "url": "https://api.openai.com/v1/chat/completions",
    "headers": "((Content-Type . application/json) (Authorization . Bearer sk-...))",
    "request_data": "{\"model\":\"gpt-4\",\"messages\":[{\"role\":\"system\",\"content\":\"You are a helpful assistant...\"},{\"role\":\"user\",\"content\":\"Hello!\"}],\"temperature\":0.7,\"max_tokens\":1024}"
  }
}
```

### 2. Raw Response Logging
Captures the exact JSON response received from LLM APIs:
```json
{
  "timestamp": "2024-01-15 14:30:27",
  "level": "debug",
  "message": "LLM response (raw)",
  "agent": "coding-assistant",
  "data": {
    "backend": "openai",
    "model": "gpt-4",
    "url": "https://api.openai.com/v1/chat/completions",
    "response_data": "((id . chatcmpl-8abc123) (object . chat.completion) (choices . [...]))"
  }
}
```

### 3. Configuration Options
- `ai-auto-complete-logging-detailed-enabled`: Toggle detailed raw logging
- Backward compatible with existing logging settings
- Can be enabled/disabled independently of basic logging

## Implementation Details

### Files Modified
1. **`logging-core.el`**:
   - Added `ai-auto-complete-logging-log-llm-request-raw`
   - Added `ai-auto-complete-logging-log-llm-response-raw`
   - Added `ai-auto-complete-logging-detailed-enabled` configuration
   - Added utility functions for toggling and status checking

2. **`logging-integration.el`**:
   - Added advice functions for all provider functions:
     - `ai-auto-complete-logging-advice-openai-provider`
     - `ai-auto-complete-logging-advice-anthropic-provider`
     - `ai-auto-complete-logging-advice-gemini-provider`
     - `ai-auto-complete-logging-advice-openrouter-provider`
   - Updated install/remove advice functions

### Technical Approach
The new system uses `cl-letf` to temporarily override the `request` function within provider functions, allowing us to:
1. Intercept the exact data being sent to APIs
2. Wrap success callbacks to capture exact responses
3. Log both request and response data with full context

## Usage

### Enable Detailed Logging
```elisp
;; Enable detailed logging
(setq ai-auto-complete-logging-detailed-enabled t)

;; Or toggle interactively
M-x ai-auto-complete-logging-toggle-detailed
```

### Check Status
```elisp
M-x ai-auto-complete-logging-status
```

### View Logs
```elisp
;; View current session logs
M-x ai-auto-complete-logging-view-current-session

;; List all log sessions
M-x ai-auto-complete-logging-list-sessions
```

### Test the System
```elisp
;; Run test to verify detailed logging works
M-x ai-auto-complete-logging-test-detailed

;; Compare old vs new systems
M-x ai-auto-complete-logging-compare-systems

;; See example data formats
M-x ai-auto-complete-logging-show-example-data
```

## Benefits

### 1. Complete Audit Trail
- Every API interaction is logged with exact payloads
- Perfect for debugging API issues
- Enables reproduction of exact LLM requests

### 2. System Prompt Visibility
- See exactly how system prompts are formatted and sent
- Understand conversation history formatting
- Debug prompt engineering issues

### 3. Model Parameter Tracking
- Log all model parameters (temperature, max_tokens, etc.)
- Analyze parameter effects on responses
- Ensure correct configuration

### 4. API Debugging
- Capture HTTP headers and endpoints
- Log API errors with full context
- Monitor API performance and usage

### 5. Security and Compliance
- Complete record of all LLM interactions
- Audit trail for sensitive operations
- Data governance and compliance support

## Backward Compatibility

The enhanced system maintains full backward compatibility:
- Existing logging continues to work unchanged
- Old log format is preserved
- New detailed logging is additive
- Can be disabled without affecting basic logging

## Performance Considerations

- Detailed logging uses debug level (only logged when debug level is enabled)
- Can be toggled on/off as needed
- Minimal performance impact when disabled
- Log files may be larger with detailed logging enabled

## Security Notes

- API keys in headers are partially sanitized in logs
- Consider log file security when detailed logging is enabled
- Raw request/response data may contain sensitive information
- Use appropriate log retention and access controls
