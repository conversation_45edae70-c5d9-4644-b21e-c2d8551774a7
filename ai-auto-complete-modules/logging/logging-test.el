;;; logging-test.el --- Test file for the enhanced logging system -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides test functions to verify that the enhanced logging system
;; correctly captures exact LLM requests and responses from provider functions.

;;; Code:

(require 'logging/logging-core)
(require 'logging/logging-integration)

;; Simple function to check if logging is working
(defun ai-auto-complete-logging-check-status ()
  "Check the current status of the logging system and show diagnostics."
  (interactive)

  (let ((buffer-name "*Logging Status*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode)

        (insert "AI Auto Complete Logging System Status\n")
        (insert "==========================================\n\n")

        ;; Check basic logging
        (insert (format "Basic logging enabled: %s\n"
                        (if ai-auto-complete-logging-enabled "YES" "NO")))
        (insert (format "Detailed logging enabled: %s\n"
                        (if ai-auto-complete-logging-detailed-enabled "YES" "NO")))
        (insert (format "Current session ID: %s\n"
                        (or ai-auto-complete-logging-current-session-id "NONE")))
        (insert (format "Log directory: %s\n" ai-auto-complete-logging-directory))
        (insert (format "Log directory exists: %s\n"
                        (if (file-exists-p ai-auto-complete-logging-directory) "YES" "NO")))

        ;; Check if advice is installed
        (insert "\nAdvice Status:\n")
        (insert (format "ai-auto-complete-complete advice: %s\n"
                        (if (advice-member-p #'ai-auto-complete-logging-advice-complete 'ai-auto-complete-complete)
                            "INSTALLED" "NOT INSTALLED")))

        ;; Check provider advice
        (insert "\nProvider Advice Status:\n")
        (dolist (provider '(openai anthropic gemini openrouter))
          (let ((provider-func (intern (format "ai-auto-complete-%s-provider" provider)))
                (advice-func (intern (format "ai-auto-complete-logging-advice-%s-provider" provider))))
            (insert (format "%s provider function exists: %s\n"
                            provider
                            (if (fboundp provider-func) "YES" "NO")))
            (insert (format "%s provider advice: %s\n"
                            provider
                            (if (and (fboundp provider-func)
                                     (advice-member-p advice-func provider-func))
                                "INSTALLED" "NOT INSTALLED")))))

        ;; Check current backend
        (insert (format "\nCurrent backend: %s\n" (ai-auto-complete-get-current-backend)))

        ;; Check log level
        (insert (format "Log level: %s\n" ai-auto-complete-logging-level))

        ;; Check log files
        (insert "\nLog Files:\n")
        (if (file-exists-p ai-auto-complete-logging-directory)
            (let ((log-files (directory-files ai-auto-complete-logging-directory t "\\.log$")))
              (if log-files
                  (dolist (file log-files)
                    (insert (format "- %s (%s)\n"
                                    (file-name-nondirectory file)
                                    (format-time-string "%Y-%m-%d %H:%M:%S"
                                                       (file-attribute-modification-time
                                                        (file-attributes file))))))
                (insert "No log files found.\n")))
          (insert "Log directory does not exist.\n"))

        ;; Instructions
        (insert "\nInstructions:\n")
        (insert "1. Run M-x ai-auto-complete-logging-test-detailed to test the system\n")
        (insert "2. Use the menu: AI Auto Complete > Logging > View Current Session Log\n")
        (insert "3. Check log files in the directory above\n")
        (insert "4. Check *Messages* buffer for debug output\n")

        (goto-char (point-min)))
      (switch-to-buffer buffer-name))))

;; Test function to demonstrate the new logging capabilities
(defun ai-auto-complete-logging-test-detailed ()
  "Test the detailed logging system by making a real LLM request.
This function demonstrates how the new logging system captures exact
JSON requests and responses from LLM providers."
  (interactive)

  ;; Ensure logging is enabled
  (unless ai-auto-complete-logging-enabled
    (setq ai-auto-complete-logging-enabled t))

  ;; Ensure detailed logging is enabled
  (unless ai-auto-complete-logging-detailed-enabled
    (setq ai-auto-complete-logging-detailed-enabled t))

  ;; Set log level to debug to see raw logging
  (setq ai-auto-complete-logging-level 'debug)

  ;; Initialize logging if not already done
  (ai-auto-complete-logging-initialize)

  ;; Install advice
  (ai-auto-complete-logging-install-advice)

  ;; Start a new session
  (ai-auto-complete-logging-start-session)

  (message "Testing enhanced logging system...")
  (message "Current session ID: %s" ai-auto-complete-logging-current-session-id)
  (message "Detailed logging enabled: %s" ai-auto-complete-logging-detailed-enabled)

  ;; Log a test message to verify basic logging works
  (ai-auto-complete-logging-log 'info "test-agent" "Testing enhanced logging system"
                               '((test . "basic-logging")))

  ;; Test the raw logging functions directly first
  (ai-auto-complete-logging-log-llm-request-raw
   'openai
   "gpt-4"
   "https://api.openai.com/v1/chat/completions"
   '(("Content-Type" . "application/json") ("Authorization" . "Bearer test-key"))
   "{\"model\":\"gpt-4\",\"messages\":[{\"role\":\"system\",\"content\":\"You are a helpful assistant.\"},{\"role\":\"user\",\"content\":\"Hello, world!\"}],\"temperature\":0.7,\"max_tokens\":1024}"
   "test-agent")

  (ai-auto-complete-logging-log-llm-response-raw
   'openai
   "gpt-4"
   "https://api.openai.com/v1/chat/completions"
   '((id . "chatcmpl-test") (object . "chat.completion") (created . 1234567890)
     (model . "gpt-4") (choices . [((index . 0) (message . ((role . "assistant")
     (content . "Hello! How can I help you today?"))) (finish_reason . "stop"))]))
   "test-agent")

  (message "Direct logging test completed.")
  (message "Log file location: %s"
           (ai-auto-complete-logging-get-session-file ai-auto-complete-logging-current-session-id))

  ;; Now test with a real LLM request if API key is available
  (let ((backend (ai-auto-complete-get-current-backend)))
    (message "Testing with real LLM request using backend: %s" backend)

    ;; Check if we have an API key for the current backend
    (let ((has-api-key
           (cond
            ((eq backend 'openai) (and (boundp 'ai-auto-complete-openai-api-key)
                                       (not (string-empty-p ai-auto-complete-openai-api-key))))
            ((eq backend 'anthropic) (and (boundp 'ai-auto-complete-anthropic-api-key)
                                          (not (string-empty-p ai-auto-complete-anthropic-api-key))))
            ((eq backend 'gemini) (and (boundp 'ai-auto-complete-gemini-api-key)
                                       (not (string-empty-p ai-auto-complete-gemini-api-key))))
            ((eq backend 'openrouter) (and (boundp 'ai-auto-complete-openrouter-api-key)
                                           (not (string-empty-p ai-auto-complete-openrouter-api-key))))
            (t nil))))

      (if has-api-key
          (progn
            (message "API key found for %s. Making real LLM request to test logging..." backend)
            ;; Make a real LLM request
            (ai-auto-complete-complete
             backend
             "Hello! This is a test message to verify the enhanced logging system is working correctly."
             '() ;; No history
             (lambda (response)
               (message "LLM Response received: %s" (substring response 0 (min 100 (length response))))
               (message "Check the log file to see both old-style and new detailed logging!")
               (message "Use M-x ai-auto-complete-log-viewer-view-current-session to view logs"))
             "claude-code"))
        (progn
          (message "No API key found for %s. Skipping real LLM request test." backend)
          (message "To test with real requests, set up an API key for your preferred backend.")
          (message "For now, check the log file to see the direct logging test results.")))))

  (message "Enhanced logging test completed. Use the menu: AI Auto Complete > Logging > View Current Session Log"))

;; Simple debug function to test if provider advice is working
(defun ai-auto-complete-logging-debug-provider-advice ()
  "Debug function to test if provider advice is being called."
  (interactive)

  (message "=== DEBUGGING PROVIDER ADVICE ===")

  ;; Check current settings
  (message "Detailed logging enabled: %s" ai-auto-complete-logging-detailed-enabled)
  (message "Log level: %s" ai-auto-complete-logging-level)
  (message "Current backend: %s" (ai-auto-complete-get-current-backend))

  ;; Enable everything
  (setq ai-auto-complete-logging-enabled t)
  (setq ai-auto-complete-logging-detailed-enabled t)
  (setq ai-auto-complete-logging-level 'debug)

  ;; Start session
  (ai-auto-complete-logging-start-session)

  ;; Install advice
  (ai-auto-complete-logging-install-advice)

  ;; Check if advice is installed
  (let ((backend (ai-auto-complete-get-current-backend)))
    (let ((provider-func (intern (format "ai-auto-complete-%s-provider" backend)))
          (advice-func (intern (format "ai-auto-complete-logging-advice-%s-provider" backend))))
      (message "Provider function %s exists: %s" provider-func (fboundp provider-func))
      (message "Advice function %s exists: %s" advice-func (fboundp advice-func))
      (message "Advice installed: %s"
               (if (and (fboundp provider-func)
                        (advice-member-p advice-func provider-func))
                   "YES" "NO"))))

  (message "Now making a test LLM request...")

  ;; Make a simple request
  (ai-auto-complete-complete
   (ai-auto-complete-get-current-backend)
   "Test message for debugging logging"
   '()
   (lambda (response)
     (message "=== LLM RESPONSE RECEIVED ===")
     (message "Response: %s" (substring response 0 (min 100 (length response))))
     (message "Check *Messages* buffer for debug output")
     (message "Check log file for raw logging entries"))
   "debug-test-agent")

  (message "=== DEBUG TEST INITIATED ===")
  (message "Watch *Messages* buffer for debug output from provider advice")))

;; Function to show the difference between old and new logging
(defun ai-auto-complete-logging-compare-systems ()
  "Compare the old and new logging systems by showing what each captures."
  (interactive)

  (let ((buffer-name "*Logging System Comparison*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode)

        (insert "AI Auto Complete Logging System Comparison\n")
        (insert "=============================================\n\n")

        (insert "OLD SYSTEM (ai-auto-complete-complete advice):\n")
        (insert "----------------------------------------------\n")
        (insert "• Captures high-level function parameters:\n")
        (insert "  - backend (e.g., 'openai')\n")
        (insert "  - context (user input text)\n")
        (insert "  - history (conversation history)\n")
        (insert "  - response (processed LLM output)\n")
        (insert "  - agent-name (if applicable)\n\n")

        (insert "• LIMITATIONS:\n")
        (insert "  - Does NOT capture exact JSON sent to LLM API\n")
        (insert "  - Does NOT capture exact JSON received from LLM API\n")
        (insert "  - Does NOT capture system prompts as sent\n")
        (insert "  - Does NOT capture model parameters (temperature, etc.)\n")
        (insert "  - Does NOT capture HTTP headers or API endpoints\n\n")

        (insert "NEW SYSTEM (provider function advice):\n")
        (insert "--------------------------------------\n")
        (insert "• Captures everything from the old system PLUS:\n")
        (insert "  - Exact JSON request payload sent to LLM API\n")
        (insert "  - Exact JSON response received from LLM API\n")
        (insert "  - Complete message arrays with system prompts\n")
        (insert "  - All model parameters (temperature, max_tokens, etc.)\n")
        (insert "  - HTTP headers including API keys (sanitized)\n")
        (insert "  - API endpoints and URLs\n")
        (insert "  - Raw conversation history as formatted for LLM\n\n")

        (insert "• BENEFITS:\n")
        (insert "  - Complete audit trail of LLM interactions\n")
        (insert "  - Debugging capability for API issues\n")
        (insert "  - Exact reproduction of LLM requests\n")
        (insert "  - Understanding of how prompts are formatted\n")
        (insert "  - Analysis of model parameter effects\n\n")

        (insert "• CONFIGURATION:\n")
        (insert "  - ai-auto-complete-logging-enabled: %s\n"
                (if ai-auto-complete-logging-enabled "enabled" "disabled"))
        (insert "  - ai-auto-complete-logging-detailed-enabled: %s\n"
                (if ai-auto-complete-logging-detailed-enabled "enabled" "disabled"))
        (insert "  - Current session: %s\n"
                (or ai-auto-complete-logging-current-session-id "none"))

        (goto-char (point-min)))
      (switch-to-buffer buffer-name))))

;; Function to demonstrate the exact data captured
(defun ai-auto-complete-logging-show-example-data ()
  "Show examples of the exact data captured by the new logging system."
  (interactive)

  (let ((buffer-name "*Logging Data Examples*"))
    (with-current-buffer (get-buffer-create buffer-name)
      (let ((inhibit-read-only t))
        (erase-buffer)
        (special-mode)

        (insert "Examples of Data Captured by Enhanced Logging\n")
        (insert "=============================================\n\n")

        (insert "1. RAW REQUEST DATA (what gets sent to LLM API):\n")
        (insert "-----------------------------------------------\n")
        (insert "{\n")
        (insert "  \"timestamp\": \"2024-01-15 14:30:25\",\n")
        (insert "  \"level\": \"debug\",\n")
        (insert "  \"message\": \"LLM request (raw)\",\n")
        (insert "  \"agent\": \"coding-assistant\",\n")
        (insert "  \"data\": {\n")
        (insert "    \"backend\": \"openai\",\n")
        (insert "    \"model\": \"gpt-4\",\n")
        (insert "    \"url\": \"https://api.openai.com/v1/chat/completions\",\n")
        (insert "    \"headers\": \"((Content-Type . application/json) (Authorization . Bearer sk-...))\",\n")
        (insert "    \"request_data\": \"{\\\"model\\\":\\\"gpt-4\\\",\\\"messages\\\":[{\\\"role\\\":\\\"system\\\",\\\"content\\\":\\\"You are a helpful coding assistant...\\\"},{\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"Write a Python function to sort a list\\\"}],\\\"temperature\\\":0.7,\\\"max_tokens\\\":1024,\\\"top_p\\\":0.9}\"\n")
        (insert "  }\n")
        (insert "}\n\n")

        (insert "2. RAW RESPONSE DATA (what gets received from LLM API):\n")
        (insert "------------------------------------------------------\n")
        (insert "{\n")
        (insert "  \"timestamp\": \"2024-01-15 14:30:27\",\n")
        (insert "  \"level\": \"debug\",\n")
        (insert "  \"message\": \"LLM response (raw)\",\n")
        (insert "  \"agent\": \"coding-assistant\",\n")
        (insert "  \"data\": {\n")
        (insert "    \"backend\": \"openai\",\n")
        (insert "    \"model\": \"gpt-4\",\n")
        (insert "    \"url\": \"https://api.openai.com/v1/chat/completions\",\n")
        (insert "    \"response_data\": \"((id . chatcmpl-8abc123) (object . chat.completion) (created . 1705327827) (model . gpt-4-0613) (choices . [((index . 0) (message . ((role . assistant) (content . def sort_list(lst):\\n    return sorted(lst)))) (finish_reason . stop))]) (usage . ((prompt_tokens . 45) (completion_tokens . 12) (total_tokens . 57))))\"\n")
        (insert "  }\n")
        (insert "}\n\n")

        (insert "3. COMPARISON WITH OLD SYSTEM:\n")
        (insert "------------------------------\n")
        (insert "Old system would only capture:\n")
        (insert "- context: \"Write a Python function to sort a list\"\n")
        (insert "- response: \"def sort_list(lst):\\n    return sorted(lst)\"\n\n")

        (insert "New system captures the COMPLETE interaction including:\n")
        (insert "- Exact system prompt sent to LLM\n")
        (insert "- All model parameters (temperature, max_tokens, etc.)\n")
        (insert "- Complete API response with metadata\n")
        (insert "- Token usage information\n")
        (insert "- API timing and performance data\n")

        (goto-char (point-min)))
      (switch-to-buffer buffer-name))))

(provide 'logging/logging-test)
;;; logging-test.el ends here
