# Debug: Why Raw Logging Isn't Working

## The Problem
You're seeing the old logging system output but not the new detailed raw logging. Here's why and how to fix it.

## Root Cause: Log Level Filtering

The **main issue** is that the raw logging functions use `debug` level, but the default log level is `info`:

```elisp
;; Raw logging uses debug level
(ai-auto-complete-logging-log 'debug agent-name "LLM request (raw)" ...)

;; But default log level is info
(defcustom ai-auto-complete-logging-level 'info ...)
```

Since `debug` (0) < `info` (1), the debug messages are filtered out by the logging system.

## Quick Fix

**Set the log level to debug:**

```elisp
M-x ai-auto-complete-logging-enable-debug
```

Or manually:
```elisp
(setq ai-auto-complete-logging-level 'debug)
```

## Step-by-Step Debugging

### 1. Check System Status
```elisp
M-x ai-auto-complete-logging-check-status
```

Look for:
- Detailed logging enabled: YES
- Log level: **debug** (not info)
- Provider advice: INSTALLED

### 2. Enable Debug Mode
```elisp
M-x ai-auto-complete-logging-enable-debug
```

### 3. Test Provider Advice
```elisp
M-x ai-auto-complete-logging-debug-provider-advice
```

This will:
- Enable all logging settings
- Install advice
- Make a test LLM request
- Show debug output in *Messages* buffer

### 4. Check for Debug Messages

In the *Messages* buffer, you should see:
```
[DEBUG-LOGGING] Anthropic provider advice triggered: model="claude-3-sonnet-20240229", agent-name="debug-test-agent", detailed-enabled=t
[DEBUG-LOGGING] Anthropic request: url="https://api.anthropic.com/v1/messages", headers=..., data=...
[DEBUG-LOGGING] Anthropic response: url="https://api.anthropic.com/v1/messages", response-data=...
```

### 5. Check Log File

In the log file, you should see both:

**Old-style logging (info level):**
```json
{
  "timestamp": "2024-01-15 14:30:25",
  "level": "info",
  "message": "LLM request",
  "agent": "debug-test-agent",
  "data": {
    "backend": "anthropic",
    "model": "claude-3-sonnet-20240229",
    "prompt": "Test message for debugging logging"
  }
}
```

**New raw logging (debug level):**
```json
{
  "timestamp": "2024-01-15 14:30:25", 
  "level": "debug",
  "message": "LLM request (raw)",
  "agent": "debug-test-agent",
  "data": {
    "backend": "anthropic",
    "model": "claude-3-sonnet-20240229",
    "url": "https://api.anthropic.com/v1/messages",
    "headers": "((Content-Type . application/json) (X-API-Key . sk-ant-...))",
    "request_data": "{\"model\":\"claude-3-sonnet-20240229\",\"system\":\"You are a helpful assistant...\",\"messages\":[{\"role\":\"user\",\"content\":\"Test message for debugging logging\"}],\"temperature\":0.7,\"max_tokens\":1024}"
  }
}
```

## Troubleshooting

### If you don't see provider advice debug messages:

1. **Check if advice is installed:**
   ```elisp
   M-x ai-auto-complete-logging-check-status
   ```

2. **Manually install advice:**
   ```elisp
   (ai-auto-complete-logging-install-advice)
   ```

3. **Check if detailed logging is enabled:**
   ```elisp
   (setq ai-auto-complete-logging-detailed-enabled t)
   ```

### If you see debug messages but no raw log entries:

1. **Check log level:**
   ```elisp
   (setq ai-auto-complete-logging-level 'debug)
   ```

2. **Check if session is active:**
   ```elisp
   M-x ai-auto-complete-logging-start-session
   ```

### If provider advice isn't being called:

1. **Check if provider functions exist:**
   ```elisp
   (fboundp 'ai-auto-complete-anthropic-provider)  ; Should be t
   ```

2. **Check current backend:**
   ```elisp
   (ai-auto-complete-get-current-backend)  ; Should match your provider
   ```

## Expected Behavior

When working correctly, you should see:

1. **In *Messages* buffer:** Debug messages showing provider advice is triggered
2. **In log file:** Both old-style (info) and new raw (debug) logging entries
3. **Raw entries contain:** Exact JSON request/response data, headers, URLs

## Functions to Use

- `ai-auto-complete-logging-enable-debug` - Enable debug logging
- `ai-auto-complete-logging-debug-provider-advice` - Test provider advice
- `ai-auto-complete-logging-check-status` - Check system status
- `ai-auto-complete-logging-test-detailed` - Full test with real LLM request

## Summary

The raw logging **is working**, but it was being filtered out by the log level. Setting the log level to debug will show you the exact JSON requests and responses being exchanged with LLM APIs.
