;;; shared-context.el --- Shared context management for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file contains functions for managing shared context across all AI auto complete modes.
;; It allows adding text files, images, and buffer content as context for all AI interactions.

;;; Code:

(require 'cl-lib)

;; Define string-empty-p if it's not available (normally provided by 's package)
(unless (fboundp 'string-empty-p)
  (defun string-empty-p (string)
    "Return t if STRING is empty or nil."
    (or (null string) (string= string ""))))

;; Variables for shared context
(defcustom ai-auto-complete-context-buffer-name "*AI Auto Complete Context*"
  "Name of the buffer used to store shared context for AI auto complete."
  :type 'string
  :group 'ai-auto-complete-context)

(defvar ai-auto-complete-context-files nil
  "List of files added as context to the AI auto complete.
Each element is a plist with properties :path, :type, :content, and :description.")

(defvar ai-auto-complete-context-images nil
  "List of images added as context to the AI auto complete.
Each element is a plist with properties :path, :data, and :description.")

(defcustom ai-auto-complete-max-file-size 1000000
  "Maximum size of files that can be added to context in bytes."
  :type 'integer
  :group 'ai-auto-complete-context)

;; Define context file extensions
(defcustom ai-auto-complete-context-file-extensions
  '(".txt" ".md" ".org" ".el" ".py" ".js" ".html" ".css" ".json" ".c" ".cpp" ".h" ".java" ".rb" ".php")
  "List of file extensions that can be added as text context."
  :type '(repeat string)
  :group 'ai-auto-complete-context)

(defcustom ai-auto-complete-image-file-extensions
  '(".png" ".jpg" ".jpeg" ".gif" ".webp")
  "List of file extensions that can be added as image context."
  :type '(repeat string)
  :group 'ai-auto-complete-context)

(defcustom ai-auto-complete-context-strategy 'manual
  "Strategy for managing context."
  :type '(choice (const :tag "Manual only" manual)
                 (const :tag "Auto-add imports" auto-imports)
                 (const :tag "Auto-add project files" project)
                 (const :tag "Auto-add directory" directory))
  :group 'ai-auto-complete-context)

(defcustom ai-auto-complete-auto-context nil
  "Whether to automatically add relevant files as context based on imports/includes."
  :type 'boolean
  :group 'ai-auto-complete-context)

(defcustom ai-auto-complete-context-max-items 20
  "Maximum number of context items to include."
  :type 'integer
  :group 'ai-auto-complete-context)

(defun ai-auto-complete-add-file-to-context ()
  "Add a file as context to the AI auto complete."
  (interactive)
  (let* ((file (read-file-name "Add file to context: "))
         (file-size (file-attribute-size (file-attributes file)))
         (ext (downcase (file-name-extension file t)))
         (description (read-string "Description (optional): ")))
    (cond
     ((> file-size ai-auto-complete-max-file-size)
      (message "File too large (max size: %d bytes)" ai-auto-complete-max-file-size))
     ((member ext ai-auto-complete-context-file-extensions)
      (ai-auto-complete--add-text-file-to-context file description))
     ((member ext ai-auto-complete-image-file-extensions)
      (ai-auto-complete--add-image-file-to-context file description))
     (t (when (y-or-n-p (format "Unrecognized file type %s. Try as text file?" ext))
          (ai-auto-complete--add-text-file-to-context file description))))))

(defun ai-auto-complete--add-text-file-to-context (file description)
  "Add FILE as text context with DESCRIPTION."
  (with-temp-buffer
    (insert-file-contents file)
    (let* ((content (buffer-string))
           (file-type (or (and (string-match "\\.[^.]+$" file)
                              (substring (match-string 0 file) 1))
                         "txt"))
           (context-item (list :path file
                              :type file-type
                              :content content
                              :description (if (string-empty-p description)
                                              (file-name-nondirectory file)
                                            description)
                              :context-type 'file)))
      (push context-item ai-auto-complete-context-files)
      (message "Added file '%s' to context" (file-name-nondirectory file))

      ;; Check if chat buffer exists and display function is available
      (when (and (get-buffer ai-auto-complete-chat-buffer-name)
                 (fboundp 'ai-auto-complete-chat-display-context-update))
        (ai-auto-complete-chat-display-context-update file "added" "file")))))

(defun ai-auto-complete--add-image-file-to-context (file description)
  "Add FILE as image context with DESCRIPTION."
  (with-temp-buffer
    (insert-file-contents-literally file)
    (let* ((data (base64-encode-string (buffer-string) t))
           (context-item (list :path file
                              :data data
                              :description (if (string-empty-p description)
                                              (file-name-nondirectory file)
                                            description)
                              :context-type 'image)))
      (push context-item ai-auto-complete-context-images)
      (message "Added image '%s' to context" (file-name-nondirectory file))

      ;; Check if chat buffer exists and display function is available
      (when (and (get-buffer ai-auto-complete-chat-buffer-name)
                 (fboundp 'ai-auto-complete-chat-display-context-update))
        (ai-auto-complete-chat-display-context-update file "added" "image")))))

(defun ai-auto-complete-add-buffer-to-context ()
  "Add current buffer content as context to the AI auto complete."
  (interactive)
  (let* ((buffer (current-buffer))
         (buffer-name (buffer-name buffer))
         (file-name (buffer-file-name buffer))
         (content (with-current-buffer buffer
                    (buffer-substring-no-properties (point-min) (point-max))))
         (description (read-string "Description (optional): "))
         (file-type (if file-name
                        (file-name-extension file-name)
                      "txt"))
         (context-item (list :buffer buffer-name
                            :type file-type
                            :content content
                            :description (if (string-empty-p description)
                                            buffer-name
                                          description)
                            :context-type 'buffer)))
    (push context-item ai-auto-complete-context-files)
    (message "Added buffer '%s' to context" buffer-name)

    ;; Check if chat buffer exists and display function is available
    (when (and (get-buffer ai-auto-complete-chat-buffer-name)
               (fboundp 'ai-auto-complete-chat-display-context-update))
      (ai-auto-complete-chat-display-context-update buffer-name "added" "buffer"))))

(defun ai-auto-complete-list-context ()
  "Display a list of all context items."
  (interactive)
  (with-current-buffer (get-buffer-create ai-auto-complete-context-buffer-name)
    (let ((inhibit-read-only t))
      (erase-buffer)
      (insert "AI Auto Complete Context Items\n")
      (insert "============================\n\n")

      ;; List text files and buffer content
      (if ai-auto-complete-context-files
          (progn
            (insert "Text Context:\n")
            (insert "------------\n")
            (dolist (item ai-auto-complete-context-files)
              (let ((desc (plist-get item :description))
                    (type (plist-get item :type))
                    (context-type (plist-get item :context-type)))
                (insert (format "[%d] %s (%s, %s)\n"
                                (+ (cl-position item ai-auto-complete-context-files) 1)
                                desc type context-type)))))
        (insert "No text context items.\n"))

      (insert "\n")

      ;; List images
      (if ai-auto-complete-context-images
          (progn
            (insert "Image Context:\n")
            (insert "-------------\n")
            (dolist (item ai-auto-complete-context-images)
              (let ((desc (plist-get item :description)))
                (insert (format "[%d] %s (image)\n"
                                (+ (cl-position item ai-auto-complete-context-images) 1)
                                desc)))))
        (insert "No image context items.\n")))

    (special-mode)
    (switch-to-buffer (current-buffer)))
  (message "Context list displayed in buffer %s" ai-auto-complete-context-buffer-name))

(defun ai-auto-complete-remove-from-context ()
  "Remove an item from context."
  (interactive)
  (let* ((text-items (mapcar (lambda (item)
                               (cons (format "%s (%s)"
                                             (plist-get item :description)
                                             (plist-get item :context-type))
                                     (cons 'text item)))
                             ai-auto-complete-context-files))
         (image-items (mapcar (lambda (item)
                                (cons (format "%s (image)"
                                              (plist-get item :description))
                                      (cons 'image item)))
                              ai-auto-complete-context-images))
         (all-items (append text-items image-items))
         selected-item)

    (if all-items
        (progn
          (setq selected-item (completing-read "Remove context item: " all-items nil t))
          (let* ((item-data (cdr (assoc selected-item all-items)))
                 (type (car item-data))
                 (item (cdr item-data))
                 (desc (plist-get item :description))
                 (context-type (plist-get item :context-type)))
            (if (eq type 'text)
                (progn
                  (setq ai-auto-complete-context-files
                        (delq item ai-auto-complete-context-files))
                  (message "Removed text context item: %s" selected-item)

                  ;; Check if chat buffer exists and display function is available
                  (when (and (get-buffer ai-auto-complete-chat-buffer-name)
                             (fboundp 'ai-auto-complete-chat-display-context-update))
                    (ai-auto-complete-chat-display-context-update desc "removed" (symbol-name context-type))))

              ;; Handle image removal
              (setq ai-auto-complete-context-images
                    (delq item ai-auto-complete-context-images))
              (message "Removed image context item: %s" selected-item)

              ;; Check if chat buffer exists and display function is available
              (when (and (get-buffer ai-auto-complete-chat-buffer-name)
                         (fboundp 'ai-auto-complete-chat-display-context-update))
                (ai-auto-complete-chat-display-context-update desc "removed" "image")))))
      (message "No context items to remove"))))

(defun ai-auto-complete-clear-context ()
  "Clear all context items."
  (interactive)
  (when (y-or-n-p "Clear all context items? ")
    (setq ai-auto-complete-context-files nil
          ai-auto-complete-context-images nil)
    (message "All context items cleared")

    ;; Check if chat buffer exists and display function is available
    (when (and (get-buffer ai-auto-complete-chat-buffer-name)
               (fboundp 'ai-auto-complete-chat-display-context-update))
      (ai-auto-complete-chat-display-context-update "all items" "cleared" "context"))))

(defun ai-auto-complete-get-context-for-prompt ()
  "Get the context as a formatted string for inclusion in prompts."
  (let ((context-text ""))
    ;; Add file context information
    (when (or ai-auto-complete-context-files ai-auto-complete-context-images)
      (setq context-text "\n\nContext Files:\n")

      ;; Add text files
      (dolist (file ai-auto-complete-context-files)
        (setq context-text
              (concat context-text
                      (format "--- %s (%s) ---\n%s\n\n"
                              (plist-get file :description)
                              (plist-get file :type)
                              (plist-get file :content)))))

      ;; For image context, we'll add a note but the actual images will be sent separately
      ;; in the API-specific functions
      (when ai-auto-complete-context-images
        (setq context-text
              (concat context-text
                      (format "--- Images (%d) ---\n"
                              (length ai-auto-complete-context-images))
                      (mapconcat
                       (lambda (img)
                         (format "Image: %s\n" (plist-get img :description)))
                       ai-auto-complete-context-images
                       "")))))
    context-text))

(provide 'shared-context)
;;; shared-context.el ends here
