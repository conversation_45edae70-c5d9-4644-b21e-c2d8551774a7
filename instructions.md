Just like our awesome tools feature, you know what would make this app even more awesome? An agentic system based on agents. What are agents? well, we already have a kind of agent in our app. It is the CHAT-AGENT. our current app works like a CHAT-AGENT that has access tools and does things on behalf of the USER (displayed with role 'You' in the current app) who asks it to do things from the chat interface. It has conversational attitude and it does what it is asked and tries to be helpful in a conversational way. That's a CHAT-AGENT. So, what are agents in general? They are like special profiles of LLMs with specific prompts and specific tools access in order to be helpful in a specfic way. They are specific, rather than general. 
What do we need to define an agent? Agents are defined by name, description, system-prompts and tools access. 
- We need the an appropriate NAME to uniquely identify it.
 -An appropriate DESCRIPTION to describe aptly what it does, what input it expects and what output it produces, waht kind of task it can be asked to acoomplish and what instructions it can be given. 
- An appropriate system-prompt for the LLMs. Lets call it AGENT-PROMPT which provides appropriate instructions to LLMs in order to fine-tune to serve the purpose of the agent. 
- Finally, tool access, TOOLS-ACESSED. An agent can have access to various tools with which it can accomplish various tasks. Right tools must be provided to the right agents for it to be useful.

# How to impplement and integrate the agentic feature in the current codebase
We must be able to easily create, remove and modify agents for the app. All the agents in the system must be aware of themselves or there must
be a mechanism which allows us to choose which agents are aware of which other agents. Agents should be able to assign tasks to each other in additon to the USER being able to assign task to any agents. 

- Lets use a simple '@' or '@agent' strings  meachanism to assign task to agents. USER can assign tasks to user by passing message with @agent <agent-name> format. Other agetns should be able to do the same thing to each other. All the agents must be actively listening for messages directed towards them.

# UI Feature
- The current chat-inteface is being used to communicate between USER and LLMs labeled as 'AI' in the chat interface. We will need to refactor the UI so that the chat-inteface acts like 'message board' or 'leader board' where communication between various agents and user and among agents themselves are displayed. Different agents can 'enter chat' and post their messages and go back and forth and it will be displayed for all to see. Each agent is a chat-participant. 

Lets implement this feature carefully wihtout breaking existing functionaltiy. After implementing the codes for agentic system that allows us to create agents, create the first 'chat-agent' yourself as an example. Feel free to ask me how to proceed if you are not sure about anything. 