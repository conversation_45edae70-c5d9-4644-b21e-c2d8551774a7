# AI Auto Complete (Unified)

This is the unified version of AI Auto Complete, with a simplified architecture that makes it easier to maintain and extend.

## New Structure

The new structure organizes the code into logical modules:

```
ai-auto-complete-modules/
├── core/
│   └── backend.el         # Core backend interface
├── providers/
│   ├── gemini.el          # Gemini provider implementation
│   ├── openai.el          # OpenAI provider implementation
│   ├── anthropic.el       # Anthropic provider implementation
│   └── openrouter.el      # OpenRouter provider implementation
├── customization/
│   ├── prompts.el         # Prompt customization
│   ├── models.el          # Model customization
│   └── modes.el           # Mode-specific customization
└── tools/
    ├── tools-core.el      # Core tool functionality
    ├── standard-tools.el  # Standard tool implementations
    ├── tools-ui.el        # Tool UI components
    └── tools-integration.el # Tool integration with backends
```

## Key Improvements

1. **Unified Backend Interface**: All backend providers implement a common interface, making it easier to add new providers.

2. **Simplified Mode-Specific Logic**: Mode-specific model selection is now handled in a more straightforward way.

3. **Integrated Prompt Customization**: Prompt customization is now directly integrated into the backend interface.

4. **Improved Tool Integration**: Tool functionality is now more modular and easier to extend.

5. **Consistent Naming Conventions**: All files and functions follow consistent naming conventions.

## Migration

To migrate from the old structure to the new unified structure:

1. Run the migration script:
   ```
   M-x load-file RET migrate-to-unified.el RET
   M-x ai-auto-complete-migrate-to-unified RET
   ```

2. Restart Emacs.

3. If you have customizations in your init.el file, you may need to update them to use the new module paths.

## Customization

The customization interface remains the same, but the underlying implementation has been improved. You can still use:

```
M-x ai-auto-complete-customize
```

to access all customization options.

## Mode-Specific Models

The new structure still supports mode-specific models, allowing you to use different backends and models for code completion, text completion, and chat. To enable mode-specific models:

```
M-x ai-auto-complete-enable-mode-specific-models
```

## Tools

The tool functionality has been improved and is now more modular. To enable tools:

```
M-x ai-auto-complete-tools-toggle
```

## Adding New Providers

To add a new provider:

1. Create a new file in the `providers` directory.
2. Implement the provider function that takes `context`, `callback`, `model`, and `system-prompt` parameters.
3. Register the provider using `ai-auto-complete-register-provider`.

## Adding New Tools

To add a new tool:

1. Create a tool function that takes a parameters alist.
2. Register the tool using `ai-auto-complete-register-tool`.

## Troubleshooting

If you encounter any issues with the new structure:

1. Check the `*Messages*` buffer for error messages.
2. Try loading the backup files from the `backup` directory.
3. Report the issue with details about the error and your configuration.
