;;; ai-auto-complete-unified.el --- AI-assisted chat interface -*- lexical-binding: t -*-

;;; Commentary:
;; This package provides an AI-assisted chat interface using various
;; AI backends including Gemini, OpenAI, Anthropic, and OpenRouter.
;; This is a simplified version focused only on chat functionality.

;;; Code:

(require 'cl-lib)
(require 'json)
(require 's)
(require 'dash)
(require 'request)

;;; Load modules

;; Add the module directory to load-path
(add-to-list 'load-path (file-name-directory (or load-file-name buffer-file-name)))
(add-to-list 'load-path (expand-file-name "ai-auto-complete-modules"
                        (file-name-directory (or load-file-name buffer-file-name))))

;; Force loading from .el files during development (optional)
;(setq load-prefer-newer t)
;(message "Load path: %s" load-path)

;; Load core modules
(require 'core/backend)
;(message "Loaded core/backend")
(require 'utils)
(message "Loaded utils")
(require 'shared-context)
;(message "Loaded shared-context")
(require 'context)
;(message "Loaded context")

;; Load provider modules
(require 'providers/gemini)
;(message "Loaded providers/gemini")
(require 'providers/openai)
;(message "Loaded providers/openai")
(require 'providers/anthropic)
;(message "Loaded providers/anthropic")
(require 'providers/openrouter)
;(message "Loaded providers/openrouter")

;; Load customization modules
(require 'customization/prompts)
;(message "Loaded customization/prompts")
(require 'customization/models)
;(message "Loaded customization/models")
(require 'customization/model-management)
;(message "Loaded customization/model-management")
(require 'customization/model-management-ui)
;(message "Loaded customization/model-management-ui")
(require 'customization/mcp-customization)
;(message "Loaded customization/mcp-customization")
(require 'customization/a2a-customization)
;(message "Loaded customization/a2a-customization")

;; Initialize model configuration
(ai-auto-complete-init-model-config)

;; Update model choices from configuration
(ai-auto-complete-update-model-choices)

;; Load feature modules
(require 'chat-mode)
;(message "Loaded chat-mode")
;(load "chat-detect-file-request")
;(message "Loaded chat-detect-file-request")
(require 'chat)
;(message "Loaded chat")

;; Load tool modules
(require 'tools/tools-core)
;(message "Loaded tools/tools-core")
(require 'tools/standard-tools)
;(message "Loaded tools/standard-tools")
(require 'tools/advanced-tools)
;(message "Loaded tools/advanced-tools")
(require 'tools/linux-tools)
;(message "Loaded tools/linux-tools")
(require 'tools/terminal-tools)
;(message "Loaded tools/terminal-tools")
(require 'tools/command-tools)
;(message "Loaded tools/command-tools")
(require 'tools/history-tools)
;(message "Loaded tools/history-tools")
(require 'tools/custom-tools)
;(message "Loaded tools/custom-tools")
(require 'tools/safety-tools)
;(message "Loaded tools/safety-tools")
(require 'tools/python-tools)
;(message "Loaded tools/python-tools")
(require 'tools/tools-ui)
;(message "Loaded tools/tools-ui")
(require 'tools/tools-integration)
;(message "Loaded tools/tools-integration")

;; Load agent modules
(require 'agents/agents-core)
;(message "Loaded agents/agents-core")
(require 'agents/agents-persistence)
;(message "Loaded agents/agents-persistence")
(require 'agents/agents-ui)
;(message "Loaded agents/agents-ui")
;; Initialize agent configuration or load from JSON
(if (file-exists-p (expand-file-name "agent-config.json"
                                    (file-name-directory (locate-library "agents/agents-core"))))
    (ai-auto-complete-load-agents)
  (progn
    ;; Load standard agents as fallback
    (require 'agents/standard-agents)
    ;(message "Loaded agents/standard-agents")
    (require 'agents/linux-agent)
    ;(message "Loaded agents/linux-agent")
    (require 'agents/python-agent)
    ;(message "Loaded agents/python-agent")
    ;; Initialize the config file with standard agents
    (ai-auto-complete-initialize-agents-config)))

;; Load task management modules
(condition-case err
    (progn
      (require 'tools/task-management)
      (message "Loaded tools/task-management")
      (require 'agents/task-management-prompts)
      (message "Loaded agents/task-management-prompts")
      (require 'task-management-init)
      (message "Loaded task-management-init"))
  (error
   (message "Warning: Failed to load task management modules: %s" (error-message-string err))))

;; Load MCP modules
(require 'mcp/mcp)
;(message "Loaded mcp/mcp")

;; Load A2A modules
(require 'a2a/a2a)
;(message "Loaded a2a/a2a")

;; Load logging modules
(require 'logging/logging)
;(message "Loaded logging/logging")

;; Load enhanced UI modules
(condition-case err
    (progn
      (require 'ui)
      (message "Loaded enhanced UI modules"))
  (error
   (message "Warning: Failed to load enhanced UI modules: %s" (error-message-string err))))

;; Load debug module if available
(when (locate-library "ai-auto-complete-debug")
  (require 'ai-auto-complete-debug))

;;; Customization

(defgroup ai-auto-complete nil
  "AI-assisted chat interface."
  :group 'convenience
  :prefix "ai-auto-complete-")

(defgroup ai-auto-complete-backends nil
  "Settings for AI backends in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-")

(defgroup ai-auto-complete-context nil
  "Settings for context management in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-context-")

(defcustom ai-auto-complete-backend 'anthropic
  "Default AI backend to use for chat."
  :type '(choice (const :tag "Gemini" gemini)
                 (const :tag "OpenAI" openai)
                 (const :tag "Anthropic" anthropic)
                 (const :tag "OpenRouter" openrouter))
  :group 'ai-auto-complete-backends)

(defcustom ai-auto-complete-gemini-api-key ""
  "API key for Google's Gemini API."
  :type 'string
  :group 'ai-auto-complete-backends)

(defcustom ai-auto-complete-openai-api-key ""
  "API key for OpenAI's API."
  :type 'string
  :group 'ai-auto-complete-backends)

(defcustom ai-auto-complete-anthropic-api-key ""
  "API key for Anthropic's API."
  :type 'string
  :group 'ai-auto-complete-backends)

(defcustom ai-auto-complete-openrouter-api-key ""
  "API key for OpenRouter's API."
  :type 'string
  :group 'ai-auto-complete-backends)

;; (defcustom ai-auto-complete-gemini-model "gemini-2.0-flash"
;;   "Gemini model to use for completion."
;;   :type '(choice
;;           (const :tag "Gemini 2.0 Flash" "gemini-2.0-flash")
;;           (const :tag "Gemini 2.5 Pro Preview" "gemini-2.5-pro-preview-03-25")
;;           (const :tag "Gemini 2.0 Flash Thinking" "gemini-2.0-flash-thinking-exp-01-21")
;;           (const :tag "Gemini 2.5 Pro Exp 3-25" "gemini-2.5-pro-exp-03-25")
;;           (string :tag "Custom Model"))
;;   :group 'ai-auto-complete-backends)

;; (defcustom ai-auto-complete-openai-model "gpt-4.1-mini"
;;   "OpenAI model to use for completion."
;;   :type '(choice
;;           (const :tag "GPT-4.1 Mini" "gpt-4.1-mini")
;;           (const :tag "GPT-4.1 Nano" "gpt-4.1-nano")
;;           (const :tag "GPT-4.1" "gpt-4.1")
;;           (const :tag "GPT-4o" "gpt-4o")
;;           (const :tag "o3-mini" "o3-mini")
;;          ; (const :tag "o3-mini-high" "o3-mini-high")
;;           (const :tag "o4-mini" "o4-mini")
;;           (string :tag "Custom Model"))
;;   :group 'ai-auto-complete-backends)

;; (defcustom ai-auto-complete-anthropic-model "claude-3-7-sonnet-20250219"
;;   "Anthropic model to use for completion."
;;   :type '(choice
;;           (const :tag "Claude 3.7 Sonnet" "claude-3-7-sonnet-20250219")
;;           (const :tag "Claude 3.5 Sonnet" "claude-3.5-sonnet-20241022")
;;           ;(const :tag "Claude 3 Opus" "claude-3-opus-20240229")
;;           (string :tag "Custom Model"))
;;   :group 'ai-auto-complete-backends)

;; (defcustom ai-auto-complete-openrouter-model "google/gemini-2.0-flash-exp:free"
;;   "OpenRouter model to use for completion."
;;   :type '(choice
;;           (const :tag "Gemini 2.0 Flash" "google/gemini-2.0-flash-exp:free")
;;           (const :tag "Gemini 2.5 Pro" "google/gemini-2.5-pro-exp-03-25:free")
;;           (const :tag "GPT-4.1 Mini" "openai/gpt-4.1-mini")
;;           (const :tag "GPT-4.1 Nano" "openai/gpt-4.1-nano")
;;           (const :tag "GPT-4.1" "openai/gpt-4.1")
;;           (const :tag "GPT-o3-mini" "openai/gpt-o3-mini")
;;           (const :tag "GPT-o4-mini" "openai/gpt-o4-mini")
;;           (const :tag "Claude 3.7 Sonnet" "anthropic/claude-3.7-sonnet")
;;           (string :tag "Custom Model"))
;;   :set (lambda (symbol value)
;;          (set-default symbol value)
;;          (message "OpenRouter model set to %s" value))
;;   :group 'ai-auto-complete-backends)


(defcustom ai-auto-complete-request-timeout 30
  "Timeout in seconds for API requests."
  :type 'integer
  :group 'ai-auto-complete)

;;; Variables

(defvar ai-auto-complete--active-mode-line " AI:Chat"
  "String displayed in the mode line to indicate active status.")

(defvar ai-auto-complete--pending-request nil
  "Flag to track if a request is currently in progress.")

;;; Chat-specific variables

(defvar-local ai-auto-complete--chat-input-marker nil
  "Marker for the current input position in the chat buffer.")

(defvar-local ai-auto-complete--chat-history nil
  "History of messages in the chat buffer.")

(defvar-local ai-auto-complete--chat-in-progress nil
  "Flag indicating whether a chat request is in progress.")

;;; Customization Entry Points

(defun ai-auto-complete-customize ()
  "Customize AI Auto Complete settings."
  (interactive)
  (customize-group 'ai-auto-complete))

;(defun ai-auto-complete-customize-chat ()
 ; "Customize AI Auto Complete chat settings."
 ; (interactive)
 ; (customize-group 'ai-auto-complete-chat))



(defun ai-auto-complete-customize-context ()
  "Customize AI Auto Complete context settings."
  (interactive)
  (customize-group 'ai-auto-complete-context))

(defun ai-auto-complete-customize-backends ()
  "Customize AI Auto Complete backend settings."
  (interactive)
  (customize-group 'ai-auto-complete-backends))

(defun ai-auto-complete-customize-models ()
  "Customize AI Auto Complete model settings."
  (interactive)
  (customize-group 'ai-auto-complete-models))

(defun ai-auto-complete-customize-sessions ()
  "Customize AI Auto Complete chat session settings."
  (interactive)
  (customize-group 'ai-auto-complete-chat-sessions))



(defun ai-auto-complete-customize-tools ()
  "Customize AI Auto Complete tool settings."
  (interactive)
  (customize-group 'ai-auto-complete-tools))

(defun ai-auto-complete-customize-agents ()
  "Customize AI Auto Complete agent settings."
  (interactive)
  (customize-group 'ai-auto-complete-agents))

(defun ai-auto-complete-customize-task-management ()
  "Customize AI Auto Complete task management settings."
  (interactive)
  (customize-group 'ai-auto-complete-task-management))

(defun ai-auto-complete-customize-mcp ()
  "Customize AI Auto Complete MCP settings."
  (interactive)
  (customize-group 'ai-auto-complete-mcp))

(defun ai-auto-complete-customize-a2a ()
  "Customize AI Auto Complete A2A settings."
  (interactive)
  (customize-group 'ai-auto-complete-a2a))

(defun ai-auto-complete-customize-logging ()
  "Customize AI Auto Complete logging settings."
  (interactive)
  (customize-group 'ai-auto-complete-logging))

;;; Menu Integration

(easy-menu-define ai-auto-complete-menu global-map "AI Auto Complete"
  '("AI Auto Complete"
    ("chat"
     ["Start Chat" ai-auto-complete-chat t]
     ["Cancel Request" ai-auto-complete-chat-cancel-request t]
     ["Clear Chat History"  ai-auto-complete-chat-clear t]
     "---")
    ("Sessions"
     ["New Session" ai-auto-complete-chat-session-new t]
     ["Save Session" ai-auto-complete-chat-session-save t]
     ["Load Session" ai-auto-complete-chat-session-load t]
     ["List Sessions" ai-auto-complete-chat-session-list t]
     ["Rename Session" ai-auto-complete-chat-session-rename t]
     ["Delete Session" ai-auto-complete-chat-session-delete t]
     "---"
     ["Toggle Auto-Save" (lambda () (interactive)
                          (setq ai-auto-complete-session-auto-save
                                (not ai-auto-complete-session-auto-save))
                          (message "Session auto-save %s"
                                   (if ai-auto-complete-session-auto-save "enabled" "disabled")))
      :style toggle :selected ai-auto-complete-session-auto-save])
    "---"
    ["Add File to Context" ai-auto-complete-add-file-to-context t]
    ["Add Buffer to Context" ai-auto-complete-add-buffer-to-context t]
    ["Remove from Context" ai-auto-complete-remove-from-context t]
    ["List Context" ai-auto-complete-list-context t]
    "---"
    ["Next Backend" ai-auto-complete-next-backend t]
    ["Select Model" ai-auto-complete-select-openrouter-model t]
    ["Manage Models" ai-auto-complete-model-management-ui t]
    "---"
    ["Customize" ai-auto-complete-customize t]
    ["Customize Backends" ai-auto-complete-customize-backends t]
    ["Customize Models" ai-auto-complete-customize-models t]
    ["Customize Context" ai-auto-complete-customize-context t]
    ["Customize Prompts" ai-auto-complete-customize-prompts t]
    ["Customize Tools" ai-auto-complete-customize-tools t]
    ["Customize Agents" ai-auto-complete-customize-agents t]
    ["Customize Task Management" ai-auto-complete-customize-task-management t]
    ["Customize MCP" ai-auto-complete-customize-mcp t]
    ["Customize A2A" ai-auto-complete-customize-a2a t]
    ["Customize Logging" ai-auto-complete-customize-logging t]
    ["Customize Sessions" ai-auto-complete-customize-sessions t]))


;;; utils.el --- Utility functions for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file contains utility functions used across the ai-auto-complete package.

;;; Code:

;;(defun ai-auto-complete--update-mode-line ()
  ;"Update the mode line indicator."
  ;; (setq ai-auto-complete--active-mode-line
  ;;       (concat " AI:"
  ;;               (symbol-name ai-auto-complete-backend)
  ;;               (if ai-auto-complete--pending-request "..." "")))
  ;; (force-mode-line-update))

;; (defun ai-auto-complete-next-backend ()
;;   "Cycle to the next AI backend."
;;   (interactive)
;;   (setq ai-auto-complete-backend
;;         (cl-case ai-auto-complete-backend
;;           (gemini 'openai)
;;           (openai 'anthropic)
;;           (anthropic 'openrouter)
;;           (openrouter 'gemini)))
;;   (message "Switched to %s backend" ai-auto-complete-backend)
;;   (ai-auto-complete--update-mode-line))



;; Add tools menu items
(ai-auto-complete-add-tools-menu-items)

;; Add agents menu items
(ai-auto-complete-add-agents-menu-items)

;; Add enhanced UI menu items
(when (fboundp 'ai-auto-complete-ui-add-menu-items)
  (ai-auto-complete-ui-add-menu-items))

;; Add MCP menu items and initialize MCP
(defun ai-auto-complete-initialize-mcp (&optional custom-directory)
  "Initialize MCP integration and menu.
If CUSTOM-DIRECTORY is provided, set it as the MCP servers directory."
  (interactive)

  ;; Use condition-case to handle errors during initialization
  (condition-case err
      (progn
        ;; Set custom directory if provided
        (when (and custom-directory (stringp custom-directory) (file-directory-p custom-directory))
          (when (boundp 'ai-auto-complete-mcp-servers-directory)
            (setq ai-auto-complete-mcp-servers-directory custom-directory)
            (when (fboundp 'customize-save-variable)
              (customize-save-variable 'ai-auto-complete-mcp-servers-directory custom-directory))))

        ;; Initialize MCP core functionality
        (when (fboundp 'ai-auto-complete-mcp-initialize)
          (ai-auto-complete-mcp-initialize))

        ;; Initialize MCP menu
        (when (fboundp 'ai-auto-complete-mcp-add-menu-items)
          (ai-auto-complete-mcp-add-menu-items)
          (when (boundp 'ai-auto-complete-mcp-servers-directory)
            (message "MCP initialized with servers directory: %s" ai-auto-complete-mcp-servers-directory))))

    ;; Handle any errors during initialization
    (error (message "Error during MCP initialization in unified module: %s" (error-message-string err)))))

;; Command to initialize MCP with a custom directory
(defun ai-auto-complete-initialize-mcp-with-custom-directory (directory)
  "Initialize MCP with a custom servers directory.
DIRECTORY should be the full path to the directory containing MCP servers."
  (interactive
   (list (read-directory-name "Set MCP servers directory: "
                             (or ai-auto-complete-mcp-servers-directory
                                 (expand-file-name "mcp-servers" user-emacs-directory)))))
  (ai-auto-complete-initialize-mcp directory))

;; Initialize MCP after Emacs startup
(add-hook 'after-init-hook 'ai-auto-complete-initialize-mcp)

;; Add A2A menu items
(defun ai-auto-complete-initialize-a2a ()
  "Initialize A2A integration and menu."
  (interactive)
  (condition-case err
      (progn
        (when (fboundp 'ai-auto-complete-a2a-add-menu-items)
          (ai-auto-complete-a2a-add-menu-items)
          (message "A2A initialized")))
    (error (message "Error during A2A initialization: %s" (error-message-string err)))))

;; Initialize A2A after Emacs startup
(add-hook 'after-init-hook 'ai-auto-complete-initialize-a2a)

;; Initialize logging
(defun ai-auto-complete-initialize-logging ()
  "Initialize logging system and menu."
  (interactive)
  (condition-case err
      (progn
        (when (fboundp 'ai-auto-complete-logging-initialize-all)
          (ai-auto-complete-logging-initialize-all)
          (message "Logging initialized")))
    (error (message "Error during logging initialization: %s" (error-message-string err)))))

;; Initialize logging after Emacs startup
(add-hook 'after-init-hook 'ai-auto-complete-initialize-logging)

;; Initialize agents
(defun ai-auto-complete-initialize-agents ()
  "Initialize agents from configuration file or create standard agents."
  (interactive)
  (condition-case err
      (progn
        (if (file-exists-p (expand-file-name "agent-config.json"
                                            (file-name-directory (locate-library "agents/agents-core"))))
            (ai-auto-complete-load-agents)
          (ai-auto-complete-initialize-agents-config))
        (message "Agents initialized"))
    (error (message "Error during agents initialization: %s" (error-message-string err)))))

;; Initialize agents after Emacs startup
(add-hook 'after-init-hook 'ai-auto-complete-initialize-agents)

;; Initialize task management
(defun ai-auto-complete-initialize-task-management ()
  "Initialize task management system and update agent prompts."
  (interactive)
  (condition-case err
      (progn
        ;; Load task management modules
        (when (locate-library "tools/task-management")
          (require 'tools/task-management)
          (message "Loaded tools/task-management"))

        (when (locate-library "agents/task-management-prompts")
          (require 'agents/task-management-prompts)
          (message "Loaded agents/task-management-prompts"))

        (when (locate-library "task-management-init")
          (require 'task-management-init)
          (message "Loaded task-management-init"))

        ;; Update agent prompts with task management instructions
        (when (fboundp 'ai-auto-complete-update-agents-with-task-prompts)
          (ai-auto-complete-update-agents-with-task-prompts)
          (message "Updated agent prompts with task management instructions"))

        (message "Task management system initialized"))
    (error (message "Error during task management initialization: %s" (error-message-string err)))))

;; Initialize task management after Emacs startup
(add-hook 'after-init-hook 'ai-auto-complete-initialize-task-management)

;; Initialize enhanced UI
(defun ai-auto-complete-initialize-enhanced-ui ()
  "Initialize enhanced UI components."
  (interactive)
  (condition-case err
      (progn
        ;; Load UI modules
        (when (locate-library "ui")
          (require 'ui)
          (message "Loaded enhanced UI modules"))

        ;; Add menu items
        (when (fboundp 'ai-auto-complete-ui-add-menu-items)
          (ai-auto-complete-ui-add-menu-items)
          (message "Added enhanced UI menu items"))

        ;; Enable UI if configured
        (when (and (boundp 'ai-auto-complete-ui-enabled)
                   ai-auto-complete-ui-enabled
                   (fboundp 'ai-auto-complete-ui-enable))
          (ai-auto-complete-ui-enable)
          (message "Enhanced UI enabled"))

        (message "Enhanced UI initialized"))
    (error (message "Error during enhanced UI initialization: %s" (error-message-string err)))))

;; Initialize enhanced UI after Emacs startup
(add-hook 'after-init-hook 'ai-auto-complete-initialize-enhanced-ui)

(provide 'ai-auto-complete-unified)
;;; ai-auto-complete-unified.el ends here
