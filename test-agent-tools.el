;; Test script for agent tools - Standalone version

;; Define variables
(defvar ai-auto-complete-chat-mode t "Whether chat mode is enabled.")
(defvar ai-auto-complete-agents-enabled t "Whether agents are enabled.")
(defvar ai-auto-complete-default-agent "<PERSON><PERSON><PERSON>" "Default agent name.")

;; Define the default callback function
(defun ai-auto-complete-tools-default-callback (response &optional agent-name)
  "Default callback to present RESPONSE to the user.
AGENT-NAME is the optional name of the agent making the request."
  (message "Default callback called with response: %s"
           (substring response 0 (min 50 (length response))))
  (message "Agent name: %s" (or agent-name "nil"))

  ;; Simulate the agent-specific handling
  (if agent-name
      (message "SUCCESS: Agent name %s was properly passed to the default callback!" agent-name)
    (message "FAILURE: Agent name was not passed to the default callback!")))

;; Define the wrapped callback function (old version with bug)
(defun ai-auto-complete-tools-wrapped-callback-old (response callback &optional agent-name)
  "Process the RESPONSE and pass to CALLBACK.
Uses the old implementation with the bug.
AGENT-NAME is the optional name of the agent making the request."
  (message "Processing response with old wrapped callback")
  (let ((effective-callback (or callback #'ai-auto-complete-tools-default-callback)))
    (message "Calling effective callback")
    (funcall effective-callback response)))

;; Define the wrapped callback function (new version with fix)
(defun ai-auto-complete-tools-wrapped-callback-new (response callback &optional agent-name)
  "Process the RESPONSE and pass to CALLBACK.
Uses the new implementation with the fix.
AGENT-NAME is the optional name of the agent making the request."
  (message "Processing response with new wrapped callback")
  (let ((effective-callback (if callback
                               callback
                             (lambda (resp)
                               (ai-auto-complete-tools-default-callback resp agent-name)))))
    (message "Calling effective callback")
    (funcall effective-callback response)))

;; Test function to simulate the flow with the old implementation
(defun test-agent-tools-flow-old ()
  "Test the agent tools flow with the old implementation."
  (interactive)

  ;; Simulate a message from agent "Jadyaa"
  (let ((agent-name "Jadyaa")
        (message "Please read the file test-file.txt"))

    ;; Log start of test
    (message "\n\n=== Starting agent tools test with OLD implementation ===\n")
    (message "Agent name: %s" agent-name)

    ;; Simulate the initial call
    (message "Initial call with agent name: %s" agent-name)

    ;; Simulate the tool call and callback
    (ai-auto-complete-tools-wrapped-callback-old
     "Tool result: This is a test file content."
     nil  ;; No callback provided, should use default
     agent-name)))

;; Test function to simulate the flow with the new implementation
(defun test-agent-tools-flow-new ()
  "Test the agent tools flow with the new implementation."
  (interactive)

  ;; Simulate a message from agent "Jadyaa"
  (let ((agent-name "Jadyaa")
        (message "Please read the file test-file.txt"))

    ;; Log start of test
    (message "\n\n=== Starting agent tools test with NEW implementation ===\n")
    (message "Agent name: %s" agent-name)

    ;; Simulate the initial call
    (message "Initial call with agent name: %s" agent-name)

    ;; Simulate the tool call and callback
    (ai-auto-complete-tools-wrapped-callback-new
     "Tool result: This is a test file content."
     nil  ;; No callback provided, should use default
     agent-name)))

;; Run the tests
(message "\n\n=== RUNNING TESTS ===\n")
(test-agent-tools-flow-old)
(message "\n")
(test-agent-tools-flow-new)
