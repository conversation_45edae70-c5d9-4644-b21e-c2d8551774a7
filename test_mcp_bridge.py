#!/usr/bin/env python3
"""
Test script for the MCP bridge.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("Test MCP Bridge: Starting up...")
print("Test MCP Bridge: Checking for MCP Python SDK...")

try:
    import mcp
    print(f"Test MCP Bridge: MCP Python SDK found")
    
    # Try to import the specific modules we need
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    print("Test MCP Bridge: All required MCP modules imported successfully")
    
except ImportError as e:
    print(f"Test MCP Bridge: Error importing MCP Python SDK: {str(e)}")
    sys.exit(1)

print("Test MCP Bridge: Ready")
print('{"status": "ready"}')

# Keep the script running
input("Press Enter to exit...")
