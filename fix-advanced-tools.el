;;; fix-advanced-tools.el --- Fix advanced tools -*- lexical-binding: t -*-

;;; Commentary:
;; This script fixes the advanced tools by replacing call-process-shell-command with shell-command-to-string

;;; Code:

(require 'cl-lib)

;; Load the advanced tools file
(let ((file-path "ai-auto-complete-modules/tools/advanced-tools.el"))
  (with-temp-buffer
    (insert-file-contents file-path)
    
    ;; Replace all instances of call-process-shell-command with shell-command-to-string
    (goto-char (point-min))
    (while (re-search-forward "(call-process-shell-command\\s-+\\([^)]+\\))" nil t)
      (let* ((cmd-expr (match-string 1))
             (cmd (car (split-string cmd-expr))))
        (replace-match (format "(progn (message \"DEBUG: Running command: %%s\" %s) (setq cmd-result (shell-command-to-string %s)) (message \"DEBUG: Command executed successfully\"))" cmd cmd))))
    
    ;; Remove buffer cleanup code
    (goto-char (point-min))
    (while (re-search-forward "(when (buffer-live-p cmd-buffer)\\s-+\\(.*\\)\\s-+\\(.*\\)\\s-+\\(.*\\))" nil t)
      (replace-match ""))
    
    ;; Write the modified content back to the file
    (write-region (point-min) (point-max) file-path)))

(message "Advanced tools fixed!")

;;; fix-advanced-tools.el ends here
