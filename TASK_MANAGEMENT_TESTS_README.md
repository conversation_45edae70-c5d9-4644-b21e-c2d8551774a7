# Task Management Tools Tests

This directory contains comprehensive tests for the task management tools in the AI Auto Complete system.

## Files

- `test-task-management.el` - Main test suite with comprehensive tests
- `run-task-management-tests.el` - Simple test runner script
- `TASK_MANAGEMENT_TESTS_README.md` - This file

## Quick Start

### Method 1: Using the Test Runner Script

1. Load the test runner:
   ```elisp
   (load-file "run-task-management-tests.el")
   ```

2. Run basic tests:
   ```elisp
   (run-basic-task-management-tests)
   ```

3. Check results in the `*Task Management Test Results*` buffer

### Method 2: Direct Evaluation

1. Open Emacs and navigate to this directory
2. Open the `*scratch*` buffer
3. Evaluate these expressions:
   ```elisp
   (load-file "run-task-management-tests.el")
   (run-basic-task-management-tests)
   ```

### Method 3: Interactive Commands

1. Load the test runner: `M-x load-file RET run-task-management-tests.el RET`
2. Run tests: `M-x eval-expression RET (run-basic-task-management-tests) RET`

## Available Test Functions

### `(run-basic-task-management-tests)`
- Comprehensive test suite for all task management tools
- Tests tool registration, creation, execution, updates, error handling
- Does not require LLM integration
- **Recommended for initial testing**

### `(quick-test-task-tools)`
- Quick verification that tools are registered and working
- Good for debugging and initial checks
- Minimal test that runs fast

### `(run-llm-integration-tests)`
- Tests task management tools with actual LLM integration
- Requires working LLM backend (Gemini, OpenAI, etc.)
- Tests real-world usage scenarios
- **Use this to test end-to-end functionality**

### `(run-all-task-management-tests)`
- Runs both basic and LLM integration tests
- Most comprehensive test suite

### `(cleanup-task-management-tests)`
- Cleans up test data and resets state
- Run this between test sessions

### `(task-management-test-help)`
- Displays detailed help information

## What the Tests Check

### 1. Tool Registration
- Verifies all 4 task management tools are properly registered:
  - `create_task_plan`
  - `execute_planned_step`
  - `update_task_plan`
  - `get_task_plan_status`

### 2. Task Plan Creation
- Tests creating a task plan with multiple steps
- Verifies plan ID generation and storage
- Checks that steps are properly structured

### 3. Task Plan Status
- Tests retrieving task plan status
- Verifies status information is complete and accurate

### 4. Step Execution
- Tests executing individual steps in a task plan
- Verifies tool calls are made correctly
- Checks progress tracking

### 5. Plan Updates
- Tests updating existing task plans with new steps
- Verifies plan modification works correctly

### 6. Error Handling
- Tests various error conditions:
  - Missing required parameters
  - Invalid plan IDs
  - Malformed requests

### 7. Data Integrity
- Verifies internal data structures are created correctly
- Checks that plan objects have all required fields

### 8. LLM Integration (if enabled)
- Tests real LLM interaction with task management tools
- Verifies tools work in actual usage scenarios

## Expected Results

### Successful Test Run
```
=== TASK MANAGEMENT TEST SUMMARY ===
Total Tests: 7
Passed: 7
Failed: 0
Success Rate: 100.0%
```

### Common Issues and Solutions

#### Tools Not Registered
**Symptom**: "Tool X is NOT registered" messages
**Solution**: 
1. Ensure `ai-auto-complete-modules/tools/task-management.el` is loaded
2. Check that `(ai-auto-complete-register-task-management-tools)` was called

#### Tools Disabled
**Symptom**: "Tools not enabled" messages
**Solution**: 
1. Set `(setq ai-auto-complete-tools-enabled t)`
2. The test runner should do this automatically

#### Missing Dependencies
**Symptom**: "Cannot load" or "function not found" errors
**Solution**:
1. Ensure you're in the correct directory
2. Check that all required modules are available
3. Verify the load path includes the necessary directories

#### LLM Integration Fails
**Symptom**: "ai-auto-complete-complete function not available"
**Solution**:
1. This is normal if LLM integration isn't set up
2. Focus on basic tests first
3. Set up LLM backend if you want to test integration

## Interpreting Results

### Test Result Buffer
The `*Task Management Test Results*` buffer shows:
- Summary statistics
- Detailed pass/fail for each test
- Active task plans created during testing

### LLM Integration Buffer
The `*Task Management LLM Test*` buffer shows:
- The test prompt sent to the LLM
- The LLM's response
- Any task plans created by the LLM

## Troubleshooting

### If Tests Fail
1. Check the detailed results for specific error messages
2. Ensure all dependencies are loaded
3. Verify tools are enabled: `ai-auto-complete-tools-enabled` should be `t`
4. Run `(quick-test-task-tools)` for basic verification

### If LLM Integration Fails
1. This is expected if no LLM backend is configured
2. Focus on basic tests which don't require LLM
3. Basic tests passing means the tools themselves work correctly

### Clean Up Between Runs
```elisp
(cleanup-task-management-tests)
```

## Manual Testing

You can also test the tools manually:

```elisp
;; Enable tools
(setq ai-auto-complete-tools-enabled t)

;; Create a test plan
(ai-auto-complete-tool-create-task-plan 
 '((task_description . "Test task")
   (agent_name . "manual-test")
   (steps . (((description . "List directory")
             (tool_name . "list_directory") 
             (tool_params . ((path . "."))))))))

;; Check what plans exist
(hash-table-count ai-auto-complete-active-task-plans)

;; Get a plan ID and check status
;; (Replace "plan-XXXXX" with actual plan ID from create result)
(ai-auto-complete-tool-get-task-plan-status '((plan_id . "plan-XXXXX")))
```

## Next Steps

After running these tests:

1. **If all tests pass**: The task management tools are working correctly
2. **If basic tests pass but LLM integration fails**: The tools work, but LLM setup may be needed
3. **If tests fail**: Check error messages and troubleshoot dependencies

The task management tools provide a foundation for agents to organize complex tasks into manageable steps, making them more effective at handling multi-step requests.
