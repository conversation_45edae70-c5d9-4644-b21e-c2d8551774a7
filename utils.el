;;; utils.el --- Utility functions for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file contains utility functions used across the ai-auto-complete package.

;;; Code:

(defun ai-auto-complete--update-mode-line ()
  "Update the mode line indicator."
  (setq ai-auto-complete--active-mode-line
        (concat " AI:"
                (symbol-name ai-auto-complete-backend)
                (if ai-auto-complete--pending-request "..." "")))
  (force-mode-line-update))

(defun ai-auto-complete-next-backend ()
  "Cycle to the next AI backend."
  (interactive)
  (setq ai-auto-complete-backend
        (cl-case ai-auto-complete-backend
          (gemini 'openai)
          (openai 'anthropic)
          (anthropic 'openrouter)
          (openrouter 'gemini)))
  (message "Switched to %s backend" ai-auto-complete-backend)
  (ai-auto-complete--update-mode-line))


(provide 'utils)
;;; utils.el ends here
