# Chat Sessions in AI Auto Complete

The Chat Sessions feature allows you to save, load, and manage your conversations with AI models. This enables you to:

- Save important conversations for future reference
- Continue conversations at a later time
- Organize conversations by topic or project
- Share conversations with others

## Using Chat Sessions

### From the Menu

The Sessions menu is available in the main AI Auto Complete menu:

1. **AI Auto Complete → Sessions → New Session**: Start a new chat session
2. **AI Auto Complete → Sessions → Save Session**: Save the current session
3. **AI Auto Complete → Sessions → Load Session**: Load a previously saved session
4. **AI Auto Complete → Sessions → List Sessions**: View and manage all saved sessions
5. **AI Auto Complete → Sessions → Rename Session**: Rename the current session
6. **AI Auto Complete → Sessions → Delete Session**: Delete a saved session
7. **AI Auto Complete → Sessions → Toggle Auto-Save**: Enable/disable automatic session saving

### Using Keyboard Shortcuts

When in the chat buffer, you can use these keyboard shortcuts:

- `C-c C-s`: Save the current session
- `C-c C-l`: Load a saved session
- `C-c s`: Open the Sessions menu

### Session List View

The `List Sessions` command opens a buffer showing all saved sessions with details:

- Session ID
- Creation date
- Last modified date
- Number of messages

In this view, you can:
- Press `RET` to load the session at point
- Press `d` to delete the session at point
- Press `r` to rename the session at point
- Press `q` to quit the session list

## Configuration

You can customize the session management behavior:

1. **AI Auto Complete → Customize Sessions**, or
2. `M-x ai-auto-complete-customize-sessions`

Available options:

- **Sessions Directory**: Where session files are stored
- **Auto-Save**: Whether to automatically save sessions when exiting chat
- **Ask on New**: Whether to prompt for a session name when starting a new chat

## Session Files

Sessions are stored as Emacs Lisp data structures in files with the `.aac-session` extension in the configured sessions directory (default: `~/.emacs.d/ai-auto-complete-sessions/`).

Each session file contains:
- Session ID
- Creation and modification timestamps
- Backend used
- Conversation history
- Context files and images
- Other metadata

## Tips

- Use descriptive names for your sessions to make them easier to find later
- Enable auto-save to avoid losing conversations
- Use the session list view to manage your sessions efficiently
- Sessions include the conversation history and context, allowing you to continue exactly where you left off
