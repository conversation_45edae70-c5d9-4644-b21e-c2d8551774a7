;;; chat.el --- Chat interface for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file contains the chat interface implementation for ai-auto-complete.
;; It provides a conversational interface to interact with AI models.

;;; Code:

;; Temporarily commented out to avoid dependency issues
;; (require 's)
(require 'cl-lib)
(require 'shared-context)
(require 'context)  ;; For context display functions
(require 'core/backend)
(require 'customization/prompts)
(require 'chat-customization)  ;; Ensure this is loaded first for face definitions
(require 'chat-sessions)  ;; Session management functionality
(require 'tools/tools-core)  ;; Tool use functionality
(require 'agents/agents-core)  ;; Agent system functionality
(require 'chat-mode)  ;; Chat mode definition
;(load "chat-detect-file-request")  ;; Load improved file detection function

;; Forward declaration for debug functions
(declare-function ai-auto-complete-debug-log "ai-auto-complete-debug")

;; Define string-empty-p if it's not available (normally provided by 's package)
(unless (fboundp 'string-empty-p)
  (defun string-empty-p (string)
    "Return t if STRING is empty or nil."
    (or (null string) (string= string ""))))


; this hook is run inside ai-auto-complete-chat-send-input which itself gets triggered on 'enter' key in chat interface
(defvar ai-auto-complete-chat-message-hook nil
  "Hook run after a user sends a message in the chat interface.
The hook function receives the message text as its argument.")

;; when this hook is run, the function #'ai-auto-complete-chat-send-to-backend
;; is called with an input message.
(add-hook 'ai-auto-complete-chat-message-hook #'ai-auto-complete-chat-send-to-backend)

;; Note: Chat-specific variables and mode definition are now in the main file
;; to ensure consistent loading order and avoid dependency issues

(defun ai-auto-complete-chat ()
  "Start or switch to the AI auto complete chat buffer."
  (interactive)
  (message "Starting chat function with backend: %s" (if (boundp 'ai-auto-complete-chat-backend) ai-auto-complete-chat-backend 'undefined))

  ;; Ensure tools advice is properly set up if tools are enabled
  (when (and (boundp 'ai-auto-complete-tools-enabled)
             ai-auto-complete-tools-enabled)
    (message "Tools are enabled for chat mode"))

  (message "Set chat backend to %s" ai-auto-complete-backend)
  (let ((chat-buffer (get-buffer-create ai-auto-complete-chat-buffer-name)))
    (with-current-buffer chat-buffer
      ;; Set up the buffer with text-mode as the base mode
      (unless (eq major-mode 'text-mode)
        (text-mode))

      ;; Enable our chat minor mode
      (unless ai-auto-complete-chat-mode
        (ai-auto-complete-chat-mode 1)
        (message "Chat mode after enabling: %s" (if ai-auto-complete-chat-mode "enabled" "disabled")))

      ;; Initialize a new session if needed
      (unless ai-auto-complete--current-session-id
        (when ai-auto-complete-session-ask-on-new
          (call-interactively 'ai-auto-complete-chat-session-new)
          ;; If user canceled the session name prompt, generate a default one
          (unless ai-auto-complete--current-session-id
            (setq ai-auto-complete--current-session-id
                  (ai-auto-complete-chat--generate-session-id nil)))))

      ;; Initialize the chat buffer
      (ai-auto-complete-chat-initialize)

      ;; Always ensure the buffer is in a writable state
      (setq buffer-read-only nil)

      ;; Make sure the input marker is set
      (unless (and ai-auto-complete--chat-input-marker
                   (marker-position ai-auto-complete--chat-input-marker))
        (goto-char (point-max))
        (setq ai-auto-complete--chat-input-marker (point-marker))))

    (switch-to-buffer chat-buffer)

    ;; Make sure the cursor is at the input position
    (when (and ai-auto-complete--chat-input-marker
               (marker-position ai-auto-complete--chat-input-marker))
      (goto-char ai-auto-complete--chat-input-marker))))

(defun ai-auto-complete-chat-initialize ()
  "Initialize the chat buffer."
(let ((backend ai-auto-complete-backend)
    (inhibit-read-only t))
       (message "Initializing chat buffer with backend: %s" backend)
       
       (erase-buffer) 
       ;; Make the header part read-only
       (insert (propertize (format "AI Auto Complete Chat - %s\n" (symbol-name backend))
                          'face 'font-lock-keyword-face
                          'read-only t
                          'front-sticky t
                          'rear-nonsticky t))
       (insert (propertize (format-time-string "Started on %Y-%m-%d %H:%M:%S\n")
                        'face 'ai-auto-complete-timestamp-face
                        'read-only t
                        'front-sticky t
                        'rear-nonsticky t))

      ;; Display session information if available
       (when ai-auto-complete--current-session-id
          (insert (propertize (format "Session: %s\n" ai-auto-complete--current-session-id)
                          'face 'ai-auto-complete-timestamp-face
                          'read-only t
                          'front-sticky t
                          'rear-nonsticky t)))

       (insert (propertize "\nEmacs: Type your message below and press Enter to send.\n\n"
                        'face 'ai-auto-complete-system-face
                        'read-only t
                        'front-sticky t
                        'rear-nonsticky t))
       ;; Make the prompt prefix read-only but allow typing after it
       (let ((prompt-start (point)))
          (insert (propertize ai-auto-complete-chat-prompt-prefix
                          'face 'ai-auto-complete-user-face
                          'read-only t
                          'front-sticky t
                          'rear-nonsticky t))
          ;; Set the input marker at the current position
          (setq ai-auto-complete--chat-input-marker (point-marker))

          ;; Make sure the cursor is at the input position
          (goto-char (point-max))

          ;; Ensure the buffer is writable at the input position
           (put-text-property (point) (point) 'read-only nil))

        ;; Make sure the buffer is in a state where the user can type
          (setq buffer-read-only nil)))

;; this function is directly attached to 'Enter' key event in chat-interface
;; (i.e. it gets invovked when user pressed 'Enter' key in chat-interface
(defun ai-auto-complete-chat-send-input ()
  "Send the current input to the AI auto complete."
  (interactive)

  ;; Debug message
  (message "ai-auto-complete-chat-send-input called")

  ;; If the input marker is not set, initialize it at the current point
  (unless (and ai-auto-complete--chat-input-marker
               (marker-position ai-auto-complete--chat-input-marker))
    (setq ai-auto-complete--chat-input-marker (point-marker))
    (message "Input marker initialized at %s" (point)))

  ;; Debug the conditions
  (message "Chat in progress: %s" ai-auto-complete--chat-in-progress)
  (message "Input marker exists: %s" (and ai-auto-complete--chat-input-marker
                                       (marker-position ai-auto-complete--chat-input-marker)))
  (message "Point >= marker: %s" (and ai-auto-complete--chat-input-marker
                                     (marker-position ai-auto-complete--chat-input-marker)
                                     (>= (point) ai-auto-complete--chat-input-marker)))

  (when (and (not ai-auto-complete--chat-in-progress)
             ai-auto-complete--chat-input-marker
             (marker-position ai-auto-complete--chat-input-marker)
             (>= (point) ai-auto-complete--chat-input-marker))
    (let* ((input-text (buffer-substring-no-properties
                       ai-auto-complete--chat-input-marker (point-max)))
           (inhibit-read-only t))
      ;; Debug the input text
      (message "Input text: '%s'" input-text)
      (when (and input-text (stringp input-text) (not (string-empty-p (string-trim input-text))))
        (message "Input text is valid, proceeding to send request")
        ;; Always proceed with the input text
        (when t
          ;; Ensure the message is directed to an agent
          (let* ((agent-message input-text)
                 (agent-info nil))

            ;; If agents are enabled, ensure the message has an agent prefix
            (when ai-auto-complete-agents-enabled
              (setq agent-message (ai-auto-complete-ensure-agent-message input-text))
              (setq agent-info (ai-auto-complete-message-to-agent agent-message)))

            (if (and ai-auto-complete-agents-enabled agent-info)
                (let ((agent-name (car agent-info))
                      (agent-content (cdr agent-info)))
                  ;; Add the message to history - use the possibly modified message with agent prefix
                  (push (cons 'user agent-message) ai-auto-complete--chat-history)
                  ;; Mark session as modified
                  (setq ai-auto-complete--session-modified t)
                  ;; Run the message hook
                 ; (run-hook-with-args 'ai-auto-complete-chat-message-hook agent-message)
                  ;; Format the input in the buffer
                  (delete-region ai-auto-complete--chat-input-marker (point-max))
                  (goto-char (point-max))
                  ;; Insert the user's message as read-only text
                  (insert (propertize agent-message
                                     'read-only t
                                     'front-sticky t
                                     'rear-nonsticky t))
                  (insert (propertize "\n\n"
                                     'read-only t
                                     'front-sticky t
                                     'rear-nonsticky t))
                  ;; Show that we're waiting for a response
                  (insert (propertize (format "AGENT-%s: " agent-name)
                                      'face 'ai-auto-complete-agent-face
                                      'read-only t
                                      'front-sticky t
                                      'rear-nonsticky t))
                  (insert (propertize "Thinking..."
                                     'face 'italic
                                     'read-only t
                                     'front-sticky t
                                     'rear-nonsticky t))
                  (setq ai-auto-complete--chat-in-progress t)
                  ;; Process the agent message
                  (message "Processing message foragent %s: %s" agent-name agent-content)
                  (ai-auto-complete-process-agent-message
                   agent-name
                   agent-content
                   ai-auto-complete--chat-history
                   (lambda (agent-name response)
                     ;; Add the response to history
                     (push (cons 'agent (cons agent-name response)) ai-auto-complete--chat-history)
                     ;; Display the response
                     (ai-auto-complete-chat-handle-agent-response agent-name response)))
                  (message "Returned with LLM response from ai-auto-complete-process-agent-message"))
              ;; Agents not enabled or invalid agent format
              (progn
                ;; Run the message hook
                (run-hook-with-args 'ai-auto-complete-chat-message-hook input-text)
                (message "About to add message to history afteer sending <input-text> to backend")

                 ;; Add the message to history
                (push (cons 'USER input-text) ai-auto-complete--chat-history)
                ;; Mark session as modified
                (setq ai-auto-complete--session-modified t)
                 ;; Format the input in the buffer
                (delete-region ai-auto-complete--chat-input-marker (point-max))
                (goto-char (point-max))
                ;; Insert the user's message as read-only text
                (insert (propertize input-text
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t))
                (insert (propertize "\n\n"
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t))
                ;; Show that we're waiting for a response
                (insert (propertize ai-auto-complete-chat-response-prefix
                                    'face 'ai-auto-complete-assistant-face
                                    'read-only t
                                    'front-sticky t
                                    'rear-nonsticky t))
                (insert (propertize "Thinking..."
                                   'face 'italic
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t))
                (setq ai-auto-complete--chat-in-progress t)
                ;; Send to the appropriate backend
                (message "Calling ai-auto-complete-chat-send-to-backend with input: %s" (substring input-text 0 (min 20 (length input-text))))
                (ai-auto-complete-chat-send-to-backend input-text)
                (message "Returned from ai-auto-complete-chat-send-to-backend")))))))))

;; Define a timeout for API requests (30 seconds)
(defvar ai-auto-complete-chat-request-timeout 30
  "Timeout in seconds for chat API requests.")

(defvar ai-auto-complete--chat-timeout-timer nil
  "Timer for tracking API request timeouts.")

(defun ai-auto-complete-chat-timeout-handler ()
  "Handle timeout for chat API requests."
  (when ai-auto-complete--chat-in-progress
    (message "API request timed out after %d seconds" ai-auto-complete-chat-request-timeout)
    (ai-auto-complete-chat-handle-response "ERROR: Request timed out. The API did not respond within the expected time. Please try again or switch to a different backend.")
    (setq ai-auto-complete--chat-timeout-timer nil)))


;; this function creates an advice function 'ai-auto-complete-tools-advice-request that wraps around
;; the target function 'ai-auto-complete-complete i.e. it gets the target function and its arguments and
;; runs before the target function runs.
(defun ai-auto-complete-chat-send-to-backend (input-text)
  "Send INPUT-TEXT to the current backend and handle the response."
  (message "Starting ai-auto-complete-chat-send-to-backend with input: %s" (substring input-text 0 (min 20 (length input-text))))

  ;; Ensure tools advice is applied if tools are enabled
  (when (and (boundp 'ai-auto-complete-tools-enabled)
             ai-auto-complete-tools-enabled
             (fboundp 'advice-member-p)
             (fboundp 'advice-add)
             (not (advice-member-p 'ai-auto-complete-tools-advice-request 'ai-auto-complete-complete)))
    (advice-add 'ai-auto-complete-complete :around #'ai-auto-complete-tools-advice-request)
    (message "Added tools advice to ai-auto-complete-complete"))
  (let ((prompt (ai-auto-complete-chat-build-prompt)))
    ;; Set up timeout timer
    (when ai-auto-complete--chat-timeout-timer
      (cancel-timer ai-auto-complete--chat-timeout-timer))
    (setq ai-auto-complete--chat-timeout-timer
          (run-with-timer ai-auto-complete-chat-request-timeout nil
                         #'ai-auto-complete-chat-timeout-handler))

    ;; Use the appropriate backend
    (let ((backend ai-auto-complete-backend))
      (message "DEBUG: Using backend for request: %s" backend)
      (cl-case backend
        (gemini
         (message "DEBUG: Calling Gemini provider")
         (ai-auto-complete--gemini-complete prompt #'ai-auto-complete-chat-handle-response-with-cleanup))
        (openai
         (message "DEBUG: Calling OpenAI provider")
         (ai-auto-complete--openai-complete prompt #'ai-auto-complete-chat-handle-response-with-cleanup))
        (anthropic
         (message "DEBUG: Calling Anthropic provider")
         (ai-auto-complete--anthropic-complete prompt #'ai-auto-complete-chat-handle-response-with-cleanup))
        (openrouter
         (message "DEBUG: Calling OpenRouter provider")
         (ai-auto-complete--openrouter-complete prompt #'ai-auto-complete-chat-handle-response-with-cleanup))))))

(defun ai-auto-complete-chat-handle-response-with-cleanup (response)
  "Handle RESPONSE from the AI and clean up timers."
  ;; Debug message
  (message "Response handler called with response: %s" (if response (substring response 0 (min 50 (length response))) "nil"))
  (message "Response type: %s" (type-of response))
  (message "Response is string: %s" (stringp response))
  (message "Response is error: %s" (and (stringp response) (string-match-p "^ERROR:" response)))

  ;; We don't need to modify tools advice here, just process the response

  ;; Cancel the timeout timer if it exists
  (when ai-auto-complete--chat-timeout-timer
    (cancel-timer ai-auto-complete--chat-timeout-timer)
    (setq ai-auto-complete--chat-timeout-timer nil))

  ;; Temporarily disable tools processing due to argument mismatch
  ;; Forward directly to the main handler
  (ai-auto-complete-chat-handle-response response))

(defun ai-auto-complete-chat-build-prompt ()
  "Build a prompt from the chat history and shared context."
  (message "Chat history length: %s" (if ai-auto-complete--chat-history
                                       (format "%d messages" (length ai-auto-complete--chat-history))
                                     "empty"))
  (message "Chat mode active: %s" (if (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode) "yes" "no"))
  ;; Get the appropriate system prompt
  (let* (
         (backend ai-auto-complete-backend)
         ;; Temporarily disable tools system prompt
         (system-prompt (ai-auto-complete-get-system-prompt backend))
         ;; Log the prompt being used for debugging
         (dummy (message "Chat using system prompt: %s" system-prompt))
         (history-text "")
         (shared-context (if (fboundp 'ai-auto-complete-get-context-for-prompt)
                            (ai-auto-complete-get-context-for-prompt)
                          ""))
         (tool-definitions ""))

    ;; Build history text from most recent messages (limited by ai-auto-complete-chat-max-history)
    (let ((history (reverse (seq-take (reverse ai-auto-complete--chat-history) ai-auto-complete-chat-max-history))))
      (setq history-text "\n\nConversation history:\n")
      (dolist (msg history)
        (let ((role (car msg))
              (content (cdr msg)))
          (setq history-text (concat history-text
                                    (cond
                                     ((eq role 'user) "User: ")
                                     ((eq role 'agent) (format "Agent %s: " (car content)))
                                     (t "Assistant: "))
                                    (if (eq role 'agent) (cdr content) content)
                                    "\n\n")))))

    ;; Add tool definitions if tools are enabled
    (when (and (boundp 'ai-auto-complete-tools-enabled)
               ai-auto-complete-tools-enabled
               (fboundp 'ai-auto-complete-get-tool-definitions))
      (setq tool-definitions (concat "\n\nTool definitions:\n"
                                  (ai-auto-complete-get-tool-definitions))))

    ;; Debug output to see what's being sent
    (message "Chat prompt components:")
   ; (message "  System prompt: %s" system-prompt)
    (message "  Shared context length: %d" (length shared-context))
    (message "  History text length: %d" (length history-text))
    (message "  Tool definitions length: %d" (length tool-definitions))
    ;(message "Chat prompt: %s" (concat system-prompt shared-context history-text tool-definitions))

    ;; More detailed debug output for shared context
    (when (not (string-empty-p shared-context))
      (message "Chat mode: Shared context found (length: %d). Preview: %s"
               (length shared-context)
               (if (> (length shared-context) 100)
                   (concat (substring shared-context 0 100) "...")
                 shared-context)))

    ;; Debug output for tool definitions
    (when (not (string-empty-p tool-definitions))
      (message "Chat mode: Tool definitions added (length: %d)"
               (length tool-definitions)))

    ;; Return the full prompt
    (concat system-prompt shared-context history-text tool-definitions)))

(defun ai-auto-complete-chat-handle-response (response)
  "Handle RESPONSE from the AI in the chat buffer."
  (if (get-buffer ai-auto-complete-chat-buffer-name)
      (progn
        ;; Debug output
        (message "Received response: %s" (or response "nil"))

        (with-current-buffer (get-buffer ai-auto-complete-chat-buffer-name)
          (let ((inhibit-read-only t))
            ;; Remove the "Thinking..." text
            (goto-char (point-max))
            (delete-region (line-beginning-position) (point-max))

            ;; Handle different response cases
            (cond
             ;; Case 1: Response is nil (API error)
             ((null response)
              (insert (propertize "emacs: "
                               'face 'ai-auto-complete-system-face
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))
              (insert (propertize "ERROR: Failed to get a response from the API. Please try again or switch to a different backend."
                               'face 'error
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t)))

             ;; Case 2: Response is an error message
             ((and (stringp response) (string-match-p "^ERROR:" response))
              (insert (propertize "emacs: "
                               'face 'ai-auto-complete-system-face
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))
              (insert (propertize response
                               'face 'error
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t)))

             ;; Case 3: Normal response
             (t
              ;; Add the response to history
              (push (cons 'assistant response) ai-auto-complete--chat-history)
              ;; Mark session as modified
              (setq ai-auto-complete--session-modified t)
              ;; Insert the response with read-only property
              (insert (propertize ai-auto-complete-chat-response-prefix
                                'face 'ai-auto-complete-assistant-face
                                'read-only t
                                'front-sticky t
                                'rear-nonsticky t))
              (insert (propertize response
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))))

            ;; Always add a new prompt, regardless of response type
            (insert (propertize "\n\n"
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t))
            ;; Make the prompt prefix read-only but allow typing after it
            (insert (propertize ai-auto-complete-chat-prompt-prefix
                              'face 'ai-auto-complete-user-face
                              'read-only t
                              'front-sticky t
                              'rear-nonsticky t))
            (setq ai-auto-complete--chat-input-marker (point-marker))

            ;; Make sure the buffer is in a state where the user can type
            (setq buffer-read-only nil)

            ;; Always reset the in-progress flag
            (setq ai-auto-complete--chat-in-progress nil))))

    ;; Buffer not found case
    (message "Chat buffer not found, cannot display response")))

(defun ai-auto-complete-chat-clear ()
  "Clear the chat history and restart the conversation."
  (interactive)
  (when ai-auto-complete-chat-mode
    (when (and ai-auto-complete--session-modified
               (y-or-n-p "Current session has unsaved changes. Save before clearing? "))
      (call-interactively 'ai-auto-complete-chat-session-save))

    (when (y-or-n-p "Clear the current chat? ")
      (setq ai-auto-complete--chat-history nil)
      (setq ai-auto-complete--current-session-id nil)
      (setq ai-auto-complete--session-modified nil)
      ;; Note: We don't clear the shared context here, as it's shared across modes
      ;; Use ai-auto-complete-clear-context to clear the shared context
      (ai-auto-complete-chat-initialize))))

(defun ai-auto-complete-chat-cancel-request ()
  "Cancel the current chat request if one is in progress."
  (interactive)
  (when ai-auto-complete--chat-in-progress
    (message "Cancelling current chat request")
    (ai-auto-complete-chat-handle-response "ERROR: Request cancelled by user.")))

(defun ai-auto-complete-chat-insert-message (message role)
  "Insert MESSAGE with ROLE in the chat buffer.
   ROLE can be 'user, 'assistant, 'emacs, 'tool, 'tool-result, or 'agent.
   If ROLE is 'agent, MESSAGE should be a cons cell (agent-name . content)."
  (with-current-buffer (get-buffer ai-auto-complete-chat-buffer-name)
    (let ((inhibit-read-only t))
      ;; Go to the end of the buffer
      (goto-char (point-max))
      ;; If there's an input marker, delete from there to the end
      (when (and ai-auto-complete--chat-input-marker
                 (marker-position ai-auto-complete--chat-input-marker))
        (delete-region ai-auto-complete--chat-input-marker (point-max)))
      ;; Insert the message with the appropriate prefix and face
      (cond
       ((eq role 'user)
        (insert (propertize ai-auto-complete-chat-prompt-prefix
                           'face 'ai-auto-complete-user-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))
        (insert (propertize message
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t)))
       ((eq role 'assistant)
        (insert (propertize ai-auto-complete-chat-response-prefix
                           'face 'ai-auto-complete-assistant-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))
        (insert (propertize message
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t)))
       ((eq role 'emacs)
        (insert (propertize "emacs: "
                           'face 'ai-auto-complete-system-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))
        (insert (propertize message
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t)))
       ((eq role 'agent)
        (let ((agent-name (car message))
              (content (cdr message)))
          (insert (propertize (format "AGENT-%s: " agent-name)
                             'face 'ai-auto-complete-agent-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t))
          (insert (propertize content
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t))))
       ((eq role 'tool)
        (insert (propertize "Tool: "
                           'face 'font-lock-function-name-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))
        (insert (propertize message
                           'face 'font-lock-constant-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t)))
       ((eq role 'tool-result)
        (insert (propertize "Tool Result: "
                           'face 'font-lock-function-name-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))
        (insert (propertize message
                           'face 'font-lock-doc-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))))
      ;; Add a new prompt
      (insert (propertize "\n\n"
                         'read-only t
                         'front-sticky t
                         'rear-nonsticky t))
      (insert (propertize ai-auto-complete-chat-prompt-prefix
                         'face 'ai-auto-complete-user-face
                         'read-only t
                         'front-sticky t
                         'rear-nonsticky t))
      (setq ai-auto-complete--chat-input-marker (point-marker)))))

(defun ai-auto-complete-chat-display-tool-call (tool-name args)
  "Display a tool call in the chat buffer.
   TOOL-NAME is the name of the tool being called.
   ARGS is a plist of arguments for the tool."
  (let ((args-str ""))
    ;; Format the arguments
    (let ((arg-index 0))
      (while (< arg-index (length args))
        (let ((key (nth arg-index args))
              (value (nth (1+ arg-index) args)))
          (when (keywordp key)
            (setq args-str (concat args-str
                                  (format "\n  %s: %s"
                                          (substring (symbol-name key) 1)
                                          value))))
          (setq arg-index (+ arg-index 2)))))

    ;; Display the tool call
    (ai-auto-complete-chat-insert-message
     (format "%s%s" tool-name args-str)
     'tool)))

(defun ai-auto-complete-chat-display-tool-result (tool-name result)
  "Display a tool result in the chat buffer.
   TOOL-NAME is the name of the tool that was called.
   RESULT is the result returned by the tool."
  (ai-auto-complete-chat-insert-message
   (format "%s returned:\n%s" tool-name result)
   'tool-result))

(defun ai-auto-complete-chat-handle-agent-response (agent-name response)
  "Handle RESPONSE from AGENT-NAME in the chat buffer."
  (if (get-buffer ai-auto-complete-chat-buffer-name)
      (progn
        ;; Debug output
        (message "Received response from agent %s: %s" agent-name (or response "nil"))

        (with-current-buffer (get-buffer ai-auto-complete-chat-buffer-name)
          (let ((inhibit-read-only t))
            ;; Remove the "Thinking..." text
            (goto-char (point-max))
            (delete-region (line-beginning-position) (point-max))

            ;; Handle different response cases
            (cond
             ;; Case 1: Response is nil (API error)
             ((null response)
              (insert (propertize "emacs: "
                               'face 'ai-auto-complete-system-face
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))
              (insert (propertize (format "ERROR: Failed to get a response from agent %s. Please try again." agent-name)
                               'face 'error
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t)))

             ;; Case 2: Response is an error message
             ((and (stringp response) (string-match-p "^ERROR:" response))
              (insert (propertize "emacs: "
                               'face 'ai-auto-complete-system-face
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))
              (insert (propertize response
                               'face 'error
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t)))

             ;; Case 3: Normal response
             (t
              ;; Check if the response contains a message to another agent
              (if (and ai-auto-complete-agents-enabled
                       (string-match-p "^@[a-zA-Z0-9_-]+\\s-+" response))
                  (let ((agent-info (ai-auto-complete-message-to-agent response)))
                    (if agent-info
                        (let ((target-agent-name (car agent-info))
                              (agent-message (cdr agent-info)))
                          ;; Display the original agent's response
                          (insert (propertize (format "AGENT-%s: " agent-name)
                                             'face 'ai-auto-complete-agent-face
                                             'read-only t
                                             'front-sticky t
                                             'rear-nonsticky t))
                          (insert (propertize response
                                             'read-only t
                                             'front-sticky t
                                             'rear-nonsticky t))
                          ;; Process the message for the target agent
                          (insert (propertize "\n\n"
                                             'read-only t
                                             'front-sticky t
                                             'rear-nonsticky t))
                          (insert (propertize (format "@%s: " target-agent-name)
                                              'face 'ai-auto-complete-agent-face
                                              'read-only t
                                              'front-sticky t
                                              'rear-nonsticky t))
                          (insert (propertize "Thinking..."
                                             'face 'italic
                                             'read-only t
                                             'front-sticky t
                                             'rear-nonsticky t))
                          (setq ai-auto-complete--chat-in-progress t)
                          ;; Process the agent message
                          (message "Processing message for agent %s: %s" target-agent-name agent-message)
                          (ai-auto-complete-process-agent-message
                           target-agent-name
                           agent-message
                           ai-auto-complete--chat-history
                           (lambda (target-agent-name response)
                             ;; Add the response to history
                             (push (cons 'agent (cons target-agent-name response)) ai-auto-complete--chat-history)
                             ;; Display the response
                             (ai-auto-complete-chat-handle-agent-response target-agent-name response))))
                      ;; Invalid agent format
                      (progn
                        ;; Just display the original response
                        (insert (propertize (format "AGENT-%s: " agent-name)
                                           'face 'ai-auto-complete-agent-face
                                           'read-only t
                                           'front-sticky t
                                           'rear-nonsticky t))
                        (insert (propertize response
                                           'read-only t
                                           'front-sticky t
                                           'rear-nonsticky t)))))
                ;; Regular response
                (insert (propertize (format "AGENT-%s: " agent-name)
                                   'face 'ai-auto-complete-agent-face
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t))
                (insert (propertize response
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t)))))

            ;; Always add a new prompt, regardless of response type
            (insert (propertize "\n\n"
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t))
            ;; Make the prompt prefix read-only but allow typing after it
            (insert (propertize ai-auto-complete-chat-prompt-prefix
                              'face 'ai-auto-complete-user-face
                              'read-only t
                              'front-sticky t
                              'rear-nonsticky t))
            (setq ai-auto-complete--chat-input-marker (point-marker))

            ;; Make sure the buffer is in a state where the user can type
            (setq buffer-read-only nil)

            ;; Always reset the in-progress flag
            (setq ai-auto-complete--chat-in-progress nil))))

    ;; Buffer not found case
    (message "Chat buffer not found, cannot display agent response")))

(provide 'chat)
;;; chat.el ends here
