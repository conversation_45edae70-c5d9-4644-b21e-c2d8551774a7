{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"id": "tUQ8OYL8sisYxLmpz6vvP", "type": "text", "x": 315.8828589033415, "y": 197.2153324562221, "width": 150.5291320289969, "height": 20, "angle": 0.044570500314094375, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a1", "roundness": null, "seed": 150539713, "version": 363, "versionNonce": 1126490671, "isDeleted": false, "boundElements": null, "updated": 1747539455659, "link": null, "locked": false, "text": "...chat-send-input()", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "...chat-send-input()", "autoResize": false, "lineHeight": 1.25}, {"id": "yZmF12DN_mHZ18G3ltABA", "type": "arrow", "x": 479.6378531277336, "y": 210.62493023669208, "width": 66.36214687226641, "height": 3.375069763307923, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a2", "roundness": {"type": 2}, "seed": 2017158913, "version": 178, "versionNonce": 1934603489, "isDeleted": false, "boundElements": null, "updated": 1747539466875, "link": null, "locked": false, "points": [[0, 0], [14.362146872266408, 3.375069763307923], [66.36214687226641, 3.375069763307923]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "lJvjos1lqa5QDP5POFih7", "type": "text", "x": 571, "y": 206.5, "width": 247.6004638671875, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a4", "roundness": null, "seed": 1242744897, "version": 126, "versionNonce": 1189262127, "isDeleted": false, "boundElements": [], "updated": 1747539485275, "link": null, "locked": false, "text": "...chat-process-agent-message()", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "...chat-process-agent-message()", "autoResize": true, "lineHeight": 1.25}, {"id": "1NHQm9-Qg2j1U6_UndIoV", "type": "text", "x": 1088, "y": 395, "width": 301.919677734375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a6", "roundness": null, "seed": 1936543329, "version": 91, "versionNonce": 26763183, "isDeleted": false, "boundElements": null, "updated": 1747537492837, "link": null, "locked": false, "text": "...chat-handle-agent-response()", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "...chat-handle-agent-response()", "autoResize": true, "lineHeight": 1.25}, {"id": "gorIDVDtRTN260bpmAm4r", "type": "arrow", "x": 1154, "y": 433, "width": 158, "height": 33, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a7", "roundness": {"type": 2}, "seed": 341089743, "version": 29, "versionNonce": 1406448687, "isDeleted": false, "boundElements": null, "updated": 1747538397451, "link": null, "locked": false, "points": [[0, 0], [-158, 33]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "CytHt0ztd2sGDRGzboKZQ", "type": "text", "x": 373, "y": 164, "width": 64.71994018554688, "height": 25, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aA", "roundness": null, "seed": 1795803663, "version": 22, "versionNonce": 1832537953, "isDeleted": false, "boundElements": null, "updated": 1747537809064, "link": null, "locked": false, "text": "chat.el", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "chat.el", "autoResize": true, "lineHeight": 1.25}, {"id": "q-SxngUBQpvWbV8cxnPos", "type": "text", "x": 555, "y": 167.5, "width": 108.3201904296875, "height": 20, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aB", "roundness": null, "seed": 1377345935, "version": 87, "versionNonce": 863570593, "isDeleted": false, "boundElements": null, "updated": 1747539471536, "link": null, "locked": false, "text": "agents-core.el", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "agents-core.el", "autoResize": true, "lineHeight": 1.25}, {"id": "81IYK5F8lT6ZEbRvBgZvG", "type": "text", "x": 1244, "y": 355, "width": 64.71994018554688, "height": 25, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aD", "roundness": null, "seed": 805820609, "version": 11, "versionNonce": 780328687, "isDeleted": false, "boundElements": null, "updated": 1747538012805, "link": null, "locked": false, "text": "chat.el", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "chat.el", "autoResize": true, "lineHeight": 1.25}, {"id": "4LXS_zLxzT9Xe5D1bs5E2", "type": "arrow", "x": 846.6355939598028, "y": 224.5, "width": 56.364406040197196, "height": 1.5, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aE", "roundness": {"type": 2}, "seed": 1379741487, "version": 187, "versionNonce": 1468356865, "isDeleted": false, "boundElements": null, "updated": 1747539507540, "link": null, "locked": false, "points": [[0, 0], [56.364406040197196, 1.5]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "i8_p8csYkDkoAjGtCQQ31", "type": "text", "x": 920, "y": 216, "width": 269.69970703125, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aF", "roundness": null, "seed": 1978843311, "version": 173, "versionNonce": 277462031, "isDeleted": false, "boundElements": [{"id": "l2a5EZG-3_cwKDieh6yyr", "type": "arrow"}, {"id": "xq8SmRq4kBkcSZXfoIz25", "type": "arrow"}], "updated": 1747539510206, "link": null, "locked": false, "text": "ai-auto-complete-complete()", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai-auto-complete-complete()", "autoResize": true, "lineHeight": 1.25}, {"id": "l2a5EZG-3_cwKDieh6yyr", "type": "arrow", "x": 1075.1825236159568, "y": 252, "width": 192.81747638404318, "height": 82, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aG", "roundness": {"type": 2}, "seed": 626082991, "version": 545, "versionNonce": 1932878383, "isDeleted": false, "boundElements": null, "updated": 1747539510206, "link": null, "locked": false, "points": [[0, 0], [192.81747638404318, 82]], "lastCommittedPoint": null, "startBinding": {"elementId": "i8_p8csYkDkoAjGtCQQ31", "focus": 0.2126486220335267, "gap": 11}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "X4hKJUjH16wy569j1cxC3", "type": "text", "x": 959, "y": 136, "width": 211.13031005859375, "height": 56.000000000000014, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aH", "roundness": null, "seed": 1992463919, "version": 452, "versionNonce": 232187727, "isDeleted": false, "boundElements": [], "updated": 1747539554832, "link": null, "locked": false, "text": "triggers tool definition \ninsertion  via advice function\nvia ", "fontSize": 14.933333333333337, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "triggers tool definition \ninsertion  via advice function\nvia ", "autoResize": true, "lineHeight": 1.25}, {"id": "gxGKI_DtnAfEFAwFIoP4m", "type": "text", "x": 1270, "y": 207, "width": 84.29991149902344, "height": 25, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aI", "roundness": null, "seed": 1464073761, "version": 10, "versionNonce": 453675919, "isDeleted": false, "boundElements": null, "updated": 1747538289819, "link": null, "locked": false, "text": "response", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "response", "autoResize": true, "lineHeight": 1.25}, {"id": "niv-alc3Fiof8N8PoYa1n", "type": "text", "x": 1041, "y": 471, "width": 504.69952392578125, "height": 100, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aJ", "roundness": null, "seed": 1906719265, "version": 440, "versionNonce": 698975759, "isDeleted": false, "boundElements": null, "updated": 1747538501864, "link": null, "locked": false, "text": "triggers parsing for tools and\nif tools are found, ai-auto-complete-complete()\nmust be called again, along with agent-name\n", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "triggers parsing for tools and\nif tools are found, ai-auto-complete-complete()\nmust be called again, along with agent-name\n", "autoResize": false, "lineHeight": 1.25}, {"id": "BNkAZn3w9b3KFgJ8OmhaF", "type": "text", "x": 491, "y": 552, "width": 406.79949951171875, "height": 50, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aK", "roundness": null, "seed": 46306817, "version": 92, "versionNonce": 795633039, "isDeleted": false, "boundElements": [{"id": "cZ0h4Rbe0axh8Twcw5nlZ", "type": "arrow"}], "updated": 1747538760994, "link": null, "locked": false, "text": "ai-auto-complete-tools-start-processing()\n", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai-auto-complete-tools-start-processing()\n", "autoResize": true, "lineHeight": 1.25}, {"id": "YGKiHqg45d3QjdCfFpbbQ", "type": "text", "x": 492, "y": 483, "width": 512.119384765625, "height": 25, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aL", "roundness": null, "seed": 2065095713, "version": 133, "versionNonce": 1160392015, "isDeleted": false, "boundElements": [{"id": "cZ0h4Rbe0axh8Twcw5nlZ", "type": "arrow"}], "updated": 1747538760994, "link": null, "locked": false, "text": "ai-auto-complete-tools-process-with-state-machine()", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai-auto-complete-tools-process-with-state-machine()", "autoResize": true, "lineHeight": 1.25}, {"id": "cZ0h4Rbe0axh8Twcw5nlZ", "type": "arrow", "x": 662, "y": 522, "width": 1, "height": 25, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aN", "roundness": {"type": 2}, "seed": 1753498657, "version": 14, "versionNonce": 892709743, "isDeleted": false, "boundElements": null, "updated": 1747538760994, "link": null, "locked": false, "points": [[0, 0], [1, 25]], "lastCommittedPoint": null, "startBinding": {"elementId": "YGKiHqg45d3QjdCfFpbbQ", "focus": 0.33956889943889235, "gap": 14}, "endBinding": {"elementId": "BNkAZn3w9b3KFgJ8OmhaF", "focus": -0.1477484673632463, "gap": 5}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "xq8SmRq4kBkcSZXfoIz25", "type": "arrow", "x": 1084.0710937439924, "y": 212, "width": 13.825069146902933, "height": 112.99999999999997, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aO", "roundness": {"type": 2}, "seed": 1192771201, "version": 565, "versionNonce": 2084250991, "isDeleted": false, "boundElements": null, "updated": 1747539554833, "link": null, "locked": false, "points": [[0, 0], [13.825069146902933, -112.99999999999997]], "lastCommittedPoint": null, "startBinding": {"elementId": "i8_p8csYkDkoAjGtCQQ31", "focus": 0.19946253067465974, "gap": 4}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "HjzPJ9mGBndc-DA0olIyX", "type": "text", "x": 1106, "y": 91, "width": 308.99261474609375, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aQ", "roundness": null, "seed": 937307233, "version": 41, "versionNonce": 1616522031, "isDeleted": false, "boundElements": null, "updated": 1747539586418, "link": null, "locked": false, "text": "ai-auto-complete-tools-advice-request()", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai-auto-complete-tools-advice-request()", "autoResize": true, "lineHeight": 1.25}, {"id": "NPr8-bVsvoljg1KaEo8r1", "type": "text", "x": 1163, "y": 62, "width": 148.06430053710938, "height": 20, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aR", "roundness": null, "seed": 1218676481, "version": 30, "versionNonce": 384741167, "isDeleted": false, "boundElements": null, "updated": 1747539605738, "link": null, "locked": false, "text": "tools-integration.el", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "tools-integration.el", "autoResize": true, "lineHeight": 1.25}, {"id": "X5BqNWw5YYod2ny2r4mpr", "type": "text", "x": 520, "y": 461, "width": 172.1283416748047, "height": 20, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aS", "roundness": null, "seed": 698478447, "version": 24, "versionNonce": 445905953, "isDeleted": false, "boundElements": null, "updated": 1747539648788, "link": null, "locked": false, "text": "tools-state-machine.el", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "tools-state-machine.el", "autoResize": true, "lineHeight": 1.25}], "appState": {"gridSize": 20, "gridStep": 5, "gridModeEnabled": false, "viewBackgroundColor": "#ffffff"}, "files": {}}