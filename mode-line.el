;;; mode-line.el --- Mode line indicators for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file contains functions for updating the mode line to show mode-specific backends.

;;; Code:

(require 'mode-models)

;; Use ai-auto-complete--get-short-model-name from the main file

(defun ai-auto-complete--update-mode-line-mode-specific ()
  "Update the mode line indicator to show mode-specific backends and models."
  (cond
   (ai-auto-complete-mode
    (let* ((backend ai-auto-complete-code-backend)
           (model (ai-auto-complete-get-current-model backend))
           (short-model (ai-auto-complete--get-short-model-name model)))
      (setq ai-auto-complete--active-mode-line
            (concat " AI-Code:"
                    (symbol-name backend)
                    "/"
                    short-model
                    (if ai-auto-complete--pending-request "..." "")))))
   (ai-auto-complete-text-mode
    (let* ((backend ai-auto-complete-text-backend)
           (model (ai-auto-complete-get-current-model backend))
           (short-model (ai-auto-complete--get-short-model-name model)))
      (setq ai-auto-complete--active-mode-line
            (concat " AI-Text:"
                    (symbol-name backend)
                    "/"
                    short-model
                    (if ai-auto-complete--pending-request "..." "")))))
   (ai-auto-complete-chat-mode
    (let* ((backend ai-auto-complete-chat-backend)
           (model (ai-auto-complete-get-current-model backend))
           (short-model (ai-auto-complete--get-short-model-name model)))
      (setq ai-auto-complete--active-mode-line
            (concat " AI-Chat:"
                    (symbol-name backend)
                    "/"
                    short-model
                    (if ai-auto-complete--pending-request "..." "")))))
   (t
    (let* ((backend ai-auto-complete-backend)
           (model (cond
                  ((eq backend 'gemini) ai-auto-complete-gemini-model)
                  ((eq backend 'openai) ai-auto-complete-openai-model)
                  ((eq backend 'anthropic) ai-auto-complete-anthropic-model)
                  ((eq backend 'openrouter) ai-auto-complete-openrouter-model)
                  (t "unknown")))
           (short-model (ai-auto-complete--get-short-model-name model)))
      (setq ai-auto-complete--active-mode-line
            (concat " AI:"
                    (symbol-name backend)
                    "/"
                    short-model
                    (if ai-auto-complete--pending-request "..." ""))))))
  (force-mode-line-update))

(provide 'mode-line)
;;; mode-line.el ends here
