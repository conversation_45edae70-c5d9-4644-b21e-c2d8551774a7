;; Set up load path
(add-to-list 'load-path (expand-file-name "ai-auto-complete-modules"))
(add-to-list 'load-path (expand-file-name "."))

;; Define minimal test environment
(defvar ai-auto-complete-mcp-enabled t)

(defun ai-auto-complete-mcp-list-servers ()
  "Mock function to list MCP servers."
  '("test-server"))

(defun ai-auto-complete-mcp-get-server (name)
  "Mock function to get MCP server info for NAME."
  (list :description "Test server" :status 'running))

(defun ai-auto-complete-get-system-prompt (backend &optional agent-name)
  "Mock function for getting system prompt for BACKEND and optional AGENT-NAME."
  (format "You are a helpful AI assistant for %s." backend))

;; Load the MCP prompt module
(load-file "ai-auto-complete-modules/mcp/mcp-prompt.el")

;; Test the advice function
(defun test-mcp-advice ()
  "Test the MCP advice function."
  ;; Test with one argument
  (message "\nTest 1: Single argument")
  (let ((result (ai-auto-complete-get-system-prompt 'gemini)))
    (message "Result: %s" result)
    (message "Contains 'You are a helpful AI assistant for gemini': %s"
             (if (string-match-p "You are a helpful AI assistant for gemini" result) "yes" "no"))
    (message "Contains 'You can use Model Context Protocol': %s"
             (if (string-match-p "You can use Model Context Protocol" result) "yes" "no"))
    (if (and (string-match-p "You are a helpful AI assistant for gemini" result)
             (string-match-p "You can use Model Context Protocol" result))
        (message "Test 1 passed: MCP advice function works correctly with single argument")
      (message "Test 1 failed: MCP advice function did not produce expected output")))

  ;; Test with two arguments
  (message "\nTest 2: Multiple arguments")
  (let ((result (ai-auto-complete-get-system-prompt 'gemini 'test-agent)))
    (message "Result: %s" result)
    (message "Contains 'You are a helpful AI assistant for gemini': %s"
             (if (string-match-p "You are a helpful AI assistant for gemini" result) "yes" "no"))
    (message "Contains 'You can use Model Context Protocol': %s"
             (if (string-match-p "You can use Model Context Protocol" result) "yes" "no"))
    (if (and (string-match-p "You are a helpful AI assistant for gemini" result)
             (string-match-p "You can use Model Context Protocol" result))
        (message "Test 2 passed: MCP advice function works correctly with multiple arguments")
      (message "Test 2 failed: MCP advice function did not produce expected output"))))

;; Run the test
(test-mcp-advice)
