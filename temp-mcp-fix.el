    (ai-auto-complete-complete
     (ai-auto-complete-get-current-backend)
     continuation-prompt
     nil  ;; Use empty history to avoid confusion with previous messages
     (lambda (continuation-response)
       (when ai-auto-complete-mcp-debug-mode
         (message "[MCP-DEBUG] Received continuation response from LLM"))
       
       ;; Check if the continuation response contains more tool calls
       (if (string-match-p "<mcp-tool\\s-+\\(name\\|server\\)=" continuation-response)
           ;; Process the new tool calls
           (ai-auto-complete-mcp-process-tool-calls continuation-response callback agent-name)
         
         ;; No more tool calls, combine the original response and continuation
         (let ((final-response (ai-auto-complete-mcp-combine-responses
                               response continuation-response tool-calls results)))
           (funcall callback final-response))))
     agent-name))))
