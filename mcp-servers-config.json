{"ElevenLabs": {"path": "elevenlabs-mcp", "transport": "server-bridge", "description": "MCP server: ElevenLabs (from settings)", "status": "stopped", "server-type": "python", "runner": "uvx", "api-key": "***************************************************", "serverArgs": ["elevenlabs-mcp"], "env": [{"ELEVENLABS_API_KEY": "***************************************************"}], "tools": [{"name": "text_to_speech", "description": "Convert text to speech with a given voice and save the output audio file to a given directory.", "parameters": [{"name": "text", "description": "The text to convert to speech.", "required": true}, {"name": "voice_name", "description": "The name of the voice to use."}, {"name": "output_directory", "description": "Directory where files should be saved. Defaults to $HOME/Desktop if not provided."}]}, {"name": "speech_to_text", "description": "Transcribe speech from an audio file and save the output text file or return the text directly.", "parameters": [{"name": "input_file_path", "description": "Path to the audio file to transcribe", "required": true}, {"name": "language_code", "description": "ISO 639-3 language code for transcription (default: \"eng\" for English)"}, {"name": "output_directory", "description": "Directory where files should be saved. Defaults to $HOME/Desktop if not provided."}]}, {"name": "text_to_sound_effects", "description": "Convert text description of a sound effect to sound effect with a given duration.", "parameters": [{"name": "text", "description": "Text description of the sound effect", "required": true}, {"name": "duration_seconds", "description": "Duration of the sound effect in seconds (0.5 to 5 seconds)"}, {"name": "output_directory", "description": "Directory where files should be saved. Defaults to $HOME/Desktop if not provided."}]}]}, "brave-search": {"path": "/home/<USER>/.emacs.d/20250503114145_20250428183208_20250426210850_ai-auto-complete-unified/ai-auto-complete-modules/mcp/bridge/typescript-mcp-bridge/node_modules/brave-search-mcp/dist/index.js", "transport": "typescript-bridge", "description": "MCP server: brave-search (from settings)", "status": "stopped", "server-type": "javascript", "runner": "node", "api-key": "BSAYZHT8BTTufnAI2AZW46b2BNAmaUr", "serverArgs": [""], "env": [{"BRAVE_API_KEY": "BSAYZHT8BTTufnAI2AZW46b2BNAmaUr"}], "tools": [{"name": "brave_web_search", "description": "Execute web searches with pagination and filtering", "parameters": [{"name": "query", "description": "Search terms", "required": true}, {"name": "count", "description": "Results per page (max 20)", "required": false}, {"name": "offset", "description": "Pagination offset (max 9)", "required": false}]}, {"name": "brave_local_search", "description": "Search for local businesses and services. Automatically falls back to web search if no local results found", "parameters": [{"name": "query", "description": "Local search terms", "required": true}, {"name": "count", "description": "Number of results (max 20)", "required": false}]}]}, "fetch-mcp": {"path": "/home/<USER>/Documents/Cline/MCP/fetch-mcp/dist/index.js", "transport": "typescript-bridge", "description": "Node.js MCP server: fetch-mcp", "status": "stopped", "server-type": "javascript", "runner": "node", "api-key": "", "serverArgs": [""], "env": [], "tools": [{"name": "fetch_html", "description": "Fetch a website and return the content as HTML", "parameters": [{"name": "url", "description": "URL of the website to fetch", "required": true}, {"name": "headers", "description": "Optional headers to include in the request"}]}, {"name": "fetch_markdown", "description": "Fetch a website and return the content as Markdown", "parameters": [{"name": "url", "description": "URL of the website to fetch", "required": true}, {"name": "headers", "description": "Optional headers to include in the request"}]}, {"name": "fetch_txt", "description": "Fetch a website, return the content as plain text (no HTML)", "parameters": [{"name": "url", "description": "URL of the website to fetch", "required": true}, {"name": "headers", "description": "Optional headers to include in the request"}]}, {"name": "fetch_json", "description": "Fetch a JSON file from a URL", "parameters": [{"name": "url", "description": "URL of the JSON to fetch", "required": true}, {"name": "headers", "description": "Optional headers to include in the request"}]}]}, "mcp-server-postgrest": {"path": "/home/<USER>/Documents/Cline/MCP/mcp-server-postgrest/index.js", "transport": "stdio", "description": "Node.js MCP server: mcp-server-postgrest", "status": "stopped", "server-type": "javascript", "runner": "node", "api-key": "", "serverArgs": [""], "env": [], "tools": [{"name": "postgrestRequest", "description": "Performs HTTP request against the PostgREST API", "parameters": [{"name": "method", "description": "HTTP method (GET, POST, PUT, PATCH, DELETE)", "required": true}, {"name": "path", "description": "API path", "required": true}, {"name": "body", "description": "Request body object"}]}, {"name": "sqlToRest", "description": "Converts SQL query to a PostgREST API request (method, path)", "parameters": [{"name": "sql", "description": "SQL query to convert", "required": true}]}]}}