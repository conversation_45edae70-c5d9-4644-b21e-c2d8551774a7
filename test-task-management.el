;;; test-task-management.el --- Tests for task management tools -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides comprehensive tests for the task management tools
;; to verify they are working correctly.

;;; Code:

;; Try to load the required modules
(condition-case err
    (progn
      (require 'tools/task-management)
      (require 'tools/tools-core))
  (error
   ;; If require fails, try loading directly
   (condition-case err2
       (progn
         (load-file "ai-auto-complete-modules/tools/tools-core.el")
         (load-file "ai-auto-complete-modules/tools/task-management.el"))
     (error
      (message "ERROR: Cannot load required modules: %s" (error-message-string err2))))))

;; Test configuration
(defvar test-task-management-debug t
  "Whether to enable debug output for task management tests.")

(defvar test-task-management-results nil
  "List to store test results.")

;; Helper function to log test results
(defun test-task-management-log (test-name success message)
  "Log a test result for TEST-NAME with SUCCESS status and MESSAGE."
  (let ((result (list :test test-name :success success :message message :timestamp (current-time))))
    (push result test-task-management-results)
    (when test-task-management-debug
      (message "[TEST-%s] %s: %s"
               (if success "PASS" "FAIL")
               test-name
               message))))

;; Helper function to check if a string contains expected content
(defun test-task-management-contains-p (string expected)
  "Check if STRING contains EXPECTED content."
  (and (stringp string) (string-match-p (regexp-quote expected) string)))

;; Test 1: Tool Registration
(defun test-task-management-tool-registration ()
  "Test that all task management tools are properly registered."
  (let ((expected-tools '("create_task_plan" "execute_planned_step"
                         "update_task_plan" "get_task_plan_status"))
        (missing-tools nil)
        (success t))

    (dolist (tool-name expected-tools)
      (let ((tool (gethash tool-name ai-auto-complete-tools)))
        (if tool
            (test-task-management-log
             (format "tool-registration-%s" tool-name)
             t
             (format "Tool %s is registered" tool-name))
          (progn
            (push tool-name missing-tools)
            (setq success nil)
            (test-task-management-log
             (format "tool-registration-%s" tool-name)
             nil
             (format "Tool %s is NOT registered" tool-name))))))

    (test-task-management-log
     "tool-registration-overall"
     success
     (if success
         "All task management tools are registered"
       (format "Missing tools: %s" missing-tools)))

    success))

;; Test 2: Create Task Plan
(defun test-task-management-create-plan ()
  "Test creating a task plan."
  (let* ((test-params '((task_description . "Test task: List files and read one")
                       (agent_name . "test-agent")
                       (notes . "This is a test plan")
                       (steps . (((description . "List current directory")
                                 (tool_name . "list_directory")
                                 (tool_params . ((path . "."))))
                                ((description . "Read a file")
                                 (tool_name . "read_file")
                                 (tool_params . ((path . "README.md"))))))))
         (result (condition-case err
                     (ai-auto-complete-tool-create-task-plan test-params)
                   (error (format "ERROR: %s" (error-message-string err))))))

    (if (and (stringp result)
             (test-task-management-contains-p result "Task plan")
             (test-task-management-contains-p result "created with"))
        (progn
          (test-task-management-log
           "create-plan"
           t
           (format "Successfully created task plan: %s" result))
          ;; Extract plan ID for use in other tests
          (when (string-match "Task plan \\([^ ]+\\) created" result)
            (setq test-task-management-plan-id (match-string 1 result)))
          t)
      (progn
        (test-task-management-log
         "create-plan"
         nil
         (format "Failed to create task plan: %s" result))
        nil))))

;; Test 3: Get Task Plan Status
(defun test-task-management-get-status ()
  "Test getting task plan status."
  (if (not (boundp 'test-task-management-plan-id))
      (progn
        (test-task-management-log
         "get-status"
         nil
         "No plan ID available from create test")
        nil)

    (let* ((test-params `((plan_id . ,test-task-management-plan-id)))
           (result (condition-case err
                       (ai-auto-complete-tool-get-task-plan-status test-params)
                     (error (format "ERROR: %s" (error-message-string err))))))

      (if (and (stringp result)
               (test-task-management-contains-p result "Task Plan:")
               (test-task-management-contains-p result "Status:")
               (test-task-management-contains-p result "Progress:"))
          (progn
            (test-task-management-log
             "get-status"
             t
             (format "Successfully got task plan status: %s" result))
            t)
        (progn
          (test-task-management-log
           "get-status"
           nil
           (format "Failed to get task plan status: %s" result))
          nil)))))

;; Test 4: Execute Planned Step
(defun test-task-management-execute-step ()
  "Test executing a planned step."
  (if (not (boundp 'test-task-management-plan-id))
      (progn
        (test-task-management-log
         "execute-step"
         nil
         "No plan ID available from create test")
        nil)

    (let* ((test-params `((plan_id . ,test-task-management-plan-id)))
           (result (condition-case err
                       (ai-auto-complete-tool-execute-planned-step test-params)
                     (error (format "ERROR: %s" (error-message-string err))))))

      (if (and (stringp result)
               (or (test-task-management-contains-p result "Step")
                   (test-task-management-contains-p result "completed")
                   (test-task-management-contains-p result "ERROR")))
          (progn
            (test-task-management-log
             "execute-step"
             t
             (format "Successfully executed planned step: %s" (substring result 0 (min 100 (length result)))))
            t)
        (progn
          (test-task-management-log
           "execute-step"
           nil
           (format "Failed to execute planned step: %s" result))
          nil)))))

;; Test 5: Update Task Plan
(defun test-task-management-update-plan ()
  "Test updating a task plan."
  (if (not (boundp 'test-task-management-plan-id))
      (progn
        (test-task-management-log
         "update-plan"
         nil
         "No plan ID available from create test")
        nil)

    (let* ((test-params `((plan_id . ,test-task-management-plan-id)
                         (updated_steps . (((description . "List parent directory")
                                           (tool_name . "list_directory")
                                           (tool_params . ((path . ".."))))
                                          ((description . "Get system info")
                                           (tool_name . "get_system_info")
                                           (tool_params . ()))))))
           (result (condition-case err
                       (ai-auto-complete-tool-update-task-plan test-params)
                     (error (format "ERROR: %s" (error-message-string err))))))

      (if (and (stringp result)
               (test-task-management-contains-p result "Task plan")
               (test-task-management-contains-p result "updated with"))
          (progn
            (test-task-management-log
             "update-plan"
             t
             (format "Successfully updated task plan: %s" result))
            t)
        (progn
          (test-task-management-log
           "update-plan"
           nil
           (format "Failed to update task plan: %s" result))
          nil)))))

;; Test 6: Error Handling
(defun test-task-management-error-handling ()
  "Test error handling for invalid parameters."
  (let ((tests '(("create-plan-no-description"
                 ai-auto-complete-tool-create-task-plan
                 ((agent_name . "test") (steps . ())))
                ("create-plan-no-steps"
                 ai-auto-complete-tool-create-task-plan
                 ((task_description . "test") (agent_name . "test")))
                ("execute-step-no-id"
                 ai-auto-complete-tool-execute-planned-step
                 ())
                ("get-status-no-id"
                 ai-auto-complete-tool-get-task-plan-status
                 ())
                ("execute-step-invalid-id"
                 ai-auto-complete-tool-execute-planned-step
                 ((plan_id . "invalid-id-12345")))))
        (all-success t))

    (dolist (test tests)
      (let* ((test-name (nth 0 test))
             (test-func (nth 1 test))
             (test-params (nth 2 test))
             (result (condition-case err
                         (funcall test-func test-params)
                       (error (format "ERROR: %s" (error-message-string err))))))

        (if (and (stringp result) (test-task-management-contains-p result "ERROR"))
            (test-task-management-log
             test-name
             t
             (format "Correctly handled error: %s" result))
          (progn
            (setq all-success nil)
            (test-task-management-log
             test-name
             nil
             (format "Did not handle error correctly: %s" result))))))

    (test-task-management-log
     "error-handling-overall"
     all-success
     (if all-success
         "All error handling tests passed"
       "Some error handling tests failed"))

    all-success))

;; Test 7: Plan Data Structure Integrity
(defun test-task-management-data-integrity ()
  "Test that task plan data structures are created correctly."
  (if (not (boundp 'test-task-management-plan-id))
      (progn
        (test-task-management-log
         "data-integrity"
         nil
         "No plan ID available from create test")
        nil)

    (let ((plan (ai-auto-complete-get-task-plan test-task-management-plan-id)))
      (if (not plan)
          (progn
            (test-task-management-log
             "data-integrity"
             nil
             "Could not retrieve plan from hash table")
            nil)

        (let ((checks '(("plan-id" . (ai-auto-complete-task-plan-id plan))
                       ("original-request" . (ai-auto-complete-task-plan-original-request plan))
                       ("status" . (ai-auto-complete-task-plan-status plan))
                       ("sub-tasks" . (ai-auto-complete-task-plan-sub-tasks plan))
                       ("agent-name" . (ai-auto-complete-task-plan-agent-name plan))))
              (all-valid t))

          (dolist (check checks)
            (let ((field-name (car check))
                  (field-value (cdr check)))
              (if field-value
                  (test-task-management-log
                   (format "data-integrity-%s" field-name)
                   t
                   (format "Field %s has value: %s" field-name field-value))
                (progn
                  (setq all-valid nil)
                  (test-task-management-log
                   (format "data-integrity-%s" field-name)
                   nil
                   (format "Field %s is missing or nil" field-name))))))

          (test-task-management-log
           "data-integrity-overall"
           all-valid
           (if all-valid
               "All data integrity checks passed"
             "Some data integrity checks failed"))

          all-valid)))))

;; Main test runner
(defun test-task-management-run-all-tests ()
  "Run all task management tests and return a summary."
  (interactive)
  (message "Starting task management tests...")

  ;; Clear previous results
  (setq test-task-management-results nil)
  (setq test-task-management-plan-id nil)

  ;; Enable tools if not already enabled
  (unless ai-auto-complete-tools-enabled
    (setq ai-auto-complete-tools-enabled t)
    (message "Enabled tools for testing"))

  ;; Run all tests
  (let ((tests '(("Tool Registration" . test-task-management-tool-registration)
                ("Create Task Plan" . test-task-management-create-plan)
                ("Get Task Plan Status" . test-task-management-get-status)
                ("Execute Planned Step" . test-task-management-execute-step)
                ("Update Task Plan" . test-task-management-update-plan)
                ("Error Handling" . test-task-management-error-handling)
                ("Data Integrity" . test-task-management-data-integrity)))
        (passed 0)
        (failed 0)
        (total 0))

    (dolist (test tests)
      (let ((test-name (car test))
            (test-func (cdr test)))
        (message "Running test: %s" test-name)
        (setq total (1+ total))
        (if (funcall test-func)
            (setq passed (1+ passed))
          (setq failed (1+ failed)))))

    ;; Generate summary
    (let ((summary (format "\n=== TASK MANAGEMENT TEST SUMMARY ===\nTotal Tests: %d\nPassed: %d\nFailed: %d\nSuccess Rate: %.1f%%\n"
                          total passed failed
                          (if (> total 0) (* 100.0 (/ (float passed) total)) 0.0))))

      ;; Display detailed results
      (with-current-buffer (get-buffer-create "*Task Management Test Results*")
        (erase-buffer)
        (insert summary)
        (insert "\n=== DETAILED RESULTS ===\n")

        (dolist (result (reverse test-task-management-results))
          (insert (format "[%s] %s: %s\n"
                         (if (plist-get result :success) "PASS" "FAIL")
                         (plist-get result :test)
                         (plist-get result :message))))

        (insert "\n=== ACTIVE TASK PLANS ===\n")
        (if (> (hash-table-count ai-auto-complete-active-task-plans) 0)
            (maphash (lambda (id plan)
                      (insert (format "Plan ID: %s\n" id))
                      (insert (format "  Request: %s\n" (ai-auto-complete-task-plan-original-request plan)))
                      (insert (format "  Status: %s\n" (ai-auto-complete-task-plan-status plan)))
                      (insert (format "  Steps: %d\n" (length (ai-auto-complete-task-plan-sub-tasks plan))))
                      (insert "\n"))
                    ai-auto-complete-active-task-plans)
          (insert "No active task plans found.\n"))

        (goto-char (point-min))
        (display-buffer (current-buffer)))

      (message "%s" summary)
      (> passed 0))))

;; Interactive test for LLM integration
(defun test-task-management-llm-integration ()
  "Test task management tools with LLM integration."
  (interactive)
  (unless ai-auto-complete-tools-enabled
    (setq ai-auto-complete-tools-enabled t)
    (message "Enabled tools for LLM integration test"))

  (let ((test-prompt "I need you to create a task plan for organizing my workspace. The plan should have these steps:
1. List the current directory contents
2. Check system information
3. List the parent directory

Please use the create_task_plan tool to create this plan, then use get_task_plan_status to check its status."))

    (message "Starting LLM integration test...")
    (message "Test prompt: %s" test-prompt)

    ;; Get current backend
    (let ((backend (if (fboundp 'ai-auto-complete-get-current-backend)
                      (ai-auto-complete-get-current-backend)
                    'default)))

      (if (fboundp 'ai-auto-complete-complete)
          (progn
            (message "Sending test prompt to LLM backend: %s" backend)
            (ai-auto-complete-complete
             backend
             test-prompt
             nil
             (lambda (response)
               (message "LLM Integration Test Response received")
               (with-current-buffer (get-buffer-create "*Task Management LLM Test*")
                 (erase-buffer)
                 (insert "=== TASK MANAGEMENT LLM INTEGRATION TEST ===\n\n")
                 (insert "Test Prompt:\n")
                 (insert test-prompt)
                 (insert "\n\nLLM Response:\n")
                 (insert response)
                 (insert "\n\n=== ACTIVE TASK PLANS AFTER TEST ===\n")
                 (if (> (hash-table-count ai-auto-complete-active-task-plans) 0)
                     (maphash (lambda (id plan)
                               (insert (format "Plan ID: %s\n" id))
                               (insert (format "  Request: %s\n" (ai-auto-complete-task-plan-original-request plan)))
                               (insert (format "  Status: %s\n" (ai-auto-complete-task-plan-status plan)))
                               (insert (format "  Agent: %s\n" (ai-auto-complete-task-plan-agent-name plan)))
                               (insert "\n"))
                             ai-auto-complete-active-task-plans)
                   (insert "No active task plans found.\n"))
                 (goto-char (point-min))
                 (display-buffer (current-buffer))))
             "Jadyaa"))
        (message "ERROR: ai-auto-complete-complete function not available")))))

;; Cleanup function
(defun test-task-management-cleanup ()
  "Clean up test data and reset task management state."
  (interactive)
  (clrhash ai-auto-complete-active-task-plans)
  (setq test-task-management-results nil)
  (setq test-task-management-plan-id nil)
  (message "Task management test data cleaned up"))

(provide 'test-task-management)
;;; test-task-management.el ends here
