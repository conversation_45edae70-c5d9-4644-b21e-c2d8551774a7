{"providers": {"gemini": {"models": {"Gemini-2.0-Flash": {"model": "gemini-2.0-flash", "temperature": 0.7, "maxOutputTokens": 1024, "topP": 0.9, "topK": 40}, "Gemini-2.5-Pro-Preview": {"model": "gemini-2.5-pro-preview-03-25", "temperature": 0.7, "maxOutputTokens": 1024, "topP": 0.9, "topK": 40}, "Gemini-2.0-Flash-Thinking": {"model": "gemini-2.0-flash-thinking-exp-01-21", "temperature": 0.7, "maxOutputTokens": 1024, "topP": 0.9, "topK": 40}, "Gemini-2.5-Pro-Exp-03-25": {"model": "gemini-2.5-pro-exp-03-25", "temperature": 0.7, "maxOutputTokens": 1024, "topP": 0.9, "topK": 40}}}, "openai": {"models": {"GPT-4.1-Mini": {"model": "gpt-4.1-mini", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}, "GPT-4.1-Mini-Second": {"model": "gpt-4.1-mini", "temperature": 0.9, "max_tokens": 2048, "top_p": 0.95, "frequency_penalty": 0.1, "presence_penalty": 0.1}, "GPT-4.1-Nano": {"model": "gpt-4.1-nano", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}, "GPT-4.1": {"model": "gpt-4.1", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}, "GPT-4o": {"model": "gpt-4o", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}, "o3-mini": {"model": "o3-mini", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}, "o4-mini": {"model": "o4-mini", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}}, "anthropic": {"models": {"Claude-3.7-Sonnet": {"model": "claude-3-7-sonnet-20250219", "temperature": 0.7, "max_tokens": 1024}, "Claude-3.5-Sonnet": {"model": "claude-3-5-sonnet-20241022", "temperature": 0.7, "max_tokens": 1024}, "Claude-Sonnet-4": {"model": "claude-sonnet-4", "temperature": 0.7, "max_tokens": 1024}, "Claude-Opus-4": {"model": "claude-opus-4", "temperature": 0.7, "max_tokens": 1024}}}, "openrouter": {"models": {"Gemini-2.0-Flash": {"model": "google/gemini-2.0-flash-exp:free", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9}, "Gemini-2.5-Pro": {"model": "google/gemini-2.5-pro-exp-03-25:free", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9}, "OpenRouter-GPT-4.1-Mini": {"model": "openai/gpt-4.1-mini", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9}, "OpenRouter-GPT-4.1-Nano": {"model": "openai/gpt-4.1-nano", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9}, "OpenRouter-GPT-4.1": {"model": "openai/gpt-4.1", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9}, "OpenRouter-GPT-4o": {"model": "openai/gpt-4o", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9}, "OpenRouter-GPT-o3-mini": {"model": "openai/gpt-o3-mini", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9}, "OpenRouter-GPT-o4-mini": {"model": "openai/gpt-o4-mini", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9}, "OpenRouter-Deepseek-v3-Free": {"model": "deepseek/deepseek-chat-v3-0324:free", "temperature": 0.7, "max_tokens": 1024, "top_p": 0.9}, "OpenRouter-Claude-3.7-Sonnet": {"model": "anthropic/claude-3-7-sonnet", "temperature": 0.7, "max_tokens": 1024}, "OpenRouter-Claude-3.5-Sonnet": {"model": "anthropic/claude-3-5-sonnet-20241022", "temperature": 0.7, "max_tokens": 1024}}}}}