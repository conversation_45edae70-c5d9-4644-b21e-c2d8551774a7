#!/usr/bin/env python3
"""
Test script for the MCP Python SDK.
"""

import sys
import os
import importlib.util

def check_module(module_name):
    """Check if a module is installed and can be imported."""
    spec = importlib.util.find_spec(module_name)
    if spec is None:
        print(f"Module {module_name} is not installed")
        return False
    else:
        print(f"Module {module_name} is installed at {spec.origin}")
        return True

# Check if MCP SDK is installed
print("Checking for MCP Python SDK...")
if check_module("mcp"):
    # Try to import the specific modules we need
    try:
        import mcp
        print(f"MCP SDK version: {getattr(mcp, '__version__', 'unknown')}")
        print(f"MCP SDK path: {mcp.__file__}")
        
        # Check for ClientSession
        if hasattr(mcp, "ClientSession"):
            print("mcp.ClientSession is available")
        else:
            print("mcp.ClientSession is not available")
            
        # Check for StdioServerParameters
        if hasattr(mcp, "StdioServerParameters"):
            print("mcp.StdioServerParameters is available")
        else:
            print("mcp.StdioServerParameters is not available")
            
        # Check for stdio_client
        check_module("mcp.client.stdio")
        try:
            from mcp.client import stdio
            if hasattr(stdio, "stdio_client"):
                print("mcp.client.stdio.stdio_client is available")
            else:
                print("mcp.client.stdio.stdio_client is not available")
        except ImportError as e:
            print(f"Error importing mcp.client.stdio: {str(e)}")
            
    except ImportError as e:
        print(f"Error importing MCP SDK: {str(e)}")
else:
    print("MCP SDK is not installed")

print("\nPython path:")
for path in sys.path:
    print(f"  {path}")

print("\nEnvironment:")
for key, value in os.environ.items():
    if "PYTHON" in key or "PATH" in key:
        print(f"  {key}={value}")

print("\nDone")
