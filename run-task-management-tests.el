;;; run-task-management-tests.el --- Simple script to run task management tests -*- lexical-binding: t -*-

;;; Commentary:
;; This is a simple script to load and run the task management tests.
;; You can execute this file to test whether the task management tools are working.

;;; Code:

;; Load required modules
(message "Loading task management test modules...")

;; Add the current directory and ai-auto-complete-modules to load path
(add-to-list 'load-path default-directory)
(add-to-list 'load-path (expand-file-name "ai-auto-complete-modules" default-directory))

;; Load the core modules first
(condition-case err
    (progn
      (require 'tools/tools-core)
      (require 'tools/task-management)
      (message "Successfully loaded core modules"))
  (error
   (message "ERROR loading core modules: %s" (error-message-string err))
   ;; Try loading directly
   (condition-case err2
       (progn
         (load-file "ai-auto-complete-modules/tools/tools-core.el")
         (load-file "ai-auto-complete-modules/tools/task-management.el")
         (message "Successfully loaded modules directly"))
     (error
      (message "ERROR loading modules directly: %s" (error-message-string err2))
      (error "Cannot proceed without core modules")))))

;; Load the test file
(condition-case err
    (progn
      (load-file "test-task-management.el")
      (message "Successfully loaded test-task-management.el"))
  (error
   (message "ERROR loading test-task-management.el: %s" (error-message-string err))
   (error "Cannot proceed without test module")))

;; Function to run basic tests
(defun run-basic-task-management-tests ()
  "Run basic task management tests without LLM integration."
  (interactive)
  (message "\n=== RUNNING BASIC TASK MANAGEMENT TESTS ===")

  ;; Ensure tools are enabled
  (setq ai-auto-complete-tools-enabled t)

  ;; Run the main test suite
  (if (fboundp 'test-task-management-run-all-tests)
      (progn
        (message "Running comprehensive test suite...")
        (test-task-management-run-all-tests))
    (message "ERROR: test-task-management-run-all-tests function not found")))

;; Function to run LLM integration tests
(defun run-llm-integration-tests ()
  "Run LLM integration tests for task management."
  (interactive)
  (message "\n=== RUNNING LLM INTEGRATION TESTS ===")

  (if (fboundp 'test-task-management-llm-integration)
      (progn
        (message "Running LLM integration test...")
        (test-task-management-llm-integration))
    (message "ERROR: test-task-management-llm-integration function not found")))

;; Function to run all tests
(defun run-all-task-management-tests ()
  "Run all task management tests."
  (interactive)
  (message "\n=== RUNNING ALL TASK MANAGEMENT TESTS ===")

  ;; Run basic tests first
  (run-basic-task-management-tests)

  ;; Wait a moment
  (sit-for 2)

  ;; Run LLM integration tests
  (run-llm-integration-tests)

  (message "\n=== ALL TESTS COMPLETED ===")
  (message "Check the *Task Management Test Results* buffer for detailed results")
  (message "Check the *Task Management LLM Test* buffer for LLM integration results"))

;; Function to clean up test data
(defun cleanup-task-management-tests ()
  "Clean up task management test data."
  (interactive)
  (if (fboundp 'test-task-management-cleanup)
      (progn
        (test-task-management-cleanup)
        (message "Task management test data cleaned up"))
    (message "ERROR: test-task-management-cleanup function not found")))

;; Quick test function for individual tools
(defun quick-test-task-tools ()
  "Quick test of individual task management tools."
  (interactive)
  (message "\n=== QUICK TOOL TEST ===")

  ;; Enable tools
  (setq ai-auto-complete-tools-enabled t)

  ;; Test tool registration
  (let ((tools '("create_task_plan" "execute_planned_step" "update_task_plan" "get_task_plan_status")))
    (message "Checking tool registration...")
    (dolist (tool-name tools)
      (let ((tool (gethash tool-name ai-auto-complete-tools)))
        (if tool
            (message "✓ Tool %s is registered" tool-name)
          (message "✗ Tool %s is NOT registered" tool-name)))))

  ;; Test creating a simple plan
  (message "\nTesting create_task_plan...")
  (let* ((test-params '((task_description . "Quick test task")
                       (agent_name . "quick-test")
                       (steps . (((description . "Test step")
                                 (tool_name . "get_system_info")
                                 (tool_params . ()))))))
         (result (condition-case err
                     (ai-auto-complete-tool-create-task-plan test-params)
                   (error (format "ERROR: %s" (error-message-string err))))))

    (if (stringp result)
        (message "✓ create_task_plan result: %s" result)
      (message "✗ create_task_plan failed: %s" result)))

  (message "\n=== QUICK TEST COMPLETED ==="))

;; Display help
(defun task-management-test-help ()
  "Display help for task management tests."
  (interactive)
  (with-current-buffer (get-buffer-create "*Task Management Test Help*")
    (erase-buffer)
    (insert "=== TASK MANAGEMENT TEST HELP ===\n\n")
    (insert "Available test functions:\n\n")
    (insert "1. (run-basic-task-management-tests)\n")
    (insert "   - Runs comprehensive tests of all task management tools\n")
    (insert "   - Tests tool registration, creation, execution, updates, error handling\n")
    (insert "   - Does not require LLM integration\n\n")
    (insert "2. (run-llm-integration-tests)\n")
    (insert "   - Tests task management tools with actual LLM integration\n")
    (insert "   - Requires working LLM backend\n")
    (insert "   - Tests real-world usage scenarios\n\n")
    (insert "3. (run-all-task-management-tests)\n")
    (insert "   - Runs both basic and LLM integration tests\n")
    (insert "   - Comprehensive test suite\n\n")
    (insert "4. (quick-test-task-tools)\n")
    (insert "   - Quick verification that tools are registered and basic functionality works\n")
    (insert "   - Good for initial debugging\n\n")
    (insert "5. (cleanup-task-management-tests)\n")
    (insert "   - Cleans up test data and resets state\n")
    (insert "   - Run this between test sessions\n\n")
    (insert "Usage:\n")
    (insert "1. Load this file: (load-file \"run-task-management-tests.el\")\n")
    (insert "2. Run any of the test functions above\n")
    (insert "3. Check the results in the generated buffers\n\n")
    (insert "Example:\n")
    (insert "M-x eval-expression RET (run-basic-task-management-tests) RET\n\n")
    (insert "Or evaluate in *scratch* buffer:\n")
    (insert "(load-file \"run-task-management-tests.el\")\n")
    (insert "(run-basic-task-management-tests)\n")
    (goto-char (point-min))
    (display-buffer (current-buffer))))

;; Auto-run basic tests if this file is executed directly
(when (and (boundp 'load-file-name) load-file-name)
  (message "Task management test runner loaded successfully!")
  (message "Run (task-management-test-help) for usage instructions")
  (message "Or run (run-basic-task-management-tests) to start testing"))

(provide 'run-task-management-tests)
;;; run-task-management-tests.el ends here
