;;; chat-sessions.el --- Session management for AI Auto Complete chat -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides session management functionality for the AI Auto Complete chat interface.
;; It allows saving, loading, and managing chat sessions, including conversation history and context.

;;; Code:

(require 'cl-lib)

;; Session management customization options
(defgroup ai-auto-complete-chat-sessions nil
  "Session management for AI Auto Complete chat."
  :group 'ai-auto-complete-chat
  :prefix "ai-auto-complete-chat-session-")

(defcustom ai-auto-complete-sessions-directory
  (expand-file-name "ai-auto-complete-sessions" user-emacs-directory)
  "Directory where chat sessions are stored."
  :type 'directory
  :group 'ai-auto-complete-chat-sessions)

(defcustom ai-auto-complete-session-auto-save nil
  "Whether to automatically save the session when exiting chat."
  :type 'boolean
  :group 'ai-auto-complete-chat-sessions)

(defcustom ai-auto-complete-session-ask-on-new t
  "Whether to ask for a session name when starting a new chat."
  :type 'boolean
  :group 'ai-auto-complete-chat-sessions)

;; Session variables
(defvar-local ai-auto-complete--current-session-id nil
  "ID of the current chat session.")

(defvar-local ai-auto-complete--session-modified nil
  "Whether the current session has been modified since last save.")

;; Core session management functions
(defun ai-auto-complete-chat--ensure-sessions-directory ()
  "Ensure the sessions directory exists."
  (unless (file-exists-p ai-auto-complete-sessions-directory)
    (make-directory ai-auto-complete-sessions-directory t)))

(defun ai-auto-complete-chat--generate-session-id (&optional name)
  "Generate a unique session ID using timestamp and optional NAME."
  (let ((timestamp (format-time-string "%Y%m%d-%H%M%S")))
    (if (and name (not (string-empty-p name)))
        (format "%s-%s" timestamp (replace-regexp-in-string "[^a-zA-Z0-9_-]" "_" name))
      timestamp)))

(defun ai-auto-complete-chat--session-file-path (session-id)
  "Get the file path for SESSION-ID."
  (expand-file-name (concat session-id ".aac-session")
                    ai-auto-complete-sessions-directory))

(defun ai-auto-complete-chat--create-session-data ()
  "Create a data structure representing the current session."
  (list :id ai-auto-complete--current-session-id
        :created-time (or (plist-get (ai-auto-complete-chat--get-session-metadata) :created-time)
                          (current-time))
        :modified-time (current-time)
        :backend ai-auto-complete-backend
        :history ai-auto-complete--chat-history
        :context-files ai-auto-complete-context-files
        :context-images ai-auto-complete-context-images
        :metadata (ai-auto-complete-chat--get-session-metadata)))

(defun ai-auto-complete-chat--get-session-metadata ()
  "Get metadata for the current session."
  (list :buffer-name (buffer-name)
        :major-mode major-mode
        :created-time (current-time)))

(defun ai-auto-complete-chat--save-session-to-file (session-data)
  "Save SESSION-DATA to a file."
  (ai-auto-complete-chat--ensure-sessions-directory)
  (let* ((session-id (plist-get session-data :id))
         (file-path (ai-auto-complete-chat--session-file-path session-id)))
    (with-temp-file file-path
      (prin1 session-data (current-buffer)))
    (message "Session saved: %s" session-id)
    file-path))

(defun ai-auto-complete-chat--load-session-from-file (file-path)
  "Load session data from FILE-PATH."
  (with-temp-buffer
    (insert-file-contents file-path)
    (goto-char (point-min))
    (read (current-buffer))))

(defun ai-auto-complete-chat--restore-session (session-data)
  "Restore a chat session from SESSION-DATA."
  (let ((inhibit-read-only t))
    ;; Set session ID
    (setq ai-auto-complete--current-session-id (plist-get session-data :id))

    ;; Restore backend
    (let ((backend (plist-get session-data :backend)))
      (when backend
        (setq ai-auto-complete-backend backend)))

    ;; Restore context
    (setq ai-auto-complete-context-files (plist-get session-data :context-files))
    (setq ai-auto-complete-context-images (plist-get session-data :context-images))

    ;; Restore chat history
    (setq ai-auto-complete--chat-history (plist-get session-data :history))

    ;; Reinitialize the chat buffer
    (ai-auto-complete-chat-initialize)

    ;; Display the conversation
    (ai-auto-complete-chat--display-conversation)

    ;; Mark as not modified
    (setq ai-auto-complete--session-modified nil)

    (message "Session loaded: %s" ai-auto-complete--current-session-id)))

(defun ai-auto-complete-chat--display-conversation ()
  "Display the current conversation history in the chat buffer."
  (let ((inhibit-read-only t))
    ;; Find where the header ends and content begins
    (goto-char (point-min))
    (while (and (not (eobp))
                (get-text-property (point) 'read-only))
      (forward-line 1))

    ;; Clear the buffer content after the header
    (delete-region (point) (point-max))

    ;; Display each message in the history (in reverse order since history is newest-first)
    (dolist (msg (reverse ai-auto-complete--chat-history))
      (let ((role (car msg))
            (content (cdr msg)))
        (cond
         ((eq role 'user)
          (insert (propertize ai-auto-complete-chat-prompt-prefix
                             'face 'ai-auto-complete-user-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t))
          (insert (propertize content
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t)))
         ((eq role 'assistant)
          (insert (propertize ai-auto-complete-chat-response-prefix
                             'face 'ai-auto-complete-assistant-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t))
          (insert (propertize content
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t)))
         ((eq role 'agent)
          (let ((agent-name (car content))
                (agent-content (cdr content)))
            (insert (propertize (format "@%s: " agent-name)
                               'face 'ai-auto-complete-agent-face
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))
            (insert (propertize agent-content
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t)))))
        (insert (propertize "\n\n"
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))))

    ;; Add a new prompt for user input
    (insert (propertize ai-auto-complete-chat-prompt-prefix
                       'face 'ai-auto-complete-user-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))
    (setq ai-auto-complete--chat-input-marker (point-marker))))

;; Interactive session management commands
(defun ai-auto-complete-chat-session-save (&optional session-name)
  "Save the current chat session.
If SESSION-NAME is provided, use it as the session name."
  (interactive
   (list (read-string "Session name (leave empty for timestamp): ")))

  ;; Generate a session ID if we don't have one yet
  (unless ai-auto-complete--current-session-id
    (setq ai-auto-complete--current-session-id
          (ai-auto-complete-chat--generate-session-id session-name)))

  ;; Create and save the session data
  (let ((session-data (ai-auto-complete-chat--create-session-data)))
    (ai-auto-complete-chat--save-session-to-file session-data)
    (setq ai-auto-complete--session-modified nil)

    ;; Reinitialize to update the header with session info
    (ai-auto-complete-chat-initialize)
    (ai-auto-complete-chat--display-conversation)))

(defun ai-auto-complete-chat-session-load ()
  "Load a saved chat session."
  (interactive)
  (ai-auto-complete-chat--ensure-sessions-directory)
  (let* ((session-files (directory-files ai-auto-complete-sessions-directory t "\\.aac-session$"))
         (session-names (mapcar (lambda (file)
                                 (let* ((session-data (ai-auto-complete-chat--load-session-from-file file))
                                        (id (plist-get session-data :id)))
                                   (cons id file)))
                               session-files))
         (selected (completing-read "Select session to load: "
                                   (mapcar #'car session-names) nil t)))

    (when (and ai-auto-complete--session-modified
               (y-or-n-p "Current session has unsaved changes. Save before loading? "))
      (call-interactively 'ai-auto-complete-chat-session-save))

    (when selected
      (let* ((file-path (cdr (assoc selected session-names)))
             (session-data (ai-auto-complete-chat--load-session-from-file file-path)))
        (ai-auto-complete-chat--restore-session session-data)))))

(defun ai-auto-complete-chat-session-new (&optional session-name)
  "Start a new chat session.
If SESSION-NAME is provided, use it as the session name."
  (interactive
   (list (when ai-auto-complete-session-ask-on-new
           (read-string "New session name (leave empty for timestamp): "))))

  (when (and ai-auto-complete--session-modified
             (y-or-n-p "Current session has unsaved changes. Save before starting new session? "))
    (call-interactively 'ai-auto-complete-chat-session-save))

  ;; Clear history and create new session
  (setq ai-auto-complete--chat-history nil)
  (setq ai-auto-complete--current-session-id
        (ai-auto-complete-chat--generate-session-id session-name))
  (setq ai-auto-complete--session-modified nil)

  ;; Reinitialize the chat buffer
  (ai-auto-complete-chat-initialize)
  (message "Started new session: %s" ai-auto-complete--current-session-id))

(defun ai-auto-complete-chat-session-list ()
  "List all saved chat sessions and allow selecting one."
  (interactive)
  (ai-auto-complete-chat--ensure-sessions-directory)
  (let* ((session-files (directory-files ai-auto-complete-sessions-directory t "\\.aac-session$"))
         (sessions (mapcar (lambda (file)
                            (let* ((session-data (ai-auto-complete-chat--load-session-from-file file))
                                   (id (plist-get session-data :id))
                                   (created (format-time-string "%Y-%m-%d %H:%M:%S"
                                                               (plist-get session-data :created-time)))
                                   (modified (format-time-string "%Y-%m-%d %H:%M:%S"
                                                                (plist-get session-data :modified-time)))
                                   (msg-count (length (plist-get session-data :history))))
                              (list id created modified msg-count file)))
                          session-files))
         (buffer (get-buffer-create "*AI Auto Complete Sessions*")))

    (with-current-buffer buffer
      (let ((inhibit-read-only t))
        (erase-buffer)
        (insert "AI Auto Complete Chat Sessions\n\n")
        (insert (format "%-30s %-20s %-20s %-10s\n" "Session ID" "Created" "Modified" "Messages"))
        (insert (make-string 80 ?-))
        (insert "\n")

        (if sessions
            (dolist (session sessions)
              (let ((id (nth 0 session))
                    (created (nth 1 session))
                    (modified (nth 2 session))
                    (msg-count (nth 3 session)))
                (insert (format "%-30s %-20s %-20s %-10d\n"
                               id created modified msg-count))))
          (insert "No saved sessions found.\n"))

        (insert "\n")
        (insert "Press RET to load a session, d to delete, r to rename, q to quit\n")

        (goto-char (point-min))
        (forward-line 3) ; Move to the first session

        (setq buffer-read-only t)

        ;; Set up keybindings for the session list buffer
        (use-local-map (make-sparse-keymap))
        (local-set-key (kbd "RET") 'ai-auto-complete-chat--session-list-load)
        (local-set-key (kbd "d") 'ai-auto-complete-chat--session-list-delete)
        (local-set-key (kbd "r") 'ai-auto-complete-chat--session-list-rename)
        (local-set-key (kbd "q") 'quit-window)))

    (switch-to-buffer buffer)))

(defun ai-auto-complete-chat--session-list-load ()
  "Load the session at point in the session list buffer."
  (interactive)
  (let ((line (buffer-substring (line-beginning-position) (line-end-position))))
    (if (string-match "^\\([^ ]+\\)" line)
        (let* ((session-id (match-string 1 line))
               (file-path (ai-auto-complete-chat--session-file-path session-id)))
          (if (file-exists-p file-path)
              (progn
                (quit-window)
                (ai-auto-complete-chat)
                (let ((session-data (ai-auto-complete-chat--load-session-from-file file-path)))
                  (ai-auto-complete-chat--restore-session session-data)))
            (message "Session file not found: %s" file-path)))
      (message "No session at point"))))

(defun ai-auto-complete-chat--session-list-delete ()
  "Delete the session at point in the session list buffer."
  (interactive)
  (let ((line (buffer-substring (line-beginning-position) (line-end-position))))
    (if (string-match "^\\([^ ]+\\)" line)
        (let* ((session-id (match-string 1 line))
               (file-path (ai-auto-complete-chat--session-file-path session-id)))
          (when (and (file-exists-p file-path)
                     (y-or-n-p (format "Delete session %s? " session-id)))
            (delete-file file-path)
            (message "Session deleted: %s" session-id)
            (ai-auto-complete-chat-session-list))) ; Refresh the list
      (message "No session at point"))))

(defun ai-auto-complete-chat--session-list-rename ()
  "Rename the session at point in the session list buffer."
  (interactive)
  (let ((line (buffer-substring (line-beginning-position) (line-end-position))))
    (if (string-match "^\\([^ ]+\\)" line)
        (let* ((old-id (match-string 1 line))
               (old-file (ai-auto-complete-chat--session-file-path old-id))
               (new-name (read-string (format "New name for session %s: " old-id)))
               (new-id (ai-auto-complete-chat--generate-session-id new-name))
               (new-file (ai-auto-complete-chat--session-file-path new-id)))
          (when (and (file-exists-p old-file)
                     (not (string-empty-p new-name)))
            ;; Load, modify, and save with new ID
            (let ((session-data (ai-auto-complete-chat--load-session-from-file old-file)))
              (setq session-data (plist-put session-data :id new-id))
              (ai-auto-complete-chat--save-session-to-file session-data)
              (delete-file old-file)
              (message "Session renamed: %s -> %s" old-id new-id)
              (ai-auto-complete-chat-session-list)))) ; Refresh the list
      (message "No session at point"))))

(defun ai-auto-complete-chat-session-delete ()
  "Delete a saved chat session."
  (interactive)
  (ai-auto-complete-chat--ensure-sessions-directory)
  (let* ((session-files (directory-files ai-auto-complete-sessions-directory t "\\.aac-session$"))
         (session-names (mapcar (lambda (file)
                                 (let* ((session-data (ai-auto-complete-chat--load-session-from-file file))
                                        (id (plist-get session-data :id)))
                                   (cons id file)))
                               session-files))
         (selected (completing-read "Select session to delete: "
                                   (mapcar #'car session-names) nil t)))

    (when selected
      (let ((file-path (cdr (assoc selected session-names))))
        (when (and (file-exists-p file-path)
                   (y-or-n-p (format "Delete session %s? " selected)))
          (delete-file file-path)
          (message "Session deleted: %s" selected))))))

(defun ai-auto-complete-chat-session-rename ()
  "Rename the current or a saved chat session."
  (interactive)
  (if ai-auto-complete--current-session-id
      ;; Rename current session
      (let* ((old-id ai-auto-complete--current-session-id)
             (new-name (read-string (format "New name for current session %s: " old-id)))
             (new-id (ai-auto-complete-chat--generate-session-id new-name)))
        (when (not (string-empty-p new-name))
          (setq ai-auto-complete--current-session-id new-id)
          (setq ai-auto-complete--session-modified t)
          (ai-auto-complete-chat-session-save)
          (message "Current session renamed: %s -> %s" old-id new-id)))

    ;; No current session, rename a saved one
    (ai-auto-complete-chat--ensure-sessions-directory)
    (let* ((session-files (directory-files ai-auto-complete-sessions-directory t "\\.aac-session$"))
           (session-names (mapcar (lambda (file)
                                   (let* ((session-data (ai-auto-complete-chat--load-session-from-file file))
                                          (id (plist-get session-data :id)))
                                     (cons id file)))
                                 session-files))
           (selected (completing-read "Select session to rename: "
                                     (mapcar #'car session-names) nil t)))

      (when selected
        (let* ((old-file (cdr (assoc selected session-names)))
               (new-name (read-string (format "New name for session %s: " selected)))
               (new-id (ai-auto-complete-chat--generate-session-id new-name))
               (new-file (ai-auto-complete-chat--session-file-path new-id)))
          (when (and (file-exists-p old-file)
                     (not (string-empty-p new-name)))
            ;; Load, modify, and save with new ID
            (let ((session-data (ai-auto-complete-chat--load-session-from-file old-file)))
              (setq session-data (plist-put session-data :id new-id))
              (ai-auto-complete-chat--save-session-to-file session-data)
              (delete-file old-file)
              (message "Session renamed: %s -> %s" selected new-id))))))))

;; Auto-save functionality
(defun ai-auto-complete-chat--maybe-auto-save-session ()
  "Save the current session if auto-save is enabled and session is modified."
  (when (and ai-auto-complete-session-auto-save
             ai-auto-complete--session-modified
             ai-auto-complete--current-session-id
             (get-buffer ai-auto-complete-chat-buffer-name))
    (with-current-buffer ai-auto-complete-chat-buffer-name
      (ai-auto-complete-chat-session-save))))

;; Add hooks for auto-saving
(defun ai-auto-complete-chat-session-setup-hooks ()
  "Set up hooks for session management."
  ;; Add to kill-buffer-hook for the chat buffer
  (add-hook 'kill-buffer-hook
            (lambda ()
              (when (and (boundp 'ai-auto-complete-chat-buffer-name)
                         (string= (buffer-name) ai-auto-complete-chat-buffer-name))
                (ai-auto-complete-chat--maybe-auto-save-session))))

  ;; Add to kill-emacs-hook
  (add-hook 'kill-emacs-hook 'ai-auto-complete-chat--maybe-auto-save-session))

;; Initialize hooks
(ai-auto-complete-chat-session-setup-hooks)

(provide 'chat-sessions)
;;; chat-sessions.el ends here
