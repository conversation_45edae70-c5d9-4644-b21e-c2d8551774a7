;;; context.el --- Chat context display for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file contains functions for displaying context updates in the chat interface.
;; The actual context management is handled by shared-context.el, which provides
;; a unified context system across all modes (chat, text, code).

;;; Code:

(require 'cl-lib)
(require 'chat-customization)  ;; Ensure faces are loaded

;; Note: We don't require shared-context here to avoid circular dependencies

;; Define string-empty-p if it's not available (normally provided by 's package)
(unless (fboundp 'string-empty-p)
  (defun string-empty-p (string)
    "Return t if STRING is empty or nil."
    (or (null string) (string= string ""))))

;; Define variables that might be missing
(defvar ai-auto-complete-max-file-size 1000000
  "Maximum size of files that can be added to context in bytes.")

(defvar ai-auto-complete-context-file-extensions '(".txt" ".md" ".org" ".el" ".py" ".js" ".html" ".css" ".json" ".c" ".cpp" ".h" ".java" ".rb" ".php")
  "List of file extensions that can be added as text context.")

(defvar ai-auto-complete-image-file-extensions '(".png" ".jpg" ".jpeg" ".gif" ".webp")
  "List of file extensions that can be added as image context.")

(defvar ai-auto-complete-chat-buffer-name "*AI Auto Complete Chat*"
  "Name of the chat buffer.")

;; Note: We use the global context variables from shared-context.el
;; (ai-auto-complete-context-files and ai-auto-complete-context-images)
;; instead of buffer-local variables for context to ensure consistency across modes.

(defvar-local ai-auto-complete--chat-input-marker nil
  "Marker for the current input position in the chat buffer.")

;; Note: Faces are defined in chat-customization.el

(defvar ai-auto-complete-chat-prompt-prefix "You: "
  "Prefix for user messages in the chat interface.")

(defvar ai-auto-complete-chat-response-prefix "AI: "
  "Prefix for AI responses in the chat interface.")

;; Note: Context management functions have been moved to shared-context.el
;; to ensure consistent behavior across all modes.

(defun ai-auto-complete-chat-display-context-update (file-name action type)
  "Display a message that FILE-NAME was ACTION as TYPE context in the chat buffer."
  (message "Attempting to display context update in chat buffer: %s %s %s" file-name action type)
  (if (get-buffer ai-auto-complete-chat-buffer-name)
      (progn
        (message "Chat buffer found, updating...")
        (with-current-buffer (get-buffer ai-auto-complete-chat-buffer-name)
          ;; Make sure we're in chat mode
          (unless ai-auto-complete-chat-mode
            (message "Enabling chat mode in buffer")
            (ai-auto-complete-chat-mode 1))

          (let ((inhibit-read-only t))
            ;; If there's an input marker, delete from there to the end
            (if (and ai-auto-complete--chat-input-marker
                     (marker-position ai-auto-complete--chat-input-marker))
                (progn
                  (message "Input marker found at position %d, clearing input area"
                           (marker-position ai-auto-complete--chat-input-marker))
                  (delete-region ai-auto-complete--chat-input-marker (point-max)))
              ;; If no input marker exists, create one at the end
              (message "No input marker found, creating one at the end of buffer")
              (goto-char (point-max))
              (setq ai-auto-complete--chat-input-marker (point-marker)))

            ;; Go to the end of the buffer
            (goto-char (point-max))

            ;; Insert the context update message
            (insert (propertize "\n"
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))
            (let ((update-message (format "System: %s %s %s as context.\n\n"
                                         (capitalize type)
                                         (file-name-nondirectory file-name)
                                         action)))
              (message "Inserting update message: %s" update-message)
              (insert (propertize update-message
                                'face 'ai-auto-complete-system-face
                                'read-only t
                                'front-sticky t
                                'rear-nonsticky t)))

            ;; Insert the prompt prefix and set the input marker
            (insert (propertize ai-auto-complete-chat-prompt-prefix
                               'face 'ai-auto-complete-user-face
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))

            ;; Set the input marker at the current position
            (setq ai-auto-complete--chat-input-marker (point-marker))
            (message "New input marker set at position %d" (marker-position ai-auto-complete--chat-input-marker))

            ;; Ensure the buffer is writable at the input position
            (put-text-property (point) (point) 'read-only nil)

            ;; Make sure the buffer is in a state where the user can type
            (setq buffer-read-only nil)

            ;; Make sure the cursor is at the input position
            (goto-char (point-max))

            ;; Ensure the buffer is visible
            (message "Making chat buffer visible")
            (display-buffer (current-buffer))
            (message "Context update completed successfully"))))
    (message "Chat buffer not found, cannot display context update")))

(provide 'context)
;;; context.el ends here
