;; Continue with LLM after executing MCP tool calls
(defun ai-auto-complete-mcp-continue-with-llm (tool-calls results response callback &optional agent-name)
  "Continue with LLM after executing MCP TOOL-CALLS with RESULTS.
RESPONSE is the original LLM response, CALLBACK is the function to call with the final result.
AGENT-NAME is the optional name of the agent making the request."
  (when ai-auto-complete-mcp-debug-mode
    (message "[MCP-DEBUG] Continuing with LLM after executing MCP tool calls"))
  
  ;; Build the continuation prompt
  (let ((continuation-prompt (ai-auto-complete-mcp-build-continuation-prompt
                             tool-calls results response)))
    
    ;; Send the continuation prompt to the LLM
    (when ai-auto-complete-mcp-debug-mode
      (message "[MCP-DEBUG] Sending continuation prompt to LLM"))
    
    (ai-auto-complete-complete
     (ai-auto-complete-get-current-backend)
     continuation-prompt
     nil  ;; Use empty history to avoid confusion with previous messages
     (lambda (continuation-response)
       (when ai-auto-complete-mcp-debug-mode
         (message "[MCP-DEBUG] Received continuation response from LLM"))
       
       ;; Check if the continuation response contains more tool calls
       (if (string-match-p "<mcp-tool\\s-+\\(name\\|server\\)=" continuation-response)
           ;; Process the new tool calls
           (ai-auto-complete-mcp-process-tool-calls continuation-response callback agent-name)
         
         ;; No more tool calls, combine the original response and continuation
         (let ((final-response (ai-auto-complete-mcp-combine-responses
                               response continuation-response tool-calls results)))
           (funcall callback final-response))))
     agent-name))))
